import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/main.dart' as app;
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:integration_test/integration_test.dart';

Future<void> main() async {
  await assessmentTest();
}

Future<void> assessmentTest() async {
  late final DioAdapter dioAdapter;
  setUpAll(() async {
    /// Cooking dependencies
    IntegrationTestWidgetsFlutterBinding.ensureInitialized();
    app.integrationMode = IntegrationMode.integration;
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
        'Is-App': true,
        // 'Cookie': Injector.instance<AppDB>().cookie,
      },
    );
    final dio = Dio(options);
    dioAdapter = DioAdapter(dio: dio);
    dio.httpClientAdapter = dioAdapter;
    await Hive.initFlutter();
    await Injector.initMockModules(dio);
    await Injector.instance.isReady<AppDB>();
  });
  testWidgets('Verify assessment flow', (tester) async {
    await app.main(child: MyDataPage());
    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 10));
    final dataProcessingYesBtn = find.byKey(const Key('data_processing_yes'));
    final dataProcessingNoBtn = find.byKey(const Key('data_processing_no'));
    expect(dataProcessingYesBtn, findsOneWidget);
    expect(dataProcessingNoBtn, findsOneWidget);
    dioAdapter.onPost(
      EndPoints.dataProcessing,
      (server) => server.reply(200, {'success': true}),
      data: {'value': true},
    );
    await tester.tap(dataProcessingYesBtn);
    final dataSharingRpiNoBtn = find.byKey(const Key('data_sharing_rpi_no'));
    final dataSharingRpiYesBtn = find.byKey(const Key('data_sharing_rpi_yes'));
    await tester.ensureVisible(dataSharingRpiYesBtn);
    await tester.pumpAndSettle();
    expect(dataSharingRpiNoBtn, findsOneWidget);
    expect(dataSharingRpiYesBtn, findsOneWidget);
    dioAdapter.onPost(
      EndPoints.dataSharingRpiUnderstood,
      (server) => server.reply(200, {'success': true}),
      data: {'value': true},
    );
    await tester.tap(dataSharingRpiYesBtn);
    await tester.pumpAndSettle();
    final dataSharingNoBtn = find.byKey(const Key('data_sharing_no'));
    final dataSharingYesBtn = find.byKey(const Key('data_sharing_yes'));
    expect(dataSharingNoBtn, findsOneWidget);
    expect(dataSharingYesBtn, findsOneWidget);
    dioAdapter.onPost(
      EndPoints.dataSharing,
      (server) => server.reply(200, {'success': true}),
      data: {'value': true},
    );
    await tester.tap(dataSharingYesBtn);
    await tester.pumpAndSettle();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    dioAdapter.onGet(
      EndPoints.getUserData,
      (server) => server.reply(200, {'success': true}),
    );
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    await testAssessmentWelcome(tester);
    await testAssessmentRecoveryProgram(tester, dioAdapter);
    await testAssessmentLife(tester, dioAdapter);
    await testAssessmentDifficultSituation(tester, dioAdapter);
  });
}

Future<void> testAssessmentWelcome(WidgetTester tester) async {
  final nextBtn = find.byKey(const Key('next_btn'));
  expect(nextBtn, findsOneWidget);
  await tester.tap(nextBtn);
  await tester.pumpAndSettle();
  final snackbar = find.text('You need to watch this video before continuing');
  expect(snackbar, findsOneWidget);
  await tester.pump(const Duration(milliseconds: 2500));
  final element = tester.element(nextBtn);

  /// Update video timer end to avoid waiting in test case
  element.read<AssessmentCubit>().isVideoEnded.value = true;
  await tester.pumpAndSettle();
  await tester.tap(nextBtn);
  await tester.pumpAndSettle();
}

Future<void> testAssessmentRecoveryProgram(WidgetTester tester, DioAdapter dioAdapter) async {
  dioAdapter.onPost(
    EndPoints.recoveryProgram,
    (server) => server.reply(200, {'success': true}),
    data: {
      'age': 19,
      'gender': 0,
      'ethnicity': 0,
      'name': 'John Doe',
      'addictionCase': 2,
      'specialAddiction': 0,
    },
  );
  final nextBtn = find.byKey(const Key('next_btn'));
  // expect(nextBtn, findsOneWidget);
  // await tester.tap(nextBtn);
  // await tester.pump();
  // final snackbar = find.text('You need to answer all the questions before you can continue');
  // expect(snackbar, findsOneWidget);
  // await tester.pump(const Duration(milliseconds: 2500));
  final nameField = find.byKey(const Key('name_field'));
  expect(nameField, findsOneWidget);
  await tester.enterText(nameField, 'John Doe');
  FocusManager.instance.primaryFocus?.unfocus();
  final ageIncrementBtn = find.byKey(const Key('age_increase_key'));
  final ageDecrementBtn = find.byKey(const Key('age_decrease_key'));
  expect(ageIncrementBtn, findsOneWidget);
  expect(ageDecrementBtn, findsOneWidget);
  await tester.tap(ageIncrementBtn);
  await tester.pump();
  final genderSelector = find.byKey(const Key('gender_select'));
  expect(genderSelector, findsOneWidget);
  await tester.tap(genderSelector);
  await tester.pump();
  await tester.tap(find.text('Male'));
  await tester.pump();
  final ethnicitySelector = find.byKey(const Key('ethnicity_select'));
  await tester.ensureVisible(ethnicitySelector);
  expect(ethnicitySelector, findsOneWidget);
  await tester.tap(ethnicitySelector);
  await tester.pump();
  await tester.tap(find.text('White: British'));
  await tester.pump();
  final alcoholAndDrugOptions = find.byKey(const Key('alcohol_drugs_1'));
  await tester.ensureVisible(alcoholAndDrugOptions);
  expect(alcoholAndDrugOptions, findsOneWidget);
  await tester.tap(alcoholAndDrugOptions);
  await tester.pump();
  final additionalAlcoholBtn = find.byKey(const Key('Alcohol'));
  await tester.ensureVisible(additionalAlcoholBtn);
  expect(additionalAlcoholBtn, findsOneWidget);
  await tester.tap(additionalAlcoholBtn);
  await tester.pump();
  await tester.tap(nextBtn);
  await tester.pumpAndSettle();
}

Future<void> testAssessmentLife(WidgetTester tester, DioAdapter dioAdapter) async {
  dioAdapter.onPost(
    EndPoints.userLife,
    (server) => server.reply(200, {'success': true}),
    data: {
      'quality': 0,
      'health': 0,
      'activities': 0,
      'relationships': 0,
      'work': 0,
      'difficulties': 5,
      'rate': 5,
    },
  );
  final nextBtn = find.byKey(const Key('next_btn'));
  expect(nextBtn, findsOneWidget);
  final lifeQualityVeryGoodBtn = find.byKey(const Key('Very good_qualityList'));
  expect(lifeQualityVeryGoodBtn, findsOneWidget);
  await tester.tap(lifeQualityVeryGoodBtn);
  await tester.pump();
  final healthVerySatisfiedBtn = find.byKey(const Key('Very satisfied_healthList'));
  await tester.ensureVisible(healthVerySatisfiedBtn);
  expect(healthVerySatisfiedBtn, findsOneWidget);
  await tester.tap(healthVerySatisfiedBtn);
  await tester.pump();
  final activityVerySatisfiedBtn = find.byKey(const Key('Very satisfied_activitiesValue'));
  await tester.ensureVisible(activityVerySatisfiedBtn);
  expect(activityVerySatisfiedBtn, findsOneWidget);
  await tester.tap(activityVerySatisfiedBtn);
  await tester.pump();
  final relationshipVerySatisfiedBtn = find.byKey(const Key('Very satisfied_relationshipsList'));
  await tester.ensureVisible(relationshipVerySatisfiedBtn);
  expect(relationshipVerySatisfiedBtn, findsOneWidget);
  await tester.tap(relationshipVerySatisfiedBtn);
  await tester.pump();
  final workVerySatisfiedBtn = find.byKey(const Key('Very satisfied_workList'));
  await tester.ensureVisible(workVerySatisfiedBtn);
  expect(workVerySatisfiedBtn, findsOneWidget);
  await tester.tap(workVerySatisfiedBtn);
  await tester.pump();
  final lifeFirstSlider = find.byKey(const Key('lifeFirstSlider'));
  await tester.ensureVisible(lifeFirstSlider);
  expect(lifeFirstSlider, findsOneWidget);
  await tester.tap(lifeFirstSlider);
  await tester.pump();
  final lifeSecondSlider = find.byKey(const Key('lifeSecondSlider'));
  await tester.ensureVisible(lifeSecondSlider);
  expect(lifeSecondSlider, findsOneWidget);
  await tester.tap(lifeSecondSlider);
  await tester.pump();
  await tester.tap(nextBtn);
  await tester.pump(const Duration(milliseconds: 500));
}

Future<void> testAssessmentDifficultSituation(WidgetTester tester, DioAdapter dioAdapter) async {
  dioAdapter.onPost(
    EndPoints.difficultSitutation,
    (server) => server.reply(200, {'success': true}),
    data: {
      'conflict': 0,
      'work': 0,
      'money': 0,
      'risks': 0,
      'pressure': 0,
      'rate': 5,
    },
  );
  final nextBtn = find.byKey(const Key('next_btn'));
  expect(nextBtn, findsOneWidget);
  final conflictNo = find.byKey(const Key('conflict_no'));
  final conflictYes = find.byKey(const Key('conflict_yes'));
  expect(conflictNo, findsOneWidget);
  expect(conflictYes, findsOneWidget);
  await tester.tap(conflictNo);
  await tester.pump();
  final problemsNo = find.byKey(const Key('problems_no'));
  final problemsYes = find.byKey(const Key('problems_yes'));
  expect(problemsNo, findsOneWidget);
  expect(problemsYes, findsOneWidget);
  await tester.ensureVisible(problemsNo);
  await tester.tap(problemsNo);
  await tester.pump();
  final moneyNo = find.byKey(const Key('money_no'));
  final moneyYes = find.byKey(const Key('money_yes'));
  expect(moneyNo, findsOneWidget);
  expect(moneyYes, findsOneWidget);
  await tester.ensureVisible(moneyNo);
  await tester.tap(moneyNo);
  await tester.pump();

  final riskyNo = find.byKey(const Key('risky_no'));
  final riskyYes = find.byKey(const Key('risky_yes'));
  expect(riskyNo, findsOneWidget);
  expect(riskyYes, findsOneWidget);
  await tester.ensureVisible(riskyNo);
  await tester.tap(riskyNo);
  await tester.pump();

  final pressuredNo = find.byKey(const Key('pressured_no'));
  final pressuredYes = find.byKey(const Key('pressured_yes'));
  expect(pressuredNo, findsOneWidget);
  expect(pressuredYes, findsOneWidget);
  await tester.ensureVisible(pressuredNo);
  await tester.tap(pressuredNo);
  await tester.pump();

  final overallSliver = find.byKey(const Key('overall_slider'));
  await tester.ensureVisible(overallSliver);
  expect(overallSliver, findsOneWidget);
  await tester.tap(overallSliver);
  await tester.pump();
  await tester.tap(nextBtn);
  await tester.pumpAndSettle();
}
