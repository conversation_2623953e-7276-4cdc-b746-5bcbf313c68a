import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/main.dart' as app;
import 'package:breakingfree_v2/res/enums.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

Future<void> main() async {
  setUpAll(
    () async {
      IntegrationTestWidgetsFlutterBinding.ensureInitialized();
      app.integrationMode = IntegrationMode.integration;
    },
  );
  await loginTest();
}

Future<void> loginTest() async {
  testWidgets('tap on the floating action button, verify counter', (tester) async {
    await app.main();
    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 10));
    final langSelector = find.byKey(const Key('lang_selector'));
    expect(langSelector, findsOneWidget);
    await tester.tap(langSelector);
    await tester.pump();
    final ukOption = find.text('United Kingdom');
    await tester.tap(ukOption.last);
    await tester.pumpAndSettle();
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle();
    final loginBtn = find.byKey(const Key('login_button'));
    final emailField = find.byKey(const Key('email_field'));
    final passField = find.byKey(const Key('pass_field'));
    await tester.enterText(emailField, '<EMAIL>');
    await tester.enterText(passField, '12345678');
    FocusManager.instance.primaryFocus!.unfocus();
    await tester.pumpAndSettle();
    await tester.tap(loginBtn);
    await tester.pumpAndSettle();
    await logout(tester);
    await forgotPassword(tester);
  });
}

Future<void> logout(WidgetTester tester) async {
  final menuBtn = find.byKey(const Key('menu_btn'));
  expect(menuBtn, findsOneWidget);
  await tester.tap(menuBtn);
  await tester.pump(const Duration(milliseconds: 500));
  final logoutBtn = find.text('Log out');
  await tester.tap(logoutBtn);
  await tester.pumpAndSettle();
  final yesBtn = find.text('Yes');
  await tester.tap(yesBtn);
  await tester.pumpAndSettle();
}

Future<void> forgotPassword(WidgetTester tester) async {
  final forgotPassBtn = find.byKey(const Key('forgot_pass_btn'));
  await tester.tap(forgotPassBtn);
  await tester.pumpAndSettle();
  final emailField = find.byKey(const Key('email'));
  await tester.tap(find.text('Reset'));
  await tester.pump();
  expect(find.text('Email is required'), findsOneWidget);
  await tester.enterText(emailField, '<EMAIL>');
  FocusManager.instance.primaryFocus!.unfocus();
  await tester.pumpAndSettle();
  await tester.tap(find.text('Reset'));
  await tester.pumpAndSettle();
  expect(find.text(AuthLocaleKeys.forgetPasswordSuccess.tr()), findsOneWidget);
}
