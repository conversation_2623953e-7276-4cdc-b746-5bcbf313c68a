<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Breaking Free</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>breakingfree_v2</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location permission is requested so we can track your location, and alert you when you are approaching a risky place you have identified.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Location permission is requested so we can track your location, and alert you when you are approaching a risky place you have identified.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location permission is requested so we can track your location, and alert you when you are approaching a risky place you have identified.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSMotionUsageDescription</key>
    <string>Used to recognize user activity information.</string>
	
	<!-- Deep Link Configuration -->
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>breakingfree.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
	</array>

	<!-- Associated Domains for Universal Links -->
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:breakingfreeonline.com.au</string>
		<string>applinks:breakingfreeonline.com</string>
		<string>applinks:breakingfreeonline.ca</string>
		<string>applinks:breakingfreeonline.us</string>
		<string>applinks:uk-com-toolkit-edfff28d1128.herokuapp.com</string>
	</array>
</dict>
</plist>
