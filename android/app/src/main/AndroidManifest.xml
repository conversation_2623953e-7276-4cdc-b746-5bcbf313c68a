<manifest xmlns:android="http://schemas.android.com/apk/res/android">
<uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
     <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
     <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
     <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
     <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
        <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
   <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
   <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <application
        android:label="Breaking Free"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />
            <!-- Deep Link Intent Filter for Password Reset -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    
    <data
        android:scheme="https"
        android:host="uk-com-toolkit-edfff28d1128.herokuapp.com"
        android:pathPrefix="/accounts/reset" />
    <data
        android:scheme="https"
        android:host="uk-com-toolkit-edfff28d1128.herokuapp.com"
        android:pathPrefix="/verify" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.ca"
        android:pathPrefix="/accounts/reset" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.ca"
        android:pathPrefix="/verify" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.com.au"
        android:pathPrefix="/accounts/reset" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.com.au"
        android:pathPrefix="/verify" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.us"
        android:pathPrefix="/accounts/reset" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.us"
        android:pathPrefix="/verify" />
     <data
        android:scheme="https"
        android:host="breakingfreeonline.com"
        android:pathPrefix="/accounts/reset" />
    <data
        android:scheme="https"
        android:host="breakingfreeonline.com"
        android:pathPrefix="/verify" />
</intent-filter>

        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
               <receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver" android:exported="true">
    <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED"/>
    </intent-filter>
</receiver>
<receiver android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" android:exported="true" />

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
         <meta-data
            android:name="com.google.android.geo.API_KEY"
           android:value="AIzaSyDbsJmfsdTiHM9O56vuUXKD4MsZuC7T6M8"/>
    </application>
</manifest>
