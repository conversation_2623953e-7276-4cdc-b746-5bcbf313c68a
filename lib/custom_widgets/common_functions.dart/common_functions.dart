import 'dart:ui';

import 'package:breakingfree_v2/notification_service/notification_helper.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;


class CommonFunction {

  static tz.TZDateTime nextInstanceOfWeekdayTime(int weekday, int hour, int minute) {
    final tz.TZDateTime now = tz.TZDateTime.now(tz.local);
    tz.TZDateTime scheduledDate = tz.TZDateTime(
      tz.local,
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );

    return scheduledDate;
  }

  static var notification = FlutterLocalNotificationsPlugin();

  static Future<void> setScheduleOneTimeNotification({
    required String date,
    required int id,
    required String payload,
    String body = '',
    String title = '',
    bool isDaily = false,
  }) async {
    // final notificationServices = NotificationServices();

    // print('set date test :- $date');
    // final dt1 = DateTime.parse(date);
    // final dt2 = DateTime.now();

    // if (dt1.isAfter(dt2)) {
    //   print('isAfter set date test :- $date');

    await LocalNotificationHelper.localNotificationHelper.sendScheduleNotification(
      date: date,
      id: id,
      body: body,
      payload: payload,
      title: title,
      isDaily: isDaily,
    );
    // }
  }

  static Future<void> schedulingNotificationWithDays({
    required String title,
    required String body,
    required int notificationID,
    required String day,
    required String payLoad,
    required int weekDay,
    required int hour,
    required int minute,
  }) async {
    try {
      // Ensure timezone is initialized
      var timezone = await FlutterTimezone.getLocalTimezone();
      tz.initializeTimeZones();
      tz.setLocalLocation(tz.getLocation(timezone));

      // Calculate the next occurrence of the desired weekday and time
      final scheduledDate = nextInstanceOfWeekdayTime(weekDay, hour, minute);

      print("[Notification] Scheduled Date (local): $scheduledDate");
      print("[Notification] Now (local): ${tz.TZDateTime.now(tz.local)}");
      print("[Notification] Title: $title, Body: $body, ID: $notificationID, Payload: $payLoad");

      final notification = LocalNotificationHelper.notification;

      AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
            "weekly_schedule_channel", 
            "Weekly Schedule Channel",
            channelDescription: "Channel for weekly scheduled notifications",
            color: const Color(0xffCDFB7B),
            importance: Importance.high,
            priority: Priority.high,
            styleInformation: BigTextStyleInformation(body, contentTitle: title)
          );
      
      DarwinNotificationDetails iosNotificationDetails =
          const DarwinNotificationDetails(
            presentAlert: true, 
            presentBadge: true, 
            presentSound: true
          );
      
      NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, 
        iOS: iosNotificationDetails
      );

      await notification.zonedSchedule(
        notificationID,
        title,
        body,
        scheduledDate,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.dayOfWeekAndTime, // <-- WEEKLY
        payload: payLoad,
      );
      print("[Notification] Scheduled successfully!");
    } catch (e, st) {
      print('[Notification] Error scheduling notification: $e\n$st');
    }
  }

  /// Schedules a test weekly notification for Tuesday at 18:48 (6:48 PM)
  // static Future<void> testWeeklyNotificationForSpecificTime() async {
  // '|||||||| schedule'.logV;
  //   // 29-07-2025 is a Tuesday, but for weekly logic, only the weekday, hour, and minute matter
  //   // We'll use weekDay = 2 (Tuesday), hour = 18, minute = 50
  //   await schedulingNotificationWithDays(
  //     title: "Test Weekly Notification",
  //     body: "This is a test notification for Tuesday at 18:50 (6:50 PM)",
  //     notificationID: 9998,
  //     payLoad: "test_weekly_notification",
  //     weekDay: DateTime.tuesday, // 2
  //     day: "WED",
  //     hour: 09,
  //     minute: 43,
  //   );
  // }

}
