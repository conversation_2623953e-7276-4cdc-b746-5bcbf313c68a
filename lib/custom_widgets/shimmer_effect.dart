import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerWidget extends StatelessWidget {
  const ShimmerWidget({
    super.key,
    this.baseColor,
    this.highlightColor,
    this.height,
    this.width,
    this.margin,
    this.child,
  });
  final Color? baseColor;
  final Color? highlightColor;
  final double? height;
  final double? width;
  final EdgeInsets? margin;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? AppColors.greyColor.withOpacity(0.2),
      highlightColor: highlightColor ?? Colors.white.withOpacity(0.2),
      child: child ??
          Container(
            margin: margin ?? EdgeInsets.zero,
            decoration: BoxDecoration(
              color: Colors.grey[300],
            ),
            width: width ?? double.infinity,
            height: height ?? AppSize.h20,
          ),
    );
  }
}
