import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomBackArrowButton extends StatelessWidget {
  const CustomBackArrowButton({
    super.key,
    this.icon,
    this.size = 25,
    this.padding,
    this.isBackEnable = false,
    this.onTap,
  });
  final Widget? icon;
  final int size;
  final double? padding;
  final bool isBackEnable;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: padding ?? AppSize.h20, top: padding ?? AppSize.h20),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap ?? () => Navigator.pop(context),
        child: Row(
          children: [
            icon ??
                Assets.icons.backArrowWhiteIcon
                    .image(color: context.themeColors.greenColor, height: AppSize.h22, width: AppSize.w22),
          ],
        ),
      ),
    );
  }
}
