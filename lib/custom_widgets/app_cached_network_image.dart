import 'package:breakingfree_v2/custom_widgets/shimmer_effect.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AppCachedNetworkImage extends StatelessWidget {
  const AppCachedNetworkImage({
    required this.imageUrl,
    this.height,
    this.width,
    this.fit,
    super.key,
  });

  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;

  @override
  Widget build(BuildContext context) {
    'imageUrl $imageUrl'.logV;
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      placeholder: (context, url) => ShimmerWidget(
        // baseColor: AppColors.red,
        // highlightColor: AppColors.primaryColor,
        child: Container(
          decoration: const BoxDecoration(
            //  shape: BoxShape.circle,
            color: Colors.white,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        return Container(
          alignment: Alignment.center,
          child: const Icon(
            Icons.error_outline,
          ),
        );
      },
    );
  }
}
