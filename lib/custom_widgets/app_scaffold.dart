import 'package:breakingfree_v2/custom_widgets/audio_pannel/audio_pannel.dart';
import 'package:breakingfree_v2/custom_widgets/audio_pannel/cubit/audio_pannel_cubit.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// ignore: must_be_immutable
class AppScaffold extends StatefulWidget {
  const AppScaffold({
    super.key,
    this.body,
    this.appBar,
    this.backgroundColor,
    this.bottomNavigationBar,
    this.bottomSheet,
    this.resizeToAvoidBottomInset,
    this.padding,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.useSafeArea = true,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
    this.drawer,
    this.scaffoldKey,
    this.infoAudioUrl,
    this.infoAudioUrlStr,
    this.isAudioPanelVisible,
    this.containAudionPannel = true,
    this.isAudioPlaying,
    this.isManuallyPaused,
  });

  final Key? scaffoldKey;
  final Widget? body;
  final PreferredSizeWidget? appBar;
  final Color? backgroundColor;
  final Widget? bottomNavigationBar;
  final Widget? bottomSheet;
  final bool? resizeToAvoidBottomInset;
  final EdgeInsetsGeometry? padding;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool useSafeArea;
  final bool extendBody;
  final ValueNotifier<bool>? isManuallyPaused;
  final bool extendBodyBehindAppBar;
  final Widget? drawer;
  final bool containAudionPannel;
  final ValueNotifier<String?>? infoAudioUrl;
  final String? infoAudioUrlStr;
  final ValueNotifier<bool>? isAudioPanelVisible;
  final ValueNotifier<bool>? isAudioPlaying;

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  String? pastUrl = '';

  /// Created only for placeholder
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  @override
  void dispose() {
    isAudioPanelVisible.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: widget.isAudioPanelVisible ?? isAudioPanelVisible,
      builder: (context, isAudioPanelVisible, child) {
        return BlocProvider(
          key: Key('${widget.infoAudioUrl?.value}-2'),
          create: (context) {
            final cubit = AudioPannelCubit(
              url: widget.infoAudioUrl?.value,
              isVisible: isAudioPanelVisible,
              isManuallyPaused1: widget.isManuallyPaused?.value ?? false,
              onCreate: (value) {},
            );
            return cubit;
          },
          child: GestureDetector(
            onTap: AppCommonFunctions.closeKeyboard,
            child: Scaffold(
              key: widget.scaffoldKey,
              drawer: widget.drawer,
              extendBody: widget.extendBody,
              extendBodyBehindAppBar: widget.extendBodyBehindAppBar,
              appBar: widget.appBar,
              backgroundColor: widget.backgroundColor ?? context.themeColors.whiteColor,
              bottomNavigationBar: widget.bottomNavigationBar,
              bottomSheet: widget.bottomSheet,
              resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
              floatingActionButton: widget.containAudionPannel
                  ? (widget.isAudioPanelVisible != null && widget.infoAudioUrl != null)
                      ? ValueListenableBuilder(
                          valueListenable: widget.isAudioPanelVisible!,
                          builder: (context, isAudioPannelVisibleV, child) {
                            'isAudioPannelVisibleV $isAudioPannelVisibleV'.logD;
                            'isAudioPannelVisibleV ${widget.isAudioPlaying}'.logD;
                            'isAudioPannelVisibleV ${widget.isManuallyPaused}'.logD;
                            return AudioPannel(
                              isManuallyPaused: widget.isManuallyPaused,
                              isVisible: isAudioPannelVisibleV,
                              isAudioPlaying: widget.isAudioPlaying,
                              url: widget.infoAudioUrl?.value,
                              onClose: () {
                                widget.isAudioPanelVisible?.value = false;
                              },
                            );
                          },
                        )
                      : widget.floatingActionButton
                  : widget.floatingActionButton,
              floatingActionButtonLocation:
                  widget.floatingActionButtonLocation ?? FloatingActionButtonLocation.centerTop,
              body: widget.body,
            ),
          ),
        );
      },
    );
  }
}
