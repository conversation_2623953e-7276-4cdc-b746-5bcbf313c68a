import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomYesNoButton extends StatelessWidget {
  const CustomYesNoButton({
    required this.exitText,
    required this.agreeText,
    required this.onTapYes,
    required this.onTapNo,
    super.key,
    this.isDownLoad = false,
    this.inYesProgress,
    this.inNoProgress,
    this.isYesNoButton = false,
    this.disableColor,
    this.noButtonColor,
    this.isYesDisable = false,
    this.isNoDisable = false,
    this.onDownloadTap,
    this.isShowArrowForward = true,
    this.isShowArrowBackward = true,
    this.padding,
  });
  final String exitText;
  final String agreeText;
  final bool isShowArrowForward;
  final bool isShowArrowBackward;

  final void Function() onTapYes;
  final void Function() onTapNo;
  final void Function()? onDownloadTap;
  final ValueNotifier<bool>? inYesProgress;
  final ValueNotifier<bool>? inNoProgress;
  final bool isYesDisable;
  final bool isDownLoad;
  final bool isNoDisable;
  final bool isYesNoButton;
  final Color? disableColor;
  final Color? noButtonColor;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: context.themeColors.scaffoldColor,
      //  color: context.themeColors.whiteColor,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Padding(
          padding:
              padding ?? EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, top: AppSize.h14, bottom: AppSize.h18),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 1,
            child: Column(
              children: [
                SizedBox(
                  height: AppSize.h34,
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Opacity(
                          opacity: isNoDisable ? 0.5 : 1,
                          child: ValueListenableBuilder(
                            valueListenable: inNoProgress ?? ValueNotifier(false),
                            builder: (context, value, child) {
                              return Material(
                                shadowColor: Colors.transparent,
                                borderRadius: BorderRadius.circular(AppSize.r32),
                                color: noButtonColor ??
                                    (isYesNoButton ? context.themeColors.orangeColor : context.themeColors.blueColor),
                                child: isDownLoad
                                    ? InkWell(
                                        onTap: onDownloadTap,
                                        child: Center(
                                          child: value
                                              ? const CupertinoActivityIndicator(
                                                  color: Colors.white,
                                                )
                                              : const Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                  children: [
                                                    Icon(
                                                      Icons.arrow_drop_down,
                                                      color: Colors.white,
                                                      //   size: AppSize.sp12,
                                                    ),
                                                    // SpaceH(AppSize.w2),
                                                    Icon(
                                                      Icons.download_rounded,
                                                      color: Colors.white,
                                                      //   size: AppSize.sp12,
                                                    ),
                                                  ],
                                                ),
                                        ),
                                      )
                                    : InkWell(
                                        onTap: isNoDisable ? null : onTapNo,
                                        child: Center(
                                          child: value
                                              ? const CupertinoActivityIndicator(
                                                  color: Colors.white,
                                                )
                                              : Row(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  children: [
                                                    if(isShowArrowBackward)  Icon(
                                                      Icons.arrow_back_ios,
                                                      color: Colors.white,
                                                      size: AppSize.sp12,
                                                    ),
                                                    if(isShowArrowBackward) SpaceH(AppSize.w8),
                                                    AppTextWidget(
                                                      exitText,
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        color: Colors.white,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                        ),
                                      ),
                              );
                            },
                          ),
                        ),
                      ),
                      SpaceH(AppSize.w12),
                      Expanded(
                        flex: 7,
                        child: Opacity(
                          opacity: isYesDisable ? 0.5 : 1,
                          child: ValueListenableBuilder(
                            valueListenable: inYesProgress ?? ValueNotifier(false),
                            builder: (context, value, child) {
                              return Material(
                                shadowColor: Colors.transparent,
                                borderRadius: BorderRadius.circular(AppSize.r32),
                                color: isYesNoButton ? context.themeColors.blueColor : context.themeColors.orangeColor,
                                child: InkWell(
                                  onTap: isYesDisable ? null : onTapYes,
                                  child: Center(
                                    child: value
                                        ? const CupertinoActivityIndicator(
                                            color: Colors.white,
                                          )
                                        : Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              AppTextWidget(
                                                agreeText,
                                                style: context.textTheme.titleSmall?.copyWith(
                                                  color: disableColor ?? Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              if (isShowArrowForward) ...[
                                                SpaceH(AppSize.w8),
                                                Icon(
                                                  Icons.arrow_forward_ios,
                                                  color: disableColor ?? Colors.white,
                                                  size: AppSize.sp12,
                                                ),
                                              ],
                                            ],
                                          ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
