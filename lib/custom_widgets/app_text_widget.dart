import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

class AppTextWidget extends StatelessWidget {
  const AppTextWidget(
    this.text, {
    super.key,
    this.style,
    this.maxLines,
    this.textAlign, this.overFlow,
  });
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow? overFlow;

  @override
  Widget build(BuildContext context) {
    return AutoSizeText(
      text,
      textAlign: textAlign,
      overflow: overFlow,
      maxLines: maxLines,
      style: style,
      
    );
  }
}
