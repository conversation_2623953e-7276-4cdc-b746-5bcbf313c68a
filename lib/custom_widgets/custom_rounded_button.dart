import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomRoundedButton extends StatelessWidget {
  const CustomRoundedButton({
    required this.title,
    this.titleColor = Colors.white,
    this.titleFontWeight = FontWeight.w400,
    this.fillColor,
    this.titleFontSize,
    this.titleTextStyle,
    this.height,
    this.width,
    this.onTap,
    this.radius,
    this.prefix,
    super.key,
    this.horizontalPadding,
    this.verticalPadding,
    this.titlePrefixGap,
    this.suffix,
    this.titleSuffixGap,
    this.strokeWidth,
    this.borderColor,
    this.loaderPadding,
    this.isLoading = false,
  });

  final String title;
  final Color titleColor;
  final Color? fillColor;
  final Color? borderColor;
  final void Function()? onTap;
  final double? radius;
  final TextStyle? titleTextStyle;
  final FontWeight titleFontWeight;
  final double? titleFontSize;
  final double? height;
  final double? width;
  final double? horizontalPadding;
  final double? verticalPadding;
  final Widget? prefix;
  final double? titlePrefixGap;
  final Widget? suffix;
  final double? titleSuffixGap;
  final double? strokeWidth;
  final double? loaderPadding;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? BtnDefaults.radius),
      child: Material(
        color: fillColor ?? context.themeColors.greenColor,
        child: InkWell(
          onTap: onTap,
          child: Container(
            height: height,
            width: width,
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding ?? BtnDefaults.horiZontalPadding,
              vertical: verticalPadding ?? BtnDefaults.verticalPadding,
            ),
            decoration: BoxDecoration(
              border: Border.all(
                color: borderColor ?? context.themeColors.whiteColor,
                width: strokeWidth ?? 0,
              ),
              borderRadius: BorderRadius.circular(radius ?? BtnDefaults.radius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (prefix != null) ...[
                  prefix!,
                  SpaceH(titlePrefixGap ?? BtnDefaults.titlePrefixGap),
                ],
                if (isLoading)
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: loaderPadding ?? AppSize.w14,
                    ),
                    child: CupertinoActivityIndicator(
                      color: context.themeColors.whiteColor,
                    ),
                  )
                else
                  Flexible(
                    child: AppTextWidget(
                      title,
                      style: titleTextStyle ??
                          context.textTheme.labelSmall?.copyWith(
                            color: titleColor,
                            fontWeight: titleFontWeight,
                            overflow: TextOverflow.ellipsis,
                            fontSize: titleFontSize ?? BtnDefaults.titleFontSize,
                          ),
                    ),
                  ),
                if (suffix != null) ...[
                  SpaceH(titleSuffixGap ?? BtnDefaults.titleSuffixGap),
                  suffix!,
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class BtnDefaults {
  BtnDefaults._();
  static final radius = AppSize.r100;
  static final horiZontalPadding = AppSize.w16;
  static final verticalPadding = AppSize.h8;
  static final titleFontSize = AppSize.sp11;
  static final titlePrefixGap = AppSize.h8;
  static final titleSuffixGap = AppSize.h8;
}
