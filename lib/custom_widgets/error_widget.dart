import 'package:flutter_html/flutter_html.dart';
import 'package:flutter/material.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';

class CustomErrorWidget extends StatelessWidget {
  const CustomErrorWidget({
    required this.errorMessgaeText,
    super.key,
    this.spacing,
  });

  final String errorMessgaeText;
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SpaceV(spacing ?? AppSize.h8),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: AppSize.h3),
              child: Icon(
                Icons.error_outline,
                color: context.themeColors.errorRedColor,
                size: AppSize.r18,
              ),
            ),
            SpaceH(AppSize.h5),
            Expanded(
              child: Html(
                data: errorMessgaeText,
                style: {
                  'body': Style(
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                    color: context.themeColors.errorRedColor,
                    fontSize: FontSize(AppSize.sp12),
                    fontWeight: FontWeight.w400,
                    fontFamily: context.textTheme.titleSmall?.fontFamily,
                  ),
                  'strong': Style(
                    margin: Margins.zero,
                    padding: HtmlPaddings.zero,
                    fontSize: FontSize(AppSize.sp12),
                    color: context.themeColors.errorRedColor,
                    fontWeight: FontWeight.bold,
                    fontFamily: context.textTheme.titleSmall?.fontFamily,
                  ),
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}


// import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
// import 'package:breakingfree_v2/extensions/ext_build_context.dart';
// import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
// import 'package:breakingfree_v2/res/dimension.dart';
// import 'package:breakingfree_v2/res/space_box.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
//
// class CustomErrorWidget extends StatelessWidget {
//   const CustomErrorWidget({required this.errorMessgaeText, super.key, this.spacing});
//   final String errorMessgaeText;
//   final double? spacing;
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         SpaceV(
//       spacing ??    AppSize.h8,
//         ),
//         Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Icon(
//               Icons.error_outline,
//               color: context.themeColors.errorRedColor,
//               size: AppSize.r18,
//             ),
//             SpaceH(AppSize.h5),
//             Expanded(
//               child: AppTextWidget(
//                 errorMessgaeText,
//                 style: context.textTheme.titleSmall?.copyWith(
//                   color: context.themeColors.errorRedColor,
//                   fontWeight: FontWeight.w400,
//                   fontSize: AppSize.sp12,
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
