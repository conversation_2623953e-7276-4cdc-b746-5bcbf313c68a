import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class AppCardWidget extends StatelessWidget {
  const AppCardWidget({super.key, this.child, this.padding});
  final Widget? child;
  final EdgeInsets? padding;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(AppSize.h14),
      decoration: BoxDecoration(
        color: context.themeColors.whiteColor,
        //  borderRadius: BorderRadius.circular(AppSize.r12),
        //   boxShadow: [
        //     BoxShadow(
        //       offset: Offset(AppSize.w8, AppSize.h8),
        //       blurRadius: AppSize.r40,
        //       color: context.themeColors.blackColor.withOpacity(.08),
        //     ),
        //   ],
      ),
      child: child,
    );
  }
}
