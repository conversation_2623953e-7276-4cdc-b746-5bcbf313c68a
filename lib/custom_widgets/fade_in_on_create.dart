import 'package:flutter/material.dart';

class FadeInOnCreate extends StatefulWidget {
  const FadeInOnCreate({
    required this.child,
    super.key,
    this.duration = const Duration(milliseconds: 300),
  });
  final Widget child;
  final Duration duration;

  @override
  State<FadeInOnCreate> createState() => _FadeInOnCreateState();
}

class _FadeInOnCreateState extends State<FadeInOnCreate> {
  double _opacity = 0;

  @override
  void initState() {
    super.initState();
    // Delay to ensure build is complete before triggering animation
    Future.delayed(Duration.zero, () {
      setState(() {
        _opacity = 1;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: _opacity,
      duration: widget.duration,
      curve: Curves.easeIn,
      child: widget.child,
    );
  }
}
