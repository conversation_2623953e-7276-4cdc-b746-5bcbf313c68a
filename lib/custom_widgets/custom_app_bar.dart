import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/audio_pannel/cubit/audio_pannel_cubit.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:simple_ripple_animation/simple_ripple_animation.dart';

class CommonAppBar extends StatefulWidget implements PreferredSizeWidget {
  const CommonAppBar({
    this.onSuffixTap,
    super.key,
    this.appbarHeight,
    this.padding,
    this.bg,
    this.onPrefixTap,
    this.prefixIcon,
    this.suffixIcon,
    this.onLogoutTap,
    this.centerWidget,
  });

  final double? appbarHeight;
  final EdgeInsetsGeometry? padding;
  final Color? bg;

  final VoidCallback? onPrefixTap;
  final VoidCallback? onSuffixTap;
  final VoidCallback? onLogoutTap;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Widget? centerWidget;

  @override
  State<CommonAppBar> createState() => _CommonAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(appbarHeight ?? AppSize.h64);
}

class _CommonAppBarState extends State<CommonAppBar> {
/// Created only for placeholder when cubit cannot be found
ValueNotifier<VolumeIconStatus> appBarVolumeStatus = ValueNotifier(VolumeIconStatus.neutral);

@override
  void dispose() {
    appBarVolumeStatus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: widget.bg ?? context.themeColors.whiteColor,
      child: SafeArea(
        child: Container(
          padding: widget.padding ??
              EdgeInsets.all(
                AppSize.w12,
              ),
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: context.themeColors.appBarBorder,
                width: 3,
              ),
              bottom: BorderSide(color: context.themeColors.appBarBorder2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: AppSize.w80,
                alignment: Alignment.centerLeft,
                child: GestureDetector(
                  key: const Key('menu_btn'),
                  onTap: widget.onPrefixTap,
                  child: widget.prefixIcon ??
                      Icon(
                        Icons.menu,
                        color: context.themeColors.greyColor,
                        size: AppSize.h24,
                      ),
                ),
              ),
              Expanded(
                child: widget.centerWidget ??
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w8),
                      child: AppCachedNetworkImage(
                        imageUrl: AuthLocaleKeys.logo.tr(),
                      ),
                    ),
              ),
              Container(
                width: AppSize.w80,
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ValueListenableBuilder(
                      valueListenable: context.read<AudioPannelCubit?>()?.appBarVolumeStatus ??
                          appBarVolumeStatus,
                      builder: (context, appBarVolumeStatusV, _) {
                        'appBarVolumeStatusV $appBarVolumeStatusV'.logD;
                        return InkWell(
                          onTap: widget.onSuffixTap,
                          child: widget.suffixIcon ??
                              (appBarVolumeStatusV == VolumeIconStatus.mutedPlaying
                                  ? RippleAnimation(
                                      duration: const Duration(milliseconds: 1200),
                                      ripplesCount: 3,
                                      minRadius: AppSize.r15,
                                      repeat: true,
                                      color: context.themeColors.blueColor,
                                      child: Icon(
                                        Icons.volume_off,
                                        size: AppSize.h24,
                                        color: context.themeColors.blackColor,
                                      ),
                                    )
                                  : Icon(
                                      appBarVolumeStatusV == VolumeIconStatus.mutedPaused
                                          ? Icons.volume_off
                                          : Icons.volume_up,
                                      size: AppSize.h24,
                                      color: appBarVolumeStatusV == VolumeIconStatus.neutral
                                          ? context.themeColors.greyColor
                                          : context.themeColors.blackColor,
                                    )),
                        );
                      },
                    ),
                    if (widget.onLogoutTap != null) ...[
                      SpaceH(AppSize.w10),
                      Flexible(
                        child: GestureDetector(
                          onTap: widget.onLogoutTap,
                          child: AppTextWidget(
                            CoreLocaleKeys.headerLogOutLabel.tr(),
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.themeColors.greenColor,
                              decoration: TextDecoration.underline,
                              // overflow: TextOverflow.ellipsis,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
