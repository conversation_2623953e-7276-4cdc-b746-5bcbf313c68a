import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class CustomIconButton extends StatelessWidget {
  const CustomIconButton({
    this.assetIcon,
    this.onTap,
    super.key,
    this.size,
    this.color,
    this.iconPath,
  });
  final double? size;
  final String? iconPath;
  final AssetGenImage? assetIcon;
  final void Function()? onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: assetIcon?.image(
            height: size ?? AppSize.h20,
            width: size ?? AppSize.h20,
            color: color ?? context.themeColors.orangeColor,
          ) ??
          (iconPath.isNotEmptyAndNotNull
              ? AppSvgAsset(
                  svgAsset: iconPath ?? '',
                  size: size ?? AppSize.h20,
                  // color: color ?? context.themeColors.orangeColor,
                )
              : const SizedBox.shrink()),
    );
  }
}
