import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomOutlinedTextfield extends StatelessWidget {
  const CustomOutlinedTextfield({
    this.controller,
    this.validator,
    this.hintText,
    this.labelText,
    this.inputFormatters,
    this.maxTextLength,
    this.readOnly = false,
    this.keyboardType,
    this.onTap,
    super.key,
    this.textAction,
    this.obscureText = false,
    this.onChanged,
    this.inputBorder,
    this.maxLine = 1,
    this.fillColor,
    this.focusNode,
    this.fontSize,
    this.onSaved,
    this.contentHeight,
    this.borderRadius,
    this.style,
    this.contentWidth,
    this.hintStyle,
    this.borderSide,
    this.textAlignVertical,
    this.isDense,
    this.autofocus,
    this.floatingLabelColor,
    this.autofillHints,
    this.textAlign = TextAlign.start,
    this.prefixIcon,
    this.suffixIcon,
    this.suffix,
    this.prefix,
    this.suffixIconConstraints,
    this.prefixIconConstraints,
    this.autovalidateMode,
    this.isError,
  });
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String?)? onChanged;

  final String? hintText;
  final String? labelText;
  final Color? floatingLabelColor;
  final int? maxTextLength;
  final bool readOnly;
  final bool obscureText;
  final TextInputType? keyboardType;
  final TextInputAction? textAction;
  final void Function()? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final InputBorder? inputBorder;
  final int? maxLine;
  final FocusNode? focusNode;
  final double? fontSize;
  final void Function(String?)? onSaved;
  final double? contentHeight;
  final double? contentWidth;
  final double? borderRadius;
  final Color? fillColor;
  final TextStyle? style;
  final TextStyle? hintStyle;
  final BorderSide? borderSide;
  final TextAlignVertical? textAlignVertical;
  final bool? isDense;
  final bool? autofocus;
  final Iterable<String>? autofillHints;
  final TextAlign textAlign;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Widget? suffix;
  final Widget? prefix;
  final BoxConstraints? suffixIconConstraints;
  final BoxConstraints? prefixIconConstraints;
  final AutovalidateMode? autovalidateMode;
  final bool? isError;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      autovalidateMode: autovalidateMode,
      focusNode: focusNode,
      controller: controller,
      keyboardType: keyboardType,
      maxLength: maxTextLength,
      validator: validator,
      textInputAction: textAction,
      onChanged: onChanged,
      readOnly: readOnly,
      obscureText: obscureText,
      onTap: onTap,
      onSaved: onSaved,
      maxLines: maxLine,
      inputFormatters: inputFormatters,
      textAlignVertical: textAlignVertical,
      textAlign: textAlign,
      
      autofocus: autofocus ?? false,
      autofillHints: autofillHints,
      style: context.textTheme.titleSmall?.copyWith(
        fontSize: fontSize ?? AppSize.sp13,
      ),
      decoration: InputDecoration(
        
        contentPadding: EdgeInsets.symmetric(
          horizontal: contentWidth ?? AppSize.w14,
          vertical: contentHeight ?? AppSize.h8,
        ),
        prefixIconConstraints: prefixIconConstraints ?? const BoxConstraints(),
        suffixIconConstraints: suffixIconConstraints ?? const BoxConstraints(),
        prefixIcon: prefixIcon == null
            ? null
            : Padding(
                padding: EdgeInsets.only(
                  left: AppSize.w14,
                  right: AppSize.w8,
                ),
                child: prefixIcon,
              ),
        prefix: prefix == null
            ? null
            : Padding(
                padding: EdgeInsets.only(
                  left: AppSize.w14,
                  right: AppSize.w8,
                ),
                child: prefix,
              ),
        suffix: suffix == null
            ? null
            : Padding(
                padding: EdgeInsets.only(
                  right: AppSize.w14,
                  left: AppSize.w8,
                ),
                child: suffix,
              ),
        suffixIcon: suffixIcon == null
            ? null
            : Padding(
                padding: EdgeInsets.only(
                  right: AppSize.w14,
                  left: AppSize.w8,
                ),
                child: suffixIcon,
              ),
        errorMaxLines: 2,
        counterText: '',
        isDense: isDense ?? true,
        border: inputBorder,
        hintText: hintText,
        labelText: labelText,
        floatingLabelStyle: context.textTheme.labelSmall?.copyWith(color: context.themeColors.textfieldTextColor),
        labelStyle: context.textTheme.titleSmall?.copyWith(
          fontSize: fontSize ?? AppSize.sp13,
          color: context.themeColors.textfieldTextColor,
        ),
        hintStyle: hintStyle ??
            context.textTheme.titleSmall?.copyWith(
              fontSize: fontSize ?? AppSize.sp13,
              color: context.themeColors.textfieldTextColor,
            ),
        errorStyle: context.textTheme.titleSmall?.copyWith(
          fontSize: fontSize ?? AppSize.sp11,
          color: context.themeColors.errorRedColor,
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            borderRadius ?? AppSize.r4,
          ),
          borderSide: borderSide ??
              BorderSide(
                color: context.themeColors.errorRedColor,
                width: 2,
              ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            borderRadius ?? AppSize.r4,
          ),
          borderSide: borderSide ??
              BorderSide(
                color: (isError ?? false)? context.themeColors.errorRedColor : context.themeColors.greyColor,
              ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            borderRadius ?? AppSize.r4,
          ),
          borderSide: borderSide ??
              BorderSide(
                color: (isError??false)?context.themeColors.errorRedColor: context.themeColors.greenColor,
                width: 2,
              ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? AppSize.r4),
          borderSide: borderSide ??
              BorderSide(
                color: context.themeColors.errorRedColor,
              ),
        ),
        filled: fillColor != null,
        fillColor: fillColor,
      ),
    );
  }
}
