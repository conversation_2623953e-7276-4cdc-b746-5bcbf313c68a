// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AppSvgAsset extends StatelessWidget {
  const AppSvgAsset({
    required this.svgAsset,
    super.key,
    this.size,
    this.color,
    this.colorFilter,
    this.fit = BoxFit.contain,
  });
  final String svgAsset;
  final double? size;
  final Color? color;
  final BoxFit fit;
  final ColorFilter? colorFilter;
  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      svgAsset,
      height: size,
      width: size,
      color: color,
      fit: fit,
      colorFilter: colorFilter,
    );
  }
}
