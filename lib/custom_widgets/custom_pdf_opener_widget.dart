import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';

class PdfFromUrlOpener {
  static Future<void> downloadAndOpenPDF(String url, ValueNotifier<bool> isLoading) async {
    isLoading.value = true;

    try {
      // Step 1: Download the PDF
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final dir = await getApplicationDocumentsDirectory();
        final filePath = '${dir.path}/drinking.pdf';
        final file = File(filePath);
        await file.writeAsBytes(response.bodyBytes);

        // Step 4: Open the PDF file using open_file
        await OpenFile.open(file.path);
      } else {
        throw Exception('Failed to download PDF');
      }
    } catch (e) {
      log('Error: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
