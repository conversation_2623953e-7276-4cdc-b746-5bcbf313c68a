import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';

class CustomConfettiAnimation extends StatefulWidget {
  const CustomConfettiAnimation({
    required this.controller,
    super.key,
  });
  final ConfettiController controller;

  @override
  State<CustomConfettiAnimation> createState() => _CustomConfettiAnimationState();
}

class _CustomConfettiAnimationState extends State<CustomConfettiAnimation> {
  @override
  void initState() {
    widget.controller.play();
    Future<dynamic>.delayed(const Duration(seconds: 5)).then(
      (value) {
        widget.controller.stop();
      },
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ConfettiWidget(
      confettiController: widget.controller,
      shouldLoop: true,
      blastDirectionality: BlastDirectionality.explosive,
      numberOfParticles: 50,
      createParticlePath: (size) {
        final path = Path();
        path.addOval(Rect.fromCircle(center: Offset.zero, radius: 5));
        return path;
      },
    );
  }
}
