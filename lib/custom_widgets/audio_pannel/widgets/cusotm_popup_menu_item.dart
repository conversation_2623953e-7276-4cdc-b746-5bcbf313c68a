import 'package:breakingfree_v2/custom_widgets/audio_pannel/cubit/audio_pannel_cubit.dart';
import 'package:breakingfree_v2/custom_widgets/audio_pannel/widgets/audio_pannel.button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class CustomPopupMenuItem extends StatelessWidget {
  const CustomPopupMenuItem({
    required this.ref,
    required this.name,
    required this.icon,
    super.key,
    this.onTap,
  });

  final AudioPannelCubit ref;
  final void Function()? onTap;
  final String name;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w8,
          vertical: AppSize.h10,
        ),
        child: Row(
          children: [
            AudioPanneButton(
              icon: icon,
              iconSize: AppSize.h20,
            ),
            SpaceH(AppSize.w8),
            Text(
              name,
              style: context.textTheme.labelSmall,
            ),
          ],
        ),
      ),
    );
  }
}
