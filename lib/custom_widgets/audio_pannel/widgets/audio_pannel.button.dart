import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class AudioPanneButton extends StatelessWidget {
  const AudioPanneButton({
    required this.icon,
    super.key,
    this.onTap,
    this.onLongPress,
    this.iconSize,
  });
  final IconData icon;
  final double? iconSize;
  final void Function()? onTap;
  final void Function()? onLongPress;
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppSize.r100),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          child: Container(
            padding: EdgeInsets.all(AppSize.h6),
            child: Icon(
              icon,
              size: iconSize ?? AppSize.h18,
              color: context.themeColors.blackColor,
            ),
          ),
        ),
      ),
    );
  }
}
