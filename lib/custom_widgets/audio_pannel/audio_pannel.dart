import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/audio_pannel/cubit/audio_pannel_cubit.dart';
import 'package:breakingfree_v2/custom_widgets/audio_pannel/widgets/audio_pannel.button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_duration.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/repository/cubit/audio_muted_cubit.dart';
import 'package:breakingfree_v2/repository/cubit/audio_muted_state.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:visibility_detector/visibility_detector.dart';

class AudioPannel extends StatefulWidget {
  const AudioPannel({
    this.isVisible = false,
    super.key,
    this.url,
    this.shouldInit = false,
    this.isManuallyPaused,
    this.isAudioPlaying,
    this.onCreate,
    this.onClose,
  });
  final String? url;
  final bool isVisible;
  final ValueNotifier<bool>? isManuallyPaused;
  final bool shouldInit;
  final ValueNotifier<bool>? isAudioPlaying;
  final VoidCallback? onClose;
  final void Function(ValueNotifier<VolumeIconStatus> value)? onCreate;

  @override
  State<AudioPannel> createState() => _AudioPannelState();
}

class _AudioPannelState extends State<AudioPannel> with WidgetsBindingObserver {
  AudioPannelCubit? cubit;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      cubit?.resumeAudio(value: false); // Pause audio
      widget.isManuallyPaused?.value = true;
      cubit?.isManuallyPaused = true;
    }
  }

  @override
  Widget build(BuildContext context) {
                              '/////////// AppDB = \\${Injector.instance<AppDB>().isAudioMuted}'.logD;
    return BlocBuilder<AudioPannelCubit, AudioPannelState>(
      builder: (context, state) {
        final ref = context.read<AudioPannelCubit>();
        cubit = ref;
        if ((widget.isManuallyPaused?.value ?? false) &&
            (widget.isManuallyPaused?.value ?? false) != ref.isManuallyPaused &&
            ref.url.isNotEmptyAndNotNull) {
          'restartAudio'.logD;
          ref.restartAudio();
        } else if (!(widget.isManuallyPaused?.value ?? false) &&
            (widget.isManuallyPaused?.value ?? false) == ref.isManuallyPaused &&
            ref.url.isNotEmptyAndNotNull) {
          ref.resumeAudio(value: true);
        }

        return VisibilityDetector(
          key: Key(widget.url ?? ''),
          onVisibilityChanged: (info) {
            'isAudioMute.value***** ${info.visibleFraction}'.logD;
            'isAudioMute.value***** ${widget.isManuallyPaused}'.logD;
            if (!widget.isVisible) {
              //   if (widget.isManuallyPaused == true) {
              //     'Audio manually paused; visibility fraction = ${info.visibleFraction}'.logD;
              //     ref.resumeAudio(value: false); // Ensure audio is stopped
              //   }
              return; // Exit early if not visible
            }
            if (info.visibleFraction == 0 && !ref.isManuallyPaused) {
              ref.resumeAudio(value: false);
            } else if (info.visibleFraction == 1 &&
                !ref.isManuallyPaused &&
                (widget.isManuallyPaused?.value ?? false) == ref.isManuallyPaused) {
              ref.resumeAudio(value: true);
            }
          },
          child: Visibility(
            visible: widget.isVisible && widget.url.isNotEmptyAndNotNull,
            child: Container(
              height: AppSize.h36,
              width: context.width * .9,
              padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
              margin: EdgeInsets.only(top: AppSize.w16),
              decoration: BoxDecoration(
                color: context.themeColors.audioPannelBg,
                borderRadius: BorderRadius.circular(AppSize.r100),
                border: Border.all(
                  color: context.themeColors.audioPannelBorder,
                ),
              ),
              child: Row(
                children: [
                  ValueListenableBuilder(
                    valueListenable: ref.isAudioPlaying,
                    builder: (context, isAudioPlayingV, _) {
                      'isAudioMute.value***** ${ref.isAudioMute}'.logD;
                      return AudioPanneButton(
                        icon: isAudioPlayingV ? Icons.pause : Icons.play_arrow,
                        onTap: () async {
                          'value == $isAudioPlayingV'.logD;
                          ref.isManuallyPaused = isAudioPlayingV;
                          widget.isManuallyPaused?.value = isAudioPlayingV;
                          await ref.resumeAudio(
                            value: !isAudioPlayingV,
                          );
                        },
                      );
                    },
                  ),
                  // SpaceH(AppSize.w2),
                  ValueListenableBuilder(
                    valueListenable: ref.currentMin,
                    builder: (context, currentMinV, _) {
                      return ValueListenableBuilder(
                        valueListenable: ref.totalMin,
                        builder: (context, totalMinV, _) {
                          return SizedBox(
                            width: AppSize.w74,
                            child: AppTextWidget(
                              maxLines: 1,
                              "${currentMinV?.format() ?? '0:00'} / ${totalMinV?.format() ?? '0:00'}",
                              textAlign: TextAlign.center,
                              style: context.textTheme.labelSmall?.copyWith(
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                  SpaceH(AppSize.w2),
                  Flexible(
                    flex: 2,
                    child: ValueListenableBuilder(
                      valueListenable: ref.currentMin,
                      builder: (context, currentMinV, _) {
                        return ValueListenableBuilder(
                          valueListenable: ref.totalMin,
                          builder: (context, totalMinV, _) {
                            final current = currentMinV?.inMilliseconds.toDouble();
                            final total = totalMinV?.inMilliseconds.toDouble();

                            return SliderTheme(
                              data: SliderThemeData(
                                overlayShape: SliderComponentShape.noOverlay,
                                thumbShape: RoundSliderThumbShape(
                                  enabledThumbRadius: AppSize.r5,
                                ),
                              ),
                              child: Slider(
                                value: current != null && current <= (total ?? 1) ? current : 0,
                                max: total ?? 1.0,
                                thumbColor: context.themeColors.blackColor.withOpacity(.8),
                                activeColor: context.themeColors.blackColor,
                                inactiveColor: context.themeColors.greyColor,
                                onChanged: (value) {
                                  ref.setProgress(value: value.toInt());
                                },
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  SpaceH(AppSize.w4),

                  ValueListenableBuilder(
                    valueListenable: ref.isAudioMute,
                    builder: (context, isAudioMuteV, _) {
                      return BlocBuilder<AudioMutedCubit, AudioMutedState>(
                        builder: (context, audioMutedState) {
                          final isAudioMutedCubit = audioMutedState.audioMuted;
                          '/////////// AppDB == \\${Injector.instance<AppDB>().isAudioMuted}, Cubit == \\${isAudioMutedCubit}, ref.isAudioMute == \\${isAudioMuteV}'.logV;
                          return AudioPanneButton(
                            icon: isAudioMuteV ? Icons.volume_off : Icons.volume_up, //(isAudioMuteV && isAudioMutedCubit )? Icons.volume_off : Icons.volume_up,
                            onTap: () async {
                              await ref.switchPauseAudioStatus();
                              '/////////// isAudioMuteV = \\${!isAudioMuteV}'.logD;
                              '/////////// AppDB = \\${Injector.instance<AppDB>().isAudioMuted}'.logD;
                              (!isAudioMuteV && Injector.instance<AppDB>().isAudioMuted)
                                ? context.read<AudioMutedCubit>().audioMutedApi(1, true)
                                : context.read<AudioMutedCubit>().audioMutedApi(1, false);
                            },
                            onLongPress: () {
                              ref.isVolumeVisible.value = !ref.isVolumeVisible.value;
                            },
                          );
                        },
                      );
                    },
                  ),
                  SpaceH(AppSize.w2),
                  InkWell(
                    onTap: () {
                      widget.onClose?.call();
                    },
                    child: Icon(
                      Icons.close,
                      color: context.themeColors.blackColor,
                      size: AppSize.sp16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
