import 'package:audioplayers/audioplayers.dart';
import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'audio_pannel_cubit.freezed.dart';
part 'audio_pannel_state.dart';

class AudioPannelCubit extends Cubit<AudioPannelState> {
  AudioPannelCubit({required this.url, required this.isVisible, required this.isManuallyPaused1, this.onCreate})
      : super(const AudioPannelState.initial()) {
    'isAudioPlaying calaled $isManuallyPaused1'.logE;
    //if (appBarVolumeStatus.value != VolumeIconStatus.playing) {
    isAudioPlaying = isAudioPlaying;
    // isAudioMute.value = appBarVolumeStatus.value == VolumeIconStatus.mutedPaused ||
    //     appBarVolumeStatus.value == VolumeIconStatus.mutedPlaying;
    //  }
    appBarVolumeStatus.value = switch (isVisible && url != null) {
      true => isManuallyPaused1
          ? VolumeIconStatus.mutedPaused
          : isAudioMute.value
              ? VolumeIconStatus.mutedPlaying
              : VolumeIconStatus.playing,
      false => VolumeIconStatus.neutral,
    };
    onCreate?.call(appBarVolumeStatus);
    setVolume(value: isAudioMute.value ? 0 : 1);
    initPlayer(url: url, isAudioPannelVisible: isVisible, isManuallyPaused1: isManuallyPaused1);
  }
  final void Function(ValueNotifier<VolumeIconStatus> value)? onCreate;
  final bool isVisible;
  final bool isManuallyPaused1;
  ValueNotifier<VolumeIconStatus> appBarVolumeStatus = ValueNotifier(VolumeIconStatus.neutral);
  ValueNotifier<bool> isAudioPlaying = ValueNotifier(false);
  ValueNotifier<bool> isAudioMute = ValueNotifier(Injector.instance<AppDB>().isAudioMuted);
  ValueNotifier<Duration?> totalMin = ValueNotifier(null);
  ValueNotifier<Duration?> currentMin = ValueNotifier(null);
  ValueNotifier<double> audioVolume = ValueNotifier(1);
  ValueNotifier<bool> isVolumeVisible = ValueNotifier(false);
  ValueNotifier<bool> showPlaybackSpeed = ValueNotifier(false);

  AudioPlayer player = AudioPlayer();
  bool isAudioPannelVisible = false;
  bool isManuallyPaused = false;
  String? url;

  Future<void> initPlayer({
    required String? url,
    bool isAudioPannelVisible = false,
    bool isManuallyPaused1 = false,
  }) async {
    // if (isManuallyPaused1 == true) {
    //   await resumeAudio(value: false);
    // }
    this.isAudioPannelVisible = isAudioPannelVisible && url.isNotEmptyAndNotNull;

    this.url = url;
    if (url.isNotEmptyAndNotNull) {
      'url +++++ ${url}'.logI;
      await player.setSource(
        UrlSource(url!),
      );
      if (isManuallyPaused1) {
        await player.pause();
        isAudioPlaying.value = false;
      }
      // 'isAudioMute +++++ ${isAudioMute.value}'.logD;
      await player.setReleaseMode(ReleaseMode.stop);
      'totalMin +++++ ${totalMin.value}'.logD;

      totalMin.value = await player.getDuration();
      isAudioPlaying.value = true;

      player.onPositionChanged.listen(
        (event) {
          if (isAudioPlaying.value) currentMin.value = event;
        },
      );
      player.onPlayerComplete.listen((event) async {
        await restartAudio(); // Restart the audio
      });
      setAppbarVolumeIconStatus();
    } else {
      await clearPlayer();
    }
  }

  void setAppbarVolumeIconStatus() {
    'isAudioMute====> +++++ ${isAudioMute.value}'.logD;

    if (isAudioPlaying.value) {
      'url====> +++++ === $url'.logD;

      appBarVolumeStatus.value = isAudioMute.value ? VolumeIconStatus.mutedPlaying : VolumeIconStatus.playing;
      
    } else {
      'isAudioPannelVisible====> +++++ === ${appBarVolumeStatus.value}'.logD;
      appBarVolumeStatus.value = isAudioPannelVisible
          ? (isAudioMute.value ? VolumeIconStatus.mutedPaused : VolumeIconStatus.playing)
          : url.isNotEmptyAndNotNull
              ? (isAudioMute.value ? VolumeIconStatus.mutedPaused : VolumeIconStatus.playing)
              : VolumeIconStatus.neutral;
      'isAudioPannelVisible====> +++++ === ${appBarVolumeStatus.value}'.logD;
      
    }
  }

  Future<void> setProgress({required int value}) async {
    final d = Duration(milliseconds: value);
    await player.seek(d);
    currentMin.value = d;
  }

  Future<void> setVolume({required double value}) async {
    await player.setVolume(value);
    audioVolume.value = value;
    if (audioVolume.value == 0) {
      isAudioMute.value = true;
    } else {
      isAudioMute.value = false;
    }
    Injector.instance<AppDB>().isAudioMuted = isAudioMute.value;
    setAppbarVolumeIconStatus();
  }

  Future<void> resumeAudio({required bool value}) async {
    'value == $value'.logD;
    if (value) {
      await player.resume();
      isAudioPlaying.value = true;
    } else {
      if (player.state == PlayerState.disposed) {
        player = AudioPlayer();
      }
      await player.pause();
      isAudioPlaying.value = false;
    }
    setAppbarVolumeIconStatus();
  }

  Future<void> switchPauseAudioStatus() async {
    'isAudioMute.value logD ${isAudioMute.value}'.logD;
    isAudioMute.value = !isAudioMute.value;
    Injector.instance<AppDB>().isAudioMuted = isAudioMute.value;
    await player.setVolume(isAudioMute.value ? 0 : 1);
    setAppbarVolumeIconStatus();
  }

  Future<void> restartAudio() async {
    // Restart audio from the beginning
    await player.seek(Duration.zero);
    await player.pause();
    isAudioPlaying.value = false;
    currentMin.value = null;
    setAppbarVolumeIconStatus();
  }

  Future<void> clearPlayer() async {
    isAudioPlaying.value = false;
    currentMin.value = null;
    await player.release();
    totalMin.value = null;
    setAppbarVolumeIconStatus();
  }

  @override
  Future<void> close() async {
    await clearPlayer();
    setAppbarVolumeIconStatus();

    await player.dispose();
    return super.close();
  }
}
