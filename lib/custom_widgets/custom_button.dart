import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    required this.title,
    required this.onTap,
    super.key,
    this.errormessage,
    this.inProgress = false,
    this.isBottom = false,
    this.isDisable = false,
    this.isFixSize = true,
    this.isFreeWidth = false,
    this.color,
    this.disableColor,
    this.border,
    this.textColor,
    this.isTextBold = false,
    this.disableTextColor,
    this.padding,
    this.isShowArrowForward = true,
    this.isSelectActivity,
    this.isOverflow = false,
  });
  final String title;
  final String? errormessage;
  final VoidCallback onTap;
  final bool isBottom;
  final bool inProgress;
  final bool isDisable;
  final bool isFixSize;
  final bool isFreeWidth;
  final bool isTextBold;
  final BoxBorder? border;
  final Color? textColor;
  final bool isShowArrowForward;
  final Color? color;
  final Color? disableColor;
  final Color? disableTextColor;
  final EdgeInsets? padding;
  final bool? isSelectActivity;
  final bool isOverflow;

  @override
  Widget build(BuildContext context) {
    return isFreeWidth
        ? Center(
            child: FittedBox(
              child: Container(
                child: getWidget(context),
              ),
            ),
          )
        : Padding(
            padding:
                padding ?? EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, top: AppSize.h8, bottom: isSelectActivity??false?AppSize.h5:AppSize.h18),
            child: Center(
              child: Container(
                width: isFreeWidth ? null : MediaQuery.of(context).size.width * 1,
                padding: EdgeInsets.symmetric(horizontal: isBottom ? 0 : 60.w),
                child: getWidget(context),
              ),
            ),
          );
  }

  Widget getWidget(BuildContext context) {
    return Column(
      children: [
        if (isBottom) ...{
          Divider(
            color: context.themeColors.whiteColor,
            height: 0,
            thickness: 1.2.h,
          ),
        },
        Container(
          height: isFixSize ? AppSize.h34 : null,
          decoration: BoxDecoration(
            border: border,
          ),
          child: Material(
            shadowColor: Colors.transparent,
            color: isDisable
                ? disableColor ?? context.themeColors.greenColor.withGreen(2)
                : color ?? context.themeColors.greenColor,
            borderRadius: BorderRadius.circular(AppSize.r32),
            child: InkWell(
              highlightColor: Colors.transparent,
              focusColor: Colors.transparent,
              onTap: isDisable
                  ? errormessage == null
                      ? null
                      : () {
                          log('message');
                          // CommonWidget.showSnackBar(content: errormessage!);
                        }
                  : onTap,
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: isFreeWidth ? 30.w : 0.w,
                  ),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: isFixSize ? 0.h : 15.h),
                    child: inProgress
                        ? const CupertinoActivityIndicator(
                            color: Colors.white,
                          )
                        : isDisable
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (isOverflow) SizedBox(
                                    width: MediaQuery.of(context).size.width * 0.7,
                                    child: AppTextWidget(
                                      overFlow: TextOverflow.ellipsis,
                                      title,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        fontSize: AppSize.sp14,
                                        color: disableTextColor ?? context.themeColors.greenColor,
                                        fontWeight: isTextBold ? FontWeight.w500 : FontWeight.w500,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                    ),
                                  ) else AppTextWidget(
                                    overFlow: TextOverflow.ellipsis,
                                    title,
                                    style: context.textTheme.titleMedium?.copyWith(
                                      fontSize: AppSize.sp14,
                                      color: disableTextColor ?? context.themeColors.greenColor,
                                      fontWeight: isTextBold ? FontWeight.w500 : FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                  ),
                                  if (isShowArrowForward) ...[
                                    SpaceH(AppSize.w8),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: disableTextColor ?? context.themeColors.greenColor,
                                      size: AppSize.sp12,
                                    ),
                                  ],
                                ],
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (isOverflow) SizedBox(
                                    width: MediaQuery.of(context).size.width * 0.7,
                                    child: AppTextWidget(
                                      overFlow: TextOverflow.ellipsis,
                                      title,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        fontSize: AppSize.sp14,
                                        color: context.themeColors.scaffoldColor,
                                        fontWeight: isTextBold ? FontWeight.w500 : FontWeight.w500,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                    ),
                                  ) else AppTextWidget(
                                    overFlow: TextOverflow.ellipsis,
                                    title,
                                    style: context.textTheme.titleMedium?.copyWith(
                                      fontSize: AppSize.sp14,
                                      color: context.themeColors.scaffoldColor,
                                      fontWeight: isTextBold ? FontWeight.w500 : FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                  ),
                                  if (isShowArrowForward) ...[
                                    SpaceH(AppSize.w8),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color: Colors.white,
                                      size: AppSize.sp12,
                                    ),
                                  ],
                                ],
                              ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
