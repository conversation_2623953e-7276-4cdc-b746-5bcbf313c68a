import 'package:flutter/material.dart';

class CustomRawScrollbar extends StatelessWidget {
  const CustomRawScrollbar({super.key, this.controller, this.child});
  final ScrollController? controller;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      controller: controller,
      //  trackColor: const Color.fromRGBO(183, 183, 183, 1),
      // thumbColor: const Color.fromRGBO(127, 128, 128, 1),
      // thickness: AppSize.w10,
      // thumbVisibility: true,
      // trackRadius: Radius.circular(AppSize.r20),
      // trackVisibility: true,
      // radius: Radius.circular(AppSize.r20),
      // scrollbarOrientation: ScrollbarOrientation.right,
      scrollbarOrientation: ScrollbarOrientation.right,
      thumbVisibility: false,

      child: child ?? const SizedBox(),
    );
  }
}
