import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class CustomSnackbar {
  static void showSnackBar({
    required String message,
    int? durationInSecond,
    Widget? prefixIcon,
    Color? color,
  }) {
    ScaffoldMessenger.of(navigatorKey.currentContext!)
      ..hideCurrentSnackBar()
      ..showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          backgroundColor: color ?? navigatorKey.currentContext!.themeColors.blackColor,
          duration: Duration(seconds: durationInSecond ?? 1),
          showCloseIcon: true,
          content: Row(
            children: [
              if (prefixIcon != null) ...[prefixIcon, SpaceH(AppSize.w8)],
              Flexible(
                child: Text(
                  message,
                  style: navigatorKey.currentContext!.textTheme.titleSmall?.copyWith(
                    color: navigatorKey.currentContext!.themeColors.whiteColor,
                    fontSize: AppSize.sp13,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
  }

  static void showErrorSnackBar({
    required String message,
    int? durationInSecond,
    Color? color,
  }) {
    if (navigatorKey.currentContext != null) {
      ScaffoldMessenger.of(navigatorKey.currentContext!)
      ..hideCurrentSnackBar()
      ..showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          backgroundColor: color ?? navigatorKey.currentContext!.themeColors.redColor,
          duration: Duration(seconds: durationInSecond ?? 1),
          showCloseIcon: true,
          content: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.error,
                color: navigatorKey.currentContext!.themeColors.whiteColor,
              ),
              SpaceH(AppSize.w10),
              Flexible(
                child: AppTextWidget(
                  textAlign: TextAlign.start,
                  message,
                  style: navigatorKey.currentContext!.textTheme.titleSmall?.copyWith(
                    color: navigatorKey.currentContext!.themeColors.whiteColor,
                    fontSize: AppSize.sp13,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  static void showSucessSnackBar({
    required String message,
    int? durationInSecond,
    Color? color,
  }) {
    ScaffoldMessenger.of(navigatorKey.currentContext!)
      ..hideCurrentSnackBar()
      ..showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          backgroundColor: color ?? navigatorKey.currentContext!.themeColors.greenColor,
          duration: Duration(seconds: durationInSecond ?? 1),
          showCloseIcon: true,
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: navigatorKey.currentContext!.themeColors.whiteColor,
              ),
              SpaceH(AppSize.w8),
              Flexible(
                child: Text(
                  message,
                  style: navigatorKey.currentContext!.textTheme.titleSmall?.copyWith(
                    color: navigatorKey.currentContext!.themeColors.whiteColor,
                    fontSize: AppSize.sp13,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
  }
}
