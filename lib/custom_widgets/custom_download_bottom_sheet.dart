import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomDownloadPopup {
  static void buildPopupMenu({
    required BuildContext context,
    Offset? offset,
    void Function()? onEmailDownload,
    void Function()? onDownLoadPdf,
  }) {
    final overlay = Overlay.of(context).context.findRenderObject()! as RenderBox;
    showMenu(
      context: context,
      constraints: const BoxConstraints(maxWidth: 180, minWidth: 180),
      position: (offset != null)
          ? RelativeRect.fromLTRB(offset.dx - 30, offset.dy + 50, 0, 0)
          : RelativeRect.fromLTRB(24, overlay.size.height - 130, overlay.size.width - 50, 30),
      items: [
        PopupMenuItem<dynamic>(
          onTap: onEmailDownload,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Row(
                children: [
                  Container(
                    height: AppSize.w34,
                    width: AppSize.w34,
                    decoration: BoxDecoration(color: context.themeColors.orangeColor, shape: BoxShape.circle),
                    child: Icon(
                      Icons.email,
                      color: context.themeColors.whiteColor,
                    ),
                  ),
                  SpaceH(AppSize.w10),
                  Expanded(
                    child: AppTextWidget(
                      CoreLocaleKeys.buttonsEmailPdf.tr(),
                      style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
                    ),
                  ),
                ],
              ),
              SpaceV(AppSize.h12),
              // Row(
              //   children: [
              //     Container(
              //       height: AppSize.w34,
              //       width: AppSize.w34,
              //       decoration: BoxDecoration(color: context.themeColors.orangeColor, shape: BoxShape.circle),
              //       child: Icon(
              //         Icons.file_download,
              //         color: context.themeColors.whiteColor,
              //       ),
              //     ),
              //     SpaceH(AppSize.w10),
              //     AppTextWidget(
              //       CoreLocaleKeys.buttonsDownloadPdf.tr(),
              //       style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
        PopupMenuItem<dynamic>(
          onTap: onDownLoadPdf,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              // Row(
              //   children: [
              //     Container(
              //       height: AppSize.w34,
              //       width: AppSize.w34,
              //       decoration: BoxDecoration(color: context.themeColors.orangeColor, shape: BoxShape.circle),
              //       child: Icon(
              //         Icons.email,
              //         color: context.themeColors.whiteColor,
              //       ),
              //     ),
              //     SpaceH(AppSize.w10),
              //     AppTextWidget(
              //       CoreLocaleKeys.buttonsEmailPdf.tr(),
              //       style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
              //     ),
              //   ],
              // ),
              // SpaceV(AppSize.h14),
              Row(
                children: [
                  Container(
                    height: AppSize.w34,
                    width: AppSize.w34,
                    decoration: BoxDecoration(color: context.themeColors.orangeColor, shape: BoxShape.circle),
                    child: Icon(
                      Icons.file_download,
                      color: context.themeColors.whiteColor,
                    ),
                  ),
                  SpaceH(AppSize.w10),
                  Expanded(
                    child: AppTextWidget(
                     CoreLocaleKeys.buttonsDownloadPdf.tr(),
                      style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
