import 'package:breakingfree_v2/custom_widgets/custom_slider/custom_slider.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class SliderScreen extends StatelessWidget {
  const SliderScreen({
    required this.selectedValue,
    required this.isClick,
    super.key,
    this.onSelect,
    this.firstText,
    this.changeTextColor = false,
    this.reverseGradient = false,
    this.secondText,
  });
  final ValueChanged<int>? onSelect;
  final ValueNotifier<int> selectedValue;
  final String? firstText;
  final String? secondText;
  final bool changeTextColor;
  final bool reverseGradient;
  final bool isClick;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: selectedValue,
      builder: (context, value, child) {
        return GestureDetector(
          onVerticalDragUpdate: (_) {},
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // SpaceV(AppSize.h30),
              Column(
                children: [
                  if (value != -1)
                    SizedBox(
                      height: AppSize.h50,
                      child: Text(
                        value.toString(),
                        style: context.textTheme.headlineMedium?.copyWith(
                          color: !changeTextColor
                              ? context.themeColors.darkGreyColor
                              : value < 3
                                  ? context.themeColors.greenColor
                                  : value < 7
                                      ? context.themeColors.orangeColor
                                      : context.themeColors.redColor,
                          fontSize: AppSize.sp50,
                          fontWeight: FontWeight.w500,
                          height: 1.2
                        ),
                      ),
                    )
                  else
                    SizedBox(
                      height: AppSize.h50,
                      child: Center(
                        child: Container(
                          height: AppSize.h3,
                          width: AppSize.w16,
                          color: const Color.fromRGBO(99, 99, 99, 1),
                        ),
                      ),
                    ),
                  SpaceV(AppSize.h30),
                  SizedBox(
                    height: AppSize.h38,
                    child: CustomSlider(
                      selectedValue: selectedValue,
                      min: 0,
                      max: 10,
                      onSelect: (value) {
                        onSelect?.call(value);
                      },
                      isGreenTop: reverseGradient,
                      borderColor: (value == -1 && isClick) ? context.themeColors.errorRedColor : Colors.transparent,
                    ),
                  ),
                ],
              ),
              SpaceV(AppSize.h10),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      firstText ?? 'Unable to cope at all',
                      style: context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp10),
                    ),
                  ),
                  const Spacer(),
                  Expanded(
                    flex: 2,
                    child: Text(
                      textAlign: TextAlign.right,
                      secondText ?? 'Able to cope with anything',
                      style: context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp10),
                    ),
                  ),
                ],
              ),
              SpaceV(AppSize.h2),
              Visibility(
                visible: isClick && selectedValue.value == -1,
                child: CustomErrorWidget(
                  spacing: AppSize.h10,
                  errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
