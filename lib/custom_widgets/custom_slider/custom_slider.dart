// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class CustomSlider extends HookWidget {
  // Width parameter for the slider

  const CustomSlider({
    required this.max,
    required this.min,
    required this.onSelect,
    required this.isGreenTop,
    required this.borderColor,// Default width, super.key,
    this.selectedValue,
    this.width
  });
  final ValueChanged<int> onSelect;
  final int min;
  final int max;
  final bool isGreenTop;
  final double? width;
  final Color borderColor;
  final ValueNotifier<int>? selectedValue;

  @override
  Widget build(BuildContext context) {
    final valueTwo = useState<int>(min);
    final valueInitialPoint = useState<int>(5);

    return SizedBox(
      width: width ?? MediaQuery.of(context).size.width,
      height: AppSize.h34,
      child:
       Generic1DSelector(
        isGreenTop: isGreenTop,
        value: selectedValue?.value == -1 ? valueInitialPoint.value : selectedValue?.value ?? valueInitialPoint.value,//valueInitialPoint.value != 5 ? valueTwo.value : valueInitialPoint.value,
        onSelect: (value) {
          valueInitialPoint.value = -1;
          valueTwo.value = value;
          onSelect(value);
        },
        gradientColors: [],
        min: min, // Pass min value
        max: max,
        borderColor: borderColor,
      ),
    );
  }
}

/// 1D Selector for a given value
class Generic1DSelector extends StatelessWidget {
  // Added max parameter

  const Generic1DSelector({
    required this.value,
    required this.isGreenTop,
    required this.onSelect,
    required this.min,
    required this.max,
    super.key,
    this.direction = Axis.horizontal,
    this.pointerColor = Colors.grey,
    this.highContrastPointer = false,
    this.gradientColors = const [Colors.white, Colors.black],
    this.withCheckers = false,
    this.borderColor = Colors.transparent
  });
  final int value;
  final Axis direction;
  final Color pointerColor;
  final bool highContrastPointer;
  final List<Color> gradientColors;
  final bool withCheckers;
  final bool isGreenTop;
  final ValueChanged<int> onSelect;
  final int min; // Added min parameter
  final int max;
  final Color borderColor;

  void _update(
    BuildContext context,
    PointerEvent event,
    Size size,
  ) {
    if (event.down) {
      final normalizedValue =
          direction == Axis.vertical ? event.localPosition.dy / size.height : event.localPosition.dx / size.width;
      final val = ((normalizedValue * (max - min)) + min).round();
      if (val >= min && val <= max) onSelect(val);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = constraints.biggest;
        return Listener(
          onPointerDown: (event) => _update(context, event, size),
          onPointerMove: (event) => _update(context, event, size),
          child: CustomPaint(
            painter: _Painter(
              isGreenTop: isGreenTop,
              value: value,
              direction: direction,
              gradientColors: gradientColors,
              withCheckers: withCheckers,
              highContrastPointer: highContrastPointer,
              min: min, // Pass min to painter
              max: max,
              borderColor: borderColor,
              // Pass max to painter
            ),
          ),
        );
      },
    ).showCursorOnHover();
  }
}

List<Color> getGradientColors(double value) {
  // Assuming your value is between 0 and 1 for the sake of this example
  // You can adjust this logic based on your requirements.

  // Define base color
  const baseColorValue = 166;

  // Calculate colors based on the value, mapping it to a range
  // Example of modifying the intensity based on the value
  const startColor = Color.fromRGBO(baseColorValue, baseColorValue, baseColorValue, 1);
  const endColor = Color.fromRGBO(baseColorValue, baseColorValue, baseColorValue, 1);

  return [startColor, endColor];
}

class _Painter extends CustomPainter {
  // Added max parameter

  _Painter( {
    required this.value,
    required this.direction,
    //required this.pointerColor,
    required this.highContrastPointer,
    required this.withCheckers,
    required this.isGreenTop,
    required this.gradientColors,
    required this.min, // Initialize min
    required this.max,
    required this.borderColor// Initialize max
  });
  final int value;
  final Axis direction;
  // final Color pointerColor;
  final bool highContrastPointer;
  final bool withCheckers;
  final bool isGreenTop;
  final List<Color> gradientColors;
  final int min; // Added min parameter
  final int max;
  final Color borderColor;

  void paintBackground(Canvas canvas, Size size) {
    if (withCheckers) {
      (direction == Axis.vertical) ? paintCheckers(canvas, size, nbCols: 2) : paintCheckers(canvas, size, nbRows: 2);
    }
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(AppSize.r6));
    final gradient = LinearGradient(
      colors: !isGreenTop
          ? [
              const Color(0xFF991700),
              const Color.fromRGBO(240, 104, 0, 1),
              const Color(0xFF00820B),
            ]
          : [
              const Color(0xFF00820B),
              const Color.fromRGBO(240, 104, 0, 1),
              const Color(0xFF991700),
            ],
      begin: direction == Axis.vertical ? Alignment.topCenter : Alignment.centerLeft,
      end: direction == Axis.vertical ? Alignment.bottomCenter : Alignment.centerRight,
    );
    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRRect(rrect, paint);
  }

  void paintPointer(Canvas canvas, Size size) {
    final x = direction == Axis.vertical ? (size.width / 2) : ((value - min) / (max - min)) * size.width;

    final y = direction == Axis.vertical ? ((value - min) / (max - min)) * size.height : (size.height / 2);

    final c = Offset(x, y);
    final w = direction == Axis.vertical ? size.width : size.height;

    // Create a Rect that bounds the RRect
    final rect = Rect.fromCenter(center: c, width: w / 1.70, height: w * 1.23);

    // Define the shadow color and offset
    final shadowColor = Colors.black.withOpacity(0.5); // Change opacity as needed
    final shadowOffset = Offset(
      AppSize.w4,
      AppSize.w4,
    ); // Horizontal offset (5 pixels to the right)

    // Draw the shadow
    final shadowPaint = Paint()
      ..color = shadowColor
      ..maskFilter = const MaskFilter.blur(BlurStyle.inner, 1); // Adjust blur radius as needed


    // Draw the shadow first
    canvas.drawRRect(RRect.fromRectAndRadius(rect.shift(shadowOffset), Radius.zero), shadowPaint);

    // Create the gradient
    final gradient = LinearGradient(
      colors: const [
        Color.fromRGBO(168, 168, 168, 1),
        Color.fromRGBO(170, 170, 170, 1),
        Color.fromRGBO(186, 186, 186, 1),
        Color.fromRGBO(193, 193, 193, 1),
        Color.fromRGBO(172, 172, 172, 1),
        Color.fromRGBO(168, 168, 168, 1),
      ],
      begin: direction == Axis.horizontal ? Alignment.topCenter : Alignment.centerLeft,
      end: direction == Axis.horizontal ? Alignment.bottomCenter : Alignment.centerRight,
    );

    // Use the Rect for the gradient
    final paint = Paint()..shader = gradient.createShader(rect);
    canvas.drawRRect(RRect.fromRectAndRadius(rect, Radius.zero), paint);

    // Add a red border
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    // Draw the pointer with the gradient
    canvas.drawRRect(RRect.fromRectAndRadius(rect, Radius.zero),borderPaint);
  }

  @override
  void paint(Canvas canvas, Size size) {
    paintBackground(canvas, size);
    paintPointer(canvas, size);
}

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

extension HoverExtensions on Widget {
  /// Changes cursor on Hover to [cursor] (default is `SystemMouseCursors.click`).
  Widget showCursorOnHover({
    SystemMouseCursor cursor = SystemMouseCursors.click,
  }) {
    return MouseRegion(cursor: cursor, child: this);
  }
}

extension ColorX on Color {
  /// Determines if the color is dark based on a [luminance] (default is `0.179`).
  bool isDark({double luminance = 0.179}) => computeLuminance() < luminance;
}

/// Paints a checker in the [canvas] of [size].
void paintCheckers(
  Canvas canvas,
  Size size, {
  int? nbRows,
  int? nbCols,
  double checkerSize = 10,
  Color darkColor = const Color(0xff777777),
  Color lightColor = const Color(0xffaaaaaa),
}) {
  nbRows ??= (nbCols == null) ? size.height ~/ checkerSize : size.height ~/ (size.width / nbCols);
  nbCols ??= size.width ~/ (size.height / nbRows);
  final checkerWidth = size.width / nbCols;
  final checkerHeight = size.height / nbRows;
  final darkPaint = Paint()..color = darkColor;
  final lightPaint = Paint()..color = lightColor;
  for (var i = 0; i < nbCols; i++) {
    for (var j = 0; j < nbRows; j++) {
      canvas.drawRect(
        Rect.fromLTWH(
          i * checkerWidth,
          j * checkerHeight,
          checkerWidth,
          checkerHeight,
        ),
        (i + j) % 2 == 0 ? darkPaint : lightPaint,
      );
    }
  }
}
