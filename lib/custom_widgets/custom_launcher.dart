import 'package:url_launcher/url_launcher.dart';

class CustomLauncher {
  static Future<void> launchURL(String url) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      try {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } catch (e) {
        throw Exception('Could not launch, error: $e');
      }
    }
  }
}
