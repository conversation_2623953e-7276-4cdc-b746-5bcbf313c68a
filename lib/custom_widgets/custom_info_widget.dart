// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class CustomInfoWidget extends StatelessWidget {
  const CustomInfoWidget({
    super.key,
    this.onCloseTap,
    this.bgColor,
    this.bodyText,
    this.bodyColor,
    this.visible = true,
    this.customWidget,
    this.padding,
    this.showShadow = true,
    this.margin,
    this.textColor,
  });
  final void Function()? onCloseTap;
  final Color? bgColor;
  final String? bodyText;
  final Color? bodyColor;
  final Color? textColor;
  final bool visible;
  final Widget? customWidget;
  final EdgeInsets? padding;
  final bool showShadow;
  final EdgeInsetsGeometry? margin;

  /*@override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: Padding(
        padding: padding ?? EdgeInsets.zero,
        child: Container(
              margin: margin,
              decoration: BoxDecoration(
                color: bgColor ?? context.themeColors.lightYellowColor,
                borderRadius: BorderRadius.circular(AppSize.r12),
                boxShadow: !showShadow
                    ? null
                    : [
                        BoxShadow(
                          color: context.themeColors.blackColor.withOpacity(0.16),
                          offset: Offset(0, AppSize.h3),
                          blurRadius: AppSize.h10,
                        ),
                        BoxShadow(
                          color: context.themeColors.blackColor.withOpacity(0.23),
                          offset: Offset(0, AppSize.h3),
                          blurRadius: AppSize.w10,
                        ),
                      ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: AppSize.h5,right: AppSize.w8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        GestureDetector(
                          onTap: onCloseTap,
                          child: Icon(
                            Icons.close,
                            color: bodyColor ?? context.themeColors.darkOrangeColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (customWidget != null || (bodyText.isNotEmptyAndNotNull))
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
                      child: customWidget ??
                          Html(
                            data: bodyText?.replaceAll('\n', '<br/>'),
                            style: {
                              'strong': Style(
                                fontSize: FontSize(AppSize.sp13),
                                color: textColor ?? context.themeColors.darkOrangeColor,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins',
                              ),
                              'body': Style(
                                fontSize: FontSize(AppSize.sp13),
                                color: textColor ?? context.themeColors.darkOrangeColor,
                                fontFamily: 'Poppins',
                              ),
                            },
                          ),
                    ),
                    SizedBox(height: AppSize.h14,)
                ],
              ),
            ),
      ),
    );
  }*/

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: Padding(
        padding: padding ?? EdgeInsets.zero,
        child: Container(
          margin: margin,
          decoration: BoxDecoration(
            color: bgColor ?? context.themeColors.lightYellowColor,
            borderRadius: BorderRadius.circular(AppSize.r12),
            boxShadow: !showShadow
                ? null
                : [
              BoxShadow(
                color: context.themeColors.blackColor.withOpacity(0.16),
                offset: Offset(0, AppSize.h3),
                blurRadius: AppSize.h10,
              ),
              BoxShadow(
                color: context.themeColors.blackColor.withOpacity(0.23),
                offset: Offset(0, AppSize.h3),
                blurRadius: AppSize.w10,
              ),
            ],
          ),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: AppSize.h12,
                  horizontal: AppSize.w20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (customWidget != null || (bodyText.isNotEmptyAndNotNull))
                      customWidget ??
                          Html(
                            data: bodyText?.replaceAll('\n', '<br/>'),
                            style: {
                              'strong': Style(
                                fontSize: FontSize(AppSize.sp13),
                                color: textColor ??
                                    context.themeColors.darkOrangeColor,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Poppins',
                              ),
                              'body': Style(
                                fontSize: FontSize(AppSize.sp13),
                                color: textColor ??
                                    context.themeColors.darkOrangeColor,
                                fontFamily: 'Poppins',
                              ),
                            },
                          ),
                  ],
                ),
              ),
              Positioned(
                top: AppSize.h4,
                right: AppSize.w6,
                child: GestureDetector(
                  onTap: onCloseTap,
                  child: Icon(
                    Icons.close,
                    color: bodyColor ?? context.themeColors.darkOrangeColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
