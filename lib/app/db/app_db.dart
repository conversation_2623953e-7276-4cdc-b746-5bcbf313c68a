// ignore: depend_on_referenced_packages

// ignore_for_file: public_member_api_docs

import 'dart:convert';

import 'package:breakingfree_v2/features/authentication_module/language_module/model/language_model.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:hive/hive.dart';

class AppDB {
  AppDB(this._box);

  static const fcmKey = 'fcm_key';
  static const platform = 'platform';
  static const _appDbBox = '_appDbBox';

  final Box<dynamic> _box;

  ///instance for data get
  static Future<AppDB> getInstance() async {
    final box = await Hive.openBox<dynamic>(_appDbBox);
    return AppDB(box);
  }

  Future<void> clearData() async {
    await _box.clear();
  }

  T getValue<T>(String key, {T? defaultValue}) => _box.get(key, defaultValue: defaultValue) as T;

  Future<void> setValue<T>(String key, T value) => _box.put(key, value);

  String? get localizedJson => getValue<String?>('localizedJson');

  set localizedJson(String? data) => setValue('localizedJson', data);

  double? get accessibilityFontSize => getValue<double?>('accessibilityFontSize');

  set accessibilityFontSize(double? data) => setValue('accessibilityFontSize', data);

  double? get accessibilityContrastValue => getValue<double?>('accessibilityContrastValue');

  set accessibilityContrastValue(double? data) => setValue('accessibilityContrastValue', data);

  bool? get accessibilityIsBoldText => getValue<bool?>('accessibilityIsBoldText');

  set accessibilityIsBoldText(bool? data) => setValue('accessibilityIsBoldText', data);

  bool? get isAppLogin => getValue<bool?>('isAppLogin');

  set isAppLogin(bool? data) => setValue('isAppLogin', data);

  // bool? get isMySituationAlert => getValue<bool?>('isMySituationAlert');

  // set isMySituationAlert(bool? data) => setValue('isMySituationAlert', data);

  // bool? get isMyActionAlert => getValue<bool?>('isMyActionAlert');

  // set isMyActionAlert(bool? data) => setValue('isMyActionAlert', data);

  // bool? get isMyLifestyleAlert => getValue<bool?>('isMyLifestyleAlert');

  // set isMyLifestyleAlert(bool? data) => setValue('isMyLifestyleAlert', data);

  String? get baseUrl => getValue<String?>('baseUrl');

  set baseUrl(String? data) => setValue('baseUrl', data);

  String? get enjoymentActivityListJson => getValue('enjoymentActivityListJson');
  set enjoymentActivityListJson(String? activityListJson) => setValue('enjoymentActivityListJson', activityListJson);

  String? get achievementActivityListJson => getValue('achievementActivityListJson');
  set achievementActivityListJson(String? activityListJson) =>
      setValue('achievementActivityListJson', activityListJson);

  List<Datum>? get mapData => getValue<String?>('mapData') != null
      ? (jsonDecode(getValue<String>('mapData')) as List).map((e) => Datum.fromJson(e as Map<String, dynamic>)).toList()
      : null;

  set mapData(List<Datum>? mapData) => setValue(
        'mapData',
        jsonEncode(
          mapData
              ?.map(
                (e) => e.toJson(),
              )
              .toList(),
        ),
      );

  set policyRequired(List<dynamic>? update) => setValue('policyRequired', update);

  ///get custom skill  data
  List<dynamic>? get policyRequired => getValue<dynamic>('policyRequired') != null ? getValue('policyRequired') : null;

  set policyMap(Map<String, dynamic>? update) => setValue('policyMap', jsonEncode(update ?? {}));

  ///get custom skill  data
  Map<String, dynamic>? get policyMap =>
      getValue<String?>('policyMap') != null ? jsonDecode(getValue<String>('policyMap')) as Map<String, dynamic> : null;

  String? get cookie => getValue('makrkers');
  set cookie(String? cookie) => setValue('makrkers', cookie);

  UserModel? get userModel => getValue<String?>('userModel') != null
      ? UserModel.fromJson(jsonDecode(getValue<String>('userModel')) as Map<String, dynamic>)
      : null;
  set userModel(UserModel? userModel) =>
      setValue('userModel', userModel == null ? null : jsonEncode(userModel.toJson()));

  UserModel? get userModelCopy => getValue<String?>('userModelCopy') != null
      ? UserModel.fromJson(jsonDecode(getValue<String>('userModelCopy')) as Map<String, dynamic>)
      : null;
  set userModelCopy(UserModel? userModel) =>
      setValue('userModelCopy', userModel == null ? null : jsonEncode(userModel.toJson()));

  LanguageModel? get langugaeModel => getValue<String?>('langugaeModel') != null
      ? LanguageModel.fromJson(jsonDecode(getValue<String>('langugaeModel')) as Map<String, dynamic>)
      : null;
  set langugaeModel(LanguageModel? userModel) =>
      setValue('langugaeModel', userModel == null ? null : jsonEncode(userModel.toJson()));

  String? get selectedLangugae => getValue('selectedLangugae');
  set selectedLangugae(String? selectedLangugae) => setValue('selectedLangugae', selectedLangugae);

  int get videoIndex => getValue('videoIndex', defaultValue: 0);
  set videoIndex(int? value) => setValue('videoIndex', value);

  bool get isAudioMuted => getValue('isAudioMuted', defaultValue: false);
  set isAudioMuted(bool value) => setValue('isAudioMuted', value);

  // set userModel(UserModel? userModel) {
  //   if (userModel == null) {
  //     setValue('userModel', null); // Clear the saved data
  //   } else {
  //     // Log the strategies before saving
  //     'Strategies to save ===> ${userModel.user.strategies?.dsAs}'.logD;

  //     setValue('userModel', jsonEncode(userModel.toJson())); // Save the model
  //   }
  // }

  // UserModel? get userModel {
  //   final userModelJson = getValue<String?>('userModel');
  //   if (userModelJson != null) {
  //     final model = UserModel.fromJson(jsonDecode(userModelJson) as Map<String, dynamic>);

  //     // Log the strategies after getting the userModel
  //     'Strategies retrieved ===> ${model.user.strategies?.dsAs}'.logD;

  //     return model;
  //   }
  //   return null;
  // }
}
