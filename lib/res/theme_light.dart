import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/theme_colors.dart';
import 'package:breakingfree_v2/res/theme_text_colors.dart';
import 'package:flutter/material.dart';

/// Application light Theme
///
final _lightThemeData = ThemeData.light(useMaterial3: true);

const _themeColors = ThemeColors(
  appBarBorder: Color(0xFF036e1b),
  appBarBorder2: Color(0xFFdcdcdc),
  dividerColor: Color(0xFFb5b5b5),
  greenColor: Color(0xFF00820B),
  gradientGreenColor: Color(0xFF21753B),
  greenBtnColor: Color(0xFF035200),
  scaffoldColor: Color.fromRGBO(245, 245, 245, 1),
  lightOrangeColor: Color(0xFFFFD59B),
  linkTextColor: Color.fromRGBO(0, 0, 235, 1),
  graphLine: Color(0xFF006ca8),
  blueColor: Color(0xFF00508E),
  orangeColor: Color(0xFFB95E04),
  darkOrangeColor: Color(0xFF993d12),
  brownColor: Color(0xFFed6c02),
  amberColor: Color(0xFFcc4601),
  lightYellowColor: Color(0xFFFFF7BF),
  redColor: Color(0xFF991700),
  lightBrownColor: Color(0xFFFFD59B),
  errorRedColor: Color(0xFFD62D00),
  lightBlueColor: Color(0xFFE5F6FD),
  darkBrownColor: Color.fromRGBO(91, 53, 0, 1),
  linkColor: Color.fromRGBO(36, 74, 140, 1),
  darkBlue: Color.fromRGBO(36, 57, 97, 1),
  greyColor: Color(0xFF696969),
  darkGreyColor: Color.fromRGBO(80, 80, 80, 1),
  disableButtonColor: Color.fromRGBO(102, 173, 110, 1),
  diagramBtnColor: Color(0xFF595959),
  switchThumbColor: Color(0xFF00599b),
  audioPannelBg: Color(0xFFf2f3f4),
  audioPannelBorder: Color(0xFFdbdbdb),
  blackColor: Colors.black,
  purpleColor: Color(0xFF666699),
  whiteColor: Colors.white,
  textfieldTextColor: Color.fromRGBO(115, 115, 115, 1),
  lightPurpleColor: Color.fromRGBO(144, 85, 173, 1),
);

const _themeTextColors = ThemeTextColors(
  text: Colors.black,
);

final lightTheme = _lightThemeData.copyWith(
  scaffoldBackgroundColor: _themeColors.whiteColor,
  textTheme: ThemeData.light(useMaterial3: false).textTheme.copyWith(
        headlineLarge: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp22,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w500,
          //letterSpacing: 1,
        ),
        headlineMedium: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp20,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w500,
          //letterSpacing: 1,
        ),
        titleLarge: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp18,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w500,
          // letterSpacing: 1,
        ),
        titleMedium: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp16,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w400,
        ),
        titleSmall: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp13,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w400,
        ),
        labelSmall: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp12,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w400,
        ),
        bodySmall: TextStyle(
          fontFamily: 'Poppins',
          fontSize: AppSize.sp8,
          color: _themeTextColors.text,
          fontWeight: FontWeight.w400,
        ),
      ),
  appBarTheme: AppBarTheme(
    backgroundColor: _themeColors.greenColor,
    titleTextStyle: TextStyle(
      fontWeight: FontWeight.w700,
      color: _themeTextColors.text,
    ),
  ),
  // timePickerTheme: TimePickerThemeData(
  //     hourMinuteTextColor: AppColors.white,
  //     // cancelButtonStyle: ButtonStyle(c),
  //     hourMinuteColor: AppColors.primaryColor,
  //     confirmButtonStyle: ButtonStyle(
  //       backgroundColor: MaterialStateProperty.resolveWith(
  //         (states) => AppColors.primaryColor, // Change this to the desired color
  //       ),
  //     ),
  //     hourMinuteShape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.circular(0), // Set the shape of the hour and minute picker
  //     ),
  //     cancelButtonStyle: ButtonStyle(backgroundColor: MaterialStateProperty.resolveWith((states) => Colors.red))),
  colorScheme: ColorScheme.light(primary: _themeColors.greenColor),
  // chipTheme: ChipThemeData(
  //   backgroundColor: AppColors.greyColor,
  //   selectedColor: _themeColors.primaryColor,
  //   side: BorderSide.none,
  //   shape: RoundedRectangleBorder(
  //     borderRadius: BorderRadius.circular(AppSize.r10),
  //   ),
  // ),
  extensions: <ThemeExtension<dynamic>>[
    _themeColors,
    _themeTextColors,
  ],
);
