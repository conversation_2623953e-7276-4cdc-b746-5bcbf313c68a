import 'package:flutter/material.dart';

/// Application Theme
final darkTheme = ThemeData.dark(useMaterial3: true).copyWith(
    /*scaffoldBackgroundColor: AppColor.scaffoldBgColor,
  textTheme: ThemeData.dark(useMaterial3: true).textTheme.copyWith(
        bodyMedium: textRegular, // default text widget
      ),
  appBarTheme: AppBarTheme(
    backgroundColor: AppColor.appbarBgColor,
    titleTextStyle: textBold.copyWith(fontSize: AppSize.sp18),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: Colors.white,
    ),
  ),
  chipTheme: ChipThemeData(
    backgroundColor: const Color(0xFF4A4A4A),
    selectedColor: AppColor.primaryColor,
    side: BorderSide.none,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppSize.r10),
    ),
  ),*/
    );
