import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:form_field_validator/form_field_validator.dart';

class ConfirmPasswordValidator extends TextFieldValidator {
  ConfirmPasswordValidator({
    required String errorText,
    required this.getPassword,
  }) : super(errorText);

  String Function() getPassword;

  @override
  bool get ignoreEmptyValues => true;

  @override
  bool isValid(String? value) {
    if (value == null) return false;

    return true;
  }

  @override
  String? call(String? value) {
    if (value?.isEmptyOrNull ?? true) {
      return errorText;
    }
    final testMessage = passwordValidator().call(value);
    if (testMessage == null) {
      if (value != getPassword()) {
        return AuthLocaleKeys.signUpPasswordNotMatch.tr();
      }
    } else {
      return testMessage;
    }
    return null;
  }
}
