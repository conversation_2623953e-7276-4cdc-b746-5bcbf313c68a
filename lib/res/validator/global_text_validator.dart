import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:form_field_validator/form_field_validator.dart';

MultiValidator emailValidator() => MultiValidator([
      RequiredValidator(
        errorText: AuthLocaleKeys.signUpEmailRequired.tr(),
      ),
      EmailValidator(
        errorText: AuthLocaleKeys.signUpEmailInvalid.tr(),
      ),
    ]);
// MultiValidator currentPasswordValidator() => MultiValidator([
//       RequiredValidator(
//         errorText:
//             rootNavKey.currentContext!.l10n.please_enter_current_password,
//       ),
//     ]);
MultiValidator passwordValidator() => MultiValidator([
      RequiredValidator(
        errorText: AuthLocaleKeys.signUpPasswordRequired.tr(),
      ),
      MinLengthValidator(
        8,
        errorText: AuthLocaleKeys.signUpPasswordMinLength.tr(),
      ),
      MaxLengthValidator(
        100,
        errorText: AuthLocaleKeys.signUpPasswordMaxLength.tr(),
      ),
    ]);
MultiValidator userNameValidator() => MultiValidator([
      RequiredValidator(
        errorText: AuthLocaleKeys.signUpCodeRequired.tr(),
      ),
    ]);
MultiValidator assessmentUserNameValidator() => MultiValidator([
      MaxLengthValidator(
        100,
        errorText: AuthLocaleKeys.signUpPasswordMaxLength.tr(),
      ),
    ]);
MultiValidator activityValidator() => MultiValidator([
      MaxLengthValidator(
        16,
        errorText: AsLocaleKeys.lsUbErrorsMaxCharacters.tr(),
      ),
    ]);
MultiValidator incrementValidator() => MultiValidator([
      RangeValidator(min: 1, max: 100, errorText: "This field is required"),
    ]);

MultiValidator riskyplaceValidator() => MultiValidator([
      RequiredValidator(
        errorText: "This field is required",
      ),
    ]);

MultiValidator goalValidator() => MultiValidator([
      RequiredValidator(
        errorText: "This field is required",
      ),
    ]);

MultiValidator otherBarrier() => MultiValidator([
      RequiredValidator(
        errorText: "This field is required",
      ),
    ]);
