import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/model/info_text_audio_model.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';

enum SupporterFieldState { addBtn, enterEmail, savedEmail, confirmDelete }

enum IntegrationMode { unit, widget, integration, debug, release }

enum PlaybackSpeed {
  normal,
  x1_25,
  x1_5,
  x1_75,
  x2,
}

extension PlaybackSpeedX on PlaybackSpeed {
  String get name {
    switch (this) {
      case PlaybackSpeed.normal:
        return 'Normal';
      case PlaybackSpeed.x1_25:
        return '1.25';
      case PlaybackSpeed.x1_5:
        return '1.5';
      case PlaybackSpeed.x1_75:
        return '1.75';
      case PlaybackSpeed.x2:
        return '2';
    }
  }
}

enum VolumeIconStatus { neutral, mutedPaused, mutedPlaying, playing }

enum MyDiagramStates {
  difficultSituation,
  negativeThoughts,
  emotionalImpact,
  lifestyle,
  unhelpfulBehaviours,
  physicalSensations
}

extension MyDiagramStatesX on MyDiagramStates {
  List<InfoTextAudioModel> get data {
    switch (this) {
      case MyDiagramStates.difficultSituation:
      case MyDiagramStates.negativeThoughts:
      case MyDiagramStates.emotionalImpact:
      case MyDiagramStates.lifestyle:
      case MyDiagramStates.unhelpfulBehaviours:
      case MyDiagramStates.physicalSensations:
        return getInfoList
            .where(
              (element) => element['text'].isNotEmptyAndNotNull && element['audio'].isNotEmptyAndNotNull,
            )
            .map(
              (e) => InfoTextAudioModel(
                audio: e['audio'] ?? '',
                text: e['text'] ?? '',
              ),
            )
            .toList();
    }
  }

  String get iconPath {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return Assets.icons.infoPage.difficultSituation;
      case MyDiagramStates.negativeThoughts:
        return Assets.icons.infoPage.negativeThoughts;
      case MyDiagramStates.emotionalImpact:
        return Assets.icons.infoPage.emotions;
      case MyDiagramStates.lifestyle:
        return Assets.icons.infoPage.lifestyle;
      case MyDiagramStates.unhelpfulBehaviours:
        return Assets.icons.infoPage.unhelpfulBehaviours;
      case MyDiagramStates.physicalSensations:
        return Assets.icons.infoPage.physicalSensation;
    }
  }

  String get typeName {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return 'dsIS';
      case MyDiagramStates.negativeThoughts:
        return 'ntIS';
      case MyDiagramStates.emotionalImpact:
        return 'eiIS';
      case MyDiagramStates.lifestyle:
        return 'lsIS';
      case MyDiagramStates.unhelpfulBehaviours:
        return 'ubIS';
      case MyDiagramStates.physicalSensations:
        return 'psIS';
    }
  }

  String? get title {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return CoreLocaleKeys.titlesInformationStrategiesDifficultSituations.tr();
      case MyDiagramStates.negativeThoughts:
        return CoreLocaleKeys.titlesInformationStrategiesNegativeThoughts.tr();
      case MyDiagramStates.emotionalImpact:
        return CoreLocaleKeys.titlesInformationStrategiesEmotionalImpact.tr();
      case MyDiagramStates.lifestyle:
        return CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr();
      case MyDiagramStates.unhelpfulBehaviours:
        return CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr();
      case MyDiagramStates.physicalSensations:
        return CoreLocaleKeys.titlesInformationStrategiesPhysicalSensations.tr();
    }
  }

  String? get subTitle {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        //return IsLocaleKeys.dsTitle.tr();
        return AsLocaleKeys.lsDsTitle.tr();
      case MyDiagramStates.negativeThoughts:
        return IsLocaleKeys.ntTitle.tr();
      case MyDiagramStates.emotionalImpact:
        return IsLocaleKeys.eiTitle.tr();
      case MyDiagramStates.lifestyle:
        return IsLocaleKeys.lsTitle.tr();
      case MyDiagramStates.unhelpfulBehaviours:
        return IsLocaleKeys.ubTitle.tr();
      case MyDiagramStates.physicalSensations:
        return IsLocaleKeys.psTitle.tr();
    }
  }

  List<String?> get getVideoList {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.dsVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();

      case MyDiagramStates.negativeThoughts:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ntVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();

      case MyDiagramStates.emotionalImpact:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.eiVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();

      case MyDiagramStates.lifestyle:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.lsVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();

      case MyDiagramStates.unhelpfulBehaviours:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ubVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();

      case MyDiagramStates.physicalSensations:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.psVideos,
          navigatorKey.currentContext!,
        ) as List<dynamic>)
            .cast<String>();
    }
  }

  String get getInfoText {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.dsInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.negativeThoughts:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ntInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.emotionalImpact:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.eiInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.lifestyle:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.lsInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.unhelpfulBehaviours:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ubInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.physicalSensations:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.psInfoText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');
    }
  }

  String get getLearnText {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.dsLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.negativeThoughts:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ntLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.emotionalImpact:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.eiLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.lifestyle:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.lsLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.unhelpfulBehaviours:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.ubLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');

      case MyDiagramStates.physicalSensations:
        return (DynamicAssetLoader.getNestedValue(
          IsLocaleKeys.psLearnText,
          navigatorKey.currentContext!,
        ) as List)
            .join('\n\n');
    }
  }

  String get getLearnAudio {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return IsLocaleKeys.dsInfoLearnAudio;

      case MyDiagramStates.negativeThoughts:
        return IsLocaleKeys.ntInfoLearnAudio;

      case MyDiagramStates.emotionalImpact:
        return IsLocaleKeys.eiInfoLearnAudio;

      case MyDiagramStates.lifestyle:
        return IsLocaleKeys.lsInfoLearnAudio;

      case MyDiagramStates.unhelpfulBehaviours:
        return IsLocaleKeys.ubInfoLearnAudio;

      case MyDiagramStates.physicalSensations:
        return IsLocaleKeys.psInfoLearnAudio;
    }
  }

  String get getInfoAudio {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return IsLocaleKeys.dsInfoAudio;

      case MyDiagramStates.negativeThoughts:
        return IsLocaleKeys.ntInfoAudio;

      case MyDiagramStates.emotionalImpact:
        return IsLocaleKeys.eiInfoAudio;

      case MyDiagramStates.lifestyle:
        return IsLocaleKeys.lsInfoAudio;

      case MyDiagramStates.unhelpfulBehaviours:
        return IsLocaleKeys.ubInfoAudio;

      case MyDiagramStates.physicalSensations:
        return IsLocaleKeys.psInfoAudio;
    }
  }

  List<Map<String?, String?>>? getInfoList1(BuildContext context, String key) {
    // Fetch the raw value from DynamicAssetLoader
    final raw = DynamicAssetLoader.getNestedValue(key, context);

    // Log the runtimeType and raw value for debugging
    print('Raw value from getNestedValue: $raw');
    print('Raw value runtimeType: ${raw.runtimeType}');

    // Check if the raw value is a List<dynamic>
    if (raw is List<dynamic>) {
      print('Entered List<dynamic> block');

      // Ensure the list contains Map<String, dynamic> items and map 'text' and 'audio'
      return raw
          .whereType<Map<String, dynamic>>()
          .map(
            (e) => {
              'text': e['text']?.toString(), // Extract 'text'
              'audio': e['audio']?.toString(), // Extract 'audio'
            },
          )
          .toList();
    }

    // Log the type if raw is not a List
    print('Raw value is not a List<dynamic>, it is: ${raw.runtimeType}');

    // Return null if raw is not a List
    return null;
  }

  List<Map<String?, String?>> get getInfoList {
    switch (this) {
      case MyDiagramStates.difficultSituation:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.dsSlidesText) ??
            [
              {
                'text': 'Everyone faces difficult situations in life',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And these situations can affect how we think and feel',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'We can put ourselves at risk by making ‘seemingly irrelevant decisions’',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Our risky choices may be deliberate or we may be unaware of them',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to plan ahead and think about the choices you make',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to stay aware and stay safe!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ds.is.6.bfo.uk.f.en.mp3',
              }
            ];
      case MyDiagramStates.negativeThoughts:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.ntSlidesText) ??
            [
              {
                'text': 'Everyone has lots of thoughts running through their mind',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And our thoughts can really affect how we feel',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'But we don’t have to listen to our negative thoughts',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And we don’t have to feel bad for no reason',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to recognise your own mind traps',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to set your mind free!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Nt.is.6.bfo.uk.f.en.mp3',
              }
            ];
      case MyDiagramStates.emotionalImpact:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.eiSlidesText) ??
            [
              {
                'text': 'Everyone experiences good and bad emotions',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Our emotions are driven by how we think',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Trying to block out our emotions will never work',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And emotional highs while under the influence are not real',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to take control of your emotions',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to stay calm in any situation!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ei.is.6.bfo.uk.f.en.mp3',
              }
            ];
      case MyDiagramStates.lifestyle:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.lsSlidesText) ??
            [
              {
                'text': 'Everyone’s lifestyle is made up of different elements',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And our own lifestyle is shaped by the choices we make',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'We may think there are too many barriers for us to change things',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'But we can achieve our goals if we take the right approach',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to focus on goals that will make a real difference',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to change your life for the better!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.is.6.bfo.uk.f.en.mp3',
              }
            ];
      case MyDiagramStates.unhelpfulBehaviours:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.ubSlidesText) ??
            [
              {
                'text': 'Everyone tries to do the right things',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'But it can be harder when life isn’t going so well',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Letting our time slip away can make us more likely to lapse',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And we can’t feel good if we do nothing fun or worthwhile',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to plan ahead and put structure in your days',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to enjoy your life and feel good!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ub.is.6.bfo.uk.f.en.mp3',
              }
            ];
      case MyDiagramStates.physicalSensations:
        return getInfoList1(navigatorKey.currentContext!, IsLocaleKeys.psSlidesText) ??
            [
              {
                'text': 'Everyone’s physical sensations ebb and flow all the time',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.1.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Our physical sensations can be caused by our emotions',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.2.bfo.uk.f.en.mp3',
              },
              {
                'text': 'Cravings can strike us without warning at any time',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.3.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And cravings can lead to powerful urges to lapse',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.4.bfo.uk.f.en.mp3',
              },
              {
                'text': 'So the key is to stay strong and not give in to your cravings',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.5.bfo.uk.f.en.mp3',
              },
              {
                'text': 'And use the action strategy to surf your cravings and urges!',
                'audio': 'https://d24v3ngjgcwbka.cloudfront.net/audio/Ps.is.6.bfo.uk.f.en.mp3',
              }
            ];
    }
  }
}

enum DiagramBtnColor {
  green,
  red,
  orange,
}

extension DiagramBtnColorX on DiagramBtnColor {
  Color get color {
    switch (this) {
      case DiagramBtnColor.green:
        return navigatorKey.currentContext!.themeColors.greenBtnColor;
      case DiagramBtnColor.orange:
        return navigatorKey.currentContext!.themeColors.orangeColor;
      case DiagramBtnColor.red:
        return navigatorKey.currentContext!.themeColors.redColor;
    }
  }

  String get name {
    switch (this) {
      case DiagramBtnColor.green:
        return DiagramLocaleKeys.labelsGreen.tr();
      case DiagramBtnColor.orange:
        return DiagramLocaleKeys.labelsAmber.tr();
      case DiagramBtnColor.red:
        return DiagramLocaleKeys.labelsRed.tr();
    }
  }
}

enum ButtonState {
  yesEnabled,
  noEnabled,
  bothDisabled;
}

enum AlertButtonState {
  yesEnabled,
  noEnabled,
  // bothDisabled;
}

extension ButtonStateValue on ButtonState {
  bool? get boolValue => switch (this) {
        ButtonState.yesEnabled => true,
        ButtonState.noEnabled => false,
        ButtonState.bothDisabled => null,
      };
}

enum DiagramInfoPannel {
  diagram,
  info,
  idea,
}

enum Achievements {
  myGoal,
  myStrategies,
  myHours,
  myDiagram,
}
