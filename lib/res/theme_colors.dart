// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

/// Theme colors
@immutable
class ThemeColors extends ThemeExtension<ThemeColors> {
  /// constructor
  const ThemeColors({
    required this.greenColor,
    required this.gradientGreenColor,
    required this.greenBtnColor,
    required this.appBarBorder,
    required this.lightPurpleColor,
    required this.appBarBorder2,
    required this.dividerColor,
    required this.scaffoldColor,
    required this.blueColor,
    required this.graphLine,
    required this.lightBlueColor,
    required this.orangeColor,
    required this.lightOrangeColor,
    required this.darkOrangeColor,
    required this.lightYellowColor,
    required this.redColor,
    required this.errorRedColor,
    required this.lightBrownColor,
    required this.brownColor,
    required this.amberColor,
    required this.darkBrownColor,
    required this.darkBlue,
    required this.linkColor,
    required this.greyColor,
    required this.darkGreyColor,
    required this.diagramBtnColor,
    required this.switchThumbColor,
    required this.audioPannelBg,
    required this.audioPannelBorder,
    required this.purpleColor,
    required this.disableButtonColor,
    required this.blackColor,
    required this.whiteColor,
    required this.textfieldTextColor,
    required this.linkTextColor,
  });

  final Color greenColor;
  final Color darkGreyColor;
  final Color gradientGreenColor;
  final Color greenBtnColor;
  final Color appBarBorder;
  final Color appBarBorder2;
  final Color dividerColor;
  final Color linkTextColor;
  final Color whiteColor;
  final Color textfieldTextColor;
  final Color lightPurpleColor;
  final Color blackColor;
  final Color disableButtonColor;
  final Color scaffoldColor;
  final Color blueColor;
  final Color lightBlueColor;
  final Color graphLine;
  final Color orangeColor;
  final Color darkOrangeColor;
  final Color lightOrangeColor;
  final Color lightYellowColor;
  final Color redColor;
  final Color errorRedColor;
  final Color lightBrownColor;
  final Color brownColor;
  final Color darkBrownColor;
  final Color linkColor;
  final Color darkBlue;
  final Color amberColor;
  final Color greyColor;
  final Color diagramBtnColor;
  final Color switchThumbColor;
  final Color audioPannelBg;
  final Color audioPannelBorder;
  final Color purpleColor;

  @override
  ThemeExtension<ThemeColors> copyWith({
    Color? greenColor,
    Color? gradientGreenColor,
    Color? greenBtnColor,
    Color? darkGreyColor,
    Color? lightPurpleColor,
    Color? appBarBorder,
    Color? lightOrangeColor,
    Color? appBarBorder2,
    Color? dividerColor,
    Color? scaffoldColor,
    Color? whiteColor,
    Color? blueColor,
    Color? orangeColor,
    Color? graphLine,
    Color? darkOrangeColor,
    Color? lightYellowColor,
    Color? redColor,
    Color? lightBrownColor,
    Color? brownColor,
    Color? darkBrownColor,
    Color? amberColor,
    Color? linkColor,
    Color? lightBlueColor,
    Color? errorRedColor,
    Color? greyColor,
    Color? diagramBtnColor,
    Color? switchThumbColor,
    Color? darkBlue,
    Color? audioPannelBg,
    Color? audioPannelBorder,
    Color? purpleColor,
    Color? linkTextColor,
    Color? disableButtonColor,
    Color? blackColor,
    Color? textfieldTextColor,
  }) {
    return ThemeColors(
      greenColor: greenColor ?? this.greenColor,
      darkBlue: darkBlue ?? this.darkBlue,
      graphLine: graphLine ?? this.graphLine,
      gradientGreenColor: gradientGreenColor ?? this.gradientGreenColor,
      greenBtnColor: greenBtnColor ?? this.greenBtnColor,
      darkBrownColor: darkBrownColor ?? this.darkBrownColor,
      amberColor: amberColor ?? this.amberColor,
      linkColor: linkColor ?? this.linkColor,
      lightOrangeColor: lightOrangeColor ?? this.lightOrangeColor,
      textfieldTextColor: textfieldTextColor ?? this.textfieldTextColor,
      appBarBorder: appBarBorder ?? this.appBarBorder,
      appBarBorder2: appBarBorder2 ?? this.appBarBorder2,
      dividerColor: dividerColor ?? this.dividerColor,
      whiteColor: whiteColor ?? this.whiteColor,
      blackColor: blackColor ?? this.blackColor,
      disableButtonColor: disableButtonColor ?? this.disableButtonColor,
      scaffoldColor: scaffoldColor ?? this.scaffoldColor,
      blueColor: blueColor ?? this.blueColor,
      lightBlueColor: lightBlueColor ?? this.lightBlueColor,
      orangeColor: orangeColor ?? this.orangeColor,
      darkOrangeColor: darkOrangeColor ?? this.darkOrangeColor,
      lightYellowColor: lightYellowColor ?? this.lightYellowColor,
      redColor: redColor ?? this.redColor,
      errorRedColor: errorRedColor ?? this.errorRedColor,
      lightBrownColor: lightBrownColor ?? this.lightBrownColor,
      brownColor: brownColor ?? this.brownColor,
      greyColor: greyColor ?? this.greyColor,
      diagramBtnColor: diagramBtnColor ?? this.diagramBtnColor,
      switchThumbColor: switchThumbColor ?? this.switchThumbColor,
      audioPannelBg: audioPannelBg ?? this.audioPannelBg,
      audioPannelBorder: audioPannelBorder ?? this.audioPannelBorder,
      purpleColor: purpleColor ?? this.purpleColor,
      linkTextColor: linkTextColor ?? this.linkTextColor,
      lightPurpleColor: lightPurpleColor ?? this.lightPurpleColor,
      darkGreyColor: darkGreyColor ?? this.darkGreyColor,
    );
  }

  @override
  ThemeExtension<ThemeColors> lerp(
    covariant ThemeExtension<ThemeColors>? other,
    double t,
  ) {
    if (other is! ThemeColors) {
      return this;
    }
    return ThemeColors(
      greenColor: Color.lerp(greenColor, other.greenColor, t)!,
      darkBlue: Color.lerp(darkBlue, other.darkBlue, t)!,
      graphLine: Color.lerp(graphLine, other.graphLine, t)!,
      gradientGreenColor: Color.lerp(gradientGreenColor, other.gradientGreenColor, t)!,
      greenBtnColor: Color.lerp(greenBtnColor, other.greenBtnColor, t)!,
      darkBrownColor: Color.lerp(darkBrownColor, other.darkBrownColor, t)!,
      amberColor: Color.lerp(amberColor, other.amberColor, t)!,
      linkColor: Color.lerp(linkColor, other.linkColor, t)!,
      lightOrangeColor: Color.lerp(lightOrangeColor, other.lightOrangeColor, t)!,
      linkTextColor: Color.lerp(linkTextColor, other.linkTextColor, t)!,
      appBarBorder: Color.lerp(appBarBorder, other.appBarBorder, t)!,
      appBarBorder2: Color.lerp(appBarBorder2, other.appBarBorder2, t)!,
      textfieldTextColor: Color.lerp(textfieldTextColor, other.textfieldTextColor, t)!,
      whiteColor: Color.lerp(whiteColor, other.whiteColor, t)!,
      blackColor: Color.lerp(blackColor, other.blackColor, t)!,
      disableButtonColor: Color.lerp(disableButtonColor, other.disableButtonColor, t)!,
      scaffoldColor: Color.lerp(scaffoldColor, other.scaffoldColor, t)!,
      blueColor: Color.lerp(blueColor, other.blueColor, t)!,
      orangeColor: Color.lerp(orangeColor, other.orangeColor, t)!,
      darkOrangeColor: Color.lerp(darkOrangeColor, other.darkOrangeColor, t)!,
      errorRedColor: Color.lerp(errorRedColor, other.errorRedColor, t)!,
      lightYellowColor: Color.lerp(lightYellowColor, other.lightYellowColor, t)!,
      redColor: Color.lerp(redColor, other.redColor, t)!,
      lightBrownColor: Color.lerp(lightBrownColor, other.lightBrownColor, t)!,
      brownColor: Color.lerp(brownColor, other.brownColor, t)!,
      lightBlueColor: Color.lerp(lightBlueColor, other.lightBlueColor, t)!,
      greyColor: Color.lerp(greyColor, other.greyColor, t)!,
      diagramBtnColor: Color.lerp(diagramBtnColor, other.diagramBtnColor, t)!,
      switchThumbColor: Color.lerp(switchThumbColor, other.switchThumbColor, t)!,
      audioPannelBg: Color.lerp(audioPannelBg, other.audioPannelBg, t)!,
      audioPannelBorder: Color.lerp(audioPannelBorder, other.audioPannelBorder, t)!,
      purpleColor: Color.lerp(purpleColor, other.purpleColor, t)!,
      lightPurpleColor: Color.lerp(lightPurpleColor, other.lightPurpleColor, t)!,
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t)!,
      darkGreyColor: Color.lerp(darkGreyColor, other.darkGreyColor, t)!,
    );
  }
}
