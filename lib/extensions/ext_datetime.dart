import 'package:flutter/material.dart';

/// Extension on [DateTime] class
extension DateTimeX on DateTime {
  /// to check if date is today
  bool isSameDay(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }

  /// to check if date is today
  bool isToday() {
    final now = DateTime.now();
    return now.day == day && now.month == month && now.year == year;
  }

  /// to check if date is yesterday
  bool isYesterday() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return yesterday.day == day && yesterday.month == month && yesterday.year == year;
  }

  /// Set Time of day
  DateTime setTimeOfDay(TimeOfDay time) {
    return DateTime(year, month, day, time.hour, time.minute);
  }

  /// Time of dat from date time
  TimeOfDay get time => TimeOfDay.fromDateTime(this);
}
