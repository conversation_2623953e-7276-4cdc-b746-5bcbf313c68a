import 'package:flutter/material.dart';

/// Extension for color
extension ColorExt on Color {
  // amount range from 0.0 to 1.0

  /// darken color
  Color darken([double amount = .1]) {
    assert(amount >= 0 && amount <= 1, 'Amount should be between 0.0 and 1.0');

    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));

    return hslDark.toColor();
  }

  /// lighten color
  Color lighten([double amount = .1]) {
    assert(amount >= 0 && amount <= 1, 'Amount should be between 0.0 and 1.0');

    final hsl = HSLColor.fromColor(this);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));

    return hslLight.toColor();
  }
}
