/// Extension on duration
extension VideoTimer on Duration {
  /// Returns HH:MM:SS formatted time.
  String format() {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    // final twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    final twoDigitMinutes = inMinutes.remainder(60);
    final twoDigitSeconds = twoDigits(inSeconds.remainder(60));
    return '$twoDigitMinutes:$twoDigitSeconds';
  }
}
