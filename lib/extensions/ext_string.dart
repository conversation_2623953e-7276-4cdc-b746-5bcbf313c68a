import 'dart:math';

/// extension for [String]
extension StringX on String {
  /// to capitalize first letter of string
  String get capitalized => this[0].toUpperCase() + substring(1).toLowerCase();

  /// mask phone number
  String get maskedPhone => replaceRange(3, length - 2, '*' * 5);

  /// mask email
  String get maskedEmail => replaceFirstMapped(RegExp(r'^(.)(.*?)([^@]?)(?=@[^@]+$)'), (m) {
        final start = m.group(1);
        final middle = '*' * max(4, m.group(2)?.length ?? 0);
        final end = m.groupCount >= 3 ? m.group(3) : start;
        return start! + middle + end!;
      });

  /// Validates URL
  bool get isValidUrl => RegExp(r'^(http|https):\/\/' // protocol
          r'([a-zA-Z0-9\-\.]+)' // domain name
          r'(\.[a-zA-Z]{2,})' // top-level domain
          r'((\/\w+)*)*' // path
          r"(\/[a-zA-Z0-9\-\._\?\,\'\/\\\+&%\$#\=~])*" // query string
          r"(#[a-zA-Z0-9\-\._\?\,\'\/\\\+&%\$#\=~]*)?$" // fragment
          )
      .hasMatch(this);

  ///Convert String to Date time's Epoch
  int get toDateTimeEpoch {
    final dateTime = DateTime.parse(this);

    // Convert to local time
    final localDateTime = dateTime.toLocal();

    return localDateTime.millisecondsSinceEpoch;
  }

  /// convert community id to community channel id for chat
  String get communityChannelId => 'community_$this';

  ///
  bool get hasAnyLink => RegExp(r'''
  /(https:\/\/www\.|http:\/\/www\.|https:\/\/|http:\/\/)?[a-zA-Z]{2,}(\.[a-zA-Z]{2,})(\.[a-zA-Z]{2,})?\/[a-zA-Z0-9]{2,}|((https:\/\/www\.|http:\/\/www\.|https:\/\/|http:\/\/)?[a-zA-Z]{2,}(\.[a-zA-Z]{2,})(\.[a-zA-Z]{2,})?)|(https:\/\/www\.|http:\/\/www\.|https:\/\/|http:\/\/)?[a-zA-Z0-9]{2,}\.[a-zA-Z0-9]{2,}\.[a-zA-Z0-9]{2,}(\.[a-zA-Z0-9]{2,})? /gm
  ''').hasMatch(this);
}
