import 'dart:async';
import 'dart:convert';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

class DynamicAssetLoader extends AssetLoader {
  DynamicAssetLoader({
    required this.assetUrlNameList,
    // required this.languageCode,
    // required this.countryDomain,
  });
  final List<String> assetUrlNameList;
  // final String languageCode;
  // final String countryDomain;

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) async {
    final mergedData = <String, dynamic>{};
    final fetchTasks = <Future<void>>[];
    'url'.logD;
    for (final assetName in assetUrlNameList) {
      '>>>>> ${locale.languageCode}'.logD;
      '<<<<< ${locale.countryCode}'.logD;
      locale.logD;
      'Injector.instance<AppDB>().localizedJson  == ${Injector.instance<AppDB>().localizedJson}'.logD;
      String? url;
      // if (locale.languageCode == 'fr') {
      // url = 'https://breakingfreeonline.$countryDomain/locales/$languageCode/$assetName.json';
      // } else {
      //   url = 'https://breakingfreeonline.com/locales/en/$assetName.json';
      // }
      url = getUrl(locale, assetName);
      fetchTasks.add(
        http.get(Uri.parse(url)).then((response) {
          url.logD;
          if (response.statusCode == 200) {
            final data = json.decode(response.body) as Map<String, dynamic>;

            mergedData.addAll({
              url!.split('/').last.split('.').first: data,
            });
          } else {
            //  print('Failed to load localization from $url: ${response.statusCode}');
          }
        }).catchError((error) {
          // print('Error fetching $url: $error');
        }),
      );
    }
    await Future.wait(fetchTasks); // Run all fetches concurrently
    if (mergedData.isNotEmpty) {
      Injector.instance<AppDB>().localizedJson = json.encode(mergedData);
      'Injector.instance<AppDB>().localizedJson ${Injector.instance<AppDB>().localizedJson}'.logD;
    } else if (Injector.instance<AppDB>().localizedJson != null) {
      mergedData.addAll(json.decode(Injector.instance<AppDB>().localizedJson!) as Map<String, dynamic>);
    } else {
      final localePath = getLocalePath(path, locale);
      return json.decode(await rootBundle.loadString(localePath)) as Map<String, dynamic>?;
    }
    return mergedData;
  }

  String getLocalePath(String basePath, Locale locale) {
    return '$basePath/${locale.toStringWithSeparator(separator: "-")}.json';
  }

  String getUrl(
    Locale locale,
    String assetName,
  ) {
    // Get the locale as a string (e.g., 'en_US')
    final localeString = locale.toString();
    // https: //breakingfreeonline.com/locales/en/$assetName.json
    'localeString $localeString'.logD;
    switch (localeString) {
      case 'en_US':
        return 'https://breakingfreeonline.us/locales/en-US/$assetName.json';
      case 'es_US':
        return 'https://breakingfreeonline.us/locales/es-US/$assetName.json';
      case 'en_UK':
        return 'https://breakingfreeonline.com/locales/en-GB/$assetName.json';
      case 'fr_CA':
        return 'https://breakingfreeonline.ca/locales/fr-CA/$assetName.json';
      case 'en_AU':
        return 'https://breakingfreeonline.com.au/locales/en-AU/$assetName.json';
      case 'en_CA':
        return 'https://breakingfreeonline.ca/locales/en-CA/$assetName.json';
      default:
        return 'https://breakingfreeonline.com/locales/en-GB/$assetName.json';
    }
  }

  static bool _hasResetLocale = false;
  static dynamic getNestedValue(String key, BuildContext context) {
    final mergedData = Injector.instance<AppDB>().localizedJson;

    if (!_hasResetLocale && Injector.instance<AppDB>().localizedJson == null) {
      _hasResetLocale = true;

      'Locale already set: ${EasyLocalization.of(context)?.locale}'.logD;

      final currentLocale = EasyLocalization.of(context)?.locale ?? const Locale('en', 'GB');

      unawaited(EasyLocalization.of(context)?.resetLocale());
      EasyLocalization.of(context)?.setLocale(currentLocale);

      // Force rebuild to reload all localization files
    }
   // 'Injector.instance<AppDB>().localizedJson ++ $mergedData'.logD;

    if (mergedData != null) {
      final jsonMap = jsonDecode(mergedData) as Map<String, dynamic>;

      final keys = key.split('.');
      dynamic value = jsonMap;
      for (final k in keys) {
     //   'AssessmentLocaleKeys.recoveryProgramQuestionsSpecialAddictionItems$value'.logD;
        if (value is Map<String, dynamic> && value.containsKey(k)) {
          value = value[k];
        } else {
          return null;
        }
      }

      // If the value is a list, return the list
      if (value is List) {
        return value;
      }
      return value;
    }
    return null; // No merged data available
  }
}
