import 'dart:convert';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LocalizationService {
  factory LocalizationService() => _instance;

  LocalizationService._internal();
  static final LocalizationService _instance = LocalizationService._internal();

  Map<String, dynamic>? _localizedData;

  // Method to load localization data
  Future<void> loadLocalizationData(BuildContext context) async {
    final locale = context.locale; 

    final mergedData = Injector.instance<AppDB>().localizedJson;

    if (mergedData != null) {
      _localizedData = jsonDecode(mergedData) as Map<String, dynamic>;
    } else {
      // Handle the case where no merged data is available
      _localizedData = null;
    }
  }

  // Method to get a nested value
  dynamic getNestedValue(String key) {
    if (_localizedData != null) {
      final keys = key.split('.');
      dynamic value = _localizedData;
      for (final k in keys) {
        if (value is Map<String, dynamic> && value.containsKey(k)) {
          value = value[k];
        } else {
          return null; // Key not found
        }
      }
      return value;
    }
    return null; // No data loaded
  }
}
