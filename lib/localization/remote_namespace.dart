class RemoteNamespace 
 {
  static const String accessibilityStatement = 'accessibilityStatement';
  static const String app = 'app';
  static const String as = 'as';
  static const String assessment = 'assessment';
  static const String checkin = 'checkin';
  static const String clock = 'clock';
  static const String core = 'core';
  static const String dashboard = 'dashboard';
  static const String data = 'data';
  static const String diagram = 'diagram';
  static const String drugs = 'drugs';
  static const String entry = 'entry';
  static const String isIs = 'is';
  static const String meetings = 'meetings';
  static const String policyUpdates = 'policyUpdates';
  static const String settings = 'settings';
  static const String toolkit = 'toolkit';
  static const String tutorial = 'tutorial';
  static const String unsubscriptions = 'unsubscriptions';
  static const String verification = 'verification';


}
