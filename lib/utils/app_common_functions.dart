import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AppCommonFunctions {
  static void closeKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static Future<LatLng?> getLatLongFromAddress({
    required String address,
  }) async {
    var data = <Location>[];
    try {
      data = await locationFromAddress(address);
    } catch (e) {
      e.logD;
    }
    return LatLng(data.elementAt(0).latitude, data.elementAt(0).longitude);
  }

  static Future<String?> getAddressDetails(LatLng latLng) async {
    var address = '';
    try {
      final placemarks = await placemarkFromCoordinates(latLng.latitude, latLng.longitude);
      final place = placemarks[0];
      address = '${place.name}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}';
    } catch (e) {
      e.logD;
    }
    address.logE;
    return address;
  }

  // static String getFormattedTranslation(String key, Map<String, String> namedArgs) {
  //   final translatedText = key.tr(namedArgs: namedArgs);
  //   return translatedText.replaceAll(RegExp('[{]|[}]'), '');
  // }

  static String formatListString(String input) {
    final wordRegex = RegExp(r'\b[A-Za-z0-9-]+\b');

    return input.replaceAllMapped(wordRegex, (match) {
      final word = match.group(0)!;

      // If the entire word is uppercase (with possible numbers or dashes), keep it as is
      if (RegExp(r'^[A-Z0-9-]+$').hasMatch(word)) {
        return word;
      }

      // Convert non-acronyms to lowercase
      return word;
    });
  }

  static String getFormattedTranslation(String key, Map<String, String> namedArgs) {
    final drug = namedArgs['drug']?.toLowerCase() ?? '';
    final langCode = EasyLocalization.of(navigatorKey.currentContext!)?.locale.languageCode ?? 'en';
    'langCode $langCode'.logV;

    if ((langCode == 'fr' || langCode == 'es') && drug.isNotEmpty) {
      final elisionListDynamic = DynamicAssetLoader.getNestedValue(
        DrugsLocaleKeys.drugElisions,
        navigatorKey.currentContext!,
      );

      if (elisionListDynamic is List) {
        final elisionList = elisionListDynamic.map((e) => e.toString().toLowerCase()).toList();
        final isElision = elisionList.contains(drug);

        namedArgs['preOf'] = isElision ? 'de l’' : 'de ';
        namedArgs['preThe'] = isElision ? 'l’' : 'la ';
        'Drug: $drug'.logV;
        'Is elision: $isElision'.logV;
        'preOf: ${namedArgs['preOf']}'.logV;
        'preThe: ${namedArgs['preThe']}'.logV;
      } else {
        // If elision list is not found or not a list
        namedArgs['preOf'] = 'de ';
        namedArgs['preThe'] = 'la ';
        'Elision list not found or invalid'.logE;
      }
    } else {
      namedArgs['preOf'] = '';
      namedArgs['preThe'] = '';
    }

    final translatedText = key.tr(namedArgs: namedArgs);
    return translatedText.replaceAll(RegExp('[{]|[}]'), '');
  }

  // static String getFormattedTranslation(String key, Map<String, String> namedArgs) {
  //   final drug = namedArgs['drug']?.toLowerCase() ?? '';
  //   final langCode = EasyLocalization.of(navigatorKey.currentContext!)?.locale.languageCode ?? 'en';
  //   'langCode $langCode'.logV;
  //   if ((langCode == 'fr' || langCode == 'es') && drug.isNotEmpty) {
  //     // Load elision list from JSON asset
  //     final elisionListDynamic = DynamicAssetLoader.getNestedValue(
  //       DrugsLocaleKeys.drugElisions,
  //       navigatorKey.currentContext!,
  //     ) as List<dynamic>;

  //     // Cast to List<String> and lowercase all items for comparison
  //     final elisionList = elisionListDynamic.map((e) => e.toString().toLowerCase()).toList();

  //     final isElision = elisionList.contains(drug);

  //     namedArgs['preOf'] = isElision ? 'de l’' : 'de ';
  //     namedArgs['preThe'] = isElision ? 'l’' : 'la ';
  //     'Drug: $drug'.logV;
  //     'Is elision: $isElision'.logV;
  //     'preOf: ${namedArgs['preOf']}'.logV;
  //     'preThe: ${namedArgs['preThe']}'.logV;
  //   } else {
  //     namedArgs['preOf'] = '';
  //     namedArgs['preThe'] = '';
  //   }

  //   final translatedText = key.tr(namedArgs: namedArgs);
  //   return translatedText.replaceAll(RegExp('[{]|[}]'), '');
  // }

  static String formatListString1(String input) {
    final wordRegex = RegExp(r'\b[A-Za-z0-9-]+\b');
    'drugName $input'.logV;
    return input.replaceAllMapped(wordRegex, (match) {
      final word = match.group(0)!;

      // If the entire word is uppercase (with possible numbers or dashes), keep it as is
      if (RegExp(r'^[A-Z0-9-]+$').hasMatch(word)) {
        'drugName ++ $word'.logE;

        return word;
      }
      'drugName ${word.toLowerCase()}'.logE;
      // Convert non-acronyms to lowercase
      return word.toLowerCase();
    });
  }

  static Future<void> scrollToKey(GlobalKey key) async {
    final context = key.currentContext;
    if (context != null) {
      await Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 400),
        alignment: 0.2,
        curve: Curves.easeInOut,
      );
    }
  }
}

class AppException implements Exception {
  AppException(this.message);
  final String message;

  @override
  String toString() => message;
}
