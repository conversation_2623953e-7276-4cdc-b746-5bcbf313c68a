import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

class GlobalConnectivity<PERSON>he<PERSON> extends StatefulWidget {
  final Widget child;

  const GlobalConnectivityChecker({super.key, required this.child});

  @override
  State<GlobalConnectivityChecker> createState() => _GlobalConnectivityCheckerState();
}

class _GlobalConnectivityCheckerState extends State<GlobalConnectivityChecker> {
  StreamSubscription<List<ConnectivityResult>>? _subscription;
  Timer? _pollTimer;
  bool _dialogShown = false;
  ConnectivityResult? _lastStatus;

  @override
  void initState() {
    super.initState();

    // Listen to connectivity stream
    _subscription = Connectivity().onConnectivityChanged.listen((results) {
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _handleConnectivity(result);
    });

    // Manual fallback polling
    _pollTimer = Timer.periodic(const Duration(seconds: 1), (_) async {
      final results = await Connectivity().checkConnectivity();
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _handleConnectivity(result);
    });

    // Initial check after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final results = await Connectivity().checkConnectivity();
      final result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      _handleConnectivity(result);
    });
  }

  void _handleConnectivity(ConnectivityResult result) {
    if (result != _lastStatus) {
      debugPrint(">?>?>?🔌 Network changed: $result");
    }

    if (result == ConnectivityResult.none) {
      _showNoInternetDialog();
    } else {
      _dismissDialog();
    }

    _lastStatus = result;
  }

  void _showNoInternetDialog() {
    if (_dialogShown || !mounted) return;

    _dialogShown = true;
    debugPrint(">?>?>?⚠️ Showing no internet dialog");

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        title: const Text("No Internet Connection"),
        content: const Text("Please check your internet settings and try again."),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context, rootNavigator: true).pop();
              _dialogShown = false;
            },
            child: const Text("OK"),
          ),
        ],
      ),
    );
  }

  void _dismissDialog() {
    if (_dialogShown && mounted) {
      Navigator.of(context, rootNavigator: true).pop();
      _dialogShown = false;
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _pollTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
