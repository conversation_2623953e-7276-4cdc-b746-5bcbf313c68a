class CoreLocaleKeys {
  // header keys
  static const headerMainMenu = 'core.header.mainMenu';
  static const headerMainMenuLabel = 'core.header.mainMenuLabel';
  static const headerSpeakerLabel = 'core.header.speakerLabel';
  static const headerLogOutLabel = 'core.header.logOutLabel';

  // sideMenu keys
  static const sideMenuAlerts = 'core.sideMenu.alerts';
  static const sideMenuAccessibility = 'core.sideMenu.accessibility';
  static const sideMenuRoleSelection = 'core.sideMenu.roleSelection';
  static const sideMenuDashboard = 'core.sideMenu.dashboard';
  static const sideMenuDiagram = 'core.sideMenu.diagram';
  static const sideMenuToolkit = 'core.sideMenu.toolkit';
  static const sideMenuClock = 'core.sideMenu.clock';
  static const sideMenuSettings = 'core.sideMenu.settings';
  static const sideMenuData = 'core.sideMenu.data';
  static const sideMenuTutorial = 'core.sideMenu.tutorial';
  static const sideMenuMeetings = 'core.sideMenu.meetings';
  static const sideMenuUsers = 'core.sideMenu.users';
  static const sideMenuLogOut = 'core.sideMenu.logOut';

  // dataWarning keys
  static const dataWarningText = 'core.dataWarning.text';
  static const dataWarningLinkText = 'core.dataWarning.linkText';

  // unitCalculator titles keys
  static const unitCalculatorTitlesTable = 'core.unitCalculator.titles.table';
  static const unitCalculatorTitlesDrink = 'core.unitCalculator.titles.drink';
  static const unitCalculatorTitlesSize = 'core.unitCalculator.titles.size';
  static const unitCalculatorTitlesStrength =
      'core.unitCalculator.titles.strength';
  static const unitCalculatorTitlesQuantity =
      'core.unitCalculator.titles.quantity';

  // unitCalculator headers keys
  static const unitCalculatorHeadersDrink = 'core.unitCalculator.headers.drink';
  static const unitCalculatorHeadersSize = 'core.unitCalculator.headers.size';
  static const unitCalculatorHeadersStrength =
      'core.unitCalculator.headers.strength';
  static const unitCalculatorHeadersQuantity =
      'core.unitCalculator.headers.quantity';

  // unitCalculator labels keys
  static const unitCalculatorLabelsAddQuantity =
      'core.unitCalculator.labels.addQuantity';
  static const unitCalculatorLabelsRemoveQuantity =
      'core.unitCalculator.labels.removeQuantity';
  static const unitCalculatorLabelsDelete = 'core.unitCalculator.labels.delete';
  static const unitCalculatorLabelsAdd = 'core.unitCalculator.labels.add';

  // unitCalculator other keys
  static const unitCalculatorBeer = 'core.unitCalculator.beer';
  static const unitCalculatorWine = 'core.unitCalculator.wine';
  static const unitCalculatorSpirits = 'core.unitCalculator.spirits';
  static const unitCalculatorAlcopop = 'core.unitCalculator.alcopop';
  static const unitCalculatorSmallBottle = 'core.unitCalculator.small_bottle';
  static const unitCalculatorBottle = 'core.unitCalculator.bottle';
  static const unitCalculatorCan = 'core.unitCalculator.can';
  static const unitCalculatorPint = 'core.unitCalculator.pint';
  static const unitCalculatorSmallGlass = 'core.unitCalculator.small_glass';
  static const unitCalculatorStandardGlass =
      'core.unitCalculator.standard_glass';
  static const unitCalculatorLargeGlass = 'core.unitCalculator.large_glass';
  static const unitCalculatorSingle = 'core.unitCalculator.single';
  static const unitCalculatorLargeSingle = 'core.unitCalculator.large_single';
  static const unitCalculatorDouble = 'core.unitCalculator.double';
  static const unitCalculatorLargeDouble = 'core.unitCalculator.large_double';
  static const unitCalculatorLargeBottle = 'core.unitCalculator.large_bottle';
  static const unitCalculatorStandardBottle =
      'core.unitCalculator.standard_bottle';

  // survey intro keys
  static const surveyIntroTitle = 'core.survey.intro.title';
  static const surveyIntroText = 'core.survey.intro.text';
  static const surveyIntroOptions = 'core.survey.intro.options';

  // survey recommend keys
  static const surveyRecommendTitle = 'core.survey.recommend.title';
  static const surveyRecommendText = 'core.survey.recommend.text';
  static const surveyRecommendLabels = 'core.survey.recommend.labels';

  // survey recommendReason keys
  static const surveyRecommendReasonTitle = 'core.survey.recommendReason.title';
  static const surveyRecommendReasonText = 'core.survey.recommendReason.text';
  static const surveyRecommendReasonPlaceholder =
      'core.survey.recommendReason.placeholder';

  // survey enhanced keys
  static const surveyEnhancedTitle = 'core.survey.enhanced.title';
  static const surveyEnhancedText = 'core.survey.enhanced.text';
  static const surveyEnhancedOptions = 'core.survey.enhanced.options';

  // survey feedback keys
  static const surveyFeedbackTitle = 'core.survey.feedback.title';
  static const surveyFeedbackText = 'core.survey.feedback.text';
  static const surveyFeedbackPlaceholder = 'core.survey.feedback.placeholder';

  // survey thankYou keys
  static const surveyThankYouTitle = 'core.survey.thankYou.title';
  static const surveyThankYouText = 'core.survey.thankYou.text';
  static const surveyThankYouFinishButton = 'core.survey.thankYou.finishButton';

  // survey errors keys
  static const surveyErrorsRequired = 'core.survey.errors.required';

  // logOut keys
  static const logOutTitle = 'core.logOut.title';
  static const logOutText = 'core.logOut.text';
  static const logOutButtonsYes = 'core.logOut.buttons.yes';
  static const logOutButtonsNo = 'core.logOut.buttons.no';

  // titles keys
  static const titlesRoleSelection = 'core.titles./role-selection';
  static const titlesProgramme = 'core.titles./programme';
  static const titlesTerms = 'core.titles./terms';
  static const titlesCustomPolicy = 'core.titles./custom-policy';
  static const titlesSignup = 'core.titles./signup';
  static const titlesData = 'core.titles./data';
  static const titlesPolicyUpdates = 'core.titles./policy-updates';
  static const titlesTutorialVideo = 'core.titles./tutorial-video';
  static const titlesAssessment = 'core.titles./assessment';
  static const titlesMyDiagram = 'core.titles./my-diagram';
  static const titlesMyRecoveryClock = 'core.titles./my-recovery-clock';
  static const titlesMyToolkit = 'core.titles./my-toolkit';
  static const titlesCheckin = 'core.titles./checkin';
  static const titlesFindMeetings = 'core.titles./find-meetings';
  static const titlesSettings = 'core.titles./settings';
  static const titlesDashboard = 'core.titles./dashboard';
  static const titlesEapDashboard = 'core.titles./eap-dashboard';
  static const titlesCam = 'core.titles./cam';
  static const titlesUnsubscribe = 'core.titles./unsubscribe';
  static const titlesSurvey = 'core.titles./survey';
  static const titlesVerify = 'core.titles./verify';
  static const titlesInformationStrategiesDifficultSituations =
      'core.titles./information-strategies/difficult-situations';
  static const titlesInformationStrategiesEmotionalImpact =
      'core.titles./information-strategies/emotional-impact';
  static const titlesInformationStrategiesNegativeThoughts =
      'core.titles./information-strategies/negative-thoughts';
  static const titlesInformationStrategiesLifestyle =
      'core.titles./information-strategies/lifestyle';
  static const titlesInformationStrategiesUnhelpfulBehaviours =
      'core.titles./information-strategies/unhelpful-behaviours';
  static const titlesInformationStrategiesPhysicalSensations =
      'core.titles./information-strategies/physical-sensations';
  static const titlesActionStrategiesEmotionalImpact =
      'core.titles./action-strategies/emotional-impact';
  static const titlesActionStrategiesPhysicalSensations =
      'core.titles./action-strategies/physical-sensations';
  static const titlesActionStrategiesDifficultSituations =
      'core.titles./action-strategies/difficult-situations';
  static const titlesActionStrategiesLifestyle =
      'core.titles./action-strategies/lifestyle';
  static const titlesActionStrategiesUnhelpfulBehaviours =
      'core.titles./action-strategies/unhelpful-behaviours';
  static const titlesActionStrategiesNegativeThoughts =
      'core.titles./action-strategies/negative-thoughts';
  static const titlesChangeYourPassword = 'core.titles./change-your-password';
  static const titlesAccessibility = 'core.titles./accessibility';
  static const titlesAlerts = 'core.titles./alerts';

  // labels keys
  static const labelsToggleVisibility = 'core.labels.toggleVisibility';
  static const labelsInformation = 'core.labels.information';
  static const labelsLearnMore = 'core.labels.learn_more';
  static const labelsDiagram = 'core.labels.diagram';
  static const labelsCreatePdf = 'core.labels.createPdf';
  static const labelsDownloadPdf = 'core.labels.downloadPdf';
  static const labelsEmailPdf = 'core.labels.emailPdf';
  static const labelsTextPlaceholder = 'core.labels.textPlaceholder';
  static const labelsSelectPlaceholder = 'core.labels.selectPlaceholder';
  static const labelsSlidePrev = 'core.labels.slidePrev';
  static const labelsSlideNext = 'core.labels.slideNext';
  static const labelsClose = 'core.labels.close';
  static const labelsPlusMinusCurrentValue =
      'core.labels.plusMinusCurrentValue';
  static const labelsPlayVideo = 'core.labels.playVideo';

  // buttons keys
  static const buttonsYes = 'core.buttons.yes';
  static const buttonsNo = 'core.buttons.no';
  static const buttonsNext = 'core.buttons.next';
  static const buttonsBack = 'core.buttons.back';
  static const buttonsExit = 'core.buttons.exit';
  static const buttonsAgree = 'core.buttons.agree';
  static const buttonsDisagree = 'core.buttons.disagree';
  static const buttonsContinue = 'core.buttons.continue';
  static const buttonsPurge = 'core.buttons.purge';
  static const buttonsDownloadPdf = 'core.buttons.downloadPdf';
  static const buttonsEmailPdf = 'core.buttons.emailPdf';
  static const buttonsDiagram = 'core.buttons.diagram';

  // images keys
  static const imagesLogoSrc = 'core.images.logo.src';
  static const imagesLogoAlt = 'core.images.logo.alt';
  static const imagesLogoWithTextSrc = 'core.images.logoWithText.src';
  static const imagesLogoWithTextAlt = 'core.images.logoWithText.alt';

  // errorBoundary keys
  static const errorBoundaryText = 'core.errorBoundary.text';
  static const errorBoundaryButton = 'core.errorBoundary.button';

  // errors keys
  static const errorsPdf = 'core.errors.pdf';
  static  String errorsCustom(String name) => 'core.errors.$name';
  static const errorsNoCheckins = 'core.errors.noCheckins';
  static const errorsAccountCreation = 'core.errors.accountCreation';
  static const errorsScroll = 'core.errors.scroll';
  static const errorsScrollAll = 'core.errors.scrollAll';
  static const errorsDefault = 'core.errors.default';

  static const accessibility = 'core.titles./accessibility';

  static const pageTitle = 'core.titles./notifications';

  // drinkingInfographic
  static const drinkingInfographicTitle = 'core.drinkingInfographic.title';
  static const drinkingInfographicDescription = 'core.drinkingInfographic.columns.description';
  static const drinkingInfographicEquivalents = 'core.drinkingInfographic.columns.equivalents';

  // Beer section
  static const drinkingInfographicBearTitle = 'core.drinkingInfographic.beer.title';
  static const drinkingInfographicBearRows = 'core.drinkingInfographic.beer.rows';

  // Wine section
static const drinkingInfographicWineTitle = 'core.drinkingInfographic.wine.title';
static const drinkingInfographicWineRows = 'core.drinkingInfographic.wine.rows';

// Spirits section
static const drinkingInfographicSpiritsTitle = 'core.drinkingInfographic.spirits.title';
static const drinkingInfographicSpiritsRows = 'core.drinkingInfographic.spirits.rows';

// Footer notes
static const drinkingInfographicFooter = 'core.drinkingInfographic.footer';

// Source
static const drinkingInfographicSource = 'core.drinkingInfographic.source';


}
