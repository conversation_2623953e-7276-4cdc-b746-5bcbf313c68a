import 'package:breakingfree_v2/res/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Prefs {
  static late SharedPreferences preferences;

  Future<void> initSharedPrefs() async {
    await SharedPreferences.getInstance().then((value) => preferences = value);
    'initSharedPrefs'.logD;
  }

  String? getString(String key) {
    return preferences.getString(key);
  }

  Future<bool> clear() {
    return preferences.clear();
  }

   Future<bool> remove(String key) {
    return preferences.remove(key);
  }

  Future<bool> setString(String key, String value) {
    return preferences.setString(key, value);
  }
  

  List<String>? getStringList(String key) {
    return preferences.getStringList(key);
  }

  Future<bool> setStringList(String key, List<String> value) {
    return preferences.setStringList(key, value);
  }

  bool? getBool(String key) {
    return preferences.getBool(key);
  }

  Future<bool> setBool(String key, bool value) async {
    return preferences.setBool(key, value);
  }

  int? getInt(String key) {
    return preferences.getInt(key);
  }

  Future<bool> setInt(String key, int value) {
    return preferences.setInt(key, value);
  }

  double? getDouble(String key) {
    return preferences.getDouble(key);
  }

  Future<bool> setDouble(String key, double value) {
    return preferences.setDouble(key, value);
  }
}
