import 'dart:io' as io;

// ignore_for_file: public_member_api_docs, sort_constructors_first, avoid_dynamic_calls
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/pages/language_page.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/policy_cubit/policy_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/policy_update_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/sign_up_account_verification.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_welcome_back_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/progess_check_day_calculation.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/localization/remote_namespace.dart';
import 'package:breakingfree_v2/location_service/cubit/location_service_cubit.dart';
import 'package:breakingfree_v2/notification_service/notification_helper.dart';
import 'package:breakingfree_v2/repository/cubit/audio_muted_cubit.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/theme_light.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/deep_link_service.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/shared_prefs.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:media_store_plus/media_store_plus.dart';
import 'package:responsive_framework/responsive_framework.dart';

final navigatorKey = GlobalKey<NavigatorState>();
final prefs = Prefs();

IntegrationMode? integrationMode;
AppLifecycleState? appState;
Map<String, dynamic>? localizationData;
String? timeZoneName;

bool get isUnitTest => integrationMode == IntegrationMode.unit;
bool get isWidgetTest => integrationMode == IntegrationMode.widget;
bool get isIntegrationTest => integrationMode == IntegrationMode.integration;
bool get isTestMode =>
    integrationMode == IntegrationMode.integration ||
    integrationMode == IntegrationMode.widget ||
    integrationMode == IntegrationMode.unit;

Future<void> main({Widget? child}) async {
  integrationMode ??= kReleaseMode ? IntegrationMode.release : IntegrationMode.debug;
  WidgetsFlutterBinding.ensureInitialized();

  await LocalNotificationHelper.initialize();
  await EasyLocalization.ensureInitialized();

  if (io.Platform.isAndroid) {
    await MediaStore.ensureInitialized();
    MediaStore.appFolder = 'Breaking Free';
  }

  await SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ],
  );
  timeZoneName = await FlutterTimezone.getLocalTimezone();

  await prefs.initSharedPrefs();

  // Hive setup
  if (child == null) {
    await Injector.initModules();
  }
  await Injector.instance.isReady<AppDB>();
  EndPoints.baseUrl = Injector.instance<AppDB>().baseUrl ?? '';

  EndPoints.baseUrl.logD;

  // final fontSize = Injector.instance<AppDB>().accessibilityFontSize ?? 1.0;
  // final contrastValue = Injector.instance<AppDB>().accessibilityContrastValue ?? 1.0;
  // final isBoldText = Injector.instance<AppDB>().accessibilityIsBoldText ?? false;

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => SignupCubit()),
        BlocProvider(create: (_) => AccessbilityCubit()..setInitialValue()),
        BlocProvider(create: (_) => LocationServiceCubit()),
        BlocProvider(create: (_) => MyAlertCubit()..initalData()),
        BlocProvider(create: (_) => PolicyCubit()..getPolicies1()),
        BlocProvider(create: (_) => LoginCubit()),
        BlocProvider(create: (_) => AssessmentCubit()),
        BlocProvider(create: (_) => AudioMutedCubit()),
      ],
      child: MyApp(rootChild: child),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
    this.rootChild,
  });

  final Widget? rootChild;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  late Widget homeWidget;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    appState = state;
    if (state == AppLifecycleState.resumed) {
      context.read<SignupCubit>().onAppLifecycleStateChanged(state);
    }
    'app State ==$appState'.logD;
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    context.read<SignupCubit>().startTimer(context);
    context.read<SignupCubit>().startGetUserTimer(context);

    // Initialize deep link service after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      'Initializing deep link service...'.logD;
      await DeepLinkService().initialize();

      // // Handle any pending deep links
      // await DeepLinkService().handlePendingResetToken();
    });
    homeWidget = getView();

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final brightness = Theme.of(context).brightness;

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        statusBarBrightness: brightness,
      ),
    );

    'Injector.instance<AppDB>().cookie ${Injector.instance<AppDB>().cookie}'.logV;

    return EasyLocalization(
      supportedLocales: const [
        Locale('en', 'UK'),
        Locale('en', 'CA'),
        Locale('en', 'US'),
        Locale('es', 'US'),
        Locale('en', 'AU'),
        Locale('fr', 'CA'),
        Locale('en', 'GB'),
      ],
      path: 'assets/translations',
      //  fallbackLocale: context.locale,
      //  fallbackLocale: context.locale,
      assetLoader: DynamicAssetLoader(
        // countryDomain: 'ca',
        // languageCode: 'en',
        assetUrlNameList: [
          RemoteNamespace.accessibilityStatement,
          RemoteNamespace.app,
          RemoteNamespace.as,
          RemoteNamespace.assessment,
          RemoteNamespace.checkin,
          RemoteNamespace.clock,
          RemoteNamespace.dashboard,
          RemoteNamespace.data,
          RemoteNamespace.diagram,
          RemoteNamespace.drugs,
          RemoteNamespace.entry,
          RemoteNamespace.isIs,
          RemoteNamespace.meetings,
          RemoteNamespace.policyUpdates,
          RemoteNamespace.settings,
          RemoteNamespace.toolkit,
          RemoteNamespace.tutorial,
          RemoteNamespace.unsubscriptions,
          RemoteNamespace.verification,
          RemoteNamespace.core,
        ],
      ),
      child: ScreenUtilInit(
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return BlocBuilder<AccessbilityCubit, AccessbilityState>(
            builder: (context, state1) {
              return MaterialApp(
                title: 'Breaking Free',
                debugShowCheckedModeBanner: false,
                navigatorKey: navigatorKey,
                theme: lightTheme,
                // home: const AssessmentMainPage(),
                // home: const PolicyUpdatePage(),
                //home: const AssessmentMainPage(),
                home: homeWidget,
                localizationsDelegates: context.localizationDelegates,
                supportedLocales: context.supportedLocales,
                locale: context.locale,
                builder: (context, widget) {
                  return MediaQuery(
                    data: MediaQuery.of(context).copyWith(
                      boldText: state1.accessibilityIsBoldText,
                      textScaler: TextScaler.linear(state1.accessibilityFontSize),
                    ),
                    child: ResponsiveBreakpoints.builder(
                      breakpoints: [
                        const Breakpoint(start: 0, end: 450, name: MOBILE),
                        const Breakpoint(start: 451, end: 800, name: TABLET),
                      ],
                      child: widget!, 
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

bool isUserModelEmpty(UserModel? userModel) {
  if (userModel == null) {
    'UserModel is null → empty'.logV;
    return true;
  }

  final userMapRaw = userModel.toJson();
  final userMap = userMapRaw['user'] as Map<dynamic, dynamic>?;

  if (userMap == null) {
    'User map inside UserModel is null → empty'.logV;
    return true;
  }

  bool isEmptyValue(dynamic value) {
    if (value == null) return true;
    if (value is String && value.trim().isEmpty) return true;
    if (value is List && value.isEmpty) return true;
    if (value is Map && value.isEmpty) return true;
    if (value is num && value == 0) return true;
    if (value is bool && value == false) return true;
    return false;
  }

  Map<String, dynamic> flattenMap(Map<dynamic, dynamic> map) {
    final flattened = <String, dynamic>{};
    map.forEach((key, value) {
      if (value is Map) {
        flattenMap(value).forEach((nestedKey, nestedValue) {
          flattened['$key.$nestedKey'] = nestedValue;
        });
      } else {
        flattened[key.toString()] = value;
      }
    });
    return flattened;
  }

  final flattenedMap = flattenMap(userMap);

  for (final entry in flattenedMap.entries) {
    if (!isEmptyValue(entry.value)) {
      'Non-empty field found: ${entry.key} = ${entry.value}'.logV;
      return false; // Found a non-empty value
    } else {
      'Empty field: ${entry.key}'.logV;
    }
  }

  'All user fields are empty'.logV;
  return true; // All values empty
}

Widget getView() {
  'Injector.instance<AppDB>().userModel ${Injector.instance<AppDB>().selectedLangugae}'.logV;
  'Injector.instance<AppDB>().userModel ${Injector.instance<AppDB>().userModel?.toJson()}'.logV;
  'Injector.instance<AppDB>().userModel ${isUserModelEmpty(Injector.instance<AppDB>().userModel)}'.logV;

  final userModel = Injector.instance<AppDB>().userModel;
  print('DEBUG: userModel loaded from Hive: ' + (userModel?.toJson().toString() ?? 'null'));
  print('DEBUG: dataProcessing: ' + (userModel?.user.dataProcessing?.toString() ?? 'null'));
  print('DEBUG: dataSharing: ' + (userModel?.user.dataSharing?.toString() ?? 'null'));
  print('DEBUG: dataSharingRpiUnderstood: ' + (userModel?.user.dataSharingRpiUnderstood?.toString() ?? 'null'));
  final notAgreed = (Injector.instance<AppDB>().policyRequired ?? [])
      .where((policyName) {
        final latestVersion = Injector.instance<AppDB>().policyMap?[policyName]?['version'];
        final hasAccepted = Injector.instance<AppDB>().userModel?.user.policyVersionsEndorsed?.any(
                  (e) => (e.policyName) == policyName && e.version == latestVersion,
                ) ??
            false;
        return !hasAccepted;
      })
      .cast<String>()
      .toList();
  'Injector.instance<AppDB>().notAgreed $notAgreed'.logV;

  if (isUserModelEmpty(userModel) &&
      Injector.instance<AppDB>().selectedLangugae != null &&
      (Injector.instance<AppDB>().selectedLangugae?.isNotEmpty ?? true)) {
    return const LoginPage();
  }

  if (userModel == null) {
    return const LanguagePage();
  }
  if (userModel.user.email == null) {
    return const LanguagePage();
  }
  // Prevent access to MyDataPage if email is not verified
  if (userModel.user.email?.verified != true) {
    // Show the account verification page instead of MyDataPage
    return SignUpAccountVerificationPage(
      email: userModel.user.email?.address ?? '',
      isVerifiedEmail: false,
      isSignUp: false,
    );
  } 
  else if (notAgreed.isNotEmpty) {
    final unacceptedPolicyList = <PolicyData>[];

    for (final policyKey in notAgreed) {
      final policy = Injector.instance<AppDB>().policyMap?[policyKey];
      final title = policy?['title'] ?? policyKey;
      final version = policy?['version']?.toString() ?? '';

      final contentMap = policy?['content'] as Map<String, dynamic>? ?? {};
      final contentList = (contentMap['text'] as List?)?.whereType<String>().toList() ?? [];
      final contentTitle = contentMap['title'] ?? '';

      'content: $contentList'.logV;

      unacceptedPolicyList.add(
        PolicyData(
          key: policyKey,
          title: title as String,
          version: version,
          content: contentList,
          contentTitle: contentTitle as String,
        ),
      );
    }

    return PolicyUpdatePage(
      policies: unacceptedPolicyList,
    );
  } else if (
    userModel.user.email?.verified == true &&
    (
      userModel.user.dataProcessing == null || //userModel.user.dataProcessing == false ||
      userModel.user.dataSharing == null || //userModel.user.dataSharing == false ||
      userModel.user.dataSharingRpiUnderstood == null //|| userModel.user.dataSharingRpiUnderstood == false
    )
  ) {
    return MyDataPage();
  } else {
    if (userModel.user.assessment?.assessmentComplete ?? false == true) {
      final state = fetchProgressCheckData();
      if (state == ProgressCheckState.optional) {
        return const ProgressCheckWelcomeBackPage();
      } else if (state == ProgressCheckState.required) {
        return const ProgressCheckWelcomeBackPage(recoveryNotRequired: false);
      } else if (state == ProgressCheckState.notNeeded) {
        return const MyDiagramPage();
      } else {
        return const MyDiagramPage();
      }
    } else {
      return const AssessmentMainPage();
    }
  }
}
