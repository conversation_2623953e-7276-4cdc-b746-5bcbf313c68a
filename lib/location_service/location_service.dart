import 'dart:async';
import 'dart:io' as Io;

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/notification_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/repository/my_alert_repository.dart';
import 'package:breakingfree_v2/location_service/cubit/location_service_cubit.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/notification_service/notification_helper.dart';
import 'package:breakingfree_v2/res/app_constant.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geofence_service/geofence_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum LocationPermissionStatus { foreground, background, none }

class LocationService {
  static final locationService = BlocProvider.of<LocationServiceCubit>(navigatorKey.currentContext!);

  static Future<void> cancelNotification({required String notificationCategory}) async {
    await LocalNotificationHelper.notification.pendingNotificationRequests().then((value) {
      for (final element in value) {
        if (element.payload == notificationCategory) {
          LocalNotificationHelper.notification.cancel(element.id);
        }
      }
    });
  }

  static Future<bool> isPermissionPreviouslyDenied(String key) async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool(key) ?? false;
}

static Future<void> markPermissionDenied(String key) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool(key, true);
}


  static List<Datum> getTriggerDataList1() {
    final entries = <MapEntry<DateTime, List<Map<String, dynamic>>>>[];
    var mapDataList = <Datum>[];

    try {
      'mapDataList ${Injector.instance<AppDB>().userModel?.user.strategies?.dsAs}'.logD;
      Injector.instance<AppDB>().userModel?.user.strategies?.dsAs?.forEach(
        (element) {
          'data ${element.data}'.logD;
          'time ${element.time}'.logD;
          final time = element.time;
          final data = element.data;
          if (time != null && data != null) {
            final dateTime = DateTime.fromMillisecondsSinceEpoch(time);
            entries.add(MapEntry(dateTime, data.map((datum) => datum.toJson()).toList()));
          }
        },
      );
      entries.sort((a, b) => b.key.compareTo(a.key));
      final mostRecentEntry = entries.isNotEmpty ? entries.first : null;
      if (mostRecentEntry != null) {
        mapDataList = mostRecentEntry.value
            .map(Datum.fromJson) // Convert each map into a Datum object
            .toList();
      }
      'mapDataList +++ $mapDataList'.logD;
      // Return the list of Datum objects
      return mapDataList;
    } catch (e) {
      'Error: $e'.logE;
      return [];
    }
  }

  static Future<void> checkRiskyPlace({required BuildContext context, bool isFromInitial = false}) async {
    'Call Risky Place'.logD;
    final alertRepository = MyAlertRepository();

    final isEnable = Injector.instance<AppDB>().userModel?.user.app?.alerts?.situations ?? true;
    'isEnable$isEnable'.logD;
    if (!isEnable) {
      await locationService.enableBackgroundTracking();
      await locationService.positions?.cancel();
      return;
    }

    'mapDataList =='.logD;
    final response = await alertRepository.notification(
      context: navigatorKey.currentContext!,
    );
    final markerList = response?.riskyPlaces?.toList();
    'response?.riskyPlaces? ${response?.riskyPlaces?.toList()}'.logD;
    'markerList populated: ${markerList?.map((e) => e.toJson()).join(', ')}'.logD;

    if (markerList != null && markerList.isEmpty) {
      return;
    }

    // Location permission handling
    if (isFromInitial == true) {
      await checkLocationForPermission(context: context);
    }

    final locationWhileInUseStatus = await Permission.locationWhenInUse.status.isDenied;
    if (locationWhileInUseStatus) {
      return;
    }

    if (locationService.list.isNotEmpty) locationService.list.clear();
    final geofenceList = <Geofence>[];
    GeofenceService.instance.clearGeofenceList();
    GeofenceService.instance.setup(useActivityRecognition: false);
    final geofenceStreamController = StreamController<Geofence>();
    GeofenceService.instance.clearAllListeners();
    await GeofenceService.instance.stop();
    //setClearData();
    var i = 0;

    locationService.setRiskyPlacesWithDistanceList(markerList ?? []);

    await Future.forEach(markerList!, (RiskyPlace element) async {
      locationService.list.add(element.lng.toString() + element.lat.toString());
      locationService.list.add("${element.lat}${element.lng}out_time");
      locationService.list.add("${element.lat}${element.lng}${element.what}in_time");

      final geofence = Geofence(
        id: i.toString(),
        latitude: element.lat ?? 0.0,
        longitude: element.lng ?? 0.0,
        radius: [GeofenceRadius(id: 'radius_100m', length: 100)],
      );
      geofenceList.add(geofence);
      'AA_S -- ${geofence.latitude},${geofence.longitude} -- ${element.what} -- ${element.what}'.logD;

      i++;
    });

    // final geofence = Geofence(
    //   id: i.toString(),
    //   latitude: markerList.last.lat ?? 0.0,
    //   longitude: markerList.last.lng ?? 0.0,
    //   radius: [GeofenceRadius(id: 'radius_50m', length: 50 )],
    // );
    // geofenceList.add(geofence);
    //'AA_S -- ${geofence.latitude},${geofence.longitude} -- $geofence --'.logD;

    Future<void> onGeofenceStatusChanged(
      Geofence geofence,
      GeofenceRadius geofenceRadius,
      GeofenceStatus geofenceStatus,
      Location location,
    ) async {
      try {
        'Geofence status changed: Geofence ID: ${geofence.id}, Status: $geofenceStatus'.logD;

        'AA_S -- geofenceStatus: $geofenceStatus -- ${geofence.id}'.logD;
        if (geofenceStatus == GeofenceStatus.ENTER) {
          await sendNotification(context, geofence.id);
        }

        geofenceStreamController.sink.add(geofence);
      } catch (e) {
        'error Geofence status changed: Geofence ID: $e'.logD;
      }
    }

    void onError(error) {
      final errorCode = getErrorCodesFromError(error);
      if (errorCode == null) {
        'Undefined error: $error'.logD;
        return;
      }

      if (errorCode == ErrorCodes.ACTIVITY_RECOGNITION_PERMISSION_PERMANENTLY_DENIED) {
        'ErrorCode: $errorCode'.logD;
      }

      'ErrorCode: $errorCode'.logD;
    }

    void onLocationChanged(Location location) {
      'location change$location.'.logD;

      locationService.setCurrentLocation('${location.latitude} / ${location.longitude}');
    }

    'Starting geofence service with ${geofenceList.length} geofences.'.logD;

    GeofenceService.instance.addLocationChangeListener(onLocationChanged);

    GeofenceService.instance.addGeofenceStatusChangeListener(onGeofenceStatusChanged);

    await GeofenceService.instance.start(geofenceList).catchError(onError);
    GeofenceService.instance.addStreamErrorListener(onError);
  }

  static Future<void> sendNotification(
    BuildContext context,
    String pos,
  ) async {
    final finalPos = int.parse(pos);
    final places = locationService.state.riskyPlacesWithDistance[finalPos];
    'app State ==$appState'.logD;
    // if (appState == AppLifecycleState.resumed || appState == AppLifecycleState.inactive) {
    //   await reminderDialog(context: context, what: places.what.toString(), how: places.how ?? '');
    // } else {
    final subTitle = places.how ?? '';
    await showNotificationAlert(
      subTitle: '$subTitle NOW!',
      payLoad: AppConstants.riskyPlaceNotification,
      summaryText: subTitle,
      title: 'REMINDER from Breaking Free!',
      body: 'I am approaching ${places.what}',
      notificationID: finalPos,
    );
    //  }
  }

  static Future<void> showNotificationAlert({
    required String title,
    required String body,
    required int notificationID,
    required String payLoad,
    String? summaryText,
    String? subTitle,
  }) async {
    final notification = FlutterLocalNotificationsPlugin();
    final androidNotificationDetails = AndroidNotificationDetails(
      'ChannelID',
      'ChannelName',
      color: const Color(0xffCDFB7B),
      styleInformation: BigTextStyleInformation(
        '$body\n${subTitle!}',
        contentTitle: title,
      ),
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );
    const iosNotificationDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    final notificationDetails = NotificationDetails(android: androidNotificationDetails, iOS: iosNotificationDetails);
    await notification.show(
      notificationID,
      title,
      '$body\n$subTitle',
      notificationDetails,
      payload: payLoad,
    );
    'NotificationID:-$notificationID ====== Name:-$body'.logD;
  }

  static Future<void> checkLocationForPermission({required BuildContext context}) async {
  '>?>?>? checkLocationForPermission STARTED'.logD;

  const key = 'location_permission_denied';
  final wasPreviouslyDenied = await isPermissionPreviouslyDenied(key);

  try {
    final status = await Permission.locationWhenInUse.status;
    '>?>?>? LocationWhenInUse Permission status: $status'.logD;

    if (status.isGranted) {
      '>?>?>? locationWhenInUse permission ALREADY GRANTED'.logD;

      final statusAlways = await Permission.locationAlways.status;
      '>?>?>? LocationAlways Permission status: $statusAlways'.logD;

      if (!statusAlways.isGranted) {
        final requestAlways = await Permission.locationAlways.request();
        '>?>?>? LocationAlways permission request result: $requestAlways'.logD;

        if (requestAlways.isGranted) {
          final backgroundEnabled = await locationService.location.isBackgroundModeEnabled();
          if (!backgroundEnabled) {
            await locationService.enableBackgroundTracking(granted: true);
          }
        }
      } else {
        final backgroundEnabled = await locationService.location.isBackgroundModeEnabled();
        if (!backgroundEnabled) {
          await locationService.enableBackgroundTracking(granted: true);
        }
      }

      return;
    }

    if (!wasPreviouslyDenied) {
      // First denial attempt
      final requestStatus = await Permission.locationWhenInUse.request();
      '>?>?>? LocationWhenInUse permission request result: $requestStatus'.logD;

      if (requestStatus.isGranted) {
        final requestAlways = await Permission.locationAlways.request();
        '>?>?>? LocationAlways permission request result: $requestAlways'.logD;

        if (requestAlways.isGranted) {
          final backgroundEnabled = await locationService.location.isBackgroundModeEnabled();
          if (!backgroundEnabled) {
            await locationService.enableBackgroundTracking(granted: true);
          }
        }
      } else {
        // First time denied → mark it so we can show dialog next time
        await markPermissionDenied(key);
      }

      return;
    }

    // Already denied before — show dialog to guide to settings
    await openDialog(
      context: context,
      title: AppLocaleKey.appEnableLocationTitle.tr(),//'Enable Location Access',
      subTitle: AppLocaleKey.appEnableLocationText.tr(),//'Please enable location manually from app settings.',
      onTap: () async {
        await openAppSettings();
        Navigator.pop(context);
      },
    );
  } catch (e) {
    '>?>?>? Exception in checkLocationForPermission: $e'.logD;
  }

  '>?>?>? checkLocationForPermission FINISHED'.logD;
}






  static Future<bool> checkLocationForPermissionWithoutPopUp(
    BuildContext context, {
    required bool riskyPlacesEnabled,
    required bool isButtonClick,
  }) async {
    'checkLocationForPermissionWithoutPopUp'.logD;

    if (!riskyPlacesEnabled) return false;

    try {
      final locationWhenInUse = await Permission.locationWhenInUse.status;
      final backgroundTrack = await locationService.location.isBackgroundModeEnabled();

      if (!locationWhenInUse.isGranted) {
        if (isButtonClick) {
          final requestWhenInUse = await Permission.locationWhenInUse.request();

          if (!requestWhenInUse.isGranted) {
            'Permission.locationWhenInUse denied'.logD;
            return false;
          }
        } else {
          return false;
        }
      }

      final locationAlways = await Permission.locationAlways.status;

      if (!locationAlways.isGranted) {
        final requestAlways = await Permission.locationAlways.request();

        if (!requestAlways.isGranted) {
          'Permission.locationAlways denied'.logD;
          return false;
        }
      }

      // At this point, both permissions are granted
      if (!backgroundTrack) {
        await locationService.enableBackgroundTracking(granted: true);
        'Background tracking enabled'.logD;
      }

      return true;
    } catch (e) {
      '_locationPermissionInIOS Silent Check Error: $e'.logD;
      return false;
    }
  }

  static Future<bool> checkNotificationForPermissionWithoutPopUp(
  BuildContext context, {
  required bool isButtonClick,
}) async {
  'checkNotificationForPermissionWithoutPopUp'.logD;

  try {
    final notificationStatus = await Permission.notification.status;

    if (!notificationStatus.isGranted) {
      if (isButtonClick) {
        final requestNotification = await Permission.notification.request();

        if (!requestNotification.isGranted) {
          'Permission.notification denied'.logD;
          return false;
        }
      } else {
        return false;
      }
    }
    
    // Notification permission granted
    'Notification permission granted'.logD;
    return true;
  } catch (e) {
    '_notificationPermissionSilentCheck Error: $e'.logD;
    return false;
  }
}


 static Future<void> openDialog({
  required BuildContext context,
  required VoidCallback onTap,
  String? title,
  String? subTitle,
}) async {
  await showCupertinoModalPopup<void>(
    context: context,
    builder: (BuildContext context) => CupertinoAlertDialog(
      title: Text(title ?? 'Location service are not enabled'),
      content: (subTitle != null && subTitle.isNotEmpty)
          ? Text(subTitle)
          : const SizedBox.shrink(),
      actions: <CupertinoDialogAction>[
        CupertinoDialogAction(
          isDestructiveAction: true,
          onPressed: () {
            // Dismiss dialog or go back
            AppNavigation.previousScreen(context);
          },
          child: Text(
            AppLocaleKey.appCancelButton.tr(),//'Cancel',
            style: TextStyle(fontFamily: 'Poppins', color: Colors.blue),
          ),
        ),
        CupertinoDialogAction(
          isDestructiveAction: true,
          onPressed: onTap,
          child: Text(
            AppLocaleKey.appSettingButton.tr(),//'Settings',
            style: TextStyle(fontFamily: 'Poppins', color: Colors.blue),
          ),
        ),
      ],
    ),
  );
}



  static Future<void> reminderDialog({
    required BuildContext context,
    required String what,
    required String how,
  }) async {
    if (Io.Platform.isIOS) {
      await showCupertinoModalPopup<void>(
        context: context,
        builder: (BuildContext context) => CupertinoAlertDialog(
          title: Text('REMINDER from Breaking Free!', style: context.textTheme.titleLarge),
          content: Text(
            'I am approaching $what \n$how',
          ),
          actions: <CupertinoDialogAction>[
            CupertinoDialogAction(
              isDestructiveAction: true,
              onPressed: () {
                // Navigator.pop(context);
                AppNavigation.previousScreen(context);
              },
              child: const Text(
                'OK',
                style: TextStyle(fontFamily: 'Poppins', color: Colors.blue),
              ),
            ),
          ],
        ),
      );
    }
    if (Io.Platform.isAndroid) {
      await showDialog<void>(
        context: navigatorKey.currentContext!,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(AppSize.sp16))),
            contentPadding: EdgeInsets.only(top: AppSize.sp12, bottom: AppSize.sp10),
            title: Text(
              'REMINDER from Breaking Free!',
              style: context.textTheme.titleLarge,
            ),
            content: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'I am approaching $what \n$how',
                    style: context.textTheme.titleSmall?.copyWith(color: context.themeColors.greyColor),
                  ),
                ],
              ),
            ),
            actions: [
              const Divider(
                color: Colors.black,
                height: 1,
              ),
              TextButton(
                child: Center(
                  child: Text(
                    'OK',
                    style: context.textTheme.titleSmall
                        ?.copyWith(fontSize: AppSize.sp16, color: context.themeColors.greenColor),
                  ),
                ),
                onPressed: () {
                  // Navigator.of(context).pop();
                  AppNavigation.previousScreen(context);
                },
              ),
            ],
          );
        },
      );
    }
  }

  static Future<void> checkNotificationPermission({required BuildContext context}) async {
  '>?>?>? checkNotificationPermission STARTED'.logD;

  const key = 'notification_permission_denied';
  final wasPreviouslyDenied = await isPermissionPreviouslyDenied(key);

  try {
    final status = await Permission.notification.status;
    '>?>?>? Notification Permission status: $status'.logD;

    if (status.isGranted) {
      '>?>?>? Notification permission ALREADY GRANTED'.logD;
    } else if (status.isDenied && !wasPreviouslyDenied) {
      // First time denial: request permission
      final requestStatus = await Permission.notification.request();
      '>?>?>? Notification permission request result: $requestStatus'.logD;

      if (!requestStatus.isGranted) {
        // Mark as denied so next time we show custom dialog
        await markPermissionDenied(key);
      }
    } else {
      // Already denied before — show custom dialog
      await openDialog(
        context: context,
        title: AppLocaleKey.appEnableNotificationTitle.tr(), //'Enable Notifications',
        subTitle: AppLocaleKey.appEnableNotificationText.tr(),//'Please enable notifications manually from app settings.',
        onTap: () async {
          await openAppSettings();
          Navigator.pop(context);
        },
      );
    }
  } catch (e) {
    '>?>?>? _notificationPermissionError: $e'.logD;
  }

  '>?>?>? checkNotificationPermission FINISHED'.logD;
}


}
