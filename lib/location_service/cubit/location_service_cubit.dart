import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/notification_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:location/location.dart' as loc;

part 'location_service_cubit.freezed.dart';
part 'location_service_state.dart';

class LocationServiceCubit extends Cubit<LocationServiceState> {
  LocationServiceCubit() : super(LocationServiceState());

  final loc.Location location = loc.Location();
  StreamSubscription<loc.LocationData>? positions;
  final List<String> list = [];

  Future<void> enableBackgroundTracking({bool granted = false}) async {
    try {
      await location.enableBackgroundMode(enable: granted);
    } catch (e) {
      '_enableBackgroundTracking$e'.logD;
    }
  }

  void setRiskyPlacesWithDistanceList(List<RiskyPlace> list) {
    emit(state.copyWith(riskyPlacesWithDistance: list));
  }

  void setCurrentLocation(String location) {
    emit(state.copyWith(location: location));
  }

  @override
  Future<void> close() {
    positions?.cancel();
    list.clear();

    return super.close();
  }
}
