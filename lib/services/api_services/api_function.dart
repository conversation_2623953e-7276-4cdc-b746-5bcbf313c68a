import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_client.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class APIFunction {
  static Future<dynamic> postAPICall(
    dynamic data, {
    required BuildContext? context,
    @required String? apiName,
    Map<String, dynamic>? header,
  }) async {
    '????? postApiData $data'.logD;
    '????? apiName $apiName'.logD;
    '????? other cookie = ${Injector.instance<AppDB>().cookie}'.logD;
    '????? other baseURL = ${EndPoints.baseUrl}'.logD;
    '????? other apiName = ${apiName}'.logD;
    try {
      final response = await ApiClient().apiClientInstance().post<Map<String, dynamic>>(
            EndPoints.baseUrl + apiName!,
            data: data,
            options: Options(
              headers: {
                'Cookie': Injector.instance<AppDB>().cookie,
                'Is-App': true,
                // ...?header,
              },
            ),
          );
          
          '????? other response of .post api = ${response.statusCode}'.logD;
          '????? other response of .post api = ${response.statusMessage}'.logD;
      return response;
    } catch (e) {
      '????? other catch in post = ${e}'.logD;
      '$e'.logD;
      log('postAPICall =====>$e');

      return e;
    }
  }

  static Future<dynamic> getAPICall({
    required String? apiName,
    required BuildContext? context,
    Map<String, dynamic>? data,
    Map<String, dynamic>? header,
  }) async {
    'apiName $apiName'.logD;

    try {
      final response = await ApiClient().apiClientInstance().get<Map<String, dynamic>>(
            EndPoints.baseUrl + apiName!,
            queryParameters: data,
            options: Options(
              headers: {
                'Cookie': Injector.instance<AppDB>().cookie,
                'Is-App': true,
                // ...?header,
              },
            ),
          );
      return response;
    } catch (e) {
      log('=======> $e');
      return e;
    }
  }

  static Future<dynamic> deleteAPICall({
    required String? apiName,
    required BuildContext? context,
    Map<String, dynamic>? data,
    Map<String, dynamic>? header,
  }) async {
    try {
      final response = await ApiClient().apiClientInstance().delete<Map<String, dynamic>>(
            EndPoints.baseUrl + apiName!,
            queryParameters: data,
            options: Options(
              headers: {
                'Cookie': Injector.instance<AppDB>().cookie,
                'Is-App': true,
                //  ...?header,
              },
            ),
          );
      return response;
    } catch (e) {
      return null;
    }
  }

  static Future<dynamic> patchAPICall(
    dynamic data, {
    required BuildContext? context,
    @required String? apiName,
    Map<String, dynamic>? header,
  }) async {
    try {
      final response = await ApiClient().apiClientInstance().patch<Map<String, dynamic>>(
            EndPoints.baseUrl + apiName!,
            data: data,
            options: Options(
              headers: {
                'Cookie': Injector.instance<AppDB>().cookie,
                'Is-App': true,
                // ...?header,
              },
            ),
          );
      return response;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return null;
    }
  }
}
