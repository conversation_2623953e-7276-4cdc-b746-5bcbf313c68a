class EndPoints {
  /// Base endpoint
  static String baseUrl = 'https://breakingfreeonline.com/api/';
  // static String baseUrl = '';

  ///header value
  static const headerContentKey = 'Content-Type';
  static const headerContentValue = 'application/json';
  static const headerContentValueFormData = 'application/form-data';
  static const headerContentValueUnlecoded =
      'application/x-www-form-urlencoded';
  static const headerAuthorizationKey = 'Authorization';
  static const headerAcceptKey = 'Accept';
  static const bearerToken = 'Bearer ';

  /// Endpoint for authentication
  static const login = 'auth/login';
  static const logout = 'auth/logout';
  static const register = 'auth/register';
  static const policies = 'policies';
  static const resendVerificationEmail = 'auth/resend-email-verification-token';
  static const verifyEmail = 'auth/verify-email';
  static const forgotPassword = 'auth/forgot-password';
  static const resetPassword = 'auth/reset-password';
  static const validateResetPasswordToken = 'auth/validate-reset-password-token';
  static const getUserData = 'auth';
  static const userStats = 'user/stats';
  static const userTimeSpent = 'user/time-spent';

  //for language
  static const language = 'app/languages';
  static const userLanguage = '/user/language';

  /// Endpoint for user related apis
  static const dataProcessing = 'user/data-processing';
  static const purgeUser = 'user/purge';
  static const dataSharing = 'user/data-sharing';
  static const dataSharingRpiUnderstood = 'user/data-sharing-rpi-understood';
  static const downloadUserData = 'user/data';

  /// Endpoint for user detail change apis
  static const changeUserName = 'user/name';
  static const changeUserEmail = 'user/email';
  static const changeUserPassword = 'user/password';
  static const changeUserSupporter = 'user/supporters';
  static const strategy = 'user/strategy';
  static const alerts = 'app/alerts';
  static const notificationData = 'app/notification-data';

  /// Assessment related apis
  static const recoveryProgram = 'user/recovery-program';
  static const userLife = 'user/life';
  static const difficultSitutation = 'user/difficult-situations';
  static const negativeThought = 'user/negative-thoughts';
  static const physicalSenstation = 'user/physical-sensations';
  static const unhelpfulBehaviour = 'user/unhelpful-behaviours';
  static const lifeStyle = 'user/lifestyle';
  static const emotionalImpact = 'user/emotional-impact';
  static const drinking = 'user/drinking';
  static const drinkingFeeling = 'user/drinking-feeling';
  static const drinkingGoal = 'user/drinking-goal';
  static const drug = 'user/drugs';
  static const drugFeeling = 'user/drugs-feeling';
  static const drugGoal = 'user/drugs-goal';
  static const thankYou = 'user/thank-you';
  static const assessmentVideo = 'user/assessment-video';
  static const bridgingVideo = 'user/bridging-video';

  static const accessibility = 'app/accessibility';
  static const checkIn = 'user/checkin';

  static const psActionStrategy = 'pdf/action-strategy-ps';
  static const eiActionStrategy = 'pdf/action-strategy-ei';
  static const ubActionStrategy = 'pdf/action-strategy-ub';
  static const ntActionStrategy = 'pdf/action-strategy-nt';
  static const lsActionStrategy = 'pdf/action-strategy-ls';
  static const dsActionStrategy = 'pdf/action-strategy-ds';
  static const progressReportActionStrategy = 'pdf/progress-report';
  static const pdfDiagram = 'pdf/diagram';
  static const informationActionStrategy = 'pdf/information-strategy';
  static const countries = 'app/countries';
  static const userPolicy = 'user/policy';

  static const userAudioMuted = '/user/audio';
}
