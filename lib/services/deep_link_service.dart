import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/pages/reset_password_page.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';

class DeepLinkService {
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();
  static final DeepLinkService _instance = DeepLinkService._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;

  // // Pending deep link data for when context is not available
  // String? _pendingResetToken;
  // String? _pendingDomain;

  /// Initialize deep link handling
  Future<void> initialize() async {
    try {
      'Initializing deep links'.logD;
      // Handle app launch from deep link (when app is closed)
      final initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        'Initial deep link: $initialUri'.logD;
        await _handleDeepLink(initialUri);
      }

      // Handle deep links when app is already running
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri uri) {
          'Deep link received: $uri'.logD;
          _handleDeepLink(uri);
        },
        onError: (Object error) {
          'Deep link error: $error'.logE;
        },
      );
    } catch (e) {
      'Error initializing deep links: $e'.logE;
    }
  }

  /// Handle incoming deep links
  Future<void> _handleDeepLink(Uri uri) async {
    try {
      'Processing deep link: $uri'.logD;
      'Host: ${uri.host}'.logD;
      'Path: ${uri.path}'.logD;
      'Path segments: ${uri.pathSegments}'.logD;

      // Check if this is a password reset link
      // Expected format: https://www.breakingfreeonline.com.au/accounts/reset/{token}
      if (_isPasswordResetLink(uri)) {
        await _handlePasswordResetLink(uri);
      } else if (_isVerifyEmailLink(uri)) {
        await _handleVerifyEmailLink(uri);
      } else {
        'Unhandled deep link: $uri'.logW;
      }
    } catch (e) {
      'Error handling deep link: $e'.logE;
    }
  }

  /// Check if the URI is a password reset link
  bool _isPasswordResetLink(Uri uri) {
    // Check for custom scheme first (for testing)
    if (uri.scheme == 'breakingfree') {
      final pathSegments = uri.pathSegments;
      final isResetPath = pathSegments.length >= 3 && pathSegments[0] == 'accounts' && pathSegments[1] == 'reset';
      'Custom scheme check: true, Path check: $isResetPath'.logD;
      return isResetPath;
    }

    // Check for breakingfreeonline.com.au domain and /accounts/reset/ path
    final isCorrectDomain = uri.host.contains('breakingfreeonline.com.au') ||
        uri.host.contains('breakingfreeonline.com') ||
        uri.host.contains('breakingfreeonline.ca') ||
        uri.host.contains('breakingfreeonline.us');

    final pathSegments = uri.pathSegments;
    final isResetPath = pathSegments.length >= 3 && pathSegments[0] == 'accounts' && pathSegments[1] == 'reset';

    'Domain check: $isCorrectDomain, Path check: $isResetPath'.logD;
    return isCorrectDomain && isResetPath;
  }

  /// Check if the URI is a verify email link
  bool _isVerifyEmailLink(Uri uri) {
    // Check for custom scheme first (for testing)
    if (uri.scheme == 'breakingfree') {
      final pathSegments = uri.pathSegments;
      final isResetPath = pathSegments.length >= 3 && pathSegments[0] == 'accounts' && pathSegments[1] == 'reset';
      'Custom scheme check: true, Path check: $isResetPath'.logD;
      return isResetPath;
    }

    // Check for breakingfreeonline.com domain and /verify path
    final isCorrectDomain = uri.host.contains('breakingfreeonline.com.au') ||
        uri.host.contains('breakingfreeonline.com') ||
        uri.host.contains('breakingfreeonline.ca') ||
        uri.host.contains('breakingfreeonline.us');

    final pathSegments = uri.pathSegments;
    final isResetPath = pathSegments.length >= 2 && pathSegments[0] == 'verify';

    'Domain check: $isCorrectDomain, Path check: $isResetPath'.logD;
    return isCorrectDomain && isResetPath;
  }

  /// Handle verify email link
  Future<void> _handleVerifyEmailLink(Uri uri) async {
    try {
      final pathSegments = uri.pathSegments;

      // Extract token from URL
      // Expected format: /verify/{token}
      if (pathSegments.length >= 2) {
        final token = pathSegments[1];

        if (token.isNotEmpty) {
          'Password reset token extracted: $token'.logD;
          await _navigateToVerifyEmail(token: token, domain: uri.toString());
        } else {
          'Empty token in verify link'.logW;
        }
      } else {
        'Invalid verify link format: ${uri.path}'.logW;
      }
    } catch (e) {
      'Error handling verify email link: $e'.logE;
    }
  }

  /// Navigate to reset password page
  Future<void> _navigateToVerifyEmail({
    required String token,
    required String domain,
  }) async {
    try {
      'Navigating to reset password page with token: $token'.logD;

      // Wait for context to be available
      var attempts = 0;
      const maxAttempts = 10;

      while (navigatorKey.currentContext == null && attempts < maxAttempts) {
        'Context not available, waiting... (attempt ${attempts + 1}/$maxAttempts)'.logD;
        await Future<void>.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      final context = navigatorKey.currentContext;
      if (context != null) {
        'Context available, navigating to reset password page'.logD;

        // Use pushAndRemoveAllScreen to ensure clean navigation
        await AppNavigation.pushAndRemoveAllScreen(
          context,
          LoginPage(token: token, domain: domain),
        );

        'Successfully navigated to reset password page'.logD;
      } else {
        'Context still not available after $maxAttempts attempts'.logE;
        // // Store for later processing
        // _pendingResetToken = token;
        // _pendingDomain = domain;
      }
    } catch (e) {
      'Error navigating to reset password: $e'.logE;
    }
  }

  /// Handle password reset deep link
  Future<void> _handlePasswordResetLink(Uri uri) async {
    try {
      final pathSegments = uri.pathSegments;

      // Extract token from URL
      // Expected format: /accounts/reset/{token}
      if (pathSegments.length >= 3) {
        final token = pathSegments[2];

        if (token.isNotEmpty) {
          'Password reset token extracted: $token'.logD;
          await _navigateToResetPassword(token: token, domain: uri.toString());
        } else {
          'Empty token in reset link'.logW;
        }
      } else {
        'Invalid reset link format: ${uri.path}'.logW;
      }
    } catch (e) {
      'Error handling password reset link: $e'.logE;
    }
  }

  /// Navigate to reset password page
  Future<void> _navigateToResetPassword({
    required String token,
    required String domain,
  }) async {
    try {
      'Navigating to reset password page with token: $token'.logD;

      // Wait for context to be available
      var attempts = 0;
      const maxAttempts = 10;

      while (navigatorKey.currentContext == null && attempts < maxAttempts) {
        'Context not available, waiting... (attempt ${attempts + 1}/$maxAttempts)'.logD;
        await Future<void>.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      final context = navigatorKey.currentContext;
      if (context != null) {
        'Context available, navigating to reset password page'.logD;

        // Use pushAndRemoveAllScreen to ensure clean navigation
        await AppNavigation.pushAndRemoveAllScreen(
          context,
          ResetPasswordPage(resetToken: token, domain: domain),
        );

        'Successfully navigated to reset password page'.logD;
      } else {
        'Context still not available after $maxAttempts attempts'.logE;
        // // Store for later processing
        // _pendingResetToken = token;
        // _pendingDomain = domain;
      }
    } catch (e) {
      'Error navigating to reset password: $e'.logE;
    }
  }

  // /// Check and handle any pending reset token
  // Future<void> handlePendingResetToken() async {
  //   if (_pendingResetToken != null && _pendingDomain != null) {
  //     final context = navigatorKey.currentContext;
  //     if (context != null) {
  //       'Handling pending reset token: $_pendingResetToken'.logD;
  //       await _navigateToResetPassword(
  //         token: _pendingResetToken!,
  //         domain: _pendingDomain!,
  //       );
  //       _pendingResetToken = null;
  //       _pendingDomain = null;
  //     }
  //   }
  // }

  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
  }

  /// Test method to simulate deep link (for development/testing)
  Future<void> testDeepLink(String url) async {
    try {
      final uri = Uri.parse(url);
      await _handleDeepLink(uri);
    } catch (e) {
      'Error testing deep link: $e'.logE;
    }
  }
}
