import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// App Dependencies Injection using GetIt
@immutable
class Injector {
  const Injector._();

  static final _injector = GetIt.instance;

  /// GetIt Instance
  static GetIt get instance => _injector;

  /// init all dependencies module wise
  static Future<void> initModules() async {
    await Hive.initFlutter();
    instance.registerSingletonAsync(AppDB.getInstance);
    await instance.isReady<AppDB>();
    instance.registerSingleton(_apiClientInstance());
  }

  static Future<void> initMockModules(Dio dio) async {
    instance
      ..registerSingletonAsync(AppDB.getInstance)
      ..registerSingleton(dio);
  }

  static Dio _apiClientInstance() {
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
       // 'Is-App': true,
      },
    );
    final dio = Dio(options);

    dio.interceptors.add(LogInterceptor(responseBody: true));
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (RequestOptions option, RequestInterceptorHandler handler) async {
          return handler.next(option);
        },
        onResponse: (Response<dynamic> response, ResponseInterceptorHandler handler) {
          if (response.statusCode == 401) {
            AppNavigation.pushAndRemoveAllScreen(navigatorKey.currentContext!,  LoginPage());
          }
          return handler.next(response);
        },
        onError: (DioException e, ErrorInterceptorHandler handler) {
          log('Dio DEFAULT Error Message :---------------> ${e.message}');
          if (e.type == DioExceptionType.unknown) {
            log('<<<<<<<-------------DEFAULT Error---------->>>>>>');
          } else if (e.type == DioExceptionType.connectionTimeout) {
            log('<<<<<<<-------------CONNECT_TIMEOUT---------->>>>>>');
          } else if (e.type == DioExceptionType.receiveTimeout) {
            log('<<<<<<<-------------RECEIVE_TIMEOUT---------->>>>>>');
          }
          if (e.response != null && e.response!.statusCode! == 404) {
            log(e.response!.statusCode!.toString());
          }
          if (e.response != null && e.response!.statusCode! == 422) {
            // showSnackbar(AppLocalizations.of(context)!.invalidMno, context);
          }
          if ((e.response != null && e.response!.statusCode! == 401) || e.response?.data == 'Unauthorized') {
            AppNavigation.pushAndRemoveAllScreen(navigatorKey.currentContext!,  LoginPage());

            // showSnackbar(AppLocalizations.of(context)!.invalidMno, context);
            log('<<<<<<<-------------401---------->>>>>>');
            log(e.response!.statusCode!.toString());
          }
          if (e.response!.statusCode! == 500) {
            // showSnackbar(AppLocalizations.of(context)!.servererror, context);
            // ScaffoldMessenger.of(navigatorKey.currentContext!)
            //     .showSnackBar(const SnackBar(content: Text('Something went wrong, please try after some time')));
          } else {
            log('else error ==> ${e.message}');
          }
          return handler.next(e);
        },
      ),
    );
    return dio;
  }
}
