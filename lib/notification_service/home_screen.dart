// import 'package:breakingfree_v2/main.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:timezone/timezone.dart' as tz;

// class HomeScreen extends StatefulWidget {
//   const HomeScreen({super.key});

//   @override
//   State<HomeScreen> createState() => _HomeScreenState();
// }

// class _HomeScreenState extends State<HomeScreen> {
//   Future<void> _zonedScheduleNotification() async {
//     try {
//       await flutterLocalNotificationsPlugin.zonedSchedule(
//         0,
//         'scheduled title',
//         'scheduled body',
//         tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
//         const NotificationDetails(
//           android: AndroidNotificationDetails(
//             'id',
//             'channel',
//             channelDescription: 'your channel description',
//           ),
//         ),
//         androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
//         uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
//       );
//     } catch (e) {
//       print('error $e');
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('data'),
//       ),
//       body: Column(
//         children: [
//           ElevatedButton(
//             child: const Text('Schedule notification to appear in 5 seconds '
//                 'based on local time zone'),
//             onPressed: () async {
//               await _zonedScheduleNotification();
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
