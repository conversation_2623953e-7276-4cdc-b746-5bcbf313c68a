import 'dart:developer';

import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class LocalNotificationHelper {
  LocalNotificationHelper._();
  static final localNotificationHelper = LocalNotificationHelper._();
  static FlutterLocalNotificationsPlugin notification = FlutterLocalNotificationsPlugin();

  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  static const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'default_channel',
    'Default Channel',
    importance: Importance.high,
  );

  static const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  static const DarwinInitializationSettings initializationSettingsDarwin = DarwinInitializationSettings(
    requestAlertPermission: false,
    requestBadgePermission: false,
    requestSoundPermission: false,
  );

  static AndroidNotificationDetails androidNotificationDetails = AndroidNotificationDetails(
    channel.id,
    channel.name,
    visibility: NotificationVisibility.public,
    importance: Importance.high,
    enableLights: true,
  );

  static DarwinNotificationDetails darwinNotificationDetails = const DarwinNotificationDetails(presentSound: true);

  static Future<void> initialize() async {
    tz.initializeTimeZones();

    final timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
    // await flutterLocalNotificationsPlugin
    //     .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
    //     ?.requestNotificationsPermission();

    // await flutterLocalNotificationsPlugin
    //     .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
    //     ?.requestPermissions(
    //       alert: true,
    //       badge: true,
    //       sound: true,
    //     );

    await flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsDarwin,
      ),
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
      onDidReceiveBackgroundNotificationResponse: onDidReceiveBackgroundNotificationResponse,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<void> zonedScheduleNotification() async {
    'sasaaassa'.logD;
    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      'scheduled title',
      'scheduled body',
      tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'id',
          'channel',
          channelDescription: 'your channel description',
        ),
      ),
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  Future<void> showNotification() async {
    await flutterLocalNotificationsPlugin.show(
      1,
      'remoteMessage.notification?.title',
      'remoteMessage.notification?.body',
      NotificationDetails(
        android: androidNotificationDetails,
        iOS: darwinNotificationDetails,
      ),
    );
  }

  static Future<void> onDidReceiveNotificationResponse(NotificationResponse payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    log('notification payload: ${payload.payload}');
  }

  static Future<void> onDidReceiveBackgroundNotificationResponse(NotificationResponse payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    log('BG notification detected');
    log('notification payload: ${payload.payload}');
  }

  Future<void> onDidReceiveLocalNotification(int? id, String? title, String? body, String? payload) async {
    // display a dialog with the notification details, tap ok to go to another page
    await showDialog<void>(
      context: navigatorKey.currentContext!,
      builder: (BuildContext context) => CupertinoAlertDialog(
        title: const Text('title'),
        content: const Text('body'),
        actions: [
          CupertinoDialogAction(
            isDefaultAction: true,
            child: const Text('OK'),
            onPressed: () async {
              Navigator.pop(navigatorKey.currentContext!);
            },
          ),
        ],
      ),
    );
  }

  Future<void> sendScheduleNotification({
    required String date,
    required int id,
    required String body,
    required String payload,
    String title = '',
    bool isDaily = false,
  }) async {
    await flutterLocalNotificationsPlugin.cancel(id);
    'notification body :- $body $id $date $payload'.logE;
    'notification body :- ${DateTimeComponents.time}'.logE;
    'notification body :- ${DateTimeComponents.dateAndTime}'.logE;
    'qq body :- ${tz.TZDateTime.from(
      DateTime.parse(date), // Parsed date from the input
      tz.local,
    )}'
        .logE;
    'createdAt is null, cannot schedule notification $date'.logE;
    'qq body :- ${tz.local}'.logE;
    await flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.parse(tz.local, date),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'default_channel_2',
          'Default Channel 2',
          importance: Importance.max,
          // ongoing: true,
          // styleInformation: BigTextStyleInformation(''),
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      // androidAllowWhileIdle: true,
      payload: payload,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: isDaily ? DateTimeComponents.time : DateTimeComponents.dateAndTime,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }
}
