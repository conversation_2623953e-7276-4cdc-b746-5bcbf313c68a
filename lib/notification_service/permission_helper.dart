import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PermissionHelper {
  static const _notificationKey = 'notification_permission_denied';
  static const _locationKey = 'location_permission_denied';

  // ------------------- SharedPreferences Helpers -------------------
  static Future<bool> _wasPreviouslyDenied(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key) ?? false;
  }

  static Future<void> _markDenied(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, true);
  }

  // ------------------- Notification Permission -------------------
  static Future<void> checkNotificationPermission({
    required BuildContext context,
    required Future<void> Function() openAppSettingsCallback,
  }) async {
    final wasDenied = await _wasPreviouslyDenied(_notificationKey);
    final status = await Permission.notification.status;

    if (wasDenied || status.isPermanentlyDenied) {
      await LocationService.openDialog(
        context: context,
        title: 'Notification permission not enabled',
        subTitle: 'To receive important alerts, please enable notifications in app settings.',
        onTap: () async {
          await openAppSettingsCallback();
          Navigator.pop(context);
        },
      );
      return;
    }

    final requestStatus = await Permission.notification.request();

    if (requestStatus.isGranted) {
      // Permission granted
    } else {
      // First denial → mark it
      await _markDenied(_notificationKey);
    }
  }

  // ------------------- Location Permission -------------------
  static Future<void> checkLocationPermission({
    required BuildContext context,
    required Future<void> Function() openAppSettingsCallback,
    required Future<bool> Function() isBackgroundEnabled,
    required Future<void> Function() enableBackgroundTracking,
  }) async {
    final wasDenied = await _wasPreviouslyDenied(_locationKey);
    final status = await Permission.locationWhenInUse.status;

    if (wasDenied || status.isPermanentlyDenied) {
      await LocationService.openDialog(
        context: context,
        title: 'Location permission not enabled',
        subTitle: 'To track risky areas, enable location access in app settings.',
        onTap: () async {
          await openAppSettingsCallback();
          Navigator.pop(context);
        },
      );
      return;
    }

    final requestStatus = await Permission.locationWhenInUse.request();

    if (requestStatus.isGranted) {
      final alwaysStatus = await Permission.locationAlways.request();

      if (alwaysStatus.isGranted) {
        final backgroundEnabled = await isBackgroundEnabled();
        if (!backgroundEnabled) {
          await enableBackgroundTracking();
        }
      } else if (alwaysStatus.isPermanentlyDenied) {
        await _markDenied(_locationKey);
        await LocationService.openDialog(
          context: context,
          title: 'Location background permission not enabled',
          subTitle: 'Enable background location access from app settings.',
          onTap: () async {
            await openAppSettingsCallback();
            Navigator.pop(context);
          },
        );
      } else {
        await _markDenied(_locationKey);
      }
    } else {
      await _markDenied(_locationKey);
    }
  }
}
