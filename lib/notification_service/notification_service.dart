// import 'dart:developer';
// import 'dart:io';

// import 'package:breakingfree_v2/main.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:flutter_timezone/flutter_timezone.dart';
// import 'package:timezone/data/latest_all.dart' as tz;
// import 'package:timezone/timezone.dart' as tz;

// class NotificationServices {
//   static FlutterLocalNotificationsPlugin notification = FlutterLocalNotificationsPlugin();

//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
//   AndroidInitializationSettings initializationSettingsAndroid =
//       const AndroidInitializationSettings('@mipmap/ic_launcher');
//   DarwinInitializationSettings initializationSettingsDarwin = const DarwinInitializationSettings();

//   late final String timeZone;

//   Future<void> initialiseNotification() async {
//     //  await _isAndroidPermissionGranted();

//     await _configureLocalTimeZone();
//     await checkAndRequestPermissions();
//     final initializationSettings = InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: const DarwinInitializationSettings(),
//     );

//     await flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
//       onDidReceiveBackgroundNotificationResponse: onDidReceiveBackgroundNotificationResponse,
//     );
//   }

//   Future<void> _configureLocalTimeZone() async {
//     tz.initializeTimeZones();
//     if (Platform.isWindows) {
//       return;
//     }
//     final timeZoneName = await FlutterTimezone.getLocalTimezone();
//     tz.setLocalLocation(tz.getLocation(timeZoneName));
//   }

//   Future<void> checkAndRequestPermissions() async {
//     if (Platform.isAndroid) {
//       final granted = await flutterLocalNotificationsPlugin
//               .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
//               ?.areNotificationsEnabled() ??
//           false;

//       if (!granted) {
//         await flutterLocalNotificationsPlugin
//             .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
//             ?.requestNotificationsPermission();
//       }
//     } else if (Platform.isIOS || Platform.isMacOS) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
//           ?.requestPermissions(
//             alert: true,
//             badge: true,
//             sound: true,
//           );
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<MacOSFlutterLocalNotificationsPlugin>()
//           ?.requestPermissions(
//             alert: true,
//             badge: true,
//             sound: true,
//           );
//     }
//   }

//   Future<void> zonedScheduleNotification() async {
//     print('sasaaassa');
//     await flutterLocalNotificationsPlugin.zonedSchedule(
//       0,
//       'scheduled title',
//       'scheduled body',
//       tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
//       const NotificationDetails(
//         android: AndroidNotificationDetails(
//           'id',
//           'channel',
//           channelDescription: 'your channel description',
//         ),
//       ),
//       androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
//       uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
//     );
//   }

//   Future<void> showNotification({required int id, required String body, required String title}) async {
//     var payload = '';

//     body = "It's time to take my next step... $body";
//     payload = 'myLifestyle_$body';
//     await flutterLocalNotificationsPlugin.cancel(id);

//     await flutterLocalNotificationsPlugin.show(
//       id,
//       title,
//       body,
//       const NotificationDetails(
//         android: AndroidNotificationDetails(
//           'id',
//           'channel',
//           importance: Importance.min,
//           ongoing: true,
//           styleInformation: BigTextStyleInformation(''),
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: DarwinNotificationDetails(),
//       ),
//       payload: payload,
//     );
//   }

//   Future<void> sendScheduleNotification({
//     required String date,
//     required int id,
//     required String body,
//     required String payload,
//     String title = '',
//     bool isDaily = false,
//   }) async {
//     await flutterLocalNotificationsPlugin.cancel(id);
//     log('notification body :- $body $id $date $payload');
//     log('notification body :- ${DateTimeComponents.time}');
//     log('notification body :- ${DateTimeComponents.dateAndTime}');
//     log('qq body :- ${tz.TZDateTime.from(
//       DateTime.parse(date), // Parsed date from the input
//       tz.local,
//     )}');
//     log('qq body :- $date');
//     try {
//       // await flutterLocalNotificationsPlugin.zonedSchedule(
//       //   id,
//       //   title,
//       //   body,
//       //   tz.TZDateTime.parse(tz.local, date),
//       //   const NotificationDetails(
//       //     android: AndroidNotificationDetails(
//       //       'default_channel_2',
//       //       'Default Channel 2',
//       //       importance: Importance.max,
//       //       // ongoing: true,
//       //       // styleInformation: BigTextStyleInformation(''),
//       //       icon: '@mipmap/ic_launcher',
//       //     ),
//       //     iOS: DarwinNotificationDetails(),
//       //   ),
//       //   // androidAllowWhileIdle: true,
//       //   payload: payload,
//       //   uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
//       //   matchDateTimeComponents: isDaily ? DateTimeComponents.time : DateTimeComponents.dateAndTime,
//       //   androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
//       // );

//       await flutterLocalNotificationsPlugin.zonedSchedule(
//         0,
//         'scheduled title',
//         'scheduled body',
//         tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5)),
//         const NotificationDetails(
//           android: AndroidNotificationDetails(
//             'default_channel_2',
//             'Default Channel 2',
//             channelDescription: 'your channel description',
//           ),
//         ),
//         uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
//         androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
//       );
//       return;
//     } catch (e) {
//       log('e notification body :- $e ');
//     }
//   }

//   // on response action
//   Future<void> onDidReceiveNotificationResponse(NotificationResponse payload) async {
//     // display a dialog with the notification details, tap ok to go to another page
//     log('notification payload: ${payload.payload}');
//   }

//   static Future<void> onDidReceiveBackgroundNotificationResponse(NotificationResponse payload) async {
//     // display a dialog with the notification details, tap ok to go to another page
//     log('BG notification detected');
//     log('notification payload: ${payload.payload}');
//   }

//   Future<void> onDidReceiveLocalNotification(int? id, String? title, String? body, String? payload) async {
//     // display a dialog with the notification details, tap ok to go to another page
//     await showDialog<void>(
//       context: navigatorKey.currentContext!,
//       builder: (BuildContext context) => CupertinoAlertDialog(
//         title: const Text('title'),
//         content: const Text('body'),
//         actions: [
//           CupertinoDialogAction(
//             isDefaultAction: true,
//             child: const Text('OK'),
//             onPressed: () async {
//               Navigator.pop(navigatorKey.currentContext!);
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
