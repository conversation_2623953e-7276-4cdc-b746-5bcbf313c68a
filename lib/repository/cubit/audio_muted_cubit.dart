import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/repository/audio_muted_repository.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'audio_muted_state.dart';

class AudioMutedCubit extends Cubit<AudioMutedState> {
  AudioMutedCubit() : super(const AudioMutedState());

final audioMutedRepository = AudioMutedRepository();
final authRepository = AuthRepository();

  Future<void> audioMutedApi(int? audioVolume, bool? audioMuted) async {
    try {
      '???????????? call'.logD;
      final response = await audioMutedRepository.audioMuted(
        audioVolume: audioVolume, //state.audioVolume,
        audioMuted: audioMuted, //state.audioMuted,
        context: navigatorKey.currentContext!,
      );
      '???????????? response of audioMuted = ${response.toString()}'.logD;
      if (response != null && response.data?['success'] == true) {
        Injector.instance<AppDB>().isAudioMuted = audioMuted ?? state.audioMuted;
        await authRepository.getUserData(context: navigatorKey.currentContext); 
        
        'AppDB.isAudioMuted updated to: ${Injector.instance<AppDB>().isAudioMuted}'.logD;
        emit(state.copyWith(audioMuted: audioMuted ?? state.audioMuted));
      }
    } catch (e) {
      '???????????? catch = $e'.logD;
    }
  }

}
