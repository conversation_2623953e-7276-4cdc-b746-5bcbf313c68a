import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';

enum ProgressCheckState {
  optional, // User can do a progress check (1-2 weeks since last check)
  required, // User must do a progress check (2+ weeks since last check)
  notNeeded, // Progress check not needed
}

class ProgressRecentData {
  // Can be DateTime or String

  ProgressRecentData({
    required this.lastProgressCheck,
    required this.assessmentTime,
    required this.createdAt,
  });
  final DateTime? lastProgressCheck;
  final DateTime assessmentTime;
  final dynamic createdAt;
}

ProgressCheckState fetchProgressCheckData() {
  final mostRecentCheckin = Injector.instance<AppDB>().userModel?.user.checkins;
  var recentTime = 0;
  if (mostRecentCheckin != null && mostRecentCheckin.isNotEmpty) {
    if (mostRecentCheckin.isNotEmpty) {
      mostRecentCheckin.sort((a, b) => b.time?.compareTo(a.time ?? 0) ?? 0);
      recentTime = mostRecentCheckin.first.time ?? 0;
      'Most Recent Progress Check State: $recentTime'.logD;
    }
  } else {
    recentTime = 0;
    'No check-ins available'.logD;
  }
  'assessment time Check State: ${DateTime.fromMillisecondsSinceEpoch(Injector.instance<AppDB>().userModel?.user.assessment?.time ?? 0)}'
      .logD;
  'createdAt time Check State: ${Injector.instance<AppDB>().userModel?.user.createdAt}'.logD;
  'createdAt time Check State: $recentTime'.logD;

  final user = ProgressRecentData(
    lastProgressCheck: recentTime == 0 ? null : DateTime.fromMillisecondsSinceEpoch(recentTime),
    assessmentTime:
        DateTime.fromMillisecondsSinceEpoch(Injector.instance<AppDB>().userModel?.user.assessment?.time ?? 0),
    createdAt: Injector.instance<AppDB>().userModel?.user.createdAt.toString(),
  );
  final state = getProgressCheckState(user);
  'Progress Check State: $state'.logD;

  return state;
}

ProgressCheckState getProgressCheckState(ProgressRecentData user, {bool progressCheckCompleted = false}) {
  final now = DateTime.now();

  // Parse createdAt to DateTime if it's a String
  DateTime userCreationDate;
  if (user.createdAt is String) {
    userCreationDate = DateTime.parse(user.createdAt as String);
  } else if (user.createdAt is DateTime) {
    userCreationDate = user.createdAt as DateTime;
  } else {
    throw ArgumentError('Invalid createdAt format');
  }
  'userCreationDate $userCreationDate'.logD;
  'userCreationDate ${user.lastProgressCheck}'.logD;
  'userCreationDate ${user.assessmentTime}'.logD;

  // Determine the most recent relevant date
  DateTime? mostRecentDate = user.lastProgressCheck ?? user.assessmentTime;

  // If a progress check was completed, reset the timer to the current date
  if (progressCheckCompleted) {
    mostRecentDate = now;
  }

  final timeSinceMostRecent = now.difference(mostRecentDate);
  'timeSinceMostRecent $timeSinceMostRecent'.logD;

  // Determine the progress check state based on the time difference
  if (timeSinceMostRecent.inDays >= 14) {
    return ProgressCheckState.required; // More than 2 weeks, user must do a progress check
  } else if (timeSinceMostRecent.inDays >= 7) {
    return ProgressCheckState.optional; // Between 1 and 2 weeks, user can do a progress check
  } else {
    return ProgressCheckState.notNeeded; // Less than 1 week, progress check not needed
  }
}
