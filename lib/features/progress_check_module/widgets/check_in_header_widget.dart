import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CheckInHeaderWidget extends StatelessWidget {
  const CheckInHeaderWidget({super.key, this.onTap, this.subTitle});
  final void Function()? onTap;
  final String? subTitle;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SpaceV(AppSize.h28),
        Stack(
          alignment: Alignment.center,
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(right: AppSize.w20),
                  child: CustomBackArrowButton(
                    // action: true,
                    padding: 0,
                    onTap: onTap ??
                        () {
                          Navigator.of(context).pop();
                        },
                  ),
                ),
              ],
            ),
            Center(
              child: AppTextWidget(
                CoreLocaleKeys.titlesCheckin.tr(),
                textAlign: TextAlign.center,
                style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        SpaceV(AppSize.h4),
        AppTextWidget(
          subTitle ?? CheckinLocaleKeys.rateTitle.tr(),
          textAlign: TextAlign.center,
          style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500, fontSize: AppSize.sp14),
        ),
        SpaceV(AppSize.h12),
        Padding(
          padding: EdgeInsets.only(bottom: AppSize.h12, top: AppSize.h10),
          child: const CustomDivider(),
        ),
      ],
    );
  }
}
