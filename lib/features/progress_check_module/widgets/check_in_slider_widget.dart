import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class CheckInSliderWidget extends StatelessWidget {
  const CheckInSliderWidget({
    required this.ref,
    required this.infoVisible,
    required this.sliderValue,
    required this.bodyText,
    required this.questionText,
    required this.audioUrl,
    required this.infoAudioUrl,
    required this.isMauallyPaused,
    required this.isClick,
    super.key,
    this.firstTest,
    this.secondTest,
    required this.onCloseTap,
    required this.oninfoTap,
    this.isReverseGradient = false,
    // this.isMauallyPaused = false,
  });
  final ProgressCheckCubit ref;
  final ValueNotifier<bool> infoVisible;
  final ValueNotifier<bool> isMauallyPaused;
  final ValueNotifier<int> sliderValue;
  final String bodyText;
  final String questionText;
  final String audioUrl;
  final String? firstTest;
  final String? secondTest;
  final ValueNotifier<String?> infoAudioUrl;
  final void Function() onCloseTap;
  final void Function() oninfoTap;
  final bool isClick;
  final bool isReverseGradient;
  // final bool isMauallyPaused;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isMauallyPaused,
      builder: (context,paused,child) {
        return ValueListenableBuilder(
          valueListenable: infoAudioUrl,
          builder: (context, value, child) {
            return Column(
              children: [
                ValueListenableBuilder(
                  valueListenable: infoVisible,
                  builder: (context, infoVisible1, child) {
                    return QuestionRowWidget(
                      oninfoTap:oninfoTap ?? () {
                        infoVisible.value = !infoVisible.value;
                        if (infoVisible.value) {
                          isMauallyPaused.value = false;
                          infoAudioUrl.value = audioUrl;
                        }else {
                           isMauallyPaused.value = true;
                        }
        
                        log('infoAudioUrl.valueinfoAudioUrl.value${ref.dsInfoVisible.value}');
                      },
                      infoWidget: CustomInfoWidget(
                        padding: EdgeInsets.only(left: AppSize.w12, right: AppSize.w12),
                        visible: infoVisible1,
                        onCloseTap: onCloseTap ??
                            () {
                              //  infoVisible.value = false;
                              infoAudioUrl.value = audioUrl;
                            },
                        bodyText: bodyText,
                      ),
                      questionText: questionText,
                    );
                  },
                ),
                SpaceV(AppSize.h20),
                Padding(
                  padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                  child: SliderScreen(
                    reverseGradient: isReverseGradient,
                    firstText: firstTest ?? 'No impact at all',
                    secondText: secondTest ?? 'Overwhelming impact',
                    onSelect: (p0) {
                      sliderValue.value = p0;
                    },
                    selectedValue: sliderValue,
                    isClick: isClick,
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
