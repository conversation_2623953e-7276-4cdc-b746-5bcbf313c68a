import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';

class CheckInModel {
  CheckInModel({
    this.success,
    this.checkins,
  });

  factory CheckInModel.fromJson(Map<String, dynamic> json) => CheckInModel(
        success: json['success'] as bool,
        checkins: json['checkins'] == null
            ? []
            : List<Checkin>.from(
                (json['checkins'] as List<dynamic>).map((x) => Checkin.fromJson(x as Map<String, dynamic>)),
              ),
      );
  bool? success;
  List<Checkin>? checkins;

  Map<String, dynamic> toJson() => {
        'success': success,
        'checkins': checkins == null ? <dynamic>[] : List<dynamic>.from(checkins!.map((x) => x.toJson())),
      };
}
