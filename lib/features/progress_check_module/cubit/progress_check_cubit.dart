import 'package:audioplayers/audioplayers.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_selection_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_thank_you_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/progress_check_repository/progress_check_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'progress_check_cubit.freezed.dart';
part 'progress_check_state.dart';

class ProgressCheckCubit extends Cubit<ProgressCheckState> {
  ProgressCheckCubit() : super(ProgressCheckState());

  final ProgressCheckRepository progressCheckRepository = ProgressCheckRepository();
  final AuthRepository authRepository = AuthRepository();

  final TextEditingController unitsController = TextEditingController(text: '0');

  //for audio control
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> progressCheckRatePaused = ValueNotifier(false);
  ValueNotifier<bool> progressCheckLifePaused = ValueNotifier(false);

  ValueNotifier<String?> infoCheckRateAudioUrl = ValueNotifier(CheckinLocaleKeys.rateAudio.tr());

  ValueNotifier<String?> infoCheckLifeAudioUrl = ValueNotifier(CheckinLocaleKeys.lifeAudio.tr());

  ValueNotifier<String?> infoDrinkingAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelDrinkingVisible = ValueNotifier(false);

  ValueNotifier<String?> infoDrugAudioUrl = ValueNotifier(CheckinLocaleKeys.drugsAudio.tr());

  ValueNotifier<String?> infoWellBeingAudioUrl = ValueNotifier(CheckinLocaleKeys.eiAudio.tr());

  ValueNotifier<String?> infoThankYouAudioUrl = ValueNotifier(CheckinLocaleKeys.thankYouAudio.tr());

  ValueNotifier<bool> dsInfoVisible = ValueNotifier(false);
  ValueNotifier<int> dsSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> ntInfoVisible = ValueNotifier(false);
  ValueNotifier<int> ntSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> eiInfoVisible = ValueNotifier(false);
  ValueNotifier<int> eiSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> psInfoVisible = ValueNotifier(false);
  ValueNotifier<int> psSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> ubInfoVisible = ValueNotifier(false);
  ValueNotifier<int> ubSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> lsInfoVisible = ValueNotifier(false);
  ValueNotifier<int> lsSliderValue = ValueNotifier(-1);

  ValueNotifier<bool> lifeDifficultyInfoVisible = ValueNotifier(false);
  ValueNotifier<int> lifeDifficultySliderValue = ValueNotifier(-1);

  ValueNotifier<bool> lifeRateInfoVisible = ValueNotifier(false);
  ValueNotifier<int> lifeRateSliderValue = ValueNotifier(-1);

  ValueNotifier<Color> borderColor = ValueNotifier(Colors.transparent);

  final AudioPlayer pageAudioPlayer = AudioPlayer();
  ValueNotifier<bool> isCloseAudio = ValueNotifier(false);

  late final Animation<double> _scaleAnimation;

  //Life page
  ValueNotifier<String> qualityValue = ValueNotifier('');
  ValueNotifier<String> healthValue = ValueNotifier('');
  ValueNotifier<String> activitiesValue = ValueNotifier('');
  ValueNotifier<String> relationshipsValue = ValueNotifier('');
  ValueNotifier<String> workValue = ValueNotifier('');

  final qualityKey = GlobalKey();
  final healthKey = GlobalKey();
  final activitiesKey = GlobalKey();
  final relationshipsKey = GlobalKey();
  final workKey = GlobalKey();

//drinking
  ValueNotifier<String> controlValue = ValueNotifier('');
  ValueNotifier<String> anxiousValue = ValueNotifier('');
  ValueNotifier<String> worryValue = ValueNotifier('');
  ValueNotifier<String> willValue = ValueNotifier('');
  ValueNotifier<String> difficultValue = ValueNotifier('');

//drug
  ValueNotifier<String> drugControlValue = ValueNotifier('');
  ValueNotifier<String> drugAnxiousValue = ValueNotifier('');
  ValueNotifier<String> drugWorryValue = ValueNotifier('');
  ValueNotifier<String> drugWillValue = ValueNotifier('');
  ValueNotifier<String> drugDifficultyValue = ValueNotifier('');

  ValueNotifier<bool> isFromProgressCheck = ValueNotifier(false);

  final dsKey = GlobalKey();
  final ntKey = GlobalKey();
  final eiKey = GlobalKey();
  final psKey = GlobalKey();
  final ubKey = GlobalKey();
  final lsKey = GlobalKey();

  final controlKey = GlobalKey();
  final anxiousKey = GlobalKey();
  final worryKey = GlobalKey();
  final willKey = GlobalKey();
  final difficultKey = GlobalKey();

  final drugControlKey = GlobalKey();
  final drugAnxiousKey = GlobalKey();
  final drugWorryKey = GlobalKey();
  final drugWillKey = GlobalKey();
  final drugDifficultyKey = GlobalKey();

  int currentDrinking = 0;
  int currentDay = 0;

  double currentdrugunitsValue = 0;
  int currentdrugfreeDaysValue = 0;
  int secondDrugunitsValue = 0;
  int secondDrugfreeDaysValue = 0;

  int thirdDrugunitsValue = 0;
  int thirdDrugfreeDaysValue = 0;
//emotional-impact
  ValueNotifier<String> eiNervousValue = ValueNotifier('');
  ValueNotifier<String> eiWorryValue = ValueNotifier('');
  ValueNotifier<String> eiInterestValue = ValueNotifier('');
  ValueNotifier<String> eidownValue = ValueNotifier('');

  final eiNervousKey = GlobalKey();
  final eiWorryKey = GlobalKey();
  final eiInterestKey = GlobalKey();
  final eidownKey = GlobalKey();

  ValueNotifier<bool> isLifeButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isDrinkingButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isDrugButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isEmotionalButtonClicked = ValueNotifier(false);
  DrugDetail drugFirstList = DrugDetail();
  DrugDetail? drugSecondist;
  DrugDetail? drugThirdist;
  //emotional-impact
  List<String> eiNervousList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.eiQuestionNervousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> eiWorryList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.eiQuestionWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> eiInterestList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.eiQuestionInterestItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> eidownList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.eiQuestionDownItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  //drug variables
  List<String> drugControlList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drugsQuestionControlItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> druganxiousList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drugsQuestionAnxiousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugworryList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drugsQuestionWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugwillList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drugsQuestionWillItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugdifficultyList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drugsQuestionDifficultyItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

//drinking variables
  List<String> controlList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drinkingQuestionControlItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> anxiousList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drinkingQuestionAnxiousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> worryList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drinkingQuestionWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> willList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drinkingQuestionWillItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> difficultList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.drinkingQuestionDifficultyItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

//life variables
  List<String> qualityList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.lifeQuestionQualityItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> healthList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.lifeQuestionHealthItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> activitiesList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.lifeQuestionActivitiesItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> relationshipsList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.lifeQuestionRelationshipsItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> workList = (DynamicAssetLoader.getNestedValue(
    CheckinLocaleKeys.lifeQuestionWorkItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  void initalAudioData(
    BuildContext context,
  ) {
    // Assuming infoAudioUrl is some reactive value you're updating
    infoAudioUrl.value = CheckinLocaleKeys.welcomeAudioOptional.tr();
  }

  Future<void> checkInAPI({
    required Map<String, dynamic> rate,
    required Map<String, dynamic> life,
    required Map<String, dynamic> ei,
    required Map<String, dynamic> drinking,
    required Map<String, dynamic> drug,
    required BuildContext context,
    required ProgressCheckCubit ref,
  }) async {
    try {
      '>?>?>? rate = ${rate}'.logV; 
      '>?>?>? life = ${life}'.logV; 
      '>?>?>? ei = ${ei}'.logV; 
      '>?>?>? drinking = ${drinking}'.logV; 
      '>?>?>? drug = ${drug}'.logV; 
      // isLoading.value = true;
      'drug ===$drinking'.logD;
      emit(state.copyWith(isApiLoading: true));
      final response = await progressCheckRepository.checkIn(
        context: context,
        rate: rate,
        life: life,
        ei: ei,
        drinking: drinking,
        drug: drug,
      );
      '>>>>>>>> next o'.logV;
      if (response != null && (response.success ?? false) == true) {
        '>>>>>>>> checkin first= ${response.checkins?.first}'.logV;
        '>>>>>>>> checkin length= ${response.checkins?.length}'.logV;
        '>>>>>>>> checkin Api = ${Injector.instance<AppDB>().userModel?.user.checkins}'.logV;
       await authRepository.getUserData(context: context);
        Injector.instance<AppDB>().userModel?.user.checkins = response.checkins;
        '//////// checkins drugs = ${Injector.instance<AppDB>().userModel?.user.checkins?.first.drugs?.drugDetails}'.logV;
        '//////// checkins drinking = ${Injector.instance<AppDB>().userModel?.user.checkins?.last.drinking}'.logV;
         
        ref.infoWellBeingAudioUrl.value = null; 

        ref.infoThankYouAudioUrl.value = CheckinLocaleKeys.thankYouAudio.tr();
        '/// navigate'.logV;
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(value: ref, child: const ProgressCheckThankYouPage()),
        );
        '/// complete navigate done'.logV;
      }else{
        '>>>>>>>> failed'.logV;
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e,stackTrace) {
      '>>>>>>>> catch'.logV;
      '>>>>>>>> error = ${e.toString()}'.logV;
      '>>>>>>>> stack = ${stackTrace.toString()}'.logV;
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    activitiesValue.dispose();
    relationshipsValue.dispose();
    workValue.dispose();
    qualityValue.dispose();
    healthValue.dispose();
    controlValue.dispose();
    anxiousValue.dispose();
    worryValue.dispose();
    willValue.dispose();
    difficultValue.dispose();
    drugControlValue.dispose();
    drugAnxiousValue.dispose();
    drugWorryValue.dispose();
    drugWillValue.dispose();
    drugDifficultyValue.dispose();
    eiNervousValue.dispose();
    eiWorryValue.dispose();
    eiInterestValue.dispose();
    eidownValue.dispose();
    controlList.clear();
    anxiousList.clear();
    worryList.clear();
    willList.clear();
    difficultList.clear();
    drugControlList.clear();

    return super.close();
  }
}
