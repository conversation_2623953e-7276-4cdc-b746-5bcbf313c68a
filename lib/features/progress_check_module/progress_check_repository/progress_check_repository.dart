import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/progress_check_module/model/checkin_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class ProgressCheckRepository {
  Future<CheckInModel?> checkIn({
    required Map<String, dynamic> rate,
    required Map<String, dynamic> life,
    required Map<String, dynamic> ei,
    required Map<String, dynamic> drinking,
    required Map<String, dynamic> drug,
    required BuildContext context,
  }) async {
    'drug ++ ${drinking.isNotEmpty}'.logE;
    'drug ++ ${{
      'rate': rate,
      'life': life,
      'ei': ei,
      if (drinking.isNotEmpty) 'drinking': drinking,
      if (drug.isNotEmpty) 'drugs': drug,
    }}'
        .logD;
    try {
      final response = await APIFunction.postAPICall(
        {
          'rate': rate,
          'life': life,
          'ei': ei,
          if (drinking.isNotEmpty) 'drinking': drinking,
          if (drug.isNotEmpty) 'drugs': drug,
        },
        apiName: EndPoints.checkIn,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return CheckInModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
