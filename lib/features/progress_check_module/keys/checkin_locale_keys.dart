class CheckinLocaleKeys {
  // Welcome section
  static const welcomeTitle = 'checkin.welcome.title';
  static const welcomeTextOptional = 'checkin.welcome.text.optional';
  static const welcomeTextNonOptional = 'checkin.welcome.text.nonOptional';
  static const welcomeAudioOptional = 'checkin.welcome.audio.optional';
  static const welcomeAudioNonOptional = 'checkin.welcome.audio.nonOptional';
  static const welcomeButtonYes = 'checkin.welcome.buttons.yes';
  static const welcomeButtonNo = 'checkin.welcome.buttons.no';
  static const welcomeButtonNext = 'checkin.welcome.buttons.next';

  // Rate section
  static const rateTitle = 'checkin.rate.title';
  static const rateAudio = 'checkin.rate.audio';

  // Rate questions
  static const rateQuestionDsLabel = 'checkin.rate.questions.ds.label';
  static const rateQuestionDsSliderLabels = 'checkin.rate.questions.ds.sliderLabels';
  static const rateQuestionDsInfoText = 'checkin.rate.questions.ds.info.text';
  static const rateQuestionDsInfoAudio = 'checkin.rate.questions.ds.info.audio';

  static const rateQuestionNtLabel = 'checkin.rate.questions.nt.label';
  static const rateQuestionNtSliderLabels = 'checkin.rate.questions.nt.sliderLabels';
  static const rateQuestionNtInfoText = 'checkin.rate.questions.nt.info.text';
  static const rateQuestionNtInfoAudio = 'checkin.rate.questions.nt.info.audio';

  static const rateQuestionEiLabel = 'checkin.rate.questions.ei.label';
  static const rateQuestionEiSliderLabels = 'checkin.rate.questions.ei.sliderLabels';
  static const rateQuestionEiInfoText = 'checkin.rate.questions.ei.info.text';
  static const rateQuestionEiInfoAudio = 'checkin.rate.questions.ei.info.audio';

  static const rateQuestionPsLabel = 'checkin.rate.questions.ps.label';
  static const rateQuestionPsSliderLabels = 'checkin.rate.questions.ps.sliderLabels';
  static const rateQuestionPsInfoText = 'checkin.rate.questions.ps.info.text';
  static const rateQuestionPsInfoAudio = 'checkin.rate.questions.ps.info.audio';

  static const rateQuestionUbLabel = 'checkin.rate.questions.ub.label';
  static const rateQuestionUbSliderLabels = 'checkin.rate.questions.ub.sliderLabels';
  static const rateQuestionUbInfoText = 'checkin.rate.questions.ub.info.text';
  static const rateQuestionUbInfoAudio = 'checkin.rate.questions.ub.info.audio';

  static const rateQuestionLsLabel = 'checkin.rate.questions.ls.label';
  static const rateQuestionLsSliderLabels = 'checkin.rate.questions.ls.sliderLabels';
  static const rateQuestionLsInfoText = 'checkin.rate.questions.ls.info.text';
  static const rateQuestionLsInfoAudio = 'checkin.rate.questions.ls.info.audio';

  // Life section
  static const lifeTitle = 'checkin.life.title';
  static const lifeAudio = 'checkin.life.audio';

  // Life questions
  static const lifeQuestionQualityLabel = 'checkin.life.questions.quality.label';
  static const lifeQuestionQualityItems = 'checkin.life.questions.quality.items';

  static const lifeQuestionHealthLabel = 'checkin.life.questions.health.label';
  static const lifeQuestionHealthItems = 'checkin.life.questions.health.items';

  static const lifeQuestionActivitiesLabel = 'checkin.life.questions.activities.label';
  static const lifeQuestionActivitiesItems = 'checkin.life.questions.activities.items';

  static const lifeQuestionRelationshipsLabel = 'checkin.life.questions.relationships.label';
  static const lifeQuestionRelationshipsItems = 'checkin.life.questions.relationships.items';

  static const lifeQuestionWorkLabel = 'checkin.life.questions.work.label';
  static const lifeQuestionWorkItems = 'checkin.life.questions.work.items';

  static const lifeQuestionDifficultiesLabel = 'checkin.life.questions.difficulties.label';
  static const lifeQuestionDifficultiesSliderLabels = 'checkin.life.questions.difficulties.sliderLabels';
  static const lifeQuestionDifficultiesInfoText = 'checkin.life.questions.difficulties.info.text';
  static const lifeQuestionDifficultiesInfoAudio = 'checkin.life.questions.difficulties.info.audio';

  static const lifeQuestionRateLabel = 'checkin.life.questions.rate.label';
  static const lifeQuestionRateSliderLabels = 'checkin.life.questions.rate.sliderLabels';
  static const lifeQuestionRateInfoText = 'checkin.life.questions.rate.info.text';
  static const lifeQuestionRateInfoAudio = 'checkin.life.questions.rate.info.audio';

  // Drinking section
  static const drinkingTitle = 'checkin.drinking.title';
  static const drinkingSubtitle = 'checkin.drinking.subtitle';
  static const drinkingAudio = 'checkin.drinking.audio';
  static const drinkingButton = 'checkin.drinking.button';

  // Drinking questions
  static const drinkingQuestionUnitsLabel = 'checkin.drinking.questions.units.label';
  static const drinkingQuestionUnitsPlusLabel = 'checkin.drinking.questions.units.plusLabel';
  static const drinkingQuestionUnitsMinusLabel = 'checkin.drinking.questions.units.minusLabel';

  static const drinkingQuestionDaysLabel = 'checkin.drinking.questions.days.label';
  static const drinkingQuestionDaysPlusLabel = 'checkin.drinking.questions.days.plusLabel';
  static const drinkingQuestionDaysMinusLabel = 'checkin.drinking.questions.days.minusLabel';

  static const drinkingQuestionControlLabel = 'checkin.drinking.questions.control.label';
  static const drinkingQuestionControlItems = 'checkin.drinking.questions.control.items';

  static const drinkingQuestionAnxiousLabel = 'checkin.drinking.questions.anxious.label';
  static const drinkingQuestionAnxiousItems = 'checkin.drinking.questions.anxious.items';

  static const drinkingQuestionWorryLabel = 'checkin.drinking.questions.worry.label';
  static const drinkingQuestionWorryItems = 'checkin.drinking.questions.worry.items';

  static const drinkingQuestionWillLabel = 'checkin.drinking.questions.will.label';
  static const drinkingQuestionWillItems = 'checkin.drinking.questions.will.items';

  static const drinkingQuestionDifficultyLabel = 'checkin.drinking.questions.difficulty.label';
  static const drinkingQuestionDifficultyItems = 'checkin.drinking.questions.difficulty.items';

  // Drugs section
  static const drugsTitle = 'checkin.drugs.title';
  static const drugsSubtitle = 'checkin.drugs.subtitle';
  static const drugsAudio = 'checkin.drugs.audio';

  // Drugs questions
  static const drugsQuestionAmountLabel = 'checkin.drugs.questions.amount.label';
  static const drugsQuestionAmountPlusLabel = 'checkin.drugs.questions.amount.plusLabel';
  static const drugsQuestionAmountMinusLabel = 'checkin.drugs.questions.amount.minusLabel';

  static const drugsQuestionFrequencyLabel = 'checkin.drugs.questions.frequency.label';
  static const drugsQuestionFrequencyPlusLabel = 'checkin.drugs.questions.frequency.plusLabel';
  static const drugsQuestionFrequencyMinusLabel = 'checkin.drugs.questions.frequency.minusLabel';

  static const drugsQuestionControlLabel = 'checkin.drugs.questions.control.label';
  static const drugsQuestionControlItems = 'checkin.drugs.questions.control.items';

  static const drugsQuestionAnxiousLabel = 'checkin.drugs.questions.anxious.label';
  static const drugsQuestionAnxiousItems = 'checkin.drugs.questions.anxious.items';

  static const drugsQuestionWorryLabel = 'checkin.drugs.questions.worry.label';
  static const drugsQuestionWorryItems = 'checkin.drugs.questions.worry.items';

  static const drugsQuestionWillLabel = 'checkin.drugs.questions.will.label';
  static const drugsQuestionWillItems = 'checkin.drugs.questions.will.items';

  static const drugsQuestionDifficultyLabel = 'checkin.drugs.questions.difficulty.label';
  static const drugsQuestionDifficultyItems = 'checkin.drugs.questions.difficulty.items';

  // Emotional Wellbeing section
  static const eiTitle = 'checkin.ei.title';
  static const eiAudio = 'checkin.ei.audio';
  static const eiSubtitle = 'checkin.ei.subtitle';

  // Emotional Wellbeing questions
  static const eiQuestionNervousLabel = 'checkin.ei.questions.nervous.label';
  static const eiQuestionNervousItems = 'checkin.ei.questions.nervous.items';

  static const eiQuestionWorryLabel = 'checkin.ei.questions.worry.label';
  static const eiQuestionWorryItems = 'checkin.ei.questions.worry.items';

  static const eiQuestionInterestLabel = 'checkin.ei.questions.interest.label';
  static const eiQuestionInterestItems = 'checkin.ei.questions.interest.items';

  static const eiQuestionDownLabel = 'checkin.ei.questions.down.label';
  static const eiQuestionDownItems = 'checkin.ei.questions.down.items';

  // Thank you section
  static const thankYouTitle = 'checkin.thank-you.title';
  static const thankYouAudio = 'checkin.thank-you.audio';
  static const thankYouText = 'checkin.thank-you.text';
  static const thankYouFinishButton = 'checkin.thank-you.finishButton';

  // Errors section
  static const errorRequired = 'checkin.errors.required';
  
}
