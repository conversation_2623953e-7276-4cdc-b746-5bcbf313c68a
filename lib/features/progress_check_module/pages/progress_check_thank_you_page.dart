import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProgressCheckThankYouPage extends StatelessWidget {
  const ProgressCheckThankYouPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProgressCheckCubit, ProgressCheckState>(
      builder: (ctx, state) {
        final ref = ctx.read<ProgressCheckCubit>();

        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              //ref.infoDrugAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
            }
          },
          child: ValueListenableBuilder(
            valueListenable: ref.infoThankYouAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                resizeToAvoidBottomInset: true,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoThankYouAudioUrl,
                appBar: CommonAppBar(
                  prefixIcon: Icon(
                    Icons.logout,
                    size: AppSize.sp20,
                  ),
        
                  onSuffixTap: () {
                    if (ref.infoThankYouAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                    }
                  },
                  onPrefixTap: () async {
                    await LogOutDialog.showLogOutDialog(context);
                  },
                  // onLogoutTap: () {},
                ),
                body: Column(
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Padding(
                            padding: const EdgeInsets.all(4),
                            child: CustomRawScrollbar(
                              child: Container(
                                padding: EdgeInsets.only(right: AppSize.w24, left: AppSize.w20),
                                child: ListView(
                                  children: [
                                    ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              SpaceV(AppSize.h18),
                                              AppTextWidget(
                                                CoreLocaleKeys.titlesCheckin.tr(),
                                                style: context.textTheme.titleSmall
                                                    ?.copyWith(fontWeight: FontWeight.w500, fontSize: AppSize.sp18),
                                              ),
                                              SpaceV(AppSize.h4),
                                              AppTextWidget(
                                                CheckinLocaleKeys.thankYouTitle.tr(),
                                                style: context.textTheme.titleSmall
                                                    ?.copyWith(fontWeight: FontWeight.w500),
                                              ),
                                              SpaceV(AppSize.h10),
                                              const CustomDivider(),
                                              SpaceV(AppSize.h20),
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  CheckinLocaleKeys.thankYouText,
                                                  context,
                                                ) as List)
                                                    .cast<String>()
                                                    .join('\n\n'),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h50),
                                            ],
                                          ),
                                          Column(
                                            children: [
                                              CustomButton(
                                                title: IsLocaleKeys.buttonsFinish.tr(),
                                                isBottom: true,
                                                padding: EdgeInsets.zero,
                                                color: context.themeColors.blueColor,
                                                onTap: () {
                                                  ref.infoThankYouAudioUrl.value = null;
                                                  AppNavigation.pushAndRemoveAllScreen(
                                                    context,
                                                    BlocProvider.value(
                                                      value: ref,
                                                      child: const DashboardPage(
                                                        isFromProgress: true,
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h20),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
