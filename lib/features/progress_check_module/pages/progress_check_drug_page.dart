import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_emotional_wellbeing.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_second_drug_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class ProgressCheckDrugPage extends StatelessWidget {
  const ProgressCheckDrugPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AssessmentDrugCubit()
        ..userDrugData()
        ..userDrugFeelingData()
        ..userDrugGoalData(),
      child: BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
        builder: (ctx, state) {
          final ref = ctx.read<ProgressCheckCubit>();
          final drugCubit = ctx.read<AssessmentDrugCubit>();
          'state.drugDetailList.first.drug ${state.drugDetailList.first.drug}'.logV;
          '///${state.drugDetailList.first.drug}'.logV;
          return PopScope(
            onPopInvokedWithResult: (didPop, result) {
              if (didPop) {
                ref.infoDrinkingAudioUrl.value = CheckinLocaleKeys.drinkingAudio.tr();
              }
            },
            child: ValueListenableBuilder(
              valueListenable: ref.infoDrugAudioUrl,
              builder: (context, value, child) {
                'infoDrugAudioUrl.drugDetailList.first.drug ${ref.infoDrugAudioUrl.value}'.logV;
    
                return AppScaffold(
                  resizeToAvoidBottomInset: true,
                  isAudioPanelVisible: ref.isAudioPannelVisible,
                  infoAudioUrl: ref.infoDrugAudioUrl,
                  appBar: CommonAppBar(
                    prefixIcon: Icon(
                      Icons.logout,
                      size: AppSize.sp20,
                    ),
    
                    onSuffixTap: () {
                      if (ref.infoDrugAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                      }
                    },
                    onPrefixTap: () async {
                      await LogOutDialog.showLogOutDialog(context);
                    },
    
                    // onLogoutTap: () {},
                  ),
                  body: Column(
                    children: [
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Padding(
                              padding: const EdgeInsets.all(4),
                              child: CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                      child: ValueListenableBuilder(
                                        valueListenable: ref.isDrugButtonClicked,
                                        builder: (context, value, child) {
                                          return ValueListenableBuilder(
                                            valueListenable: drugCubit.isUnitAndDrugsButton,
                                            builder: (context, value, child) {
                                              return Column(
                                                children: [
                                                  Column(
                                                    children: [
                                                      CheckInHeaderWidget(
                                                        subTitle: CheckinLocaleKeys.drugsTitle.tr(),
                                                        onTap: () {
                                                          ref.progressCheckLifePaused.value = false;
    
                                                          ref.infoDrinkingAudioUrl.value =
                                                              CheckinLocaleKeys.drinkingAudio.tr();
                                                          Navigator.of(context).pop();
                                                        },
                                                      ),
                                                      // SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        CheckinLocaleKeys.drugsSubtitle.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          color: context.themeColors.darkGreyColor,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionAmountLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                            'unit': drugCubit.formatString(state.drugDetailList.first.unit ?? ''),
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                      Column(
                                                        children: [
                                                          CustomAgeSelcetionWidget(
                                                            key: drugCubit.unitsKey,
                                                            controller: drugCubit.drugFirstGoalUnitsController,
                                                            onIncreaseTap: () {
                                                              drugCubit.increaseDrugGoalUnitsValue(
                                                                drugCubit.getDrugValueForKey(
                                                                  state.drugDetailList.first.drug ?? '',
                                                                ),
                                                                state.drugDetailList.first.unit,
                                                              );
                                                            },
                                                            onDecreaseTap: () {
                                                              drugCubit.decreaseDrugUnitValue(
                                                                drugCubit.getDrugValueForKey(
                                                                  state.drugDetailList.first.drug ?? '',
                                                                ),
                                                                state.drugDetailList.first.unit,
                                                              );
                                                            },
                                                            unitText: drugCubit.drugFirstGoalUnitsController.text.trim(),
                                                            //unitText: drugCubit.drugGoalUnitsController.text.trim(),
                                                            isButtonClicked: drugCubit.isUnitAndDrugsButton.value,
                                                          ),
                                                          if (drugCubit.isUnitAndDrugsButton.value &&
                                                              (state.currentdrugunitsValue == 0 &&
                                                                  state.currentdrugfreeDaysValue > 0))
                                                            Padding(
                                                              padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                                              child: CustomErrorWidget(
                                                                errorMessgaeText: AssessmentLocaleKeys
                                                                    .errorsInvalidUnitsMessage
                                                                    .tr(),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionFrequencyLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',)
                                                          },
                                                        ),
    
                                                        //  CheckinLocaleKeys.drugsQuestionFrequencyLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                      Column(
                                                        children: [
                                                          CustomAgeSelcetionWidget(
                                                            key: drugCubit.freeDayKey,
                                                            controller: drugCubit.drugFirstGoalFreeDayController,
                                                            onIncreaseTap: drugCubit.firstIncreaseDrugGoalFreeDayValue,
                                                            onDecreaseTap: drugCubit.firstDecreaseDrugFreeDayValue,
                                                            unitText: state.firstDrugfreeDaysValue,
                                                            //unitText: drugCubit.drugGoalFreeDayController.text.trim(),
                                                            isButtonClicked: drugCubit.isUnitAndDrugsButton.value,
                                                            inputFormatters: [
                                            FilteringTextInputFormatter.digitsOnly,
                                            TextInputFormatter.withFunction(
                                                  (oldValue, newValue) {
                                                if (newValue.text.isEmpty) {
                                                  return newValue;
                                                }

                                                final value = int.tryParse(newValue.text);
                                                if (value != null && value <= 7) {
                                                  return newValue;
                                                }

                                                return oldValue;
                                              },
                                            ),
                                          ],
                                                          ),
                                                          if (drugCubit.isUnitAndDrugsButton.value &&
                                                              (state.currentdrugunitsValue as num > 0 && // change 0.0
                                                                  state.currentdrugfreeDaysValue == 0))
                                                            Padding(
                                                              padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                                              child: CustomErrorWidget(
                                                                errorMessgaeText: AssessmentLocaleKeys
                                                                    .errorsInvalidDaysMessage
                                                                    .tr(),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionControlLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                          },
                                                        ),
    
                                                        //  CheckinLocaleKeys.drugsQuestionControlLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: ref.drugControlKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: ref.drugControlValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: ref.isDrugButtonClicked.value,
                                                              options: ref.drugControlList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                ref.drugControlValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrugButtonClicked.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionAnxiousLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',)
                                                          },
                                                        ),
    
                                                        // CheckinLocaleKeys.drugsQuestionAnxiousLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: ref.drugAnxiousKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: ref.drugAnxiousValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: ref.isDrugButtonClicked.value,
                                                              options: ref.druganxiousList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                ref.drugAnxiousValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrugButtonClicked.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionWorryLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',)
                                                          },
                                                        ),
                                                        //  CheckinLocaleKeys.drugsQuestionWorryLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: ref.drugWorryKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: ref.drugWorryValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: ref.isDrugButtonClicked.value,
                                                              options: ref.drugworryList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                ref.drugWorryValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrugButtonClicked.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionWillLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                          },
                                                        ),
                                                        //  CheckinLocaleKeys.drugsQuestionWillLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: ref.drugWillKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: ref.drugWillValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: ref.isDrugButtonClicked.value,
                                                              options: ref.drugwillList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                ref.drugWillValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrugButtonClicked.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          CheckinLocaleKeys.drugsQuestionDifficultyLabel,
                                                          {
                                                            'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              state.drugDetailList.first.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                          },
                                                        ),
    
                                                        //  CheckinLocaleKeys.drugsQuestionDifficultyLabel.tr(
                                                        //   namedArgs: {
                                                        //     'drug': state.drugDetailList.first.drug ?? '',
                                                        //   },
                                                        // ),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: ref.drugDifficultyKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: ref.drugDifficultyValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: ref.isDrugButtonClicked.value,
                                                              options: ref.drugdifficultyList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                ref.drugDifficultyValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrugButtonClicked.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h50),
                                                    ],
                                                  ),
                                                  MultiValueListenableBuilder(
                                                    valueListenables: [
                                                      ref.drugControlValue,
                                                      ref.drugAnxiousValue,
                                                      ref.drugWorryValue,
                                                      ref.drugWillValue,
                                                      ref.drugDifficultyValue,
                                                      drugCubit.drugGoalFreeDayController,
                                                      drugCubit.drugGoalUnitsController,
                                                    ],
                                                    builder: (context, List<dynamic> value, child) {
                                                      final drugControlValue = value[0];
                                                      final drugAnxiousValue = value[1];
                                                      final drugWorryValue = value[2];
                                                      final drugWillValue = value[3];
                                                      final drugDifficultyValue = value[4];
    
                                                      final isButtonEnabled = drugControlValue != '' &&
                                                          drugAnxiousValue != '' &&
                                                          drugWorryValue != '' &&
                                                          drugWillValue != '' &&
                                                          drugDifficultyValue != '';
                                                      return Column(
                                                        children: [
                                                          CustomButton(
                                                            title: CoreLocaleKeys.buttonsNext.tr(),
                                                            isBottom: true,
                                                            padding: EdgeInsets.zero,
                                                            color: context.themeColors.blueColor,
                                                            onTap: () async {
                                                              
                                                              // ref
                                                              //   ..currentdrugunitsValue = state.currentdrugunitsValue
                                                              //   ..currentdrugfreeDaysValue = state.currentdrugfreeDaysValue;
                                                              '${{
                                                                {
                                                                  "cocaine": {
                                                                    "drug": state.drugDetailList.first.drug,
                                                                    "unit": state.drugDetailList.first.unit,
                                                                    "amount": state.currentdrugunitsValue,
                                                                    "frequency": state.currentdrugfreeDaysValue,
                                                                    "control": ref.drugControlList
                                                                        .indexOf(ref.drugControlValue.value),
                                                                    "anxious": ref.druganxiousList
                                                                        .indexOf(ref.drugAnxiousValue.value),
                                                                    "will": ref.drugwillList
                                                                        .indexOf(ref.drugWillValue.value),
                                                                    "difficulty": ref.drugdifficultyList
                                                                        .indexOf(ref.drugDifficultyValue.value),
                                                                    "worry": ref.drugworryList
                                                                        .indexOf(ref.drugWorryValue.value),
                                                                  },
                                                                }
                                                              }}'
                                                                  .logD;
                                                              ref.isDrugButtonClicked.value = true;
    
                                                              if ((state.currentdrugunitsValue as num >
                                                                          0 && //change 0.0
                                                                      state.currentdrugfreeDaysValue == 0) ||
                                                                  (state.currentdrugfreeDaysValue > 0 &&
                                                                      state.currentdrugunitsValue == 0)) {
                                                                drugCubit.isUnitAndDrugsButton.value = true;
                                                              } else {
                                                                drugCubit.isUnitAndDrugsButton.value = false;
                                                              }
    
                                                              if (state.currentdrugunitsValue == 0 &&
                                                                  state.currentdrugfreeDaysValue > 0) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  drugCubit.unitsKey,
                                                                );
                                                                return;
                                                              }
                                                              if (state.currentdrugfreeDaysValue == 0 &&
                                                                  state.currentdrugunitsValue as num > 0) {
                                                                //change 0.0
                                                                await AppCommonFunctions.scrollToKey(
                                                                  drugCubit.freeDayKey,
                                                                );
                                                                return;
                                                              }
    
                                                              if (ref.drugControlValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  ref.drugControlKey,
                                                                );
                                                                return;
                                                              }
                                                              if (ref.drugAnxiousValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  ref.drugAnxiousKey,
                                                                );
                                                                return;
                                                              }
                                                              if (ref.drugWorryValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  ref.drugWorryKey,
                                                                );
                                                                return;
                                                              }
                                                              if (ref.drugWillValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(ref.drugWillKey);
                                                                return;
                                                              }
                                                              if (ref.drugDifficultyValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  ref.drugDifficultyKey,
                                                                );
                                                                return;
                                                              }
    
    
                                                              if (!isButtonEnabled) {
                                                                // CustomSnackbar.showErrorSnackBar(
                                                                //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                                // );
                                                              } else {
                                                                '>?>?>? drug unit value = ${drugCubit.drugGoalUnitsController.value.text}'.logV;
                                                                '>?>?>? drug frequncy value = ${drugCubit.drugGoalFreeDayController.value.text}'.logV;
                                                                ref.infoDrugAudioUrl.value = null;
                                                                '>?>?>? length = ${state.drugDetailList.length}'.logV;
                                                                if (state.drugDetailList.length >= 2) {
                                                                  ref.drugFirstList = state.drugDetailList[0];
                                                                  ref.infoAudioUrl.value = null;
                                                                  '>?>?>? drugFirstList = ${ref.drugFirstList}'.logV;
    
                                                                  await AppNavigation.nextScreen(
                                                                    context,
                                                                    BlocProvider.value(
                                                                      value: drugCubit,
                                                                      child: ProgressCheckSecondDrugPage(
                                                                        progressCheckCubit: ref,
                                                                      ),
                                                                    ),
                                                                  );
                                                                } else {
                                                                  if(state.drugDetailList.isNotEmpty){
                                                                    '>?>?>? if '.logV;
                                                                    ref.drugFirstList = state.drugDetailList[0];
                                                                    '>?>?>? amount = ${ref.drugFirstList.amount}'.logV;
                                                                    '>?>?>? frequency = ${ref.drugFirstList.frequency}'.logV;
                                                                  }
                                                                  await AppNavigation.nextScreen(
                                                                    context,
                                                                    BlocProvider.value(
                                                                      value: ref,
                                                                      child: ProgressCheckEmotionalWellbeing(unit: drugCubit.drugFirstGoalUnitsController.text,days: drugCubit.drugFirstGoalFreeDayController.text,),
                                                                    ),
                                                                  );
                                                                }
                                                                
                                                                // AppNavigation.nextScreen(
                                                                //   context,
                                                                //   BlocProvider.value(value: progressCheckCubit, child: const ProgressCheckDrugPage()),
                                                                // );
                                                              }
                                                            },
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
