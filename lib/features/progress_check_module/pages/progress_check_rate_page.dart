import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_life_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_slider_widget.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProgressCheckRatePage extends StatelessWidget {
  const ProgressCheckRatePage({super.key, this.isFromDashboardPage = false});
  final bool isFromDashboardPage;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (isFromDashboardPage) {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          } else {
            Navigator.of(context).pop();
          }
        }
      },
      child: BlocBuilder<ProgressCheckCubit, ProgressCheckState>(
        builder: (ctx, state) {
          final ref = ctx.read<ProgressCheckCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoCheckRateAudioUrl,
            builder: (context, value, child) {
              return ValueListenableBuilder(
                valueListenable: ref.isLifeButtonClicked,
                builder: (context, value, child) {
                  return AppScaffold(
                    isManuallyPaused: ref.progressCheckRatePaused,
                    resizeToAvoidBottomInset: true,
                    isAudioPanelVisible: ref.isAudioPannelVisible,
                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                    appBar: CommonAppBar(
                      prefixIcon: Icon(
                        Icons.logout,
                        size: AppSize.sp20,
                      ),
      
                      onSuffixTap: () {
                        if (ref.infoCheckRateAudioUrl.value.isNotEmptyAndNotNull) {
                          ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                        }
                      },
                      onPrefixTap: () async {
                        await LogOutDialog.showLogOutDialog(context);
                      },
                      // onLogoutTap: () {},
                    ),
                    body: Column(
                      children: [
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Padding(
                                padding: const EdgeInsets.all(4),
                                child: ValueListenableBuilder(
                                  valueListenable: ref.isLifeButtonClicked,
                                  builder: (context, value, child) {
                                    return SingleChildScrollView(
                                      key: const PageStorageKey('progress_check_scroll'),
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  CheckInHeaderWidget(
                                                    onTap: () {
                                                      ref.isLifeButtonClicked.value = false;
                                                      ref.infoAudioUrl.value =
                                                          CheckinLocaleKeys.welcomeAudioOptional.tr();
                                                      ref.dsInfoVisible.value = false;
                                                      ref.ntInfoVisible.value = false;
                                                      ref.eiInfoVisible.value = false;
                                                      ref.psInfoVisible.value = false;
                                                      ref.ubInfoVisible.value = false;
                                                      ref.lsInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                    
                                                      if (isFromDashboardPage) {
                                                        Navigator.of(context).pop();
                                                        Navigator.of(context).pop();
                                                      } else {
                                                        if (isFromDashboardPage) {
                                                          Navigator.of(context).pop();
                                                          Navigator.of(context).pop();
                                                        } else {
                                                          Navigator.of(context).pop();
                                                        }
                                                      }
                                                    },
                                                  ),
                                    
                                                  //   SpaceV(AppSize.h12),
                                                  //   SpaceV(AppSize.h12),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.dsInfoVisible.value = !ref.dsInfoVisible.value;
                                                      if (ref.dsInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionDsInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.dsInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                                      ref.dsInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    bodyText: CheckinLocaleKeys.rateQuestionDsInfoText.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionDsLabel.tr(),
                                                    infoVisible: ref.dsInfoVisible,
                                                    audioUrl: CheckinLocaleKeys.rateQuestionDsInfoAudio.tr(),
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionDsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionDsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.dsKey,
                                                    sliderValue: ref.dsSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.ntInfoVisible.value = !ref.ntInfoVisible.value;
                                                      if (ref.ntInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionNtInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.ntInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                    
                                                      ref.ntInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    audioUrl: CheckinLocaleKeys.rateQuestionNtInfoAudio.tr(),
                                                    bodyText: CheckinLocaleKeys.rateQuestionNtInfoText.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionNtLabel.tr(),
                                                    infoVisible: ref.ntInfoVisible,
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionNtSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionNtSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.ntKey,
                                                    sliderValue: ref.ntSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.eiInfoVisible.value = !ref.eiInfoVisible.value;
                                                      if (ref.eiInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionEiInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.eiInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                    
                                                      ref.eiInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    audioUrl: CheckinLocaleKeys.rateQuestionEiInfoAudio.tr(),
                                                    bodyText: CheckinLocaleKeys.rateQuestionEiInfoText.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionEiLabel.tr(),
                                                    infoVisible: ref.eiInfoVisible,
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionEiSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionEiSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.eiKey,
                                                    sliderValue: ref.eiSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.psInfoVisible.value = !ref.psInfoVisible.value;
                                                      if (ref.psInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionPsInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.psInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                    
                                                      ref.psInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    bodyText: CheckinLocaleKeys.rateQuestionPsInfoText.tr(),
                                                    audioUrl: CheckinLocaleKeys.rateQuestionPsInfoAudio.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionPsLabel.tr(),
                                                    infoVisible: ref.psInfoVisible,
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionPsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionPsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.psKey,
                                                    sliderValue: ref.psSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.ubInfoVisible.value = !ref.ubInfoVisible.value;
                                                      if (ref.ubInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionUbInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.ubInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                    
                                                      ref.ubInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    audioUrl: CheckinLocaleKeys.rateQuestionUbInfoAudio.tr(),
                                                    bodyText: CheckinLocaleKeys.rateQuestionUbInfoText.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionUbLabel.tr(),
                                                    infoVisible: ref.ubInfoVisible,
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionUbSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionUbSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.ubKey,
                                                    sliderValue: ref.ubSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CheckInSliderWidget(
                                                    oninfoTap: () {
                                                      ref.lsInfoVisible.value = !ref.lsInfoVisible.value;
                                                      if (ref.lsInfoVisible.value) {
                                                        ref.progressCheckRatePaused.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateQuestionLsInfoAudio.tr();
                                                      } else {
                                                        ref.progressCheckRatePaused.value = true;
                                                        ref.lsInfoVisible.value = false;
                                                        ref.infoCheckRateAudioUrl.value =
                                                            CheckinLocaleKeys.rateAudio.tr();
                                                      }
                                                    },
                                                    isMauallyPaused: ref.progressCheckRatePaused,
                                                    isReverseGradient: true,
                                                    onCloseTap: () {
                                                      ref.progressCheckRatePaused.value = true;
                                    
                                                      ref.lsInfoVisible.value = false;
                                                      ref.infoCheckRateAudioUrl.value =
                                                          CheckinLocaleKeys.rateAudio.tr();
                                                    },
                                                    infoAudioUrl: ref.infoCheckRateAudioUrl,
                                                    audioUrl: CheckinLocaleKeys.rateQuestionLsInfoAudio.tr(),
                                                    bodyText: CheckinLocaleKeys.rateQuestionLsInfoText.tr(),
                                                    questionText: CheckinLocaleKeys.rateQuestionLsLabel.tr(),
                                                    infoVisible: ref.lsInfoVisible,
                                                    firstTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionLsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .first,
                                                    secondTest: (DynamicAssetLoader.getNestedValue(
                                                      CheckinLocaleKeys.rateQuestionLsSliderLabels,
                                                      context,
                                                    ) as List)
                                                        .cast<String>()
                                                        .last,
                                                    ref: ref,
                                                    key: ref.lsKey,
                                                    sliderValue: ref.lsSliderValue,
                                                    isClick: ref.isLifeButtonClicked.value,
                                                  ),
                                                  SpaceV(AppSize.h50),
                                                ],
                                              ),
                                              Column(
                                                children: [
                                                  CustomButton(
                                                    padding: EdgeInsets.zero,
                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                    isBottom: true,
                                                    color: context.themeColors.blueColor,
                                                    onTap: () async {
                                                      ref.isLifeButtonClicked.value = true;
                                                      if (ref.dsSliderValue.value == -1 ||
                                                          ref.psSliderValue.value == -1 ||
                                                          ref.ntSliderValue.value == -1 ||
                                                          ref.eiSliderValue.value == -1 ||
                                                          ref.lsSliderValue.value == -1 ||
                                                          ref.ubSliderValue.value == -1) {
                                                        ref.isLifeButtonClicked.value = true;
                                                        if (ref.dsSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.dsKey);
                                                          return;
                                                        }
                                                        if (ref.ntSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.ntKey);
                                                          return;
                                                        }
                                                        if (ref.eiSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.eiKey);
                                                          return;
                                                        }
                                                        if (ref.psSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.psKey);
                                                          return;
                                                        }
                                                        if (ref.ubSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.ubKey);
                                                          return;
                                                        }
                                                        if (ref.lsSliderValue.value == -1) {
                                                          await AppCommonFunctions.scrollToKey(ref.lsKey);
                                                          return;
                                                        }
                                                      } else {
                                                        ref.isLifeButtonClicked.value = false;
                                                        ref.infoCheckRateAudioUrl.value = null;
                                                        ref.dsInfoVisible.value = false;
                                                        ref.ntInfoVisible.value = false;
                                                        ref.eiInfoVisible.value = false;
                                                        ref.psInfoVisible.value = false;
                                                        ref.ubInfoVisible.value = false;
                                                        ref.lsInfoVisible.value = false;
                                                        //  ref.infoCheckRateAudioUrl.value = null;
                                                        '${{
                                                          "ds": ref.dsSliderValue,
                                                          "nt": ref.ntSliderValue,
                                                          "ei": ref.eiSliderValue,
                                                          "ps": ref.psSliderValue,
                                                          "ub": ref.ubSliderValue,
                                                          "ls": ref.lsSliderValue,
                                                        }}'
                                                            .logD;
                                    
                                                        await AppNavigation.nextScreen(
                                                          context,
                                                          BlocProvider.value(
                                                            value: ref,
                                                            child: const ProgressCheckLifePage(),
                                                          ),
                                                        );
                                                      }
                                                    },
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
