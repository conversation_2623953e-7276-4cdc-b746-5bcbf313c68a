import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_pdf_opener_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drinking_help_me_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/us_drinking_helper_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_drug_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_emotional_wellbeing.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class ProgressCheckDrinkingPage extends StatelessWidget {
  const ProgressCheckDrinkingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AssessmentDrinkingCubit(),
      child: BlocBuilder<AssessmentDrinkingCubit, AssessmentDrinkingState>(
        builder: (ctx, state) {
          final ref = ctx.read<AssessmentDrinkingCubit>();
          final progressCheckCubit = BlocProvider.of<ProgressCheckCubit>(context);
          return PopScope(
            onPopInvokedWithResult: (didPop, result) {
              if (didPop) {
                progressCheckCubit.infoCheckLifeAudioUrl.value = CheckinLocaleKeys.lifeAudio.tr();
              }
            },
            child: ValueListenableBuilder(
              valueListenable: BlocProvider.of<ProgressCheckCubit>(context).infoDrinkingAudioUrl,
              builder: (context,value,child) {
                return AppScaffold(
                  resizeToAvoidBottomInset: true,
                  isAudioPanelVisible: BlocProvider.of<ProgressCheckCubit>(context).isAudioPannelDrinkingVisible,
                  infoAudioUrl: BlocProvider.of<ProgressCheckCubit>(context).infoDrinkingAudioUrl,
                  appBar: CommonAppBar(
                    prefixIcon: Icon(
                      Icons.logout,
                      size: AppSize.sp20,
                    ),
                
                    onSuffixTap: () {
                      if (BlocProvider.of<ProgressCheckCubit>(context).infoDrinkingAudioUrl.value.isNotEmptyAndNotNull) {
                        BlocProvider.of<ProgressCheckCubit>(context).isAudioPannelDrinkingVisible.value =
                            !BlocProvider.of<ProgressCheckCubit>(context).isAudioPannelDrinkingVisible.value;
                      }
                    },
                    onPrefixTap: () async {
                      await LogOutDialog.showLogOutDialog(context);
                    },
                
                    // onLogoutTap: () {},
                  ),
                  body: Column(
                    children: [
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Padding(
                              padding: const EdgeInsets.all(4),
                              child: CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                      child: ValueListenableBuilder(
                                        valueListenable: ref.isDrinkingButtonClick,
                                        builder: (context, value, child) {
                                          return ValueListenableBuilder(
                                            valueListenable: ref.isUnitAndDrinkingButton,
                                            builder: (context, value, child) {
                                              '>?>?>?>? locale = ${context.locale}'.logV;
                                              return Column(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      CheckInHeaderWidget(
                                                        subTitle: CheckinLocaleKeys.drinkingTitle.tr(),
                                                        onTap: () {
                                                          progressCheckCubit.progressCheckLifePaused.value = false;
                                                          progressCheckCubit.infoCheckLifeAudioUrl.value =
                                                              CheckinLocaleKeys.lifeAudio.tr();
                                                          Navigator.of(context).pop();
                                                        },
                                                      ),
                                                      // SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        CheckinLocaleKeys.drinkingSubtitle.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          color: context.themeColors.darkGreyColor,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionUnitsLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h18),
                                                      Column(
                                                        children: [
                                                          CustomAgeSelcetionWidget(
                                                            key: ref.unitKey,
                                                            controller: ref.unitsController,
                                                            onIncreaseTap: ref.increaseDrinkingValue,
                                                            onDecreaseTap: ref.decreaseDrinkingValue,
                                                            unitText: ref.unitsController.text,
                                                            isButtonClicked: ref.isUnitAndDrinkingButton.value,
                                                          ),
                                                          if (ref.isUnitAndDrinkingButton.value &&
                                                              (state.currentDrinkingValue == 0 &&
                                                                  state.currentDayValue > 0))
                                                            Padding(
                                                              padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                                              child: CustomErrorWidget(
                                                                errorMessgaeText:
                                                                    AssessmentLocaleKeys.errorsInvalidUnitsMessage.tr(),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h18),
                                                      if (context.locale == const Locale('en', 'GB') ||
                                                          context.locale == const Locale('en', 'UK'))
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.drinkHelpMeCalculate,
                                                          builder: (context, value, child) {
                                                            return Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                Padding(
                                                                  padding: EdgeInsets.only(
                                                                      left: AppSize.w34, right: AppSize.w28,),
                                                                  child: CustomRoundedButton(
                                                                    onTap: () {
                                                                      ref.drinkHelpMeCalculate.value =
                                                                          !ref.drinkHelpMeCalculate.value;
                                                                      ref
                                                                        ..clearDrinkHistory()
                                                                        ..initializeDrinkItems();
                                                                    },
                                                                    title: AssessmentLocaleKeys.drinkingButton.tr(),
                                                                    fillColor: context.themeColors.orangeColor,
                                                                  ),
                                                                ),
                                                                SpaceV(AppSize.h18),
                                                                ValueListenableBuilder(
                                                                  valueListenable: ref.drinkItemsNotifier,
                                                                  builder: (context, value, child) {
                                                                    return DrinkingHelpMeWidget(
                                                                      visible: ref.drinkHelpMeCalculate.value,
                                                                      drinkItems: ref.drinkItemsNotifier.value,
                                                                      titleText: state.drinkHelpMeCalculateTitleText,
                                                                      drinkItemName:
                                                                          state.selecteddrinkHelpMeCalculateTitleText,
                                                                      subTitleText:
                                                                          state.selecteddrinkHelpMeCalculateSubTitleText,
                                                                      percentageText: state
                                                                          .selecteddrinkHelpMeCalculatePercentageText,
                                                                      showBackArrow: state.showBackArrownQuestion,
                                                                      backonTap: ref.goBack,
                                                                      assessmentCubit: ref,
                                                                      onCloseTap: () {
                                                                        ref.drinkHelpMeCalculate.value = false;
                                                                      },
                                                                    );
                                                                  },
                                                                ),
                                                              ],
                                                            );
                                                          },
                                                        )
                                                      else if (context.locale == const Locale('en', 'US') ||
                                                          context.locale == const Locale('es', 'US'))
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.usdrinkHelpMeCalculate,
                                                          builder: (context, value, child) {
                                                            return Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                Padding(
                                                                  padding: EdgeInsets.only(
                                                                      left: AppSize.w34, right: AppSize.w28,),
                                                                  child: CustomRoundedButton(
                                                                    onTap: () {
                                                                      ref.usdrinkHelpMeCalculate.value =
                                                                          !ref.usdrinkHelpMeCalculate.value;
                                                                    },
                                                                    title: AssessmentLocaleKeys.drinkingButton.tr(),
                                                                    fillColor: context.themeColors.orangeColor,
                                                                  ),
                                                                ),
                                                                SpaceV(AppSize.h18),
                                                                Visibility(
                                                                  visible: ref.usdrinkHelpMeCalculate.value,
                                                                  child: const UsDrinkingHelperWidget(),
                                                                ),
                                                              ],
                                                            );
                                                          },
                                                        )
                                                      else if (context.locale == const Locale('en', 'CA') ||
                                                          context.locale == const Locale('fr', 'CA'))
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.cadrinkHelpMeCalculate,
                                                          builder: (context, value, child) {
                                                            return ValueListenableBuilder(
                                                              valueListenable: ref.isPdfLoading,
                                                              builder: (context, value, child) {
                                                                return Padding(
                                                                  padding: EdgeInsets.only(
                                                                      left: AppSize.w34, right: AppSize.w28,),
                                                                  child: value
                                                                      ? Center(
                                                                          child: CupertinoActivityIndicator(
                                                                            radius: AppSize.r8,
                                                                            color: context.themeColors.greenColor,
                                                                          ),
                                                                        )
                                                                      : CustomRoundedButton(
                                                                          onTap: () async {
                                                                            ref.cadrinkHelpMeCalculate.value =
                                                                                !ref.cadrinkHelpMeCalculate.value;
                                                                            await PdfFromUrlOpener.downloadAndOpenPDF(
                                                                              'assessment.drinking.pdf'.tr(),
                                                                              ref.isPdfLoading,
                                                                            );
                                                                          },
                                                                          title: AssessmentLocaleKeys.drinkingButton.tr(),
                                                                          fillColor: context.themeColors.orangeColor,
                                                                        ),
                                                                );
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      SpaceV(AppSize.h20),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionDaysLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h18),
                                                      Column(
                                                        children: [
                                                          CustomAgeSelcetionWidget(
                                                            key: ref.dayKey,
                                                            controller: ref.daysController,
                                                            onIncreaseTap: ref.increaseDaysValue,
                                                            onDecreaseTap: ref.decreaseDayValue,
                                                            unitText: ref.daysController.text,
                                                            isButtonClicked: ref.isUnitAndDrinkingButton.value,
                                                            inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  TextInputFormatter.withFunction(
                                        (oldValue, newValue) {
                                      if (newValue.text.isEmpty) {
                                        return newValue;
                                      }

                                      final value = int.tryParse(newValue.text);
                                      if (value != null && value <= 7) {
                                        return newValue;
                                      }

                                      return oldValue;
                                    },
                                  ),
                                ],
                                                          ),
                                                          if (ref.isUnitAndDrinkingButton.value &&
                                                              (state.currentDrinkingValue > 0 &&
                                                                  state.currentDayValue == 0))
                                                            Padding(
                                                              padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                                              child: CustomErrorWidget(
                                                                errorMessgaeText:
                                                                    AssessmentLocaleKeys.errorsInvalidDaysMessage.tr(),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionControlLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: progressCheckCubit.controlKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: progressCheckCubit.controlValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked:
                                                                  progressCheckCubit.isDrinkingButtonClicked.value,
                                                              options: progressCheckCubit.controlList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                progressCheckCubit.controlValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrinkingButtonClick.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionAnxiousLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: progressCheckCubit.anxiousKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: progressCheckCubit.anxiousValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked:
                                                                  progressCheckCubit.isDrinkingButtonClicked.value,
                                                              options: progressCheckCubit.anxiousList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                progressCheckCubit.anxiousValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrinkingButtonClick.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionWorryLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: progressCheckCubit.worryKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: progressCheckCubit.worryValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked:
                                                                  progressCheckCubit.isDrinkingButtonClicked.value,
                                                              options: progressCheckCubit.worryList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                progressCheckCubit.worryValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrinkingButtonClick.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: CheckinLocaleKeys.drinkingQuestionWillLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: progressCheckCubit.willKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: progressCheckCubit.willValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked:
                                                                  progressCheckCubit.isDrinkingButtonClicked.value,
                                                              options: progressCheckCubit.willList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                progressCheckCubit.willValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrinkingButtonClick.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText:
                                                            CheckinLocaleKeys.drinkingQuestionDifficultyLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      SizedBox(
                                                        key: progressCheckCubit.difficultKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: progressCheckCubit.difficultValue,
                                                          builder: (context, value, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked:
                                                                  progressCheckCubit.isDrinkingButtonClicked.value,
                                                              options: progressCheckCubit.difficultList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                progressCheckCubit.difficultValue.value = newValue ?? '';
                                                              },
                                                              isError: ref.isDrinkingButtonClick.value && value.isEmpty,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h50),
                                                    ],
                                                  ),
                                                  MultiValueListenableBuilder(
                                                    valueListenables: [
                                                      ref.unitsController, // TextEditingController
                                                      ref.daysController, //
                                                      progressCheckCubit.controlValue,
                                                      progressCheckCubit.anxiousValue,
                                                      progressCheckCubit.worryValue,
                                                      progressCheckCubit.willValue,
                                                      progressCheckCubit.difficultValue,
                                                    ],
                                                    builder: (context, List<dynamic> values, child) {
                                                      //  final unitsController = values[0].text;
                                                      // final daysController = values[1].text;
                                                      final controlValue = values[2];
                                                      final anxiousValue = values[3];
                                                      final worryValue = values[4];
                                                      final willValue = values[5];
                                                      final difficultValue = values[6];
                
                                                      final isButtonEnabled = controlValue != '' &&
                                                          anxiousValue != '' &&
                                                          worryValue != '' &&
                                                          willValue != '' &&
                                                          difficultValue != '';
                
                                                      return Column(
                                                        children: [
                                                          CustomButton(
                                                            title: CoreLocaleKeys.buttonsNext.tr(),
                                                            isBottom: true,
                                                            padding: EdgeInsets.zero,
                                                            color: context.themeColors.blueColor,
                                                            onTap: () async {
                                                              '${{
                                                                {
                                                                  "units": state.currentDrinkingValue,
                                                                  "days": state.currentDayValue,
                                                                  "control": progressCheckCubit.controlList
                                                                      .indexOf(progressCheckCubit.controlValue.value),
                                                                  "anxious": progressCheckCubit.anxiousList
                                                                      .indexOf(progressCheckCubit.anxiousValue.value),
                                                                  "worry": progressCheckCubit.worryList
                                                                      .indexOf(progressCheckCubit.worryValue.value),
                                                                  "will": progressCheckCubit.willList
                                                                      .indexOf(progressCheckCubit.willValue.value),
                                                                  "difficulty": progressCheckCubit.difficultList
                                                                      .indexOf(progressCheckCubit.difficultValue.value),
                                                                },
                                                              }}'
                                                                  .logD;
                                                              ref.isDrinkingButtonClick.value = true;
                                                              if ((state.currentDrinkingValue > 0 &&
                                                                      state.currentDayValue == 0) ||
                                                                  (state.currentDayValue > 0 &&
                                                                      state.currentDrinkingValue == 0)) {
                                                                ref.isUnitAndDrinkingButton.value = true;
                                                              } else {
                                                                ref.isUnitAndDrinkingButton.value = false;
                                                              }
                                                              if (state.currentDayValue > 0 &&
                                                                  state.currentDrinkingValue == 0) {
                                                                await AppCommonFunctions.scrollToKey(ref.unitKey);
                                                                return;
                                                              }
                                                              if (state.currentDrinkingValue > 0 &&
                                                                  state.currentDayValue == 0) {
                                                                await AppCommonFunctions.scrollToKey(ref.dayKey);
                                                                return;
                                                              }
                                                              if (progressCheckCubit.controlValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                    progressCheckCubit.controlKey,);
                                                                return;
                                                              }
                                                              if (progressCheckCubit.anxiousValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                    progressCheckCubit.anxiousKey,);
                                                                return;
                                                              }
                                                              if (progressCheckCubit.worryValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                    progressCheckCubit.worryKey,);
                                                                return;
                                                              }
                                                              if (progressCheckCubit.willValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                    progressCheckCubit.willKey,);
                                                                return;
                                                              }
                                                              if (progressCheckCubit.difficultValue.value.isEmpty) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                    progressCheckCubit.difficultKey,);
                                                                return;
                                                              }
      
                                                              'isButtonEnabled $isButtonEnabled'.logD;
                                                              'ass ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}'
                                                                  .logD;
                
                                                              if (!isButtonEnabled) {
                                                                // CustomSnackbar.showErrorSnackBar(
                                                                //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                                // );
                                                              } else {
                                                                progressCheckCubit
                                                                  ..currentDrinking = int.tryParse(ref.unitsController.text) ?? state.currentDrinkingValue//state.currentDrinkingValue
                                                                  ..currentDay = int.tryParse(ref.daysController.text) ?? state.currentDayValue;//state.currentDayValue;
                                                                if (Injector.instance<AppDB>()
                                                                        .userModel
                                                                        ?.user
                                                                        .assessment
                                                                        ?.rp
                                                                        ?.addictionCase ==
                                                                    2) {
                                                                  BlocProvider.of<ProgressCheckCubit>(context)
                                                                      .infoDrinkingAudioUrl
                                                                      .value = null;
                                                                  //  progressCheckCubit.infoDrugAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
                
                                                                  await AppNavigation.nextScreen(
                                                                    context,
                                                                    BlocProvider.value(
                                                                      value: progressCheckCubit,
                                                                      child: const ProgressCheckDrugPage(),
                                                                    ),
                                                                  );
                                                                } else {
                                                                  await AppNavigation.nextScreen(
                                                                    context,
                                                                    BlocProvider.value(
                                                                      value: progressCheckCubit,
                                                                      child: const ProgressCheckEmotionalWellbeing(),
                                                                    ),
                                                                  );
                                                                }
                                                              }
                                                            },
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
