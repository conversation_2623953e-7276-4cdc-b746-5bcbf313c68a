import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class ProgressCheckEmotionalWellbeing extends StatelessWidget {
  const ProgressCheckEmotionalWellbeing({super.key,this.days,this.unit,this.secondDrugDays,this.secondDrugUnit,this.thirdDrugDays,this.thirdDrugUnit});

  final String? unit;
  final String? days;
  final String? secondDrugUnit;
  final String? secondDrugDays;
  final String? thirdDrugUnit;
  final String? thirdDrugDays;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProgressCheckCubit, ProgressCheckState>(
      builder: (ctx, state) {
        final ref = ctx.read<ProgressCheckCubit>();
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
      if (didPop) {
        ref.isEmotionalButtonClicked.value = false;
          //ref.infoDrugAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
      }
    },
          child: ValueListenableBuilder(
            valueListenable: ref.infoWellBeingAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                resizeToAvoidBottomInset: true,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoWellBeingAudioUrl,
                appBar: CommonAppBar(
                  prefixIcon: Icon(
                    Icons.logout,
                    size: AppSize.sp20,
                  ),
          
                  onSuffixTap: () {
                    if (ref.infoWellBeingAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                    }
                  },
                  onPrefixTap: () async {
                    await LogOutDialog.showLogOutDialog(context);
                  },
          
                  // onLogoutTap: () {},
                ),
                body: Column(
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Padding(
                            padding: const EdgeInsets.all(4),
                            child: CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                    child: ValueListenableBuilder(
                                      valueListenable: ref.isEmotionalButtonClicked,
                                      builder: (context, value, child) {
                                        return Column(
                                          children: [
                                            Column(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                CheckInHeaderWidget(
                                                  subTitle: CheckinLocaleKeys.eiTitle.tr(),
                                                  onTap: () {
                                                    //Todo: manage according selected screen
                                                    //  ref.infoAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
                                                    Navigator.of(context).pop();
                                                    ref.isEmotionalButtonClicked.value = false;
                                                  },
                                                ),
          
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.eiQuestionNervousLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
          
                                                SizedBox(
                                                  key: ref.eiNervousKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.eiNervousValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isEmotionalButtonClicked.value,
                                                        options: ref.eiNervousList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.eiNervousValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isEmotionalButtonClicked.value && value.isEmpty,
                                                                                                                        );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
          
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.eiQuestionWorryLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                //Todo : Added static text in all screen
                                                SizedBox(
                                                  key: ref.eiWorryKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.eiWorryValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isEmotionalButtonClicked.value,
                                                        options: ref.eiWorryList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.eiWorryValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isEmotionalButtonClicked.value && value.isEmpty,
                                                                                                                        );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
          
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.eiQuestionInterestLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                ValueListenableBuilder(
                                                  valueListenable: ref.eiInterestValue,
                                                  builder: (context, value, child) {
                                                    return CustomRadioListWidget(
                                                      isButtonClicked: ref.isEmotionalButtonClicked.value,
                                                      options: ref.eiInterestList,
                                                      selectedValue: value, // Safely access first item
                                                      onChanged: (newValue) {
                                                        ref.eiInterestValue.value = newValue ?? '';
                                                      },
                                                      isError: ref.isEmotionalButtonClicked.value && value.isEmpty,
                                  );
                                                  },
                                                ),
                                                SpaceV(AppSize.h30),
          
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.eiQuestionDownLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.eidownKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.eidownValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isEmotionalButtonClicked.value,
                                                        options: ref.eidownList,
                                                        selectedValue: value,
                                                        onChanged: (newValue) {
                                                          ref.eidownValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isEmotionalButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
          
                                                SpaceV(AppSize.h50),
                                              ],
                                            ),
                                            MultiValueListenableBuilder(
                                              valueListenables: [
                                                ref.eiNervousValue,
                                                ref.eiWorryValue,
                                                ref.eiInterestValue,
                                                ref.eidownValue,
                                              ],
                                              builder: (context, List<dynamic> values, child) {
                                                final qualityValue = values[0];
                                                final healthValue = values[1];
                                                final activitiesValue = values[2];
                                                final relationshipsValue = values[3];
          
                                                final isButtonEnabled = qualityValue != '' &&
                                                    healthValue != '' &&
                                                    activitiesValue != '' &&
                                                    relationshipsValue != '';
          
                                                return Column(
                                                  children: [
                                                    CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      isBottom: true,
                                                      inProgress: state.isApiLoading,
                                                      color: context.themeColors.blueColor,
                                                      onTap: () async {
                                                        '${{
                                                          "ei": {
                                                            "nervous":
                                                                ref.eiNervousList.indexOf(ref.eiNervousValue.value),
                                                            "worry": ref.eiWorryList.indexOf(ref.eiWorryValue.value),
                                                            "interest":
                                                                ref.eiInterestList.indexOf(ref.eiInterestValue.value),
                                                            "down": ref.eidownList.indexOf(ref.eidownValue.value),
                                                          },
                                                        }}'
                                                            .logD;
                                                        ref.isEmotionalButtonClicked.value = true;
                                                        if (ref.eiNervousValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.eiNervousKey);
                                                          return;
                                                        }
                                                        if (ref.eiWorryValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.eiWorryKey);
                                                          return;
                                                        }
                                                        if (ref.eiInterestValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.eiInterestKey);
                                                          return;
                                                        }
                                                        if (ref.eidownValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.eidownKey);
                                                          return;
                                                        }
          
                                                        if (!isButtonEnabled) {
                                                          // CustomSnackbar.showErrorSnackBar(
                                                          //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                          // );
                                                        } else {
                                                          '>?>?>? ref.drugFirstList.drug = ${ref.drugFirstList.drug}'.logV;
                                                          'ref.drugFirstList${ref.drugFirstList.drug}'.logD;
                                                          '>?>?>?>? before api'.logV;
                                                          
                                                          await ref.checkInAPI(
                                                            rate: {
                                                              'ds': ref.dsSliderValue.value,
                                                              'nt': ref.ntSliderValue.value,
                                                              'ei': ref.eiSliderValue.value,
                                                              'ps': ref.psSliderValue.value,
                                                              'ub': ref.ubSliderValue.value,
                                                              'ls': ref.lsSliderValue.value,
                                                            },
                                                            life: {
                                                              'quality':
                                                                  ref.qualityList.indexOf(ref.qualityValue.value),
                                                              'health': ref.healthList.indexOf(ref.healthValue.value),
                                                              'activities':
                                                                  ref.activitiesList.indexOf(ref.activitiesValue.value),
                                                              'relationships': ref.relationshipsList
                                                                  .indexOf(ref.relationshipsValue.value),
                                                              'work': ref.workList.indexOf(ref.workValue.value),
                                                              'difficulties': ref.lifeDifficultySliderValue.value,
                                                              'rate': ref.lifeRateSliderValue.value,
                                                            },
                                                            ei: {
                                                              'nervous':
                                                                  ref.eiNervousList.indexOf(ref.eiNervousValue.value),
                                                              'worry': ref.eiWorryList.indexOf(ref.eiWorryValue.value),
                                                              'interest':
                                                                  ref.eiInterestList.indexOf(ref.eiInterestValue.value),
                                                              'down': ref.eidownList.indexOf(ref.eidownValue.value),
                                                            },
                                                            drinking: (Injector.instance<AppDB>()
                                                                        .userModel
                                                                        ?.user
                                                                        .assessment
                                                                        ?.rp
                                                                        ?.addictionCase !=
                                                                    1)
                                                                ? {
                                                                    'units': ref.currentDrinking,
                                                                    'days': ref.currentDay,
                                                                    'control':
                                                                        ref.controlList.indexOf(ref.controlValue.value),
                                                                    'anxious':
                                                                        ref.anxiousList.indexOf(ref.anxiousValue.value),
                                                                    'worry':
                                                                        ref.worryList.indexOf(ref.worryValue.value),
                                                                    'will': ref.willList.indexOf(ref.willValue.value),
                                                                    'difficulty': ref.difficultList
                                                                        .indexOf(ref.difficultValue.value),
                                                                  }
                                                                : {},
                                                            drug: ref.drugFirstList.drug != null
                                                                ? {
                                                                    '${ref.drugFirstList.drug}': {
                                                                      'drug': ref.drugFirstList.drug,
                                                                      'unit': ref.drugFirstList.unit,
                                                                      'amount': unit ?? 0,//ref.currentdrugunitsValue,
                                                                      'frequency': days ?? 0,//ref.currentdrugfreeDaysValue,
                                                                      'control': ref.drugControlList
                                                                          .indexOf(ref.drugControlValue.value),
                                                                      'anxious': ref.druganxiousList
                                                                          .indexOf(ref.drugAnxiousValue.value),
                                                                      'will': ref.drugwillList
                                                                          .indexOf(ref.drugWillValue.value),
                                                                      'difficulty': ref.drugdifficultyList
                                                                          .indexOf(ref.drugDifficultyValue.value),
                                                                      'worry': ref.drugworryList
                                                                          .indexOf(ref.drugWorryValue.value),
                                                                    },
                                                                    if (ref.drugSecondist != null)
                                                                      '${ref.drugSecondist?.drug}': {
                                                                        'drug': ref.drugSecondist?.drug,
                                                                        'unit': ref.drugSecondist?.unit,
                                                                        'amount': secondDrugUnit ?? 0,//ref.secondDrugunitsValue,
                                                                        'frequency': secondDrugDays ?? 0,//ref.secondDrugfreeDaysValue,
                                                                      },
                                                                    if (ref.drugThirdist != null)
                                                                      '${ref.drugThirdist?.drug}': {
                                                                        'drug': ref.drugThirdist?.drug,
                                                                        'unit': ref.drugThirdist?.unit,
                                                                        'amount': thirdDrugUnit ?? 0,//ref.thirdDrugunitsValue,
                                                                        'frequency': thirdDrugDays ?? 0,//ref.thirdDrugfreeDaysValue,
                                                                      },
                                                                  }
                                                                : {},
                                                            context: context,
                                                            ref: ref,
                                                          );
                                                        }
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                  ],
                                                );
                                              },
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
