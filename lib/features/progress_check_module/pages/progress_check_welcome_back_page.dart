import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_rate_page.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProgressCheckWelcomeBackPage extends StatefulWidget {
  const ProgressCheckWelcomeBackPage({super.key, this.recoveryNotRequired = true, this.isFromDashboardPage = false});
  final bool recoveryNotRequired;
  final bool isFromDashboardPage;

  @override
  State<ProgressCheckWelcomeBackPage> createState() => _ProgressCheckWelcomeBackPageState();
}

class _ProgressCheckWelcomeBackPageState extends State<ProgressCheckWelcomeBackPage> {
  late ProgressCheckCubit cubit;
  @override
  void initState() {
    super.initState(); // Always call super.initState() first

    cubit = ProgressCheckCubit();
    if (!widget.isFromDashboardPage) {
      cubit.initalAudioData(context);
    }

    if (widget.isFromDashboardPage) {
      //  cubit.infoAudioUrl.value = null;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppNavigation.nextScreen(
          context,
          BlocProvider.value(
            value: cubit, // Use the local instance of cubit
            child: ProgressCheckRatePage(
              isFromDashboardPage: widget.isFromDashboardPage,
            ),
          ),
        );
      });
    }
  }


  @override
  Widget build(BuildContext context) {
    '///hy'.logV;
    return BlocProvider.value(
      value: cubit,
      child: BlocListener<ProgressCheckCubit, ProgressCheckState>(
        listener: (context1, state) {
          'isFromDashboardPage ${widget.isFromDashboardPage}'.logE;
          final ref = context1.read<ProgressCheckCubit>();

          if (widget.isFromDashboardPage) {}
        },
        child: BlocBuilder<ProgressCheckCubit, ProgressCheckState>(
          builder: (ctx, state) {
            final ref = ctx.read<ProgressCheckCubit>();
            Injector.instance<AppDB>().userModel?.user.assessment?.rp?.specialAddiction?.toString().logD;

            return ValueListenableBuilder(
              valueListenable: ref.infoAudioUrl,
              builder: (context, value, child) {
                return AppScaffold(
                  resizeToAvoidBottomInset: true,
                  isAudioPanelVisible: ref.isAudioPannelVisible,
                  infoAudioUrl: ref.infoAudioUrl,
                  appBar: CommonAppBar(
                    prefixIcon: Icon(
                      Icons.logout,
                      size: AppSize.sp20,
                    ),
            
                    onSuffixTap: () {
                      if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                      }
                    },
                    onPrefixTap: () async {
                      await LogOutDialog.showLogOutDialog(context);
                    },
                    // onLogoutTap: () {},
                  ),
                  body: Column(
                    children: [
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Padding(
                              padding: EdgeInsets.only(
                                right: AppSize.w4,
                              ),
                              child: CustomRawScrollbar(
                                child: Padding(
                                  padding: EdgeInsets.only(right: AppSize.w8, left: AppSize.w8, top: AppSize.w8),
                                  child: ListView(
                                    children: [
                                      ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding:
                                              EdgeInsets.symmetric(horizontal: AppSize.w16, vertical: AppSize.h16),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  //  SpaceV(AppSize.h18),
                                                  AppTextWidget(
                                                    CoreLocaleKeys.titlesCheckin.tr(),
                                                    style: context.textTheme.titleSmall?.copyWith(
                                                      fontWeight: FontWeight.w500,
                                                      fontSize: AppSize.sp18,
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h4),
                                                  AppTextWidget(
                                                    CheckinLocaleKeys.welcomeTitle.tr(),
                                                    style: context.textTheme.titleSmall
                                                        ?.copyWith(fontWeight: FontWeight.w500),
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  const CustomDivider(),
                                                  SpaceV(AppSize.h20),
                                                  if (widget.recoveryNotRequired)
                                                    AppTextWidget(
                                                      (DynamicAssetLoader.getNestedValue(
                                                        CheckinLocaleKeys.welcomeTextOptional,
                                                        context,
                                                      ) as List)
                                                          .join('\n\n'),
                                                      style: context.textTheme.titleSmall,
                                                    )
                                                  else
                                                    AppTextWidget(
                                                      (DynamicAssetLoader.getNestedValue(
                                                        CheckinLocaleKeys.welcomeTextNonOptional,
                                                        context,
                                                      ) as List)
                                                          .join('\n\n'),
                                                      style: context.textTheme.titleSmall,
                                                    ),
                                                  SpaceV(AppSize.h20),
                                                ],
                                              ),
                                              if (widget.recoveryNotRequired)
                                                SafeArea(
                                                  child: CustomYesNoButton(
                                                    padding: EdgeInsets.zero,
                                                    exitText: CoreLocaleKeys.buttonsNo.tr(),
                                                    agreeText: CoreLocaleKeys.buttonsYes.tr(),
                                                    isYesNoButton: true,
                                                    onTapYes: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        BlocProvider.value(
                                                          value: ref,
                                                          child: const ProgressCheckRatePage(),
                                                        ),
                                                      );
                                                    },
                                                    onTapNo: () {
                                                      ref.infoAudioUrl.value = null;
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        BlocProvider.value(value: ref, child: const MyDiagramPage()),
                                                      );
                                                    },
                                                  ),
                                                )
                                              else
                                                SafeArea(
                                                  child: CustomButton(
                                                    padding: EdgeInsets.zero,
                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                    isBottom: true,
                                                    color: context.themeColors.blueColor,
                                                    onTap: () {
                                                      ref.infoAudioUrl.value = null;
            
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        BlocProvider.value(
                                                          value: ref,
                                                          child: const ProgressCheckRatePage(),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
