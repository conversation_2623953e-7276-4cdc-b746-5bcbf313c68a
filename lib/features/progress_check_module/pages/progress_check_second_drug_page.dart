import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_emotional_wellbeing.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_third_drug_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProgressCheckSecondDrugPage extends StatelessWidget {
  const ProgressCheckSecondDrugPage({required this.progressCheckCubit, super.key});
  final ProgressCheckCubit progressCheckCubit;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
      builder: (ctx, state) {
        final ref = progressCheckCubit;
        final drugCubit = ctx.read<AssessmentDrugCubit>();
        final secondDrug = state.drugDetailList[1];
    
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
      if (didPop) {
          ref.infoDrugAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
      }
    },
          child: AppScaffold(
            resizeToAvoidBottomInset: true,
            appBar: CommonAppBar(
              prefixIcon: Icon(
                Icons.logout,
                size: AppSize.sp20,
              ),
          
              onSuffixTap: () {
                // if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                //   ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                // }
              },
              onPrefixTap: () async {
                await LogOutDialog.showLogOutDialog(context);
              },
          
              // onLogoutTap: () {},
            ),
            body: Column(
              children: [
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return Padding(
                        padding: const EdgeInsets.all(4),
                        child: CustomRawScrollbar(
                          child: SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: constraints.maxHeight),
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                child: ValueListenableBuilder(
                                  valueListenable: ref.isDrinkingButtonClicked,
                                  builder: (context, value, child) {
                                    return Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            CheckInHeaderWidget(subTitle: CheckinLocaleKeys.drugsTitle.tr(),),
                                            AppTextWidget(
                                              CheckinLocaleKeys.drugsSubtitle.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                color: context.themeColors.darkGreyColor,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            QuestionRowWidget(
                                              questionText: AppCommonFunctions.getFormattedTranslation(
                                                CheckinLocaleKeys.drugsQuestionAmountLabel,
                                                {
                                                  'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              secondDrug.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                  'unit': drugCubit.formatString(secondDrug.unit?? ''),
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h10),
                                            CustomAgeSelcetionWidget(
                                              controller: drugCubit.drugSecondGoalUnitsController,
                                              onIncreaseTap: () {
                                                drugCubit.secondIncreaseDrugGoalUnitsValue(
                                                  drugCubit.getDrugValueForKey(
                                                    secondDrug.drug ?? '',
                                                  ),
                                                  secondDrug.unit,
                                                );
                                              },
                                              onDecreaseTap: () {
                                                drugCubit.secondDecreaseDrugGoalUnitsValue(
                                                  drugCubit.getDrugValueForKey(
                                                    secondDrug.drug ?? '',
                                                  ),
                                                  secondDrug.unit,
                                                );
                                              },
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText: AppCommonFunctions.getFormattedTranslation(
                                                CheckinLocaleKeys.drugsQuestionFrequencyLabel,
                                                {
                                                  'drug': drugCubit.formatString(drugCubit.getFormattedDrugName(
              secondDrug.drug ?? '',
              DrugAndUnitList.drugList,
           ) ?? '',),
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h10),
                                            CustomAgeSelcetionWidget(
                                              inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly,
                                            TextInputFormatter.withFunction(
                                              (oldValue, newValue) {
                                                if (newValue.text.isEmpty) {
                                                  return newValue;
                                                }

                                                final value =
                                                    int.tryParse(newValue.text);
                                                if (value != null &&
                                                    value <= 7) {
                                                  return newValue;
                                                }

                                                return oldValue;
                                              },
                                            ),
                                          ],
                                              controller: drugCubit.drugSecondGoalFreeDayController,
                                              onIncreaseTap: drugCubit.secondIncreaseDrugGoalFreeDayValue,
                                              onDecreaseTap: drugCubit.secondDecreaseDrugFreeDayValue,
                                            ),
                                            SpaceV(AppSize.h50),
                                          ],
                                        ),
                                        Column(
                                          children: [
                                            CustomButton(
                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                              isBottom: true,
                                              padding: EdgeInsets.zero,
                                              color: context.themeColors.blueColor,
                                              onTap: () {
                                                ref.drugSecondist = state.drugDetailList[1];
                                                'ref.drugSecondist  ${ref.drugSecondist}'.logV;
                                                'ref.drugSecondist  ${state.drugDetailList.length}'.logV;
          
                                                if (state.drugDetailList.length >= 3) {
                                                  AppNavigation.nextScreen(
                                                    context,
                                                    BlocProvider.value(
                                                      value: drugCubit,
                                                      child: ProgressCheckThirdDrugPage(
                                                        progressCheckCubit: ref,
                                                      ),
                                                    ),
                                                  );
                                                } else {
                                                  AppNavigation.nextScreen(
                                                    context,
                                                    BlocProvider.value(
                                                      value: ref,
                                                      child: ProgressCheckEmotionalWellbeing(
                                                        unit: drugCubit.drugFirstGoalUnitsController.text,
                                                        days: drugCubit.drugFirstGoalFreeDayController.text,
                                                        secondDrugUnit: drugCubit.drugSecondGoalUnitsController.text,
                                                        secondDrugDays: drugCubit.drugSecondGoalFreeDayController.text,
                                                      ),
                                                    ),
                                                  );
                                                }
          
                                                // '${{
                                                //   "cocaine": {
                                                //     "drug": secondDrug.drug,
                                                //     "unit": secondDrug.unit,
                                                //     "amount": state.secondDrugunitsValue,
                                                //     "frequency": state.secondDrugfreeDaysValue,
                                                //   },
                                                // }}'
                                                //     .logD;
                                                // ref.secondDrugfreeDaysValue = state.secondDrugfreeDaysValue;
                                                // ref.secondDrugunitsValue = state.secondDrugunitsValue;
                                              },
                                            ),
                                            SpaceV(
                                              AppSize.h20,
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
