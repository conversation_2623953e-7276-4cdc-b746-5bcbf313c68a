import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/progress_check_module/cubit/progress_check_cubit.dart';
import 'package:breakingfree_v2/features/progress_check_module/keys/checkin_locale_keys.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_drinking_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_drug_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_header_widget.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/check_in_slider_widget.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class ProgressCheckLifePage extends StatelessWidget {
  const ProgressCheckLifePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProgressCheckCubit, ProgressCheckState>(
      builder: (ctx, state) {
        final ref = ctx.read<ProgressCheckCubit>();
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              ref.isLifeButtonClicked.value = false;
              ref.lifeRateInfoVisible.value = false;
              ref.lifeDifficultyInfoVisible.value = false;
              ref.lifeRateInfoVisible.value = false;
              ref.lifeDifficultyInfoVisible.value = false;
              ref.infoCheckRateAudioUrl.value = CheckinLocaleKeys.rateAudio.tr();
              ref.progressCheckRatePaused.value = false;
            }
          },
          child: ValueListenableBuilder(
            valueListenable: ref.infoCheckLifeAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                resizeToAvoidBottomInset: true,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoCheckLifeAudioUrl,
                isManuallyPaused: ref.progressCheckLifePaused,
                appBar: CommonAppBar(
                  prefixIcon: Icon(
                    Icons.logout,
                    size: AppSize.sp20,
                  ),
    
                  onSuffixTap: () {
                    if (ref.infoCheckLifeAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                    }
                  },
                  onPrefixTap: () async {
                    await LogOutDialog.showLogOutDialog(context);
                  },
    
                  // onLogoutTap: () {},
                ),
                body: Column(
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Padding(
                            padding: const EdgeInsets.all(4),
                            child: CustomRawScrollbar(
                              child: SingleChildScrollView(
                                key: const PageStorageKey('progress_check_scroll'),
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(horizontal: AppSize.w22),
                                    child: ValueListenableBuilder(
                                      valueListenable: ref.isLifeButtonClicked,
                                      builder: (context, isLifeButtonClicked, child) {
                                        return Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              children: [
                                                CheckInHeaderWidget(
                                                  subTitle: CheckinLocaleKeys.lifeTitle.tr(),
                                                  onTap: () {
                                                    ref.isLifeButtonClicked.value = false;
                                                    ref.lifeRateInfoVisible.value = false;
                                                    ref.lifeDifficultyInfoVisible.value = false;
                                                    ref.lifeRateInfoVisible.value = false;
                                                    ref.lifeDifficultyInfoVisible.value = false;
                                                    ref.infoCheckRateAudioUrl.value =
                                                        CheckinLocaleKeys.rateAudio.tr();
                                                    Navigator.of(context).pop();
                                                  },
                                                ),
                                                // SpaceV(AppSize.h20),
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.lifeQuestionQualityLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.qualityKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.qualityValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isLifeButtonClicked.value,
                                                        options: ref.qualityList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.qualityValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isLifeButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.lifeQuestionHealthLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.healthKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.healthValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isLifeButtonClicked.value,
                                                        options: ref.healthList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.healthValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isLifeButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.lifeQuestionActivitiesLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.activitiesKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.activitiesValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isLifeButtonClicked.value,
                                                        options: ref.activitiesList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.activitiesValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isLifeButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.lifeQuestionRelationshipsLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.relationshipsKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.relationshipsValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isLifeButtonClicked.value,
                                                        options: ref.relationshipsList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.relationshipsValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isLifeButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText: CheckinLocaleKeys.lifeQuestionWorkLabel.tr(),
                                                ),
                                                SpaceV(AppSize.h4),
                                                SizedBox(
                                                  key: ref.workKey,
                                                  child: ValueListenableBuilder(
                                                    valueListenable: ref.workValue,
                                                    builder: (context, value, child) {
                                                      return CustomRadioListWidget(
                                                        isButtonClicked: ref.isLifeButtonClicked.value,
                                                        options: ref.workList,
                                                        selectedValue: value, // Safely access first item
                                                        onChanged: (newValue) {
                                                          ref.workValue.value = newValue ?? '';
                                                        },
                                                        isError: ref.isLifeButtonClicked.value && value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                CheckInSliderWidget(
                                                  oninfoTap: () {
                                                    ref.lifeDifficultyInfoVisible.value =
                                                        !ref.lifeDifficultyInfoVisible.value;
                                                    if (ref.lifeDifficultyInfoVisible.value) {
                                                      ref.progressCheckRatePaused.value = false;
                                                      ref.infoCheckLifeAudioUrl.value =
                                                          CheckinLocaleKeys.lifeQuestionDifficultiesInfoAudio.tr();
                                                    } else {
                                                      ref.progressCheckRatePaused.value = true;
                                                      ref.lifeDifficultyInfoVisible.value = false;
                                                      ref.infoCheckLifeAudioUrl.value =
                                                          CheckinLocaleKeys.lifeAudio.tr();
                                                    }
                                                  },
                                                  isMauallyPaused: ref.progressCheckLifePaused,
                                                  isReverseGradient: false,
                                                  onCloseTap: () {
                                                    ref.progressCheckLifePaused.value = true;
                                                    ref.lifeDifficultyInfoVisible.value = false;
                                                    ref.infoCheckLifeAudioUrl.value =
                                                        CheckinLocaleKeys.lifeAudio.tr();
                                                  },
                                                  infoAudioUrl: ref.infoCheckLifeAudioUrl,
                                                  bodyText: CheckinLocaleKeys.lifeQuestionDifficultiesInfoText.tr(),
                                                  questionText: CheckinLocaleKeys.lifeQuestionDifficultiesLabel.tr(),
                                                  infoVisible: ref.lifeDifficultyInfoVisible,
                                                  audioUrl: CheckinLocaleKeys.lifeQuestionDifficultiesInfoAudio.tr(),
                                                  ref: ref,
                                                  sliderValue: ref.lifeDifficultySliderValue,
                                                  firstTest: (DynamicAssetLoader.getNestedValue(
                                                    CheckinLocaleKeys.lifeQuestionDifficultiesSliderLabels,
                                                    context,
                                                  ) as List)
                                                      .cast<String>()
                                                      .first,
                                                  secondTest: (DynamicAssetLoader.getNestedValue(
                                                    CheckinLocaleKeys.lifeQuestionDifficultiesSliderLabels,
                                                    context,
                                                  ) as List)
                                                      .cast<String>()
                                                      .last,
                                                  isClick: ref.isLifeButtonClicked.value,
                                                ),
                                                SpaceV(AppSize.h30),
                                                CheckInSliderWidget(
                                                  oninfoTap: () {
                                                    ref.lifeRateInfoVisible.value = !ref.lifeRateInfoVisible.value;
                                                    if (ref.lifeRateInfoVisible.value) {
                                                      ref.progressCheckRatePaused.value = false;
                                                      ref.infoCheckLifeAudioUrl.value =
                                                          CheckinLocaleKeys.lifeQuestionRateInfoAudio.tr();
                                                    } else {
                                                      ref.progressCheckRatePaused.value = true;
                                                      ref.lifeRateInfoVisible.value = false;
                                                      ref.infoCheckLifeAudioUrl.value =
                                                          CheckinLocaleKeys.lifeAudio.tr();
                                                    }
                                                  },
                                                  isMauallyPaused: ref.progressCheckLifePaused,
                                                  isReverseGradient: false,
                                                  onCloseTap: () {
                                                    ref.progressCheckLifePaused.value = true;
    
                                                    ref.lifeRateInfoVisible.value = false;
                                                    ref.infoCheckLifeAudioUrl.value =
                                                        CheckinLocaleKeys.lifeAudio.tr();
                                                  },
                                                  infoAudioUrl: ref.infoCheckLifeAudioUrl,
                                                  bodyText: CheckinLocaleKeys.lifeQuestionRateInfoText.tr(),
                                                  questionText: CheckinLocaleKeys.lifeQuestionRateLabel.tr(),
                                                  infoVisible: ref.lifeRateInfoVisible,
                                                  ref: ref,
                                                  audioUrl: CheckinLocaleKeys.lifeQuestionRateInfoAudio.tr(),
                                                  sliderValue: ref.lifeRateSliderValue,
                                                  firstTest: (DynamicAssetLoader.getNestedValue(
                                                    CheckinLocaleKeys.lifeQuestionRateSliderLabels,
                                                    context,
                                                  ) as List)
                                                      .cast<String>()
                                                      .first,
                                                  secondTest: (DynamicAssetLoader.getNestedValue(
                                                    CheckinLocaleKeys.lifeQuestionRateSliderLabels,
                                                    context,
                                                  ) as List)
                                                      .cast<String>()
                                                      .last,
                                                  isClick: ref.isLifeButtonClicked.value,
                                                ),
                                                SpaceV(AppSize.h50),
                                              ],
                                            ),
                                            MultiValueListenableBuilder(
                                              valueListenables: [
                                                ref.qualityValue,
                                                ref.healthValue,
                                                ref.activitiesValue,
                                                ref.relationshipsValue,
                                                ref.workValue,
                                                ref.lifeDifficultySliderValue,
                                                ref.lifeRateSliderValue,
                                              ],
                                              builder: (context, List<dynamic> values, child) {
                                                final qualityValue = values[0];
                                                final healthValue = values[1];
                                                final activitiesValue = values[2];
                                                final relationshipsValue = values[3];
                                                final workValue = values[4];
                                                final lifeDifficultySliderValue = values[5];
                                                final lifeRateSliderValue = values[6];
    
                                                final isButtonEnabled = lifeDifficultySliderValue != -1 &&
                                                    lifeRateSliderValue != -1 &&
                                                    qualityValue != '' &&
                                                    healthValue != '' &&
                                                    activitiesValue != '' &&
                                                    relationshipsValue != '' &&
                                                    workValue != '';
    
                                                return Column(
                                                  children: [
                                                    CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      isBottom: true,
                                                      color: context.themeColors.blueColor,
                                                      onTap: () async {
                                                        '${{
                                                          {
                                                            "quality":
                                                                ref.qualityList.indexOf(ref.qualityValue.value),
                                                            "health": ref.healthList.indexOf(ref.healthValue.value),
                                                            "activities":
                                                                ref.activitiesList.indexOf(ref.activitiesValue.value),
                                                            "relationships": ref.relationshipsList
                                                                .indexOf(ref.relationshipsValue.value),
                                                            "work": ref.workList.indexOf(ref.workValue.value),
                                                            "difficulties": ref.lifeDifficultySliderValue,
                                                            "rate": ref.lifeRateSliderValue,
                                                          },
                                                        }}'
                                                            .logD;
                                                        ref.isLifeButtonClicked.value = true;
    
                                                        if (ref.qualityValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.qualityKey);
                                                          return;
                                                        }
                                                        if (ref.healthValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.healthKey);
                                                          return;
                                                        }
                                                        if (ref.activitiesValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.activitiesKey);
                                                          return;
                                                        }
                                                        if (ref.relationshipsValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.relationshipsKey);
                                                          return;
                                                        }
                                                        if (ref.workValue.value.isEmpty) {
                                                          await AppCommonFunctions.scrollToKey(ref.workKey);
                                                          return;
                                                        }
                                                        if (!isButtonEnabled) {
                                                          // CustomSnackbar.showErrorSnackBar(
                                                          //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                          // );
                                                        } else {
                                                          ref.infoCheckLifeAudioUrl.value = null;
                                                          ref.lifeRateInfoVisible.value = false;
                                                          ref.lifeDifficultyInfoVisible.value = false;
                                                          if (Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .assessment
                                                                  ?.rp
                                                                  ?.addictionCase ==
                                                              0) {
                                                            ref.infoDrinkingAudioUrl.value =
                                                                CheckinLocaleKeys.drinkingAudio.tr();
    
                                                            AppNavigation.nextScreen(
                                                              context,
                                                              BlocProvider.value(
                                                                value: ref,
                                                                child: const ProgressCheckDrinkingPage(),
                                                              ),
                                                            );
                                                          } else if (Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .assessment
                                                                  ?.rp
                                                                  ?.addictionCase ==
                                                              1) {
                                                            {
                                                              //  ref.infoDrugAudioUrl.value = CheckinLocaleKeys.drugsAudio.tr();
    
                                                              await AppNavigation.nextScreen(
                                                                context,
                                                                BlocProvider.value(
                                                                  value: ref,
                                                                  child: const ProgressCheckDrugPage(),
                                                                ),
                                                              );
                                                            }
                                                          } else {
                                                            ref.infoDrinkingAudioUrl.value =
                                                                CheckinLocaleKeys.drinkingAudio.tr();
    
                                                            await AppNavigation.nextScreen(
                                                              context,
                                                              BlocProvider.value(
                                                                value: ref,
                                                                child: const ProgressCheckDrinkingPage(),
                                                              ),
                                                            );
                                                          }
                                                        }
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                  ],
                                                );
                                              },
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
