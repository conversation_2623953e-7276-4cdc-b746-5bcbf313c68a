import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/pages/language_page.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class CountryWarningPage extends StatefulWidget {
  const CountryWarningPage({super.key});

  @override
  State<CountryWarningPage> createState() => _CountryWarningPageState();
}

class _CountryWarningPageState extends State<CountryWarningPage> {
  final ScrollController _scrollController = ScrollController();
  ValueNotifier<bool> isAtndTrigger = ValueNotifier(false);
  bool hasScrolledToBottom = false;
  String? _title;
  String _formattedHtmlContent = '';

  void _processContent() {
    final contentList = (DynamicAssetLoader.getNestedValue(
      AppLocaleKey.appLocaleSetupCountryWarning,
      navigatorKey.currentContext!,
    ) as List<dynamic>)
        .cast<String>();

    for (final line in contentList) {
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('!') && _title == null) {
        _title = trimmedLine.substring(1).trim(); // Extract the title (first heading)
      } else {
        _formattedHtmlContent += trimmedLine.startsWith('!')
            ? '<h1>${trimmedLine.substring(1).trim()}</h1>' // Convert remaining ! lines to <h1>
            : '<p>$trimmedLine</p>';
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _processContent();
    // Listen to scroll events to check if the user has scrolled to the bottom
    _scrollController.addListener(_checkIfScrolledToBottom);
  }

  void _checkIfScrolledToBottom() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
      hasScrolledToBottom = true;
      isAtndTrigger.value = true;
      log('At the bottom');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          //crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const CustomBackArrowButton(),
            Container(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
              child: Column(
                children: [
                  AppTextWidget(
                    _title ?? 'Default Title',
                    style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                  ),
                  SpaceV(AppSize.h10),
                  const CustomDivider(),
                  SpaceV(AppSize.h20),
                ],
              ),
            ),
            Expanded(
              child: CustomRawScrollbar(
                controller: _scrollController,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSize.w20,
                  ),
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    child: Html(
                      data: _formattedHtmlContent,
                      onLinkTap: (url, attributes, element) async {},
                      style: {
                        'h1': Style(
                          fontSize: FontSize(AppSize.sp18),
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.w600,
                        ),
                        'p': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                        'body': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                      },
                    ),
                  ),
                ),
              ),
            ),
            ValueListenableBuilder(
              valueListenable: isAtndTrigger,
              builder: (context, value, child) {
                return CustomYesNoButton(
                  isYesNoButton: true,
                  exitText: CoreLocaleKeys.buttonsExit.tr(),
                  agreeText: CoreLocaleKeys.buttonsAgree.tr(),
                  // inYesProgress: isAtndTrigger,
                  //   disableColor: !value ? Colors.grey : Colors.white,
                  onTapYes: () async {
                    await Navigator.of(context).pushReplacement(MaterialPageRoute<void>(builder: (context) => const LoginPage()));
                  },
                  onTapNo: () {
                    AppNavigation.previousScreen(context);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
