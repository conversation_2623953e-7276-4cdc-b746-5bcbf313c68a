import 'dart:io';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/cubit/language_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/pages/country_warning_page.dart';
// import 'package:breakingfree_v2/features/authentication_module/language_module/cubit/language_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/widgets/dropdown_selector.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/global_connectivity_checker.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LanguagePage extends StatefulWidget {
  const LanguagePage({super.key});

  @override
  State<LanguagePage> createState() => _LanguagePageState();
}

class _LanguagePageState extends State<LanguagePage> {
  bool _isSubmitting = false;
  @override
  Widget build(BuildContext context) {
          // context.read<LoginCubit>().isENLanguageLoading.value = false;
          //     context.read<LoginCubit>().isENLanguageLoading.value = true;
          //     '????? on back isENLanguageLoading.value = ${context.read<LoginCubit>().isENLanguageLoading.value}'.logV;
          //     '????? setLanguageLoader.value = ${context.read<LoginCubit>().hashCode}'.logD;
          //     '????? backk'.logD;
          //     //if (didPop) {
          //     '????? back'.logD;
                
          //      final selected = Injector.instance<AppDB>().selectedLangugae;
          
          // // Define fallback groups
          // final fallbackGroups = {
          //   'en-CA': ['en-CA', 'fr-CA'],
          //   'en-US': ['en-US', 'es-US'],
          // };
          
          // String? resolvedLang;
          
          // // First check fallbacks
          // for (final entry in fallbackGroups.entries) {
          //   if (entry.value.any((code) => selected?.contains(code) == true)) {
          //     resolvedLang = entry.key;
          //     break;
          //   }
          // }
          
          // // If no fallback match, do direct match
          // resolvedLang ??= ['en-AU', 'en-GB', 'en-CA', 'en-US']
          //     .firstWhere((lang) => selected?.contains(lang) == true,);
          
          // // Apply language if matched
          // if (resolvedLang != null) {
          //   'Resolved lang = $resolvedLang'.logD;
            
          //    //context.read<LoginCubit>().setLanguageByValue(context, resolvedLang);
            
          // }
          
          //       context.read<LoginCubit>().isENLanguageLoading.value = false;
          //       //await context.read<LoginCubit>().setLanguageIfExists(context,'en-AU');
          //       AppNavigation.replaceScreen(context, const LanguagePage());
          //     // No need to manually call Navigator.pop or AppNavigation
          //     //}
    return PopScope(
      canPop: Platform.isIOS,
        onPopInvoked: (didPop) {
          '????? backk'.logD;
          //if (didPop) {
          '????? back'.logD;
          if (!didPop) {
            exit(0);
          }
          // No need to manually call Navigator.pop or AppNavigation
          //}
        },
      child: BlocProvider(
        create: (context) => LanguageCubit()..fetchCountryApi(),
        child: BlocBuilder<LanguageCubit, LanguageState>(
          builder: (ctx, state) {
            final ref = ctx.read<LanguageCubit>();
            final bool isLoading = state.maybeWhen(
              loading: () => true,
              countryLoading: () => true,
              langugaeLoading: () => true,
              seetLanguageLoading: () => true,
              orElse: () => false,
            ) || _isSubmitting;
            return IgnorePointer(
              ignoring: isLoading,
              child: Scaffold(
                resizeToAvoidBottomInset: true,
                body: SafeArea(
                  bottom: false,
                  child: AppLoader(
                    isShowLoader: isLoading,
                    child: ValueListenableBuilder(
                      valueListenable: ref.isENLanguageLoading,
                      builder: (context, value, child)  {
                        return AppLoader(
                          isShowLoader: ref.isENLanguageLoading.value,
                          child: LayoutBuilder(
                            builder: (context, constrains) {
                              return SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                  child: ValueListenableBuilder(
                                    valueListenable: ref.isButtonClicked,
                                    builder: (context, value, child) {
                                      return Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  right: AppSize.w20,
                                                  left: AppSize.w20,
                                                  top: AppSize.h20,
                                                ),
                                                child: Column(
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        // if (context.locale.languageCode == 'en')
                                                        Assets.images.ukHeaderLogo.image(
                                                          width: double.infinity,
                                                        ),
                                                        // else
                                                        // Assets.images.headerLogoFr.image(
                                                        //   width: double.infinity,
                                                        // ),
                                                        SpaceV(AppSize.h40),
                                                        AppTextWidget(
                                                          AppLocaleKey.appLocaleSetupCountry.tr(),
                                                          style: context.textTheme.titleSmall
                                                              ?.copyWith(fontWeight: FontWeight.w600),
                                                        ),
                                                        SpaceV(AppSize.h6),
                                                        DropdownSelector(
                                                          //   selectedOption: ref.selectedCountry, // Preserve selected value
                                                          isError: ref.isButtonClicked.value && ref.selectedCountry.isEmpty,
                          
                                                          key: const Key('lang_selector'),
                                                          options: (ref.coutryModel?.countries
                                                                  ?.map(
                                                                    (e) => e.name,
                                                                  )
                                                                  .toList() ??
                                                              [])
                                                            ..sort(),
                                                          hintText: AppLocaleKey.appLocaleSetupChooseCountry.tr(),
                                                          onSelected: (p0) async{
                                                            ref.isButtonClicked.value = false;
                                                            '=p0 ====>$p0'.logV;
                                                            ref.languageModel.clear();
                                                            ref.selectedCountry = p0 as String;
                                                            final selectedCountry = ref.coutryModel?.countries?.firstWhere(
                                                              (element) => element.name == p0,
                                                            );
                                                            if (selectedCountry?.url?.isNotEmpty ?? false) {
                                                              EndPoints.baseUrl = '${selectedCountry!.url!}/api/';
                                                            }
                          
                                                            Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;
                          
                                                            await ref.fetchLanguageApi(context);
                                                            '????? language = ${Injector.instance<AppDB>().langugaeModel?.languages?.map((e) => e.value).contains('en-US')}'.logV;
                                                            await ref.fetchCountryApi();
      
                                                            'logs ===>${EndPoints.baseUrl}'.logV;
                                                            ref.isButtonClicked.value = false;
                                                          },
                                                        ),
                          
                                                        SpaceV(AppSize.h20),
                                                        if (ref.languageModel.isNotEmpty && ref.languageModel.length >= 2) ...[
                                                          AppTextWidget(
                                                            AppLocaleKey.appLocaleSetupLanguage.tr(),
                                                            style: context.textTheme.titleSmall
                                                                ?.copyWith(fontWeight: FontWeight.w600),
                                                          ),
                                                          SpaceV(AppSize.h6),
                                                          
                                                          // DropdownSelector(
                                                          //                       onSelected: (selectedLabel) async {
                                                          //                         // Log the selected value and corresponding language code
                                                          //                         'Selected Language: $selectedLabel'.logD;
                                                          //                         ref.isENLanguageLoading.value = true;
                                                          //                         final selectedLanguage = Injector.instance<AppDB>()
                                                          //                             .langugaeModel
                                                          //                             ?.languages
                                                          //                             ?.firstWhere(
                                                          //                               (e) => e.label == selectedLabel,
                                                          //                             );
                                        
                                                          //                         if (selectedLanguage != null) {
                                                          //                           // Log the value of the selected language
                                                          //                           'Language Value: ${selectedLanguage.value}'.logD;
                                        
                                                          //                           // Parse language and country code from the selected value (e.g., "en-US" to "en" and "US")
                                                          //                           final localeParts = selectedLanguage.value?.split(
                                                          //                             '-',
                                                          //                           );
                                                          //                           if (localeParts != null &&
                                                          //                               localeParts.length == 2) {
                                                          //                             final locale = Locale(
                                                          //                               localeParts[0],
                                                          //                               localeParts[1],
                                                          //                             );
                                        
                                                          //                             // Set the new locale
                                                          //                             await context.setLocale(
                                                          //                               locale,
                                                          //                             );
                                                          //                             final data1 = locale.toString().replaceAll(
                                                          //                                   '_',
                                                          //                                   '-',
                                                          //                                 );
                                                          //                             Injector.instance<AppDB>().selectedLangugae =
                                                          //                                 data1;
                                                          //                             ref.isENLanguageLoading.value = false;
                                                          //                             'Locale changed to: $locale'.logD;
                                                          //                           } else {
                                                          //                             ref.isENLanguageLoading.value = false;
                                                          //                             'Invalid language code format: ${selectedLanguage.value}'
                                                          //                                 .logD;
                                                          //                           }
                                                          //                           ref.isENLanguageLoading.value = false;
                                                          //                         }
                                                          //                       },
                                                          //                       width: MediaQuery.of(
                                                          //                             context,
                                                          //                           ).size.width *
                                                          //                           0.5,
                                                          //                       selectedOption: Injector.instance<AppDB>()
                                                          //                           .langugaeModel
                                                          //                           ?.languages
                                                          //                           ?.firstWhere(
                                                          //                             (element) =>
                                                          //                                 element.value ==
                                                          //                                 Injector.instance<AppDB>().selectedLangugae,
                                                          //                           )
                                                          //                           .label,
                                                          //                       options: Injector.instance<AppDB>()
                                                          //                               .langugaeModel
                                                          //                               ?.languages
                                                          //                               ?.map(
                                                          //                                 (e) => e.label,
                                                          //                               )
                                                          //                               .toList() ??
                                                          //                           [],
                                                          //                     ),
                          
                          
                                                          DropdownSelector(
                                                            isError: ref.isButtonClicked.value && ref.selectedLanguage.isEmpty,
                                                            options: ref.languages?.languages
                                                                    ?.map(
                                                                      (e) => e.label,
                                                                    )
                                                                    .toList() ??
                                                                [],
                                                            hintText: ref.coutryModel?.localisations?.chooseLanguage ??
                                                                'Choose a language',
                                                                onSelected: (selectedLabel) async {
                                                                  ref.isButtonClicked.value = false;
                                                                                  // Log the selected value and corresponding language code
                                                                                  'Selected Language: $selectedLabel'.logD;
                                                                                  ref.isENLanguageLoading.value = true;
                                                                                  final selectedLanguage = Injector.instance<AppDB>()
                                                                                      .langugaeModel
                                                                                      ?.languages
                                                                                      ?.firstWhere(
                                                                                        (e) => e.label == selectedLabel,
                                                                                      );
                                        
                                                                                  if (selectedLanguage != null) {
                                                                                    ref.selectedLanguage = selectedLanguage.value ?? "";
                                                                                    // Log the value of the selected language
                                                                                    'Language Value: ${selectedLanguage.value}'.logD;
                                        
                                                                                    // Parse language and country code from the selected value (e.g., "en-US" to "en" and "US")
                                                                                    final localeParts = selectedLanguage.value?.split(
                                                                                      '-',
                                                                                    );
                                                                                    if (localeParts != null &&
                                                                                        localeParts.length == 2) {
                                                                                      final locale = Locale(
                                                                                        localeParts[0],
                                                                                        localeParts[1],
                                                                                      );
                                        
                                                                                      // Set the new locale
                                                                                      await context.setLocale(
                                                                                        locale,
                                                                                      );
                                                                                      final data1 = locale.toString().replaceAll(
                                                                                            '_',
                                                                                            '-',
                                                                                          );
                                                                                      Injector.instance<AppDB>().selectedLangugae =
                                                                                          data1;
                                                                                      ref.isENLanguageLoading.value = false;
                                                                                      'Locale changed to: $locale'.logD;
                                                                                    } else {
                                                                                      ref.isENLanguageLoading.value = false;
                                                                                      'Invalid language code format: ${selectedLanguage.value}'
                                                                                          .logD;
                                                                                    }
                                                                                    ref.isENLanguageLoading.value = false;
                                                                                  }
                                                                                },
                                                                
                                                                
                                                            // onSelected: (p0) async {
                                                            //   'p0 $p0'.logV;
                          
                                                            //   final languageCode = ref.languages?.languages?.firstWhere(
                                                            //     (element) => element.label == p0,
                                                            //   );
                                                            //   // final languageCode = ref.languageModel.firstWhere(
                                                            //   //   (code) => ref.languageMap[code] == p0,
                                                            //   //   orElse: () => '', // Fallback if no match is found
                                                            //   // );
                                                            //   'languageCode $languageCode'.logV;
                                                            //   ref.selectedLanguage = languageCode?.value ?? '';
                                                            //   Injector.instance<AppDB>().selectedLangugae =
                                                            //       ref.selectedLanguage;
                          
                                                            //   ref.selectedLanguage.logD;
                                                            //   if (ref.selectedLanguage != '') {
                                                            //     ref.updateLnaguge();
                                                            //     // await context.setLocale(
                                                            //     //   Locale(
                                                            //     //     ref.selectedLanguage.split('-').first,
                                                            //     //     ref.selectedLanguage.split('-').last,
                                                            //     //   ),
                                                            //     // );
                                                            //     ref.initalLanguage();
                                                            //   } else {
                                                            //     // CustomSnackbar.showErrorSnackBar(
                                                            //     //   message: 'Please select coutry or language',
                                                            //     // );
                                                            //   }
                                                            // },
      
                                                            
                                                          ),
                          
                          
                                                        ],
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          SafeArea(
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                right: AppSize.w20,
                                                left: AppSize.w20,
                                                bottom: AppSize.h20,
                                              ),
                                              child: CustomButton(
                                                isShowArrowForward: false,
                                                isBottom: true,
                                                padding: EdgeInsets.zero,
                                                title: AppLocaleKey.appLocaleSetupSubmitButton.tr(),
                                                onTap: () async {
                                                  setState(() => _isSubmitting = true);
                                                  try {
                                                    ref.isButtonClicked.value = true;
                                                    final code = ref.getCountryDomain(ref.selectedCountry);
                                                    ref.selectedLanguage.logD;
                                                    code.logD;
                                                    '????? ref.selectedLanguage =${ref.selectedLanguage}'.logV;
                                                    if (ref.selectedLanguage != '') {
                                                      '===>${context.locale.toString().replaceAll('_', '-')}'.logV;
                                                      await EasyLocalization.of(context)!.setLocale(
                                                        Locale(
                                                          ref.selectedLanguage.split('-').first,
                                                          ref.selectedLanguage.split('-').last,
                                                        ),
                                                      );
                                                      await context.setLocale(
                                                        Locale(
                                                          ref.selectedLanguage.split('-').first,
                                                          ref.selectedLanguage.split('-').last,
                                                        ),
                                                      );
                                                      final countryWarningList = DynamicAssetLoader.getNestedValue(
                                                        AppLocaleKey.appLocaleSetupCountryWarning,
                                                        navigatorKey.currentContext!,
                                                      ) as List<dynamic>?;
                          
                                                      if (countryWarningList != null && countryWarningList.isNotEmpty) {
                                                        await Navigator.of(context).push(
                                                          MaterialPageRoute<void>(
                                                            builder: (context) => const CountryWarningPage(),
                                                          ),
                                                        );
                                                      } else {
                                                        await Navigator.of(context).push(
                                                          MaterialPageRoute<void>(builder: (context) => const LoginPage()),
                                                        );
                                                      }
                                                    } else {
                                                      //CustomSnackbar.showErrorSnackBar(message: 'Please select coutry or language');
                                                    }
                                                  } finally {
                                                    if (mounted) setState(() => _isSubmitting = false);
                                                  }
                                                },
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
