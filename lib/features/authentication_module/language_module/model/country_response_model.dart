class CoutryResponseDataModel {
  CoutryResponseDataModel({
    this.success,
    this.countries,
    this.localisations,
  });

  factory CoutryResponseDataModel.fromJson(Map<String, dynamic> json) => CoutryResponseDataModel(
        success: json['success'] as bool,
        countries: json['countries'] == null
            ? []
            : List<Country>.from(
                (json['countries'] as List<dynamic>).map((x) => Country.fromJson(Map<String, dynamic>.from(x as Map))),
              ),
        localisations: json['localisations'] == null
            ? null
            : Localisations.fromJson(json['localisations'] as Map<String, dynamic>),
      );
  bool? success;
  List<Country>? countries;
  Localisations? localisations;

  Map<String, dynamic> toJson() => {
        'success': success,
        'countries': countries == null ? <dynamic>[] : List<dynamic>.from(countries!.map((x) => x.toJson())),
        'localisations': localisations?.toJson(),
      };
}

class Country {
  Country({
    this.name,
    this.code,
    this.url,
  });

  factory Country.fromJson(Map<String, dynamic> json) => Country(
        name: json['name'] as String? ?? '',
        code: json['code'] as String? ?? '',
        url: json['url'] as String? ?? '',
      );
  String? name;
  String? code;
  String? url;

  Map<String, dynamic> toJson() => {
        'name': name,
        'code': code,
        'url': url,
      };
}

class Localisations {
  Localisations({
    this.country,
    this.chooseCountry,
    this.language,
    this.chooseLanguage,
    this.submitButton,
  });

  factory Localisations.fromJson(Map<String, dynamic> json) => Localisations(
        country: json['country'] as String? ?? '',
        chooseCountry: json['chooseCountry'] as String? ?? '',
        language: json['language'] as String? ?? '',
        chooseLanguage: json['chooseLanguage'] as String? ?? '',
        submitButton: json['submitButton'] as String? ?? '',
      );
  String? country;
  String? chooseCountry;
  String? language;
  String? chooseLanguage;
  String? submitButton;

  Map<String, dynamic> toJson() => {
        'country': country,
        'chooseCountry': chooseCountry,
        'language': language,
        'chooseLanguage': chooseLanguage,
        'submitButton': submitButton,
      };
}
