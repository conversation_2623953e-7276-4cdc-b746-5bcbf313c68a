// To parse this JSON data, do
//
//     final languageModel = languageModelFromJson(jsonString);

class LanguageModel {
  LanguageModel({
    this.success,
    this.languages,
  });

  factory LanguageModel.fromJson(Map<String, dynamic> json) => LanguageModel(
        success: json['success'] as bool,
        languages: json['languages'] == null
            ? []
            : List<Language>.from(
                (json['languages'] as List<dynamic>).map((x) => Language.fromJson(x as Map<String, dynamic>)),),
      );
  bool? success;
  List<Language>? languages;

  Map<String, dynamic> toJson() => {
        'success': success,
        'languages': languages == null ? <dynamic>[] : List<dynamic>.from(languages!.map((x) => x.toJson())),
      };
}

class Language {
  Language({
    this.value,
    this.label,
  });

  factory Language.fromJson(Map<String, dynamic> json) => Language(
        value: json['value'] as String,
        label: json['label'] as String,
      );
  String? value;
  String? label;

  Map<String, dynamic> toJson() => {
        'value': value,
        'label': label,
      };
}
