import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/language_repository/language_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/model/country_response_model.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/model/language_model.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'language_cubit.freezed.dart';
part 'language_state.dart';

class LanguageCubit extends Cubit<LanguageState> {
  LanguageCubit() : super(const LanguageState.initial());
  final LanguageRepository repository = LanguageRepository();

  List<String> languageModel = [];
  CoutryResponseDataModel? coutryModel;
  LanguageModel? languages;

  final languageMap = {
    'en': 'English',
    'fr': 'French',
    'es': 'Spanish',
  };

  String selectedCountry = '';
  String selectedLanguage = '';

  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isENLanguageLoading = ValueNotifier(false);

  //ValueNotifier<bool> isENLanguageLoading =  ValueNotifier(false);

  void updateLnaguge() {
    emit(const LanguageState.langugaeLoading());
  }

  void initalLanguage() {
    emit(const LanguageState.initial());
  }

  String getDeviceLocale() {
    final deviceLocale = PlatformDispatcher.instance.locale;
    'data ===>${'${deviceLocale.languageCode}-${deviceLocale.countryCode}'}'.logV;
    return '${deviceLocale.languageCode}-${deviceLocale.countryCode}';
  }

  Future<void> fetchLanguageApi(BuildContext context, {bool autoSelect = false, String? url}) async {
    emit(const LanguageState.loading());
    try {
      final response = await repository.language(
        context: navigatorKey.currentContext!,
      );
      //'????? response  = ${response?.languages?.map((e) => e.value) ?? ''}'.logV;
      if (response != null && (response.success ?? false) == true) {
        languages = response;
        Injector.instance<AppDB>().langugaeModel = response;
        languageModel.clear();
        languageModel = response.languages
                ?.map((e) => e.value)
                .whereType<String>() // Filters out null values
                .toList() ??
            [];

        if ((languageModel.isNotEmpty && languageModel.length == 1) || autoSelect) {
          selectedLanguage = languageModel.first;
          Injector.instance<AppDB>().selectedLangugae = selectedLanguage;
          '====== ets ${Injector.instance<AppDB>().selectedLangugae}'.logV;

          if (selectedLanguage != '') {
            // Update app locale and AppDB to keep dropdown in sync
            // final parts = selectedLanguage.split('-');
            // if (parts.length == 2) {
            //   final locale = Locale(parts[0], parts[1]);
            //   await EasyLocalization.of(context)!.setLocale(locale);
            //   await context.setLocale(locale);
            //   Injector.instance<AppDB>().selectedLangugae = selectedLanguage;
            //   'Language successfully set to $selectedLanguage'.logD;
            // } else {
            //   'Invalid language format: $selectedLanguage'.logE;
            // }
            updateLnaguge();
            // await EasyLocalization.of(context)!.setLocale(
            //   Locale(
            //     selectedLanguage.split('-').first,
            //     selectedLanguage.split('-').last,
            //   ),
            // );
            initalLanguage();
          } else {
            CustomSnackbar.showErrorSnackBar(
              message: 'Please select coutry or language',
            );
          }
        } else {
          '====== ets'.logV;
          selectedLanguage = '';
        }
      }
      emit(const LanguageState.initial());
    } catch (e) {
      emit(const LanguageState.initial());
    }
  }

   Future<void> setLanguageByValue(BuildContext context,) async {
    emit(const LanguageState.countryLoading());
    //context.read<LoginCubit>().isENLanguageLoading.value = false;
              context.read<LoginCubit>().isENLanguageLoading.value = true;
              '????? on back isENLanguageLoading.value = ${context.read<LoginCubit>().isENLanguageLoading.value}'.logV;
              '????? setLanguageLoader.value = ${context.read<LoginCubit>().hashCode}'.logD;
              '????? backk'.logD;
              //if (didPop) {
              '????? back'.logD;
                
               final selected = Injector.instance<AppDB>().selectedLangugae;
          
          // Define fallback groups
          final fallbackGroups = {
            'en-CA': ['en-CA', 'fr-CA'],
            'en-US': ['en-US', 'es-US'],
          };
          
          String? resolvedLang;
          
          // First check fallbacks
          for (final entry in fallbackGroups.entries) {
            if (entry.value.any((code) => selected?.contains(code) == true)) {
              resolvedLang = entry.key;
              break;
            }
          }
          
          // If no fallback match, do direct match
          resolvedLang ??= ['en-AU', 'en-GB', 'en-CA', 'en-US']
              .firstWhere((lang) => selected?.contains(lang) == true,);
          
          // Apply language if matched
          if (resolvedLang != null) {
            '????? Resolved lang = $resolvedLang'.logD;
            
             //context.read<LoginCubit>().setLanguageByValue(context, resolvedLang);
            
          }
          
                //context.read<LoginCubit>().isENLanguageLoading.value = false;
                //await context.read<LoginCubit>().setLanguageIfExists(context,'en-AU');
                // AppNavigation.replaceScreen(context, const LanguagePage());
              // No need to manually call Navigator.pop or AppNavigation
              //}
//setLanguageLoader.value = true;
  final languages = Injector.instance<AppDB>().langugaeModel?.languages;

if (languages != null) {
  for (var lang in languages) {
    '????? Label: ${lang.label}, Value: ${lang.value}'.logD;
  }
} else {
  '????? No languages found in AppDB'.logE;
}
  '????? valid'.logV;
  final parts = resolvedLang.split('-');
  if (parts.length != 2) {
    '????? Invalid language format: $resolvedLang'.logV;
    return;
    
  }

  final locale = Locale(parts[0], parts[1]);
  '????? here'.logV;
  '?????locale = $locale'.logV;
  // Set the locale via EasyLocalization and context
  await EasyLocalization.of(context)!.setLocale(locale);
  '????? here 2'.logV;
  await context.setLocale(locale);
  '????? here 3'.logV;

  // Save to AppDB
  Injector.instance<AppDB>().selectedLangugae = resolvedLang;

  '????? Language set to $resolvedLang'.logD;
}

Future<void> setLanguageAutomatically() async {
  final context = navigatorKey.currentContext;
  if (context == null) {
    'Context is null — cannot set language.'.logE;
    return;
  }

  //setLanguageLoader.value = true;

  final selected = Injector.instance<AppDB>().selectedLangugae;
  if (selected == null) {
    'No selected language in AppDB.'.logE;
    //setLanguageLoader.value = false;
    return;
  }

  // Language fallback logic
  String? resolvedLang;
  final fallbackGroups = {
    'en-CA': ['en-CA', 'fr-CA'],
    'en-US': ['en-US', 'es-US'],
  };

  for (final entry in fallbackGroups.entries) {
    if (entry.value.any((code) => selected.contains(code))) {
      resolvedLang = entry.key;
      break;
    }
  }

  // Exact match fallback
  resolvedLang ??= ['en-GB', 'en-AU', 'en-CA', 'en-US'].firstWhere(
    (lang) => selected.contains(lang),
    orElse: () => '',
  );

  if (resolvedLang.isEmpty) {
    'No matching or fallback language found.'.logE;
    //setLanguageLoader.value = false;
    return;
  }

  final parts = resolvedLang.split('-');
  if (parts.length != 2) {
    'Invalid language format: $resolvedLang'.logE;
    //setLanguageLoader.value = false;
    return;
  }

  final locale = Locale(parts[0], parts[1]);
  'Setting locale to: $locale'.logD;

  try {
    await EasyLocalization.of(context)!.setLocale(locale);
    await context.setLocale(locale);
    Injector.instance<AppDB>().selectedLangugae = resolvedLang;
    'Language successfully set to $resolvedLang'.logD;
  } catch (e) {
    'Failed to set locale: $e'.logE;
  }

  //setLanguageLoader.value = false;
}



  Future<void> fetchCountryApi({bool autoSelect = false, String? url}) async {
    emit(const LanguageState.countryLoading());
    await setLanguageAutomatically();
    try {
      '????? other baseUrl before = ${EndPoints.baseUrl}'.logD;
      final response = await repository.getCountry(
        context: navigatorKey.currentContext!,
        lang: getDeviceLocale(),
      );
      '????? response of country = ${response?.countries?.map((e) => e.name) ?? ''}'.logV;
      if (response != null && (response.success ?? false) == true) {
        //   coutryModel.clear();
        coutryModel = response;
        'coutryModel $coutryModel'.logV;
      }
      emit(const LanguageState.initial());
      if (autoSelect && url != null) {
        coutryModel?.countries?.forEach(
          (element) async {
            (Uri.parse(element.url!).host.replaceAll('www.', '') == Uri.parse(url).host.replaceAll('www.', '')).logD;
            Uri.parse(url).host.replaceAll('www.', '').logD;
            Uri.parse(element.url!).host.replaceAll('www.', '').logD;
            if (Uri.parse(element.url!).host.replaceAll('www.', '') == Uri.parse(url).host.replaceAll('www.', '')) {
              selectedCountry = element.name ?? '';
              final selectedCountryModel = element;
              if (selectedCountryModel.url?.isNotEmpty ?? false) {
                EndPoints.baseUrl = '${selectedCountryModel.url!}/api/';
                
              }
              '????? other baseUrl b after = ${EndPoints.baseUrl}'.logD;
              Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;
              '????? other baseUrl b after = ${EndPoints.baseUrl}'.logD;
              await fetchLanguageApi(navigatorKey.currentContext!, autoSelect: autoSelect, url: url);
              
            }
          },
        );
      }
    } catch (e) {
      emit(const LanguageState.initial());
    }
  }

  String getCountryDomain(String country) {
    switch (country) {
      case 'United Kingdom':
        return 'UK';
      case 'United States':
        return 'US';
      case 'Australia':
        return 'AU';
      case 'Canada':
        return 'CA';
      default:
        return '';
    }
  }

  void selectBaseUrl(String coutry) {
    switch (coutry) {
      case 'United Kingdom':
        EndPoints.baseUrl = 'https://breakingfreeonline.com/api/';
      case 'United States':
        EndPoints.baseUrl = 'https://breakingfreeonline.us/api/';
      case 'Canada':
        EndPoints.baseUrl = 'https://breakingfreeonline.ca/api/';
      case 'Australia':
        EndPoints.baseUrl = 'https://breakingfreeonline.com.au/api/';
      default:
        EndPoints.baseUrl = 'https://breakingfreeonline.com/api/';
    }

    Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;
    Injector.instance<AppDB>().baseUrl.logD;
    'EndPoints.baseUrl === ${EndPoints.baseUrl}'.logD;
    'Injector.instance<AppDB>().baseUrl === ${Injector.instance<AppDB>().baseUrl}'.logD;
    // fetchLanguageApi(c);
  }
}
