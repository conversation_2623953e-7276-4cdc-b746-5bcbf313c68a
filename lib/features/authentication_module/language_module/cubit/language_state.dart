part of 'language_cubit.dart';

@freezed
class LanguageState with _$LanguageState {
  const factory LanguageState.initial() = _Initial;
  const factory LanguageState.loading() = _LoadingState;
  const factory LanguageState.countryLoading() = _CountryLoadingState;
  const factory LanguageState.langugaeLoading() = _LangugaeLoadingState;
  const factory LanguageState.seetLanguageLoading() = _SetLanguageLoadingState;
}
