import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/model/country_response_model.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/model/language_model.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class LanguageRepository {
  Future<LanguageModel?> language({
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: EndPoints.language,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return LanguageModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<CoutryResponseDataModel?> getCountry({
    required BuildContext context,
    required String lang,
  }) async {
    try {
      EndPoints.baseUrl = '';
      final response = await APIFunction.getAPICall(
        apiName: '${EndPoints.countries}?lang=$lang',
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return CoutryResponseDataModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
