import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

Future<void> checkInternetAndShowDialog(BuildContext context) async {
  final connectivityResult = await Connectivity().checkConnectivity();
  
  if (connectivityResult == ConnectivityResult.none) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("No Internet Connection"),
        content: const Text("Please check your internet settings and try again."),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("Try Again"),
          )
        ],
      ),
    );
  }
}
