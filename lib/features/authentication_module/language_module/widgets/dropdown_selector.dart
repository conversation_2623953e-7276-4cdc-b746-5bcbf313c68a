import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class DropdownSelector<T> extends StatefulWidget {
  const DropdownSelector({
    required this.options,
    this.hintText,
    this.onSelected,
    this.selectedOption,
    super.key,
    this.width,
    this.height,
    this.isError,
  });
  final List<T> options;
  final String? hintText;
  final void Function(T?)? onSelected;
  final T? selectedOption;
  final double? width;
  final double? height;
  final bool? isError;
  @override
  State<DropdownSelector<T>> createState() => _DropdownSelectorState<T>();
}

class _DropdownSelectorState<T> extends State<DropdownSelector<T>> {
  T? dropdownValue;

  @override
  void initState() {
    super.initState();
    dropdownValue.logD;
    //  widget.selectedOption = null;
    dropdownValue = widget.selectedOption;
    '??????????????? dropdownValue = ${dropdownValue}'.logD;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          // width: double.infinity,
          child: DropdownMenu<T>(
            inputDecorationTheme: InputDecorationTheme(
              constraints: BoxConstraints(
                maxHeight: AppSize.h46,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSize.r4),
                borderSide: BorderSide(
                  color: context.themeColors.greyColor,
                  width: 2,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSize.r4),
                borderSide: BorderSide(
                  color: widget.isError ?? false ? context.themeColors.errorRedColor : context.themeColors.greyColor,
                  width: 2,
                ),
              ),
            ),
            initialSelection: dropdownValue,
            onSelected: (value) {
              // This is called when the user selects an item.
              setState(() {
                dropdownValue = value;
              });
              widget.onSelected?.call(value);
            },
            hintText: widget.hintText,
            textStyle: context.textTheme.titleSmall,
            width: widget.width ?? context.width - AppSize.w40,
            dropdownMenuEntries: widget.options.map<DropdownMenuEntry<T>>((value) {
              return DropdownMenuEntry<T>(
                value: value,
                label: value.toString(),
              );
            }).toList(),
          ),
        ),
        if (widget.isError ?? false)
          CustomErrorWidget(
            spacing: AppSize.h5,
            errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
          )
        else
          const SizedBox(),
      ],
    );
  }
}
