part of 'login_cubit.dart';

@immutable
sealed class LoginState {}

/// Login Initial State
final class LoginInitialState extends LoginState {}

/// Loading login state
final class LoginLoadingState extends LoginState {}

/// Successful login state
final class LoginSuccessState extends LoginState {}

/// Login failed state
final class LoginEmailNotVerifiedState extends LoginState {}

/// Failed login state
final class LoginFailureState extends LoginState {
    final String errorMessage;
  LoginFailureState(this.errorMessage);
}

/// Failed login state
/// Loading login state
final class PolicyLoadingState extends LoginState {}

/// Successful login state
final class PolicySuccessState extends LoginState {}

final class PolicyFailureState extends LoginState {}


final class SendPolicyLoadingState extends LoginState {}
