import 'dart:async';
import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/cubit/language_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/pages/language_page.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/policy_cubit/policy_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/policy_update_page.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_welcome_back_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/progess_check_day_calculation.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/notification_service/notification_helper.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/loading_overlay/loading_overlay.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:url_launcher/url_launcher.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit({this.domain, this.token}) : super(LoginInitialState()) {
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) async {
        if (token != null && domain != null) {
          await verifyEmail(token!, domain!);
        }
        'Locale already set: ${EasyLocalization.of(navigatorKey.currentContext!)?.locale}'.logD;

        final savedLang = Injector.instance<AppDB>().selectedLangugae;

        if (savedLang?.isNotEmpty ?? false) {
          final localeParts = savedLang!.replaceAll('-', '_').split('_');
          final savedLocale = Locale(localeParts[0], localeParts.length > 1 ? localeParts[1] : null);

          final currentLocale = EasyLocalization.of(navigatorKey.currentContext!)?.locale;

          if (savedLocale != currentLocale) {
            await EasyLocalization.of(navigatorKey.currentContext!)?.setLocale(savedLocale);
            await navigatorKey.currentContext!.read<PolicyCubit>().getPolicies1();

            'Locale updated to: $savedLocale'.logI;
          } else {
            'Locale already set: $currentLocale'.logD;
            await navigatorKey.currentContext!.read<PolicyCubit>().getPolicies1();
          }
          emit(LoginInitialState());
        }
      },
    );
    if (Injector.instance<AppDB>().userModel?.user.assessment?.assessmentComplete ?? false == true) {
      WidgetsBinding.instance.addPostFrameCallback(
        (timeStamp) async {
          await FlutterLocalNotificationsPlugin()
              .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
              ?.requestNotificationsPermission();

          await FlutterLocalNotificationsPlugin()
              .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
              ?.requestPermissions(
                alert: true,
                badge: true,
                sound: true,
              );
        },
      );
    }
  }
  final String? domain;
  final String? token;
  Map<String, dynamic>? policy;
  Map<String, dynamic> policyMap = {};
  List<dynamic> policyRequired = [];

  ValueNotifier<bool> showPassword = ValueNotifier(false);

  void togglevisibility() {
    showPassword.value = !showPassword.value;
  }

  final FocusNode emailFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();

  final ScrollController scrollController = ScrollController();
  ValueNotifier<int> expandableIndex = ValueNotifier(-1);
  ValueNotifier<int> acceptedPolicyCount = ValueNotifier(0);

  ValueNotifier<bool> showPolicyWarningBox = ValueNotifier(false);
  ValueNotifier<bool> isPolicyContinue = ValueNotifier(false);
  ValueNotifier<bool> isLoginButtonClicked = ValueNotifier(false);
  final AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isENLanguageLoading = ValueNotifier(false);

  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  ValueNotifier<bool> emailValidate = ValueNotifier(false);
  ValueNotifier<bool> passwordValidate = ValueNotifier(false);

  ValueNotifier<String> emailError = ValueNotifier('');
  ValueNotifier<String> passwordError = ValueNotifier('');

  ValueNotifier<String> verifyEmailMsg = ValueNotifier('');
  bool isVerifyEmailError = false;

  ValueNotifier<String> verifyResetMsg = ValueNotifier('nn');
  ValueNotifier<bool> isVerifyResetError = ValueNotifier(false);

  ValueNotifier<bool> isReloadPage = ValueNotifier(false);

  // ValueNotifier<bool> isFocusChange = ValueNotifier(false);
  // bool isFirstLoginAttempt = true;
  // FocusNode focusNode = FocusNode();

  final formKey = GlobalKey<FormState>();

ValueNotifier<bool> setLanguageLoader = ValueNotifier(false);

 Future<void> setLanguageByValue(BuildContext context, String languageValue) async {
//setLanguageLoader.value = true;
  final languages = Injector.instance<AppDB>().langugaeModel?.languages;

if (languages != null) {
  for (var lang in languages) {
    '????? Label: ${lang.label}, Value: ${lang.value}'.logD;
  }
  setLanguageLoader.value = false;
} else {
  'No languages found in AppDB'.logE;
  setLanguageLoader.value = false;
}

  final parts = languageValue.split('-');
  if (parts.length != 2) {
    'Invalid language format: $languageValue'.logE;
    setLanguageLoader.value = false;
    return;
    
  }

  final locale = Locale(parts[0], parts[1]);
  '????? local login = $locale'.logD;
  // Set the locale via EasyLocalization and context
  await EasyLocalization.of(context)!.setLocale(locale);
  await context.setLocale(locale);

  // Save to AppDB
  Injector.instance<AppDB>().selectedLangugae = languageValue;

  'Language set to $languageValue'.logD;
  setLanguageLoader.value = false;
}

  Future<void> verifyEmail(String token, String url) async {
    try {
      isReloadPage.value = true;
      '???????????????????/ is reload page =  [38;5;244m${isReloadPage.value} [0m'.logD;
      '??????????????? selectedLanguage = ${Injector.instance<AppDB>().selectedLangugae}'.logV;
      // Only set to 'en-US' if no language was previously selected
      if ((Injector.instance<AppDB>().selectedLangugae ?? '').isEmpty) {
        Injector.instance<AppDB>().selectedLangugae = 'en-US';
      }
      '??????????????? selectedLanguage = ${Injector.instance<AppDB>().selectedLangugae}'.logD;
      LoadingOverlay.instance().show(context: navigatorKey.currentContext!);
      // await Injector.instance<AppDB>().clearData();
      '====== ets before a = ${Injector.instance<AppDB>().selectedLangugae}'.logV;
      final langCubit = LanguageCubit();
      '====== ets before b = ${Injector.instance<AppDB>().selectedLangugae}'.logV;
      await langCubit.fetchCountryApi(autoSelect: true, url: url);
      '====== ets before c = ${Injector.instance<AppDB>().selectedLangugae}'.logV;
      emit(LoginInitialState());
      final response = await authRepository.verifyEmail(
        token: token,
        context: navigatorKey.currentContext,
      );
      response.logD;

      '???????????????????????????????????? response.entries.first.key = ${response.entries.first.key}'.logV;

      verifyEmailMsg.value = response.entries.first.key;
      isVerifyEmailError = response.entries.first.value;
      LoadingOverlay.instance().hide();
    } catch (e) {
      LoadingOverlay.instance().hide();
      'Error verifying email: $e'.logE;
    }
  }

  Future<void> launchURL(String url) async {
    try {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalNonBrowserApplication,
      );
    } catch (e) {
      try {
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalNonBrowserApplication,
        );
      } catch (e) {
        'test open'.logV;

        throw Exception('Could not launch, error: $e');
      }
    }
  }

  List<String> getPoliciesNotYetAgreed({
    required List<dynamic> policiesRequired,
    required Map<String, dynamic> policies,
    required List<dynamic>? userEndorsedPolicies,
  }) {
    return policiesRequired
        .where((policyName) {
          final latestVersion = policies[policyName]?['version'];
          final hasAccepted = userEndorsedPolicies?.any(
                (e) => e['policyName'] == policyName && e['version'] == latestVersion,
              ) ??
              false;
          return !hasAccepted;
        })
        .cast<String>()
        .toList();
  }

  void updateState() {
    'update'.logV;
    emit(LoginInitialState());
  }

  bool emailValid() {
    var isValid = true;
    final emailValid =
        RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(emailController.text);
    // Email
    if (emailController.text.trim().isEmpty) {
      emailError.value = AuthLocaleKeys.logInEmailRequired.tr();
      isValid = false;
      emailError.value = '';
    } else if (!emailValid) {
      emailError.value = AuthLocaleKeys.logInEmailInvalid.tr();
      isValid = false;
      emailError.value = '';
    } else {
      emailError.value = '';
    }
    return isValid;
  }

  bool passwordValid() {
    emit(LoginInitialState());
    var isValid = true;

    if (passwordController.text.isEmpty) {
      passwordError.value = AuthLocaleKeys.logInPasswordRequired.tr();
      isValid = false;
      passwordError.value = '';
      emit(LoginInitialState());
    } else {
      passwordError.value = '';
    }
    return isValid;
  }

  bool validateAllFields() {
    var isValid = true;
    final emailValid =
        RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(emailController.text);
    // Email
    if (emailController.text.trim().isEmpty) {
      emailError.value = AuthLocaleKeys.logInEmailRequired.tr();
      isValid = false;
    } else if (!emailValid) {
      emailError.value = AuthLocaleKeys.logInEmailInvalid.tr();
      isValid = false;
    } else {
      emailError.value = '';
    }

    // Password
    if (passwordController.text.isEmpty) {
      passwordError.value = AuthLocaleKeys.logInPasswordRequired.tr();
      isValid = false;
    } else {
      passwordError.value = '';
    }
    return isValid;
  }


  Future<void> login(BuildContext context) async {
    try {
      emit(LoginLoadingState());
      final data = await authRepository.login(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
        context: context,
      );
      '????? other data = ${data?.toJson()}'.logD;
      if (data != null && (data.user.email?.verified ?? false)) {
        'WWWWWWW 1'.logV;
        Injector.instance<AppDB>().userModel = data;
        Injector.instance<AppDB>().userModelCopy = data;
        Injector.instance<AppDB>().isAudioMuted = data.user.audioMuted ?? false;
        print('DEBUG: userModel SAVED to Hive (verified): ' + (data?.toJson().toString() ?? 'null'));

        if (Injector.instance<AppDB>().localizedJson == null) {
          'Locale already set: ${EasyLocalization.of(navigatorKey.currentContext!)?.locale}'.logD;

          final currentLocale = EasyLocalization.of(navigatorKey.currentContext!)?.locale ?? const Locale('en', 'GB');

          unawaited(EasyLocalization.of(navigatorKey.currentContext!)?.resetLocale());
          //   await EasyLocalization.of(navigatorKey.currentContext!)?.resetLocale();

          await EasyLocalization.of(navigatorKey.currentContext!)?.setLocale(currentLocale);

          // Force rebuild to reload all localization files
        }
        'context.locale.countryCode : ${context.locale.countryCode}'.logD;

        await context.read<SignupCubit?>()?.getUserData(context);
        context.read<SignupCubit>().startTimer(context);
        context.read<SignupCubit>().startGetUserTimer(context);
        final createdAtString = Injector.instance<AppDB>().userModel?.user.createdAt;
        if (createdAtString != null) {
          final createdAt = DateTime.parse(createdAtString.toString());

          final scheduledDate = createdAt.add(const Duration(days: 14));
          final scheduledDateString = scheduledDate.toString();

          'Notification scheduled for: $scheduledDateString'.logV;
          if (Injector.instance<AppDB>().userModel?.user.checkins?.isEmpty ?? false) {
            await LocalNotificationHelper.localNotificationHelper.sendScheduleNotification(
              date: scheduledDateString,
              id: 101,
              body: 'Complete a progress check now to update your dashboard.',
              payload: 'progress_check',
              title: "It's time for your progress check",
            );
          }
        } else {
          'createdAt is null, cannot schedule notification.'.logV;
        }
        if (data.user.app?.accessibility != null) {
          Injector.instance<AppDB>().accessibilityContrastValue =
              double.parse(data.user.app?.accessibility?.contrast?.toString() ?? '0.0');
          Injector.instance<AppDB>().accessibilityFontSize =
              double.parse(data.user.app?.accessibility?.fontSize?.toString() ?? '0.0');

          Injector.instance<AppDB>().accessibilityIsBoldText = data.user.app?.accessibility?.fontBold;
          BlocProvider.of<AccessbilityCubit>(context).setInitialValue();
        }
        await getPolicies();
        if (data.user.app?.alerts != null) {
          BlocProvider.of<MyAlertCubit>(context).initalData();
        }

        Injector.instance<AppDB>().isAppLogin = true;
        emit(LoginSuccessState());
      } else if (data != null && !(data.user.email?.verified ?? false)) {
        'WWWWWWW 2'.logV;
        Injector.instance<AppDB>().userModel = data;
        Injector.instance<AppDB>().userModelCopy = data;
        print('DEBUG: userModel SAVED to Hive (unverified): ' + (data?.toJson().toString() ?? 'null'));
        emit(LoginEmailNotVerifiedState());
      } else {
        'WWWWWWW 3'.logV;
        emit(LoginFailureState(''));
        emit(LoginFailureState(''));
      }
      debugPrint(state.toString());
    } catch (e) {
      'Injector.instance<AppDB>().localizedJson $e'.logV;

      // emit(LoginFailureState(e.toString()));
      'Injector.instance<AppDB>().localizedJson $e'.logV;
      print('login error $e');
      emit(LoginFailureState(e.toString()));
    }
  }

  String removeAfterPlus(String isoString) {
    final plusIndex = isoString.indexOf('+');
    return plusIndex != -1 ? isoString.substring(0, plusIndex) : isoString;
  }

  String formatWithForcedOffset(DateTime dt, String forcedOffset) {
    final y = dt.year.toString().padLeft(4, '0');
    final m = dt.month.toString().padLeft(2, '0');
    final d = dt.day.toString().padLeft(2, '0');
    final hh = dt.hour.toString().padLeft(2, '0');
    final mm = dt.minute.toString().padLeft(2, '0');
    final ss = dt.second.toString().padLeft(2, '0');

    return '$y-$m-$d'
        'T$hh:$mm:$ss'
        '$forcedOffset'; // e.g., '+01:00'
  }

  Future<void> getPolicies() async {
    //  emit(PolicyLoadingState());

    try {
      final policiesResponse = await authRepository.getPolicies(
        context: navigatorKey.currentContext,
      );

      if (policiesResponse != null && policiesResponse.data != null) {
        final data = policiesResponse.data;

        // Save the full policy map
        policy = data;

        policyRequired = List<String>.from(data?['policiesRequired'] as List<dynamic>);
        policyMap = Map<String, dynamic>.from(data?['policies'] as Map<String, dynamic>);
        Injector.instance<AppDB>().policyRequired = policyRequired;
        Injector.instance<AppDB>().policyMap = policyMap;

        final notAgreed = (Injector.instance<AppDB>().policyRequired ?? [])
            .where((policyName) {
              final latestVersion = Injector.instance<AppDB>().policyMap?[policyName]?['version'];
              final hasAccepted = Injector.instance<AppDB>().userModel?.user.policyVersionsEndorsed?.any(
                        (e) => (e.policyName) == policyName && e.version == latestVersion,
                      ) ??
                  false;
              return !hasAccepted;
            })
            .cast<String>()
            .toList();

        if (notAgreed.isNotEmpty) {
          final unacceptedPolicyList = <PolicyData>[];

          for (final policyKey in notAgreed) {
            final policy = Injector.instance<AppDB>().policyMap?[policyKey];
            final title = policy?['title'] ?? policyKey;
            final version = policy?['version']?.toString() ?? '';

            final contentMap = policy?['content'] as Map<String, dynamic>? ?? {};
            final contentList = (contentMap['text'] as List?)?.whereType<String>().toList() ?? [];
            final contentTitle = contentMap['title'] ?? '';

            'content: $contentList'.logV;

            unacceptedPolicyList.add(
              PolicyData(
                key: policyKey,
                title: title as String,
                version: version,
                content: contentList,
                contentTitle: contentTitle as String,
              ),
            );
          }

          await AppNavigation.nextScreen(
            navigatorKey.currentContext!,
            PolicyUpdatePage(
              policies: unacceptedPolicyList,
            ),
          );
        }

        // You can pass notAgreed to the UI or save it internally if needed
        // this.policiesNotYetAgreed = notAgreed;

        //   emit(PolicySuccessState());
      } else {
        emit(PolicyFailureState());
      }
    } catch (e, stackTrace) {
      '❌ Error fetching policies: $e\n$stackTrace'.logE;
      emit(PolicyFailureState());
    }
  }

  void selectBaseUrl(String coutry) {
    switch (coutry) {
      case 'en-GB':
        EndPoints.baseUrl = 'https://breakingfreeonline.com/api/';
      case 'en-US' || 'es-US':
        EndPoints.baseUrl = 'https://breakingfreeonline.us/api/';
      case 'en-CA' || 'fr-CA':
        EndPoints.baseUrl = 'https://breakingfreeonline.ca/api/';
      case 'en-AU':
        EndPoints.baseUrl = 'https://breakingfreeonline.com.au/api/';
      default:
        EndPoints.baseUrl = 'https://breakingfreeonline.com/api/';
    }
  }

  Future<void> sendPolicy({required BuildContext context}) async {
    emit(SendPolicyLoadingState());

    try {
      final response = await authRepository.sendPolicy(
        context: context,
        policyVersionsEndorsed: [
          ...?Injector.instance<AppDB>().userModel?.user.policyVersionsEndorsed?.map(
                (e) => {'policyName': e.policyName, 'version': e.version},
              ),
          ...Injector.instance<AppDB>().policyRequired?.map(
                    (policyName) => {
                      'policyName': policyName,
                      'version': Injector.instance<AppDB>().policyMap?[policyName]?['version'],
                    },
                  ) ??
              [],
        ],
      );
      if (response != null && response.data!['success'] == true) {
        final userModel = Injector.instance<AppDB>().userModel;

        if (userModel?.user.email == null) {
          return AppNavigation.replaceScreen(context, const LanguagePage());
        } else if (userModel?.user.dataProcessing == null ||
            userModel?.user.dataProcessing == false ||
            userModel?.user.dataSharingRpiUnderstood == null ||
            userModel?.user.dataSharing == null) {
          'data ++++ ===+ ${userModel?.toJson()}'.logD;
          '??? from here 2'.logD;
          return AppNavigation.replaceScreen(context, MyDataPage());
        } else {
          if (userModel?.user.assessment?.assessmentComplete ?? false == true) {
            final state = fetchProgressCheckData();
            if (state == ProgressCheckState.optional) {
              return AppNavigation.replaceScreen(context, const ProgressCheckWelcomeBackPage());
            } else if (state == ProgressCheckState.required) {
              return AppNavigation.replaceScreen(
                context,
                const ProgressCheckWelcomeBackPage(recoveryNotRequired: false),
              );
            } else if (state == ProgressCheckState.notNeeded) {
              '>?>?>? 5'.logV;
              return AppNavigation.replaceScreen(context, const MyDiagramPage());
            } else {
              '>?>?>? 6'.logV;
              return AppNavigation.replaceScreen(context, const MyDiagramPage());
            }
          } else {
            return AppNavigation.replaceScreen(context, const AssessmentMainPage());
          }
        }
      } else {}
      emit(LoginInitialState());
    } catch (e) {
      emit(LoginInitialState());
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      log('message$e');
    }
  }


  @override
  Future<void> close() {
    // TODO: implement close
    emailFocusNode.dispose();
    passwordFocusNode.dispose();
    return super.close();
  }

  void clearInput() {
    emailController.clear();
    passwordController.clear();
    emailError.value = '';
    passwordError.value = '';
    emit(LoginInitialState());
  }
}

class PolicyData {
  PolicyData({
    required this.key,
    required this.title,
    required this.version,
    required this.content,
    required this.contentTitle,
  });
  final String key;
  final String title;
  final String version;
  final String contentTitle;
  final List<String> content;
}
