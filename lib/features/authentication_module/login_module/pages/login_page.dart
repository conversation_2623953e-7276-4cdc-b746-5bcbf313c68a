import 'dart:io';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/authentication_module/forget_password_module/pages/forget_password_page.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/pages/language_page.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/widgets/dropdown_selector.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/policy_update_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/sign_up_account_verification.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/terms_condition_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_welcome_back_page.dart';
import 'package:breakingfree_v2/features/progress_check_module/widgets/progess_check_day_calculation.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({this.domain, this.token, this.isResetInvalidTokenError, super.key});
  final String? domain;
  final String? token;
  final bool? isResetInvalidTokenError;

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    //  ctx.read<LoginCubit>() emailFocusNode.dispose();
    //   passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Unfocus all nodes when returning
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  Widget build(BuildContext context) {

    '????? other baseUrl from Login via Injector = ${Injector.instance<AppDB>().baseUrl}'.logD;
    '????? other baseUrl from Login via EndPoint = ${EndPoints.baseUrl}'.logD;
    '????? language = ${Injector.instance<AppDB>().selectedLangugae}'.logD;
    return BlocProvider(
      create: (context) => LoginCubit(domain: widget.domain, token: widget.token)..selectBaseUrl(Injector.instance<AppDB>().selectedLangugae ?? ''),
      child: Builder(
        builder: (context) {
          return PopScope(
            canPop: Platform.isIOS,
            onPopInvoked: (didPop) async{
              
              // context.read<LoginCubit>().isENLanguageLoading.value = false;
              // context.read<LoginCubit>().isENLanguageLoading.value = true;
              // '????? on back isENLanguageLoading.value = ${context.read<LoginCubit>().isENLanguageLoading.value}'.logV;
              // '????? setLanguageLoader.value = ${context.read<LoginCubit>().hashCode}'.logD;
              // '????? backk'.logD;
              // //if (didPop) {
              // '????? back'.logD;
              
              if (!didPop) {
                AppNavigation.replaceScreen(context, const LanguagePage());
          //      final selected = Injector.instance<AppDB>().selectedLangugae;
          
          // // Define fallback groups
          // final fallbackGroups = {
          //   'en-CA': ['en-CA', 'fr-CA'],
          //   'en-US': ['en-US', 'es-US'],
          // };
          
          // String? resolvedLang;
          
          // // First check fallbacks
          // for (final entry in fallbackGroups.entries) {
          //   if (entry.value.any((code) => selected?.contains(code) == true)) {
          //     resolvedLang = entry.key;
          //     break;
          //   }
          // }
          
          // // If no fallback match, do direct match
          // resolvedLang ??= ['en-AU', 'en-GB', 'en-CA', 'en-US']
          //     .firstWhere((lang) => selected?.contains(lang) == true,);
          
          // // Apply language if matched
          // if (resolvedLang != null) {
          //   'Resolved lang = $resolvedLang'.logD;
          //   AppNavigation.replaceScreen(context, const LanguagePage());
          //   await context.read<LoginCubit>().setLanguageByValue(context, resolvedLang);
          // }
          
          //       context.read<LoginCubit>().isENLanguageLoading.value = false;
                //await context.read<LoginCubit>().setLanguageIfExists(context,'en-AU');
                //AppNavigation.replaceScreen(context, const LanguagePage());
              }
              // No need to manually call Navigator.pop or AppNavigation
              //}
          
            },
            child: GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Scaffold(
                resizeToAvoidBottomInset: true,
                body: SafeArea(
                  bottom: false,
                  child: BlocBuilder<LoginCubit, LoginState>(
                    builder: (ctx, state) {
                      context.locale.countryCode.logD;
                      final loginCubit = ctx.read<LoginCubit>();
                      return BlocListener<LoginCubit, LoginState>(
                        listener: (context, state) {
                          '????? other baseUrl final = ${EndPoints.baseUrl}'.logD;
                          '>?>?>? page load = ${loginCubit.isVerifyResetError.value}'.logV;
                          if (state is LoginSuccessState) {
                            final data = Injector.instance<AppDB>().userModel?.user;
                            final notAgreed = (Injector.instance<AppDB>().policyRequired ?? [])
                                .where((policyName) {
                                  final latestVersion = Injector.instance<AppDB>().policyMap?[policyName]?['version'];
                                  final hasAccepted =
                                      Injector.instance<AppDB>().userModel?.user.policyVersionsEndorsed?.any(
                                                (e) => (e.policyName) == policyName && e.version == latestVersion,
                                              ) ??
                                          false;
                                  return !hasAccepted;
                                })
                                .cast<String>()
                                .toList();
                            'notAgreed $notAgreed'.logV;
                            if (notAgreed.isNotEmpty) {
                              final unacceptedPolicyList = <PolicyData>[];
          
                              for (final policyKey in notAgreed) {
                                final policy = Injector.instance<AppDB>().policyMap?[policyKey];
                                final title = policy?['title'] ?? policyKey;
                                final version = policy?['version']?.toString() ?? '';
          
                                final contentMap = policy?['content'] as Map<String, dynamic>? ?? {};
                                final contentList = (contentMap['text'] as List?)?.whereType<String>().toList() ?? [];
                                final contentTitle = contentMap['title'] ?? '';
          
                                'content: $contentList'.logV;
          
                                unacceptedPolicyList.add(
                                  PolicyData(
                                    key: policyKey,
                                    title: title as String,
                                    version: version,
                                    content: contentList,
                                    contentTitle: contentTitle as String,
                                  ),
                                );
                              }
          
                              AppNavigation.nextScreen(
                                context,
                                PolicyUpdatePage(
                                  policies: unacceptedPolicyList,
                                ),
                              );
                            }
          
                            if (data != null &&
                                (data.dataProcessing == null ||
                                    data.dataProcessing == false ||
                                    data.dataSharingRpiUnderstood == null ||
                                    data.dataSharing == null)) {
                              // (data['dataProcessing'] == null ||
                              //     data['dataProcessing'] == false ||
                              //     data['dataSharingRPIUnderstood'] == null ||
                              //     data['dataSharing'] == null)) {
                              'data ++++ ==${data.toJson()}'.logD;
                              '??? from here 3'.logD;
                              AppNavigation.pushAndRemoveAllScreen(
                                context,
                                MyDataPage(),
                              );
                            } else {
                              if (Injector.instance<AppDB>().userModel?.user.assessment?.assessmentComplete ?? false) {
                                context.read<MyAlertCubit>().setPlanningScheduleNotification();
                                final state = fetchProgressCheckData();
                                if (state == ProgressCheckState.optional) {
                                  AppNavigation.pushAndRemoveAllScreen(
                                    context,
                                    const ProgressCheckWelcomeBackPage(),
                                  );
                                } else if (state == ProgressCheckState.required) {
                                  AppNavigation.pushAndRemoveAllScreen(
                                    context,
                                    const ProgressCheckWelcomeBackPage(
                                      recoveryNotRequired: false,
                                    ),
                                  );
                                } else if (state == ProgressCheckState.notNeeded) {
                                  '>?>?>? 7'.logV;
                                  AppNavigation.pushAndRemoveAllScreen(
                                    context,
                                    const MyDiagramPage(),
                                  );
                                } else {
                                  '>?>?>? 8'.logV;
                                  AppNavigation.pushAndRemoveAllScreen(
                                    context,
                                    const MyDiagramPage(),
                                  );
                                }
          
                                // return const LanguagePage();
                              } else {
                                AppNavigation.pushAndRemoveAllScreen(
                                  context,
                                  const AssessmentMainPage(),
                                );
                              }
                            }
                          } else if (state is LoginEmailNotVerifiedState) {
                            AppNavigation.pushAndRemoveAllScreen(
                              context,
                              BlocProvider.value(
                                value: loginCubit,
                                child: SignUpAccountVerificationPage(
                                  email: loginCubit.emailController.text,
                                  isSignUp: false,
                                ),
                              ),
                            );
                          }
                        },
                        child: IgnorePointer(
                          ignoring: state is LoginLoadingState,
                          child: ValueListenableBuilder(
                            valueListenable: loginCubit.isENLanguageLoading,
                            
                            builder: (context, value, child) {
                              '????? isEnLanguageLoading.value = ${loginCubit.isENLanguageLoading.value}'.logV;
                              return ValueListenableBuilder(
                                valueListenable: loginCubit.setLanguageLoader,
                                builder: (context, value, child) {
                                  '????? val = setLanguageLoader.value = ${loginCubit.hashCode} = ${loginCubit.setLanguageLoader.value}'.logD;
                                  return AppLoader(
                                    isShowLoader: loginCubit.setLanguageLoader.value,
                                    child: AppLoader(
                                      isShowLoader: loginCubit.isENLanguageLoading.value,
                                      
                                      child: LayoutBuilder(
                                        builder: (context, constrains) {
                                          return SingleChildScrollView(
                                            child: ConstrainedBox(
                                              constraints: BoxConstraints(
                                                minHeight: constrains.maxHeight,
                                              ),
                                              child: Column(
                                                //  crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    children: [
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                          right: AppSize.w20,
                                                          left: AppSize.w20,
                                                          top: AppSize.h50,
                                                        ),
                                                        child: Form(
                                                          key: loginCubit.formKey,
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              AnimatedSwitcher(
                                                                transitionBuilder: (child, animation) => FadeTransition(
                                                                  opacity: animation,
                                                                  child: child,
                                                                ),
                                                                duration: const Duration(
                                                                  milliseconds: 500,
                                                                ),
                                                                child: Column(
                                                                  children: [
                                                                    if ((Injector.instance<AppDB>()
                                                                                .langugaeModel
                                                                                ?.languages
                                                                                ?.length ??
                                                                            1) >
                                                                        1)
                                                                      Row(
                                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                        children: [
                                                                          DropdownSelector(
                                                                            key: ValueKey(context.locale.toString()),
                                                                            width: MediaQuery.of(
                                                                                  context,
                                                                                ).size.width *
                                                                                0.5,
                                                                            selectedOption: (() {
                                                                              final locale = context.locale;
                                                                              final value = '${locale.languageCode}-${locale.countryCode}';
                                                                              final languages = Injector.instance<AppDB>()
                                                                                  .langugaeModel
                                                                                  ?.languages;
                                                                              if (languages == null) return null;
                                                                              var lang;
                                                                              for (final l in languages) {
                                                                                if (l.value == value) {
                                                                                  lang = l;
                                                                                  break;
                                                                                }
                                                                              }
                                                                              print('Dropdown: context.locale= [32m$locale [0m, value= [32m$value [0m, lang.label= [32m${lang?.label} [0m');
                                                                              return lang != null ? lang.label : null;
                                                                            })(),
                                                                            onSelected: (selectedLabel) async {
                                                                              // Log the selected value and corresponding language code
                                                                              'Selected Language: $selectedLabel'.logD;
                                                                              loginCubit.isENLanguageLoading.value = true;
                                                                              final selectedLanguage = Injector.instance<AppDB>()
                                                                                  .langugaeModel
                                                                                  ?.languages
                                                                                  ?.firstWhere(
                                                                                    (e) => e.label == selectedLabel,
                                                                                  );

                                                                              if (selectedLanguage != null) {
                                                                                // Log the value of the selected language
                                                                                'Language Value: ${selectedLanguage.value}'.logD;

                                                                                // Parse language and country code from the selected value (e.g., "en-US" to "en" and "US")
                                                                                final localeParts = selectedLanguage.value?.split(
                                                                                  '-',
                                                                                );
                                                                                if (localeParts != null &&
                                                                                    localeParts.length == 2) {
                                                                                  final locale = Locale(
                                                                                    localeParts[0],
                                                                                    localeParts[1],
                                                                                  );
                                                                                  await context.setLocale(
                                                                                    locale,
                                                                                  );
                                                                                  // Set the new locale (only EasyLocalization)
                                                                                  //await EasyLocalization.of(context)!.setLocale(locale);
                                                                                  final data1 = locale.toString().replaceAll(
                                                                                        '_',
                                                                                        '-',
                                                                                      );
                                                                                  Injector.instance<AppDB>().selectedLangugae =
                                                                                      data1;
                                                                                  loginCubit.isENLanguageLoading.value = false;
                                                                                  'Locale changed to: $locale'.logD;
                                                                                  setState(() {}); // Force rebuild after locale change
                                                                                } else {
                                                                                  loginCubit.isENLanguageLoading.value = false;
                                                                                  'Invalid language code format: ${selectedLanguage.value}'
                                                                                      .logD;
                                                                                }
                                                                                loginCubit.isENLanguageLoading.value = false;
                                                                              }
                                                                            },
                                                                            
                                                                            options: Injector.instance<AppDB>()
                                                                                    .langugaeModel
                                                                                    ?.languages
                                                                                    ?.map(
                                                                                      (e) => e.label,
                                                                                    )
                                                                                    .toList() ??
                                                                                [],
                                                                          ),
                                                                          SizedBox(
                                                                            width: AppSize.w20,
                                                                          ),
                                                                          if (context.locale == const Locale('en', 'US') ||
                                                                              context.locale == const Locale('es', 'US'))
                                                                            AppCachedNetworkImage(
                                                                              width: AppSize.w100,
                                                                              key: Key(AuthLocaleKeys.headerFlag.tr()),
                                                                              imageUrl: AuthLocaleKeys.headerFlag.tr(),
                                                                            )
                                                                          else if (context.locale == const Locale('en', 'CA') ||
                                                                              context.locale == const Locale('fr', 'CA'))
                                                                            AppCachedNetworkImage(
                                                                              width: AppSize.w100,
                                                                              key: Key(AuthLocaleKeys.headerFlag.tr()),
                                                                              imageUrl: AuthLocaleKeys.headerFlag.tr(),
                                                                            ),
                                                                          // if (context.locale.countryCode == 'UK')
                                                                          //   AppCachedNetworkImage(
                                                                          //     imageUrl: AuthLocaleKeys.flagGB.tr(),
                                                                          //   )
                                                                          // else if (context.locale.countryCode == 'CA')
                                                                          //    const AppCachedNetworkImage(
                                                                          //     imageUrl:'https://awscomassets.breakingfreeonline.com/images/flag-canada.png',
                                                                          //   )
                                                                          // else if (context.locale.countryCode == 'US')
                                                                          //   AppCachedNetworkImage(
                                                                          //     imageUrl: AuthLocaleKeys.flagUS.tr(),
                                                                          //   )
                                                                          // else if (context.locale.countryCode == 'AU')
                                                                          //   AppCachedNetworkImage(
                                                                          //     imageUrl: AuthLocaleKeys.flagAusSmall.tr(),
                                                                          //     height: AppSize.h40,
                                                                          //     //width: AppSize.h40,
                                                                          //   )
                                                                          // else
                                                                          //   AppCachedNetworkImage(
                                                                          //     imageUrl: AuthLocaleKeys.flagGB.tr(),
                                                                          //   ),
                                                                        ],
                                                                      )
                                                                    else if (context.locale == const Locale('en', 'AU'))
                                                                      Row(
                                                                        mainAxisAlignment: MainAxisAlignment.end,
                                                                        children: [
                                                                          AppCachedNetworkImage(
                                                                            width: AppSize.w100,
                                                                            imageUrl: AuthLocaleKeys.flagAusSmall.tr(),
                                                                          ),
                                                                        ],
                                                                      )
                                                                    else if (context.locale == const Locale('en', 'GB'))
                                                                      Row(
                                                                        mainAxisAlignment: MainAxisAlignment.end,
                                                                        children: [
                                                                          AppCachedNetworkImage(
                                                                            width: AppSize.w100,
                                                                            key: Key(AuthLocaleKeys.headerFlag.tr()),
                                                                            imageUrl: AuthLocaleKeys.headerFlag.tr(),
                                                                          ),
                                                                        ],
                                                                      ),
                                                                  ],
                                                                ),
                                                              ),
                                                              SpaceV(AppSize.h40),
                                                              //  if (context.locale.countryCode == 'UK')
                                                              Assets.images.ukHeaderLogo.image(
                                                                width: double.infinity,
                                                              ),
                                    
                                                              SpaceV(AppSize.h40),
                                                              ValueListenableBuilder(
                                                                valueListenable: loginCubit.verifyEmailMsg,
                                                                builder: (context, verifyEmailMsg, child) {
                                                                  if (verifyEmailMsg.isEmpty) {
                                                                    return const SizedBox.shrink();
                                                                  } else {
                                                                    return Column(
                                                                      crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                      children: [
                                                                        Text(
                                                                          verifyEmailMsg == 'This account has already been verified.'? AuthLocaleKeys.emailTokenUsed.tr() : verifyEmailMsg == 'Your verification link may have already been used or you may have copied it incorrectly. If this issue persists, please contact customer services.' ? AuthLocaleKeys.emailTokenInvalid.tr() : verifyEmailMsg == 'Your email is now verified. You may log in with your email address and password.' ? AuthLocaleKeys.verifyEmailSuccess.tr() : verifyEmailMsg == 'Your new email address has been verified. You can now log in with your new email address.' ? AuthLocaleKeys.reVerifySuccess.tr() : verifyEmailMsg,
                                                                          //verifyEmailMsg,
                                                                          textAlign: TextAlign.center,
                                                                          style: context.textTheme.titleSmall?.copyWith(
                                                                            fontSize: AppSize.sp13,
                                                                            color: loginCubit.isVerifyEmailError
                                                                                ? context.themeColors.errorRedColor
                                                                                : context.themeColors.greenColor,
                                                                          ),
                                                                        ),
                                                                        SpaceV(AppSize.h10),
                                                                      ],
                                                                    );
                                                                  }
                                                                },
                                                              ),
                                    
                                                              Visibility(
                                                                visible: widget.isResetInvalidTokenError ?? false,
                                                                child: ValueListenableBuilder(
                                                                  valueListenable: loginCubit.isVerifyResetError,
                                                                  builder: (context, isVerifyResetError, child) {
                                                                    return Column(
                                                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                                                        children: [
                                                                          Text(
                                                                            AuthLocaleKeys.errTokenInvalid.tr(),
                                                                            textAlign: TextAlign.center,
                                                                            style: context.textTheme.titleSmall?.copyWith(
                                                                              fontSize: AppSize.sp13,
                                                                              color: context.themeColors.errorRedColor
                                                                            ),
                                                                          ),
                                                                          SpaceV(AppSize.h10),
                                                                        ],
                                                                      );
                                                                  },
                                                                ),
                                                              ),
                                                              // AppTextWidget(
                                    
                                                              ValueListenableBuilder(
                                                                valueListenable: loginCubit.isLoginButtonClicked,
                                                                builder: (context, value, child) {
                                                                  return ValueListenableBuilder(
                                                                    valueListenable: loginCubit.emailError,
                                                                    builder: (
                                                                      context,
                                                                      emailError,
                                                                      _,
                                                                    ) {
                                                                      return Column(
                                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                                        children: [
                                                                          CustomOutlinedTextfield(
                                                                            controller: loginCubit.emailController,
                                                                            textAction: TextInputAction.next,
                                                                            focusNode: loginCubit.emailFocusNode,
                                                                            labelText: AuthLocaleKeys.signUpEmailLabel.tr(),
                                                                            isError: loginCubit.isLoginButtonClicked.value &&
                                                                                emailError != '',
                                                                            // validator: emailValidator().call,
                                                                            onChanged: (_) {
                                                                              loginCubit.emailValid();
                                                                            },
                                                                          ),
                                                                          if (loginCubit.isLoginButtonClicked.value &&
                                                                              emailError != '')
                                                                            CustomErrorWidget(
                                                                              errorMessgaeText: emailError,
                                                                            ),
                                                                        ],
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                              ),
                                                              SpaceV(AppSize.h20),
                                                              ValueListenableBuilder(
                                                                valueListenable: loginCubit.isLoginButtonClicked,
                                                                builder: (context, value, child) {
                                                                  return ValueListenableBuilder(
                                                                    valueListenable: loginCubit.showPassword,
                                                                    builder: (
                                                                      context,
                                                                      value,
                                                                      child,
                                                                    ) {
                                                                      return ValueListenableBuilder(
                                                                        valueListenable: loginCubit.passwordError,
                                                                        builder: (
                                                                          context,
                                                                          passwordError,
                                                                          child,
                                                                        ) {
                                                                          return Column(
                                                                            children: [
                                                                              CustomOutlinedTextfield(
                                                                                focusNode: loginCubit.passwordFocusNode,
                                                                                obscureText: !loginCubit.showPassword.value,
                                                                                isError: loginCubit.isLoginButtonClicked.value &&
                                                                                    passwordError != '',
                                                                                textAction: TextInputAction.done,
                                                                                suffixIcon: GestureDetector(
                                                                                  onTap: loginCubit.togglevisibility,
                                                                                  child: Icon(
                                                                                    loginCubit.showPassword.value
                                                                                        ? Icons.visibility
                                                                                        : Icons.visibility_off,
                                                                                    color: context.themeColors.textfieldTextColor,
                                                                                    size: AppSize.sp18,
                                                                                  ),
                                                                                ),
                                                                                controller: loginCubit.passwordController,
                                                                                // validator: passwordValidator().call,
                                                                                //obscureText: true,
                                                                                labelText: AuthLocaleKeys.logInPasswordLabel.tr(),
                                                                                onChanged: (_) {
                                                                                  loginCubit.passwordValid();
                                                                                },
                                                                              ),
                                                                              if (loginCubit.isLoginButtonClicked.value &&
                                                                                  passwordError != '')
                                                                                CustomErrorWidget(
                                                                                  errorMessgaeText: passwordError,
                                                                                ),
                                                                            ],
                                                                          );
                                                                        },
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                              ),
                                                              ValueListenableBuilder(
                                                                valueListenable: loginCubit.passwordError,
                                                                builder: (
                                                                  context,
                                                                  value,
                                                                  child,
                                                                ) {
                                                                  if (state is LoginFailureState &&
                                                                      loginCubit.validateAllFields() &&
                                                                      state.errorMessage == AuthLocaleKeys.logInInvalid.tr()) {
                                                                    return CustomErrorWidget(
                                                                      errorMessgaeText: AuthLocaleKeys.logInInvalid.tr(),
                                                                    );
                                                                  } else {
                                                                    return const SizedBox();
                                                                  }
                                                                },
                                                              ),
                                                              SpaceV(AppSize.h20),
                                                              InkWell(
                                                                onTap: () {
                                                                  FocusManager.instance.primaryFocus?.unfocus();
                                                                  // loginCubit.formKey.currentState?.reset();
                                    
                                                                  AppNavigation.nextScreen(
                                                                    context,
                                                                    const ForgetPasswordPage(),
                                                                  );
                                                                  loginCubit.clearInput();
                                                                },
                                                                child: AppTextWidget(
                                                                  AuthLocaleKeys.logInForgotPasswordLabel.tr(),
                                                                  style: context.textTheme.titleSmall?.copyWith(
                                                                    fontWeight: FontWeight.w400,
                                                                    color: context.themeColors.greenColor,
                                                                    decoration: TextDecoration.underline,
                                                                  ),
                                                                ),
                                                              ),
                                                              SpaceV(AppSize.h34),
                                                              Center(
                                                                child: RichText(
                                                                  text: TextSpan(
                                                                    text: '${AuthLocaleKeys.noAccountText.tr()} ',
                                                                    style: context.textTheme.titleSmall?.copyWith(
                                                                      fontWeight: FontWeight.w500,
                                                                    ),
                                                                    children: [
                                                                      TextSpan(
                                                                        recognizer: TapGestureRecognizer()
                                                                          ..onTap = () {
                                                                            FocusManager.instance.primaryFocus?.unfocus();
                                                                            AppCommonFunctions.closeKeyboard();
                                                                            loginCubit.formKey.currentState?.reset();
                                                                            AppNavigation.nextScreen(
                                                                              context,
                                                                              const TermsAndConditionPage(),
                                                                            );
                                                                            loginCubit.clearInput();
                                                                          },
                                                                        text: AuthLocaleKeys.signUpText.tr(),
                                                                        style: context.textTheme.titleSmall?.copyWith(
                                                                          fontWeight: FontWeight.w400,
                                                                          color: context.themeColors.greenColor,
                                                                          decoration: TextDecoration.underline,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  
                                                  Column(
                                                    children: [
                                                      GestureDetector(
                                                        onTap: () async {
                                                          FocusManager.instance.primaryFocus?.unfocus();
                                                          AppCommonFunctions.closeKeyboard();
                                                          //loginCubit.formKey.currentState?.reset();
                                                          'test open'.logV;
                                                          if (context.locale.countryCode == 'GB') {
                                                            await launchUrl(
                                                              Uri.parse('https://breakingfreeonline.com/faq/'),
                                                            );
                                                          } else if (context.locale.countryCode == 'US') {
                                                            await launchUrl(
                                                              Uri.parse('https://breakingfreeonline.us/faq/'),
                                                            );
                                                          } else if (context.locale.countryCode == 'CA') {
                                                            await launchUrl(
                                                              Uri.parse('https://breakingfreeonline.ca/faq/'),
                                                            );
                                                          } else {
                                                            await launchUrl(
                                                              Uri.parse('https://breakingfreeonline.com.au/faq/'),
                                                            );
                                                          }
                                    
                                                          loginCubit.clearInput();
                                                        },
                                                        child: AppTextWidget(
                                                          'FAQs',
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontWeight: FontWeight.w500,
                                                            color: context.themeColors.greenColor,
                                                            decoration: TextDecoration.underline,
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      CustomButton(
                                                        isBottom: true,
                                                        isShowArrowForward: false,
                                                        inProgress: state is LoginLoadingState,
                                                        title: AuthLocaleKeys.logInText.tr(),
                                                        onTap: () async {
                                                          loginCubit.isLoginButtonClicked.value = true;
                                                          //   Injector.instance<AppDB>().userModel.logD;
                                                          FocusManager.instance.primaryFocus?.unfocus();
                                                          // if (loginCubit.formKey.currentState?.validate() ?? false) {
                                                          if (loginCubit.validateAllFields()) {
                                                            loginCubit.isLoginButtonClicked.value = false;
                                                            await loginCubit.login(context);
                                                          }
                                                          
                                                          // Injector.instance<AppDB>().userModel?.user.nameOnCertificate.logD;
                                                        },
                                                        // },
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
