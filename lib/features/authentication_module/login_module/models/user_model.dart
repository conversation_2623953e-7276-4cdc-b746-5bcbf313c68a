import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';

class UserModel {
  UserModel({
    required this.success,
    required this.user,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        success: json['success'] as bool,
        user: User.fromJson(json['user'] as Map<String, dynamic>? ?? {}),
      );
  bool success;
  User user;

  Map<String, dynamic> toJson() => {
        'success': success,
        'user': user.toJson(),
      };
}

// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

class User {
  User({
    this.email,
    this.stats,
    this.app,
    this.appData,
    this.resetPassword,
    this.id,
    this.loginTokens,
    this.checkins,
    this.password,
    this.createdAt,
    this.timeSpent,
    this.audioMuted,
    this.dataProcessing,
    this.dataSharing,
    this.nameOnCertificate,
    this.supporters,
    this.userType,
    this.serviceId,
    this.role,
    this.dataDownloads,
    this.dataSharingRpiUnderstood,
    this.audioVolume,
    this.policyVersionsEndorsed,
    this.lang,
    this.surveyNps,
    this.assessment,
    this.strategies,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        email: json['email'] == null ? null : Email.fromJson(json['email'] as Map<String, dynamic>),
        stats: json['stats'] == null ? null : Stats.fromJson(json['stats'] as Map<String, dynamic>),
        app: json['app'] == null ? null : App.fromJson(json['app'] as Map<String, dynamic>),
        appData: json['appData'] == null ? null : AppData.fromJson(json['appData'] as Map<String, dynamic>),
        resetPassword: json['resetPassword'] == null
            ? null
            : ResetPassword.fromJson(json['resetPassword'] as Map<String, dynamic>),
        id: json['_id'] as String?,
        //Todo: update the response after uncomment
        loginTokens:
            json['loginTokens'] == null ? [] : List<String>.from((json['loginTokens'] as List<dynamic>).map((x) => x)),
        checkins: json['checkins'] == null
            ? []
            : List<Checkin>.from(
                (json['checkins'] as List<dynamic>).map((x) => Checkin.fromJson(x as Map<String, dynamic>)),
              ),
        password: json['password'] == null ? null : json['password'] as String,
        createdAt: json['createdAt'] == null ? null : DateTime.parse(json['createdAt'] as String),
        timeSpent: json['timeSpent'] as int? ?? 0,
        audioMuted: json['audioMuted'] == null ? null : json['audioMuted'] as bool,
        dataProcessing: json['dataProcessing'] == null ? null : json['dataProcessing'] as bool,
        dataSharing: json['dataSharing'] == null ? null : json['dataSharing'] as bool,
        nameOnCertificate: json['nameOnCertificate'] == null ? '' : json['nameOnCertificate'] as String,
        supporters:
            json['supporters'] == null ? [] : List<String>.from((json['supporters'] as List<dynamic>).map((x) => x)),
        userType: json['userType'] as String?,
        serviceId: json['serviceId'] as String?,
        role: json['role'] == null ? null : json['role'] as String,
        dataDownloads: json['dataDownloads'] == null ? null : json['dataDownloads'] as int,
        dataSharingRpiUnderstood:
            json['dataSharingRPIUnderstood'] == null ? null : json['dataSharingRPIUnderstood'] as bool,
        audioVolume: json['audioVolume'] == null ? null : (json['audioVolume'] as dynamic)?.toDouble() as double,
        policyVersionsEndorsed: json['policyVersionsEndorsed'] == null
            ? []
            : List<PolicyVersionsEndorsed>.from(
                (json['policyVersionsEndorsed'] as List<dynamic>?)?.map(
                      (x) => PolicyVersionsEndorsed.fromJson(x as Map<String, dynamic>),
                    ) ??
                    [],
              ),
        lang: json['lang'] == null ? null : json['lang'] as String,
        surveyNps: json['surveyNPS'] == null ? null : SurveyNps.fromJson(json['surveyNPS'] as Map<String, dynamic>),
        assessment: json['assessment'] == null ? null : Assessment.fromJson(json['assessment'] as Map<String, dynamic>),
        strategies: json['strategies'] == null ? null : Strategies.fromJson(json['strategies'] as Map<String, dynamic>),
      );
  Email? email;
  Stats? stats;
  AppData? appData;
  App? app;
  ResetPassword? resetPassword;
  String? id;
  List<String>? loginTokens;
  List<Checkin>? checkins;
  String? password;
  DateTime? createdAt;
  int? timeSpent;
  bool? audioMuted;
  bool? dataProcessing;
  bool? dataSharing;
  String? nameOnCertificate;
  List<String>? supporters;
  String? userType;
  String? serviceId;
  String? role;
  int? dataDownloads;
  bool? dataSharingRpiUnderstood;
  double? audioVolume;
  List<PolicyVersionsEndorsed>? policyVersionsEndorsed;
  String? lang;
  SurveyNps? surveyNps;
  Assessment? assessment;
  Strategies? strategies;

  Map<String, dynamic> toJson() => {
        'email': email?.toJson(),
        'stats': stats?.toJson(),
        'app': app?.toJson(),
        'resetPassword': resetPassword?.toJson(),
        '_id': id,
        'loginTokens': loginTokens == null ? <dynamic>[] : List<dynamic>.from(loginTokens!.map((x) => x)),
        'checkins': checkins == null ? <dynamic>[] : List<dynamic>.from(checkins!.map((x) => x.toJson())),
        'password': password,
        'createdAt': createdAt?.toIso8601String(),
        'timeSpent': timeSpent,
        'audioMuted': audioMuted,
        'dataProcessing': dataProcessing,
        'dataSharing': dataSharing,
        'nameOnCertificate': nameOnCertificate,
        'supporters': supporters == null ? <dynamic>[] : List<dynamic>.from(supporters!.map((x) => x)),
        'userType': userType,
        'serviceId': serviceId,
        'role': role,
        'dataDownloads': dataDownloads,
        'dataSharingRPIUnderstood': dataSharingRpiUnderstood,
        'audioVolume': audioVolume,
        'policyVersionsEndorsed':
            policyVersionsEndorsed == null ? <dynamic>[] : List<dynamic>.from(policyVersionsEndorsed!.map((x) => x.toJson())),
        'lang': lang,
        'surveyNPS': surveyNps?.toJson(),
        'assessment': assessment?.toJson(),
        'strategies': strategies?.toJson(),
      };
}

class App {
  App({
    this.accessibility,
    this.alerts,
  });

  factory App.fromJson(Map<String, dynamic> json) => App(
        accessibility: json['accessibility'] == null
            ? null
            : AppAccessibility.fromJson(json['accessibility'] as Map<String, dynamic>),
        alerts: json['alerts'] == null ? null : Alerts.fromJson(json['alerts'] as Map<String, dynamic>),
      );
  AppAccessibility? accessibility;
  Alerts? alerts;

  Map<String, dynamic> toJson() => {
        'accessibility': accessibility?.toJson(),
        'alerts': alerts?.toJson(),
      };
}

class AppAccessibility {
  AppAccessibility({
    this.fontSize,
    this.fontBold,
    this.contrast,
  });

  factory AppAccessibility.fromJson(Map<String, dynamic> json) => AppAccessibility(
        fontSize: json['fontSize'] as dynamic,
        fontBold: json['fontBold'] as bool?,
        contrast: json['contrast'] as dynamic,
      );
  dynamic fontSize;
  bool? fontBold;
  dynamic contrast;

  Map<String, dynamic> toJson() => {
        'fontSize': fontSize,
        'fontBold': fontBold,
        'contrast': contrast,
      };
}

class Alerts {
  Alerts({
    this.progressChecks,
    this.situations,
    this.activities,
    this.commitments,
  });

  factory Alerts.fromJson(Map<String, dynamic> json) => Alerts(
        progressChecks: json['progressChecks'] as bool?,
        situations: json['situations'] as bool?,
        activities: json['activities'] as bool?,
        commitments: json['commitments'] as bool?,
      );
  bool? progressChecks;
  bool? situations;
  bool? activities;
  bool? commitments;

  Map<String, dynamic> toJson() => {
        'progressChecks': progressChecks,
        'situations': situations,
        'activities': activities,
        'commitments': commitments,
      };
}

class AppData {
  AppData({
    this.accessibility,
    this.loginAccess,
    this.notificationsEnabled,
    this.lastTimeActive,
  });

  factory AppData.fromJson(Map<String, dynamic> json) => AppData(
    ///Todo: check issue
        // accessibility: json['accessibility'] == null
        //     ? null
        //     : AppDataAccessibility.fromJson(json['accessibility'] as Map<String, dynamic>),
        loginAccess:
            json['loginAccess'] == null ? [] : List<int>.from((json['loginAccess'] as List<dynamic>).map((x) => x)),
        notificationsEnabled: json['notificationsEnabled'] == null
            ? null
            : NotificationsEnabled.fromJson(json['notificationsEnabled'] as Map<String, dynamic>),
        lastTimeActive: json['lastTimeActive'] as int?,
      );
  AppDataAccessibility? accessibility;
  List<int>? loginAccess;
  NotificationsEnabled? notificationsEnabled;
  int? lastTimeActive;

  Map<String, dynamic> toJson() => {
        'accessibility': accessibility?.toJson(),
        'loginAccess': loginAccess == null ? <dynamic>[] : List<dynamic>.from(loginAccess!.map((x) => x)),
        'notificationsEnabled': notificationsEnabled?.toJson(),
        'lastTimeActive': lastTimeActive,
      };
}

class AppDataAccessibility {
  AppDataAccessibility();

  factory AppDataAccessibility.fromJson() => AppDataAccessibility();

  Map<String, dynamic> toJson() => {};
}

class NotificationsEnabled {
  NotificationsEnabled({
    this.riskyPlaces,
    this.activities,
    this.commitments,
  });

  factory NotificationsEnabled.fromJson(Map<String, dynamic> json) => NotificationsEnabled(
        riskyPlaces: json['riskyPlaces'] as bool,
        activities: json['activities'] as bool,
        commitments: json['commitments'] as bool,
      );
  bool? riskyPlaces;
  bool? activities;
  bool? commitments;

  Map<String, dynamic> toJson() => {
        'riskyPlaces': riskyPlaces,
        'activities': activities,
        'commitments': commitments,
      };
}

class Checkin {
  Checkin({
    this.time,
    this.ei,
    this.life,
    this.rate,
    this.drugs,
    this.drinking,
  });

  factory Checkin.fromJson(Map<String, dynamic> json) => Checkin(
        time: json['time'] as int,
        ei: json['ei'] == null ? null : CheckinEi.fromJson(json['ei'] as Map<String, dynamic>),
        life: json['life'] == null ? null : CheckinLife.fromJson(json['life'] as Map<String, dynamic>),
        rate: json['rate'] == null ? null : CheckinRate.fromJson(json['rate'] as Map<String, dynamic>),
        drugs: json['drugs'] == null ? null : CheckinDrugs.fromJson(json['drugs'] as Map<String, dynamic>),
        drinking: json['drinking'] == null ? null : CheckInDrinking.fromJson(json['drinking'] as Map<String, dynamic>),
      );
  int? time;
  CheckinEi? ei;
  CheckinLife? life;
  CheckinRate? rate;
  CheckinDrugs? drugs;
  CheckInDrinking? drinking;

  Map<String, dynamic> toJson() => {
        'time': time,
        'ei': ei?.toJson(),
        'life': life?.toJson(),
        'rate': rate?.toJson(),
        'drugs': drugs?.toJson(),
        'drinking': drinking?.toJson(),
      };
}

class CheckInDrinking {
  CheckInDrinking({
    this.units,
    this.days,
    this.control,
    this.anxious,
    this.worry,
    this.will,
    this.difficulty,
  });

  factory CheckInDrinking.fromJson(Map<String, dynamic> json) => CheckInDrinking(
        units: json['units'] as int?,
        days: json['days'] as int?,
        control: json['control'] as int?,
        anxious: json['anxious'] as int?,
        worry: json['worry'] as int?,
        will: json['will'] as int?,
        difficulty: json['difficulty'] as int?,
      );
  int? units;
  int? days;
  int? control;
  int? anxious;
  int? worry;
  int? will;
  int? difficulty;

  Map<String, dynamic> toJson() => {
        'units': units,
        'days': days,
        'control': control,
        'anxious': anxious,
        'worry': worry,
        'will': will,
        'difficulty': difficulty,
      };
}

class CheckinDrugs {
  CheckinDrugs({required this.drugDetails});

  factory CheckinDrugs.fromJson(Map<String, dynamic> json) {
    final parsedDrugs = <String, CheckInDrug>{};
    json.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        parsedDrugs[key] = CheckInDrug.fromJson(value);
      }
    });
    return CheckinDrugs(drugDetails: parsedDrugs);
  }
  final Map<String, CheckInDrug> drugDetails;

  Map<String, dynamic> toJson() {
    return drugDetails.map((key, value) => MapEntry(key, value.toJson()));
  }
}

class CheckInDrug {
  CheckInDrug({
    this.amount,
    this.frequency,
    this.control,
    this.anxious,
    this.worry,
    this.will,
    this.difficulty,
  });

  factory CheckInDrug.fromJson(Map<String, dynamic> json) {
    return CheckInDrug(
      amount: json['amount'] as dynamic?,
      frequency: json['frequency'] as int,
      control: json['control'] as int?,
      anxious: json['anxious'] as int?,
      worry: json['worry'] as int?,
      will: json['will'] as int?,
      difficulty: json['difficulty'] as int?,
    );
  }

  dynamic? amount;
  int? frequency;
  int? control;
  int? anxious;
  int? worry;
  int? will;
  int? difficulty;

  Map<String, dynamic> toJson() => {
        'amount': amount,
        'frequency': frequency,
        'control': control,
        'anxious': anxious,
        'worry': worry,
        'will': will,
        'difficulty': difficulty,
      };
}

class CheckinEi {
  CheckinEi({
    this.nervous,
    this.worry,
    this.down,
    this.interest,
  });

  factory CheckinEi.fromJson(Map<String, dynamic> json) => CheckinEi(
        nervous: json['nervous'] as int?,
        worry: json['worry'] as int?,
        down: json['down'] as int?,
        interest: json['interest'] as int?,
      );
  int? nervous;
  int? worry;
  int? down;
  int? interest;

  Map<String, dynamic> toJson() => {
        'nervous': nervous,
        'worry': worry,
        'down': down,
        'interest': interest,
      };
}

class CheckinLife {
  CheckinLife({
    this.quality,
    this.health,
    this.activities,
    this.relationships,
    this.work,
    this.difficulties,
    this.rate,
  });

  factory CheckinLife.fromJson(Map<String, dynamic> json) => CheckinLife(
        quality: json['quality'] as int?,
        health: json['health'] as int?,
        activities: json['activities'] as int?,
        relationships: json['relationships'] as int?,
        work: json['work'] as int?,
        difficulties: json['difficulties'] as int?,
        rate: json['rate'] as int?,
      );
  int? quality;
  int? health;
  int? activities;
  int? relationships;
  int? work;
  int? difficulties;
  int? rate;

  Map<String, dynamic> toJson() => {
        'quality': quality,
        'health': health,
        'activities': activities,
        'relationships': relationships,
        'work': work,
        'difficulties': difficulties,
        'rate': rate,
      };
}

class CheckinRate {
  CheckinRate({
    this.ds,
    this.nt,
    this.ei,
    this.ps,
    this.ub,
    this.ls,
  });

  factory CheckinRate.fromJson(Map<String, dynamic> json) => CheckinRate(
        ds: json['ds'] as int,
        nt: json['nt'] as int,
        ei: json['ei'] as int,
        ps: json['ps'] as int,
        ub: json['ub'] as int,
        ls: json['ls'] as int,
      );
  int? ds;
  int? nt;
  int? ei;
  int? ps;
  int? ub;
  int? ls;

  Map<String, dynamic> toJson() => {
        'ds': ds,
        'nt': nt,
        'ei': ei,
        'ps': ps,
        'ub': ub,
        'ls': ls,
      };
}

class Email {
  Email({
    this.address,
    this.verified,
    this.token,
    this.unverified,
  });

  factory Email.fromJson(Map<String, dynamic> json) => Email(
        address: json['address'] as String,
        verified: json['verified'] as bool,
        token: json['token'] == null ? null : json['token'] as String,
        unverified: json['unverified'] as String? ?? '',
      );
  String? address;
  bool? verified;
  String? token;
  String? unverified;

  Map<String, dynamic> toJson() => {
        'address': address,
        'verified': verified,
        'token': token,
        'unverified': unverified,
      };
}

class PolicyVersionsEndorsed {
  PolicyVersionsEndorsed({
    this.policyName,
    this.version,
    this.timestamp,
  });

  factory PolicyVersionsEndorsed.fromJson(Map<String, dynamic> json) => PolicyVersionsEndorsed(
        policyName: json['policyName'] as String,
        version: json['version'] as String,
        timestamp: json['timestamp'] as int,
      );
  String? policyName;
  String? version;
  int? timestamp;

  Map<String, dynamic> toJson() => {
        'policyName': policyName,
        'version': version,
        'timestamp': timestamp,
      };
}

class ResetPassword {
  ResetPassword({
    this.token,
    this.expires,
  });

  factory ResetPassword.fromJson(Map<String, dynamic> json) => ResetPassword(
        token: json['token'] == null ? null : json['token'] as String,
        expires: json['expires'] == null ? null : json['expires'] as int,
      );
  String? token;
  int? expires;

  Map<String, dynamic> toJson() => {
        'token': token,
        'expires': expires,
      };
}

class Stats {
  Stats({
    this.platform,
    this.meetingFinder,
    this.timeBronze,
    this.timeGold,
    this.timePlatinum,
    this.timeSilver,
    this.diagramViewed,
    this.eiAsPdf,
    this.psAsVideo1,
    this.psAsVideo2,
    this.psAsVideo3,
  });

  factory Stats.fromJson(Map<String, dynamic> json) => Stats(
        platform: json['platform'] == null ? null : Platform.fromJson(json['platform'] as Map<String, dynamic>),
        meetingFinder: json['meetingFinder'] == null
            ? null
            : MeetingFinder.fromJson(json['meetingFinder'] as Map<String, dynamic>),
        timeBronze: json['timeBronze'] == null ? null : json['timeBronze'] as int,
        timeGold: json['timeGold'] == null ? null : json['timeGold'] as int,
        timePlatinum: json['timePlatinum'] == null ? null : json['timePlatinum'] as int,
        timeSilver: json['timeSilver'] == null ? null : json['timeSilver'] as int,
        diagramViewed: json['diagramViewed'] == null ? null : json['diagramViewed'] as int,
        eiAsPdf: json['eiASPdf'] == null
            ? []
            : List<int>.from((json['eiASPdf'] as List<dynamic>?)?.map((x) => x as int) ?? []),
        psAsVideo1: json['psASVideo1'] == null
            ? []
            : List<int>.from((json['psASVideo1'] as List<dynamic>?)?.map((x) => x as int) ?? []),
        psAsVideo2: json['psASVideo2'] == null
            ? []
            : List<int>.from((json['psASVideo2'] as List<dynamic>?)?.map((x) => x as int) ?? []),
        psAsVideo3: json['psASVideo3'] == null
            ? []
            : List<int>.from((json['psASVideo3'] as List<dynamic>?)?.map((x) => x as int) ?? []),
      );
  Platform? platform;
  MeetingFinder? meetingFinder;
  int? timeBronze;
  int? timeGold;
  int? timePlatinum;
  int? timeSilver;
  int? diagramViewed;
  List<int>? eiAsPdf;
  List<int>? psAsVideo1;
  List<int>? psAsVideo2;
  List<int>? psAsVideo3;

  Map<String, dynamic> toJson() => {
        'platform': platform?.toJson(),
        'meetingFinder': meetingFinder?.toJson(),
        'timeBronze': timeBronze,
        'timeGold': timeGold,
        'timePlatinum': timePlatinum,
        'timeSilver': timeSilver,
        'diagramViewed': diagramViewed,
        'eiASPdf': eiAsPdf == null ? <dynamic>[] : List<dynamic>.from(eiAsPdf!.map((x) => x)),
        'psASVideo1': psAsVideo1 == null ? <dynamic>[] : List<dynamic>.from(psAsVideo1!.map((x) => x)),
        'psASVideo2': psAsVideo2 == null ? <dynamic>[] : List<dynamic>.from(psAsVideo2!.map((x) => x)),
        'psASVideo3': psAsVideo3 == null ? <dynamic>[] : List<dynamic>.from(psAsVideo3!.map((x) => x)),
      };
}

class MeetingFinder {
  MeetingFinder({
    this.viewed,
    this.link1Clicked,
    this.link2Clicked,
    this.link3Clicked,
    this.link4Clicked,
  });

  factory MeetingFinder.fromJson(Map<String, dynamic> json) => MeetingFinder(
        viewed: json['viewed'] == null ? null : json['viewed'] as int,
        link1Clicked: json['link1Clicked'] == null ? null : json['link1Clicked'] as int,
        link2Clicked: json['link2Clicked'] == null ? null : json['link2Clicked'] as int,
        link3Clicked: json['link3Clicked'] == null ? null : json['link3Clicked'] as int,
        link4Clicked: json['link4Clicked'] == null ? null : json['link4Clicked'] as int,
      );
  int? viewed;
  int? link1Clicked;
  int? link2Clicked;
  int? link3Clicked;
  int? link4Clicked;

  Map<String, dynamic> toJson() => {
        'viewed': viewed,
        'link1Clicked': link1Clicked,
        'link2Clicked': link2Clicked,
        'link3Clicked': link3Clicked,
        'link4Clicked': link4Clicked,
      };
}

class Platform {
  Platform({
    this.desktop,
    this.mobile,
  });

  factory Platform.fromJson(Map<String, dynamic> json) => Platform(
        desktop: json['desktop'] as int,
        mobile: json['mobile'] as int? ?? 0,
      );
  int? desktop;
  int? mobile;

  Map<String, dynamic> toJson() => {
        'desktop': desktop,
        'mobile': mobile,
      };
}

class SurveyNps {
  SurveyNps({
    this.doNotBother,
  });

  factory SurveyNps.fromJson(Map<String, dynamic> json) => SurveyNps(
        doNotBother: json['doNotBother'] as bool?,
      );
  bool? doNotBother;

  Map<String, dynamic> toJson() => {
        'doNotBother': doNotBother,
      };
}
