class NotificationModel {
  NotificationModel({
    this.success,
    this.riskyPlaces,
    this.activities,
    this.commitments,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) => NotificationModel(
        success: json['success'] as bool,
        riskyPlaces: json['riskyPlaces'] == null
            ? []
            : List<RiskyPlace>.from(
                (json['riskyPlaces'] as List<dynamic>).map((x) => RiskyPlace.fromJson(x as Map<String, dynamic>)),
              ),
        activities: json['activities'] == null
            ? []
            : List<Activities>.from(
                (json['activities'] as List<dynamic>).map((x) => Activities.fromJson(x as Map<String, dynamic>)),
              ),
        commitments: json['commitments'] == null
            ? []
            : List<Activity>.from(
                (json['commitments'] as List<dynamic>).map((x) => Activity.fromJson(x as Map<String, dynamic>)),
              ),
      );
  bool? success;
  List<RiskyPlace>? riskyPlaces;
  List<Activities>? activities;
  List<Activity>? commitments;

  Map<String, dynamic> toJson() => {
        'success': success,
        'riskyPlaces': riskyPlaces == null ? <dynamic>[] : List<dynamic>.from(riskyPlaces!.map((x) => x.toJson())),
        'activities': activities == null ? <dynamic>[] : List<dynamic>.from(activities!.map((x) => x.toJson())),
        'commitments': commitments == null ? <dynamic>[] : List<dynamic>.from(commitments!.map((x) => x.toJson())),
      };
}

class Activity {
  Activity({
    this.name,
    this.date,
  });

  factory Activity.fromJson(Map<String, dynamic> json) => Activity(
        name: json['name'] as String,
        date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
      );
  String? name;
  DateTime? date;

  Map<String, dynamic> toJson() => {
        'name': name,
        'date': date?.toIso8601String(),
      };
}

class Activities {
  Activities({
    this.name,
    this.time,
    this.type,
    this.day,
    this.date,
  });

  factory Activities.fromJson(Map<String, dynamic> json) => Activities(
        name: json['name'] as String,
        time: json['time'] as String,
        type: json['type'] as String,
        day: json['day'] as String,
        date: json['date'] == null ? null : DateTime.parse(json['date'] as String),
      );
  String? name;
  String? time;
  String? type;
  String? day;
  DateTime? date;

  Map<String, dynamic> toJson() => {
        'name': name,
        'time': time,
        'type': type,
        'day': day,
        'date': date?.toIso8601String(),
      };
}

class RiskyPlace {
  RiskyPlace({
    this.lat,
    this.lng,
    this.what,
    this.how,
  });

  factory RiskyPlace.fromJson(Map<String, dynamic> json) => RiskyPlace(
        lat: json['lat']?.toDouble() as double,
        lng: json['lng']?.toDouble() as double,
        what: json['what'] as String,
        how: json['how'] as String,
      );
  double? lat;
  double? lng;
  String? what;
  String? how;

  Map<String, dynamic> toJson() => {
        'lat': lat,
        'lng': lng,
        'what': what,
        'how': how,
      };
}
