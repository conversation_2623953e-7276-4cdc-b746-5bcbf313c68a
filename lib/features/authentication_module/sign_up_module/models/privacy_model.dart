class PrivacyModel {
  PrivacyModel({
    required this.policies,
    required this.policiesRequired,
  });

  factory PrivacyModel.fromJson(Map<String, dynamic> json) => PrivacyModel(
        policies: Policies.fromJson(Map<String, dynamic>.from(json['policies'] as Map)),
        policiesRequired: List<dynamic>.from(json['policiesRequired'] as List),
      );
  Policies policies;
  List<dynamic> policiesRequired;

  Map<String, dynamic> toJson() => {
        'policies': policies.toJson(),
        'policiesRequired': List<dynamic>.from(policiesRequired.map((x) => x)),
      };
}

class Policies {
  Policies({
    this.eulaUser,
    this.eulaCam,
    this.eulaSf,
    this.privacy,
    this.privacySf,
  });

  factory Policies.fromJson(Map<String, dynamic> json) => Policies(
        eulaUser: Eula.fromJson(Map<String, dynamic>.from(json['eulaUser'] as Map)),
        eulaCam: Eula.fromJson(Map<String, dynamic>.from(json['eulaCam'] as Map)),
        eulaSf: json['eulaSf'] == null ? null : EulaSf.fromJson(Map<String, dynamic>.from(json['eulaSf'] as Map)),
        privacy: json['privacy'] == null ? null : Privacy.fromJson(Map<String, dynamic>.from(json['privacy'] as Map)),
        privacySf:
            json['privacySf'] == null ? null : PrivacySf.fromJson(Map<String, dynamic>.from(json['privacySf'] as Map)),
      );
  Eula? eulaUser;
  Eula? eulaCam;
  EulaSf? eulaSf;
  Privacy? privacy;
  PrivacySf? privacySf;

  Map<String, dynamic> toJson() => {
        'eulaUser': eulaUser?.toJson(),
        'eulaCam': eulaCam?.toJson(),
        'eulaSf': eulaSf?.toJson(),
        'privacy': privacy?.toJson(),
        'privacySf': privacySf?.toJson(),
      };
}

class Eula {
  Eula({
    required this.version,
    required this.title,
    required this.content,
    required this.date,
  });

  factory Eula.fromJson(Map<String, dynamic> json) => Eula(
        version: json['version'] as String? ?? '',
        title: json['title'] as String? ?? '',
        content: EulaCamContent.fromJson(Map<String, dynamic>.from(json['content'] as Map)),
        date: json['date'] as String? ?? '',
      );
  String version;
  String title;
  EulaCamContent content;
  String date;

  Map<String, dynamic> toJson() => {
        'version': version,
        'title': title,
        'content': content.toJson(),
        'date': date,
      };
}

class EulaCamContent {
  EulaCamContent({
    required this.title,
    required this.text,
  });

  factory EulaCamContent.fromJson(Map<String, dynamic> json) => EulaCamContent(
        title: json['title'] as String? ?? '',
        text: List<String>.from(json['text'] as List),
      );
  String title;
  List<String> text;

  Map<String, dynamic> toJson() => {
        'title': title,
        'text': List<dynamic>.from(text.map((x) => x)),
      };
}

class EulaSf {
  EulaSf({
    required this.version,
    required this.title,
    required this.text,
  });

  factory EulaSf.fromJson(Map<String, dynamic> json) => EulaSf(
        version: json['version'] as String? ?? '',
        title: json['title'] as String? ?? '',
        text: List<String>.from(json['text'] as List),
      );
  String version;
  String title;
  List<String> text;

  Map<String, dynamic> toJson() => {
        'version': version,
        'title': title,
        'text': List<dynamic>.from(text.map((x) => x)),
      };
}

class Privacy {
  Privacy({
    required this.version,
    required this.title,
    required this.content,
    required this.date,
  });

  factory Privacy.fromJson(Map<String, dynamic> json) => Privacy(
        version: json['version'] as String? ?? '',
        title: json['title'] as String? ?? '',
        content: PrivacyContent.fromJson(Map<String, dynamic>.from(json['content'] as Map)),
        date: json['date'] as String? ?? '',
      );
  String version;
  String title;
  PrivacyContent content;
  String date;

  Map<String, dynamic> toJson() => {
        'version': version,
        'title': title,
        'content': content.toJson(),
        'date': date,
      };
}

class PrivacyContent {
  PrivacyContent({
    required this.title,
    required this.text,
  });

  factory PrivacyContent.fromJson(Map<String, dynamic> json) => PrivacyContent(
        title: json['title'] as String? ?? '',
        text: List<dynamic>.from(json['text'] as List),
      );
  String title;
  List<dynamic> text;

  Map<String, dynamic> toJson() => {
        'title': title,
        'text': List<dynamic>.from(text.map((x) => x)),
      };
}

class TextClass {
  TextClass({
    required this.type,
    required this.header,
    required this.rows,
  });

  factory TextClass.fromJson(Map<String, dynamic> json) => TextClass(
        type: json['type'] as String? ?? '',
        header: List<String>.from(json['header'] as List),
        rows: List<List<String>>.from((json['rows'] as List).map((x) => List<String>.from(x as List))),
      );
  String type;
  List<String> header;
  List<List<String>> rows;

  Map<String, dynamic> toJson() => {
        'type': type,
        'header': List<dynamic>.from(header.map((x) => x)),
        'rows': List<dynamic>.from(rows.map((x) => List<dynamic>.from(x.map((x) => x)))),
      };
}

class PrivacySf {
  PrivacySf({
    required this.version,
    required this.title,
    required this.text,
  });

  factory PrivacySf.fromJson(Map<String, dynamic> json) => PrivacySf(
        version: json['version'] as String? ?? '',
        title: json['title'] as String? ?? '',
        text: List<dynamic>.from(json['text'] as List),
      );
  String version;
  String title;
  List<dynamic> text;

  Map<String, dynamic> toJson() => {
        'version': version,
        'title': title,
        'text': List<dynamic>.from(text.map((x) => x)),
      };
}
