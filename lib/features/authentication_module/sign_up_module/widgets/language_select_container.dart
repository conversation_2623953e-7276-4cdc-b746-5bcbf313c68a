import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class LanguageSelectContainer extends StatelessWidget {
  const LanguageSelectContainer({super.key, this.languageName, this.onTap, this.borderRadius, this.isLanguageLoading});
  final String? languageName;
  final BorderRadiusGeometry? borderRadius;
  final void Function()? onTap;
  final ValueNotifier<bool>? isLanguageLoading;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ValueListenableBuilder(
        valueListenable: isLanguageLoading ?? ValueNotifier(false),
        builder: (context, value, child) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w20, vertical: AppSize.h8),
            decoration: BoxDecoration(
              color: context.locale.languageCode == (languageName?.toLowerCase() ?? 'en')
                  ? context.themeColors.greenColor
                  : const Color(0xffd9f4d3),
              borderRadius: borderRadius ??
                  BorderRadius.only(
                    bottomLeft: Radius.circular(AppSize.r14),
                    topLeft: Radius.circular(AppSize.r14),
                  ),
            ),
            child: value
                ? CupertinoActivityIndicator(
                    color: context.themeColors.whiteColor,
                  )
                : Text(
                    languageName ?? 'EN',
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      decoration: context.locale.languageCode == (languageName?.toLowerCase() ?? 'en')
                          ? TextDecoration.underline
                          : null,
                      color: context.locale.languageCode == (languageName?.toLowerCase() ?? 'en')
                          ? Colors.white
                          : const Color.fromRGBO(82, 85, 82, 1),
                    ),
                  ),
          );
        },
      ),
    );
  }
}
