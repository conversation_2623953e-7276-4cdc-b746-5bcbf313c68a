import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

class PolicyItemView extends StatefulWidget {
  const PolicyItemView({
    required this.title,
    required this.policy,
    required this.completeCallback,
    required this.parentScrollController,
    required this.onExpand,
    required this.canExpandIndex,
    required this.index,
    super.key,
  });
  final String title;
  final String policy;
  final void Function() completeCallback;
  final void Function() onExpand;
  final int canExpandIndex;
  final int index;
  final ScrollController parentScrollController;

  @override
  State<PolicyItemView> createState() => _PolicyItemViewState();
}

class _PolicyItemViewState extends State<PolicyItemView> {
  final ScrollController _scrollController = ScrollController();
  ValueNotifier<bool> isAtndTrigger = ValueNotifier(false);
  ValueNotifier<bool> isAShowPolicyContainer = ValueNotifier(false);
  bool hasScrolledToBottom = false;

  @override
  void initState() {
    super.initState();
    // Listen to scroll events to check if the user has scrolled to the bottom
    _scrollController.addListener(_checkIfScrolledToBottom);
  }

  void _checkIfScrolledToBottom() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
      // hasScrolledToBottom = true;
      'object'.logD;
      if (!isAtndTrigger.value) {
        widget.completeCallback();
        isAtndTrigger.value = true;
      }

      log('At the bottom');
    }
  }

  @override
  void didUpdateWidget(covariant PolicyItemView oldWidget) {
    if (widget.canExpandIndex != oldWidget.canExpandIndex && widget.index != widget.canExpandIndex) {
      isAShowPolicyContainer.value = false;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    isAtndTrigger.dispose();
    isAShowPolicyContainer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ValueListenableBuilder(
          valueListenable: isAShowPolicyContainer,
          builder: (context, value, child) {
            return InkWell(
              onTap: () {
                if (!isAShowPolicyContainer.value) {
                  widget.onExpand();
                }
                isAShowPolicyContainer.value = !isAShowPolicyContainer.value;
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: const Color.fromRGBO(224, 224, 224, 1)),
                  color: context.themeColors.scaffoldColor,
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h14),
                  child: Row(
                    children: [
                      Icon(
                        isAShowPolicyContainer.value
                            ? Icons.keyboard_arrow_down_outlined
                            : Icons.arrow_forward_ios_outlined,
                        color: const Color.fromRGBO(113, 113, 113, 1),
                        size: isAShowPolicyContainer.value ? AppSize.sp22 : AppSize.sp18,
                      ),
                      Expanded(
                        flex: 10,
                        child: Padding(
                          padding: EdgeInsets.only(left: AppSize.w12),
                          child: AppTextWidget(
                            widget.title,
                            style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp13),
                          ),
                        ),
                      ),
                      const Spacer(),
                      ValueListenableBuilder(
                        valueListenable: isAtndTrigger,
                        builder: (context, value, child) {
                          return value
                              ? Icon(
                                  Icons.check,
                                  color: context.themeColors.greenColor,
                                )
                              : const SizedBox();
                        },
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
        // Scrollable policy content
        ValueListenableBuilder(
          valueListenable: isAShowPolicyContainer,
          builder: (context, value, child) {
            return Visibility(
              visible: isAShowPolicyContainer.value,
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.4, // Adjust the height as per your requirement
                child: NotificationListener(
                  onNotification: (ScrollNotification notification) {
                    if (notification is ScrollUpdateNotification) {
                      if (notification.metrics.pixels == notification.metrics.maxScrollExtent) {
                        widget.parentScrollController.animateTo(
                          widget.parentScrollController.position.maxScrollExtent,
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeIn,
                        );
                      } else if (notification.metrics.pixels == notification.metrics.minScrollExtent) {
                        widget.parentScrollController.animateTo(
                          widget.parentScrollController.position.minScrollExtent,
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeIn,
                        );
                      }
                    }
                    return true;
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color.fromRGBO(224, 224, 224, 1)),
                      color: context.themeColors.whiteColor,
                    ),
                    child: CustomRawScrollbar(
                      controller: _scrollController,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: AppSize.h10, horizontal: AppSize.w12),
                          child: Html(
                            data: '<p>${widget.policy}</p>',
                            onLinkTap: (url, attributes, element) async {
                              url.logD;
                              if (url?.isNotEmpty ?? false) {
                                await launchUrl(
                                  Uri.parse('${'https://breakingfreeonline.com'}$url'),
                                );
                              }
                            },
                            style: {
                              'h1': Style(
                                fontSize: FontSize(AppSize.sp18),
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w600,
                              ),
                              'p': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                              'body': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
