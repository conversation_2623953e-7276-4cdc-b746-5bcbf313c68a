import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:flutter/material.dart';

class CustomTextfield extends StatelessWidget {
  const CustomTextfield(
      {super.key, this.textEditingController, this.inputType, this.onChange, this.obSecure, this.validator,});
  final TextEditingController? textEditingController;
  final TextInputType? inputType;
  final void Function(String value)? onChange;
  final bool? obSecure;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w400),
      controller: textEditingController,
      onChanged: onChange,
      keyboardType: inputType,
      obscureText: obSecure ?? false,
      cursorColor: Colors.black,
      validator: validator,
      decoration: const InputDecoration(
        enabledBorder: UnderlineInputBorder(),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            width: 2,
          ),
        ),
        border: UnderlineInputBorder(),
      ),
    );
  }
}
