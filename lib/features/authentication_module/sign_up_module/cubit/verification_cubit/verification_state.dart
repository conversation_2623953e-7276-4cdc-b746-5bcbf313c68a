part of 'verification_cubit.dart';

/// Policy state for handling Verification cubit.
sealed class VerificationState {}

/// Initial Verification state.
final class InitialVerificationState extends VerificationState {}

/// Loading Verification state.
final class LoadingVerificationState extends VerificationState {}

/// Success Verification state.
final class SuccessVerificationState extends VerificationState {}

/// Error Verification state.
final class FailedVerificationState extends VerificationState {}
