import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'verification_state.dart';

class VerificationCubit extends Cubit<VerificationState> {
  VerificationCubit() : super(InitialVerificationState());

  // String verifiedText = '';
  // String notVerifiedTextUser = '';
  // String notVerifiedText = '';
  // String notVerifiedTextBottomUserFirst = '';
  // String notVerifiedTextBottomFirst = '';
  // String signUpSuccessText1 = '';
  // String notVerifiedTextBottomUserLast = '';
  // String notVerifiedTextBottomLast = '';


  final AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isRequestLoading= ValueNotifier(false);
  ValueNotifier<bool> isVerifiedDataLoading= ValueNotifier(true);

  Future<void> resendVerificationEmail() async {
    emit(LoadingVerificationState());
    try {
      isRequestLoading.value = true;
      final policies = await authRepository.resendVerificationEmail(context: navigatorKey.currentContext);
      if (policies != null && policies.data != null) {
        emit(SuccessVerificationState());
      } else {
        emit(FailedVerificationState());
      }
    } catch (e) {
      emit(FailedVerificationState());
    } finally {
      isRequestLoading.value = false;
    }
  }
}
