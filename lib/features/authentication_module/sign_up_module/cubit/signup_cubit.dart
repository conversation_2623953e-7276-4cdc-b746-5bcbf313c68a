import 'dart:async';
import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/policy_cubit/policy_cubit.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/cubit/setting_cubit.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'signup_state.dart';

class SignupCubit extends Cubit<SignupState> {
  SignupCubit() : super(SignupInitialState()) {
    // getPolicies1();

    navigatorKey.currentContext?.read<PolicyCubit>().getPolicies1();
  }

  final AuthRepository authRepository = AuthRepository();

  final TextEditingController emailController = TextEditingController();
  final TextEditingController confirmEmailController = TextEditingController();

  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final TextEditingController accessCodeController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  Map<String, dynamic>? policy;
  final formKey = GlobalKey<FormState>();

  ValueNotifier<bool> showPasswordSuggestion = ValueNotifier(false);
  ValueNotifier<int> expandableIndex = ValueNotifier(-1);
  ValueNotifier<int> acceptedPolicyCount = ValueNotifier(0);

  ValueNotifier<bool> showPolicyWarningBox = ValueNotifier(false);
  ValueNotifier<bool> isPolicyContinue = ValueNotifier(false);
  ValueNotifier<bool> isSignUpButtonClicked = ValueNotifier(false);

  ValueNotifier<String> emailError = ValueNotifier('');
  ValueNotifier<String> confirmEmailError = ValueNotifier('');
  ValueNotifier<String> passwordError = ValueNotifier('');
  ValueNotifier<String> confirmPasswordError = ValueNotifier('');
  ValueNotifier<String> accessCodeError = ValueNotifier('');

  int timeCounter = 0;
  int userDatatimeCounter = 0;
  Timer? timer;
  Timer? getUsertimer;
  bool isAppActive = true;
  // Toggle visibility for SuggestPasswordFormatWidget

  // final signUpNoteList = DynamicAssetLoader.getNestedValue(
  // AuthLocaleKeys.signUpNote,
  // navigatorKey.currentContext!,
  // ) as List;

  // final List<String> signUpNoteList = (DynamicAssetLoader.getNestedValue(
  //   AuthLocaleKeys.signUpNote,
  //   navigatorKey.currentContext!,
  // ) as List)
  //     .cast<String>();

  void startTimer(BuildContext context) {
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 60), (timer) {
      if (isAppActive) {
        'timeCounter +++ $timeCounter'.logD;
        if ((Injector.instance<AppDB>().isAppLogin ?? false) == true) {
          timeCounter += 60;
          emit(TimeSuccessState(timeCounter));
          'timeCounter +++ $timeCounter'.logD;
          userTimeSpentApi(context: context);
        }
      }
    });
  }

  void startGetUserTimer(BuildContext context) {
    getUsertimer?.cancel(); // Stop any existing timer before starting a new one
    getUsertimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (isAppActive) {
        'getCounter +++ $userDatatimeCounter'.logD;

        timeCounter += 300;
        // userDatatimeCounter += 5;
        emit(UserTimeSuccessState(userDatatimeCounter));
        'getCounter +++ $timeCounter'.logD;
        getUserData(context);
      }
    });
  }

  void onAppLifecycleStateChanged(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      isAppActive = true;
      getUserData(navigatorKey.currentContext!);
      startGetUserTimer(navigatorKey.currentContext!);
      startTimer(navigatorKey.currentContext!);
    } else {
      isAppActive = false;
      stopTimer();
      // getUsertimer?.cancel();
    }
  }

  void stopTimer() {
    timer?.cancel();
    timeCounter = 0;
    userDatatimeCounter = 0;
    getUsertimer?.cancel();
    emit(TimeSuccessState(timeCounter));
    emit(UserTimeSuccessState(userDatatimeCounter));
  }

  bool emailValid() {
    var isValid = true;
    final emailValid =
        RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(emailController.text);
    // Email
    if (emailController.text.trim().isEmpty) {
      emailError.value = AuthLocaleKeys.signUpEmailRequired.tr();
      isValid = false;
      emailError.value = '';
    } else if (!emailValid) {
      emailError.value = AuthLocaleKeys.signUpEmailInvalid.tr();
      isValid = false;
      emailError.value = '';
    } else {
      emailError.value = '';
    }
    return isValid;
  }

  bool confirmEmailValid() {
    var isValid = true;

    // Confirm Email
    if (confirmEmailController.text.trim().isEmpty) {
      confirmEmailError.value = AuthLocaleKeys.signUpEmailRequired.tr();
      isValid = false;
      confirmEmailError.value = '';
    } else if (emailController.text.trim().toLowerCase() != confirmEmailController.text.trim().toLowerCase()) {
      confirmEmailError.value = AuthLocaleKeys.signUpEmailNotMatch.tr();
      isValid = false;
      confirmEmailError.value = '';
    } else {
      confirmEmailError.value = '';
    }
    return isValid;
  }

  bool passwordValid() {
    var isValid = true;

    if (passwordController.text.trim().isEmpty) {
      passwordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      isValid = false;
      passwordError.value = '';
    } else if (passwordController.text.trim().length < 8) {
      passwordError.value = AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
      passwordError.value = '';
    } else {
      passwordError.value = '';
    }
    return isValid;
  }

  bool confirmPasswordValid() {
    var isValid = true;

    if (confirmPasswordController.text.trim().isEmpty) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      isValid = false;
      confirmPasswordError.value = '';
    } else if (confirmPasswordController.text.trim() != passwordController.text.trim()) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordNotMatch.tr();
      isValid = false;
      confirmPasswordError.value = '';
    } else if (confirmPasswordController.text.length < 8) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
      confirmPasswordError.value = '';
    } else {
      confirmPasswordError.value = '';
    }
    return isValid;
  }

  bool accessCodeValid() {
    emit(SignupInitialState());
    var isValid = true;

    // Access code
    if (accessCodeController.text.trim().isEmpty) {
      accessCodeError.value = AuthLocaleKeys.signUpCodeRequired.tr();
      isValid = false;
      accessCodeError.value = '';
    } else {
      accessCodeError.value = '';
    }

    return isValid;
  }

  bool validateAllFields() {
    var isValid = true;

    final emailValid =
        RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(emailController.text);
    // Email
    if (emailController.text.trim().isEmpty) {
      emailError.value = AuthLocaleKeys.signUpEmailRequired.tr();
      isValid = false;
    } else if (!emailValid) {
      emailError.value = AuthLocaleKeys.signUpEmailInvalid.tr();
      isValid = false;
    } else {
      emailError.value = '';
    }

    // Confirm Email
    if (confirmEmailController.text.trim().isEmpty) {
      confirmEmailError.value = AuthLocaleKeys.signUpEmailRequired.tr();
      isValid = false;
    } else if (confirmEmailController.text.trim().toLowerCase() != emailController.text.trim().toLowerCase()) {
      confirmEmailError.value = AuthLocaleKeys.signUpEmailNotMatch.tr();
      isValid = false;
    } else {
      confirmEmailError.value = '';
    }

    // Password
    if (passwordController.text.trim().isEmpty) {
      passwordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      isValid = false;
    } else if (passwordController.text.length < 8) {
      passwordError.value = AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
    } else {
      passwordError.value = '';
    }

    // Confirm Password
    if (confirmPasswordController.text.trim().isEmpty) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      isValid = false;
    } else if (confirmPasswordController.text.trim() != passwordController.text.trim()) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordNotMatch.tr();
      isValid = false;
    } else if (confirmPasswordController.text.length < 8) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
    } else {
      confirmPasswordError.value = '';
    }

    // Access code
    if (accessCodeController.text.trim().isEmpty) {
      accessCodeError.value = AuthLocaleKeys.signUpCodeRequired.tr();
      isValid = false;
    } else {
      accessCodeError.value = '';
    }

    return isValid;
  }

  void togglePasswordSuggestion() {
    showPasswordSuggestion.value = !showPasswordSuggestion.value;
  }

  Future<void> createAccount(BuildContext context) async {
    emit(SignupLoadingState());
    if (formKey.currentState?.validate() ?? false) {
      
      try {
        final response = await authRepository.signUp(
          email: emailController.text,
          password: passwordController.text,
          serviceCode: accessCodeController.text,
          context: context,
        );
        
        if (response != null && response.data!['success'] == true) {
          emit(SignupSuccessState());
        } else {
          emit(SignupFailureState(''));
        }
      } catch (e) {
        'Error: $e'.logD;
        // CustomSnackbar.showErrorSnackBar(
        //   message: e.toString(),
        // );
        if (e is AppException) {
          emit(SignupFailureState(e.message)); // Use the exception's message
        } else {
          emit(SignupFailureState('Something went wrong, please try again.'));
        }
      }
    }
  }

  Future<void> getPolicies() async {
    emit(SignupLoadingState());
    try {
      final policies = await authRepository.getPolicies(context: navigatorKey.currentContext);
      if (policies != null && policies.data != null) {
        policy = policies.data;
        emit(SignupSuccessState());
      } else {
        emit(SignupFailureState('Failed to fetch policies.'));
      }
    } catch (e) {
      if (e is AppException) {
        emit(SignupFailureState(e.message));
      } else {
        emit(SignupFailureState('Something went wrong, please try again.'));
      }
    }
  }

  Future<void> logOutApi(BuildContext context) async {
    emit(LogOutLoadingState());
    try {
      final response = await authRepository.logOut(
        context: context,
      );
      if (response != null && response.data!['success'] == true) {

      } else {}
      emit(SignupInitialState());
    } catch (e) {
      emit(SignupInitialState());
      log('message$e');
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
    }
  }

  Future<void> getUserData(BuildContext context) async {
    try {
      await authRepository.getUserData(context: context);
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
    }
  }

  Future<void> userTimeSpentApi({required BuildContext context}) async {
    try {
      final response = await authRepository.userTimeSpent(
        context: context,
      );
      if (response != null && response.data!['success'] == true) {
        'userTimeSpentApi $response'.logD;
      } else {}
    } catch (e) {
      log('message$e');
    }
  }

  //  Future<void> getPolicies() async {
  //   emit(LoadingPolicyState());
  //   try {
  //     final policies = await authRepository.getPolicies(context: navigatorKey.currentContext);
  //     if (policies != null && policies.data != null) {
  //       'privacyModel.policies.eulaUser.content.text${policies.data}'.logD;

  //       emit(SuccessPolicyState(PrivacyModel.fromJson(policies.data!), policies.data!));
  //     } else {
  //       'privacyModel.policies.eulaUser.content.text=='.logD;

  //       emit(FailedPolicyState());
  //     }
  //   } catch (e) {
  //     'privacyModel.policies.eulaUser.content.text'.logD;

  //     emit(FailedPolicyState());
  //   }
  // }

  void clearInput() {
    emailController.clear();
    confirmEmailController.clear();
    passwordController.clear();
    confirmPasswordController.clear();
    accessCodeController.clear();
    emailError.value = '';
    confirmEmailError.value = '';
    passwordError.value = '';
    confirmPasswordError.value = '';
    accessCodeError.value = '';
    emit(SignupInitialState());
  }

  @override
  Future<void> close() {
    emailController.dispose();
    confirmEmailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    accessCodeController.dispose();
    scrollController.dispose();
    expandableIndex.dispose();
    acceptedPolicyCount.dispose();
    emailError.dispose();
    confirmEmailError.dispose();
    passwordError.dispose();
    confirmPasswordError.dispose();
    accessCodeError.dispose();
    // timer.dispose();
    return super.close();
  }
}
