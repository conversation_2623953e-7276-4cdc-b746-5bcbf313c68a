import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/models/privacy_model.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'policy_state.dart';

class PolicyCubit extends Cubit<PolicyState> {
  PolicyCubit() : super(InitialPolicyState()) {
    //getPolicies();
  }
  PrivacyModel? termsPrivcey;

  final AuthRepository authRepository = AuthRepository();

  List<String> policyData() {
    final content = navigatorKey.currentContext!
        .read<PolicyCubit>()
        .termsPrivcey
        ?.policies
        .eulaUser
        ?.content;

    final rawTitle = content?.title ?? '';
    final cleanedTitle = rawTitle.replaceFirst(RegExp(r'^!'), '');

    final processedTexts = List<String>.from(content?.text ?? []).map((line) {
      final cleanedLine = line.replaceAllMapped(
        RegExp(r'!+(\d)'), // Matches one or more '!' before a digit
            (match) => match.group(1)!,
      );

      final numberPattern = RegExp(r'\b\d+(\.\d+)?\b');
      final formattedLine = cleanedLine.replaceAllMapped(
        numberPattern,
            (match) => '<b>${match.group(0)}</b>',
      );

      return formattedLine;
    }).toList()
      ..insert(
        0,
        '<div style="text-align:center"><h4>$cleanedTitle</h4></div>',
      );

    return processedTexts;
  }




  Future<void> getPolicies1() async {
    emit(LoadingPolicyState());
    try {
      final policies = await authRepository.getPolicies(context: navigatorKey.currentContext);
      if (policies != null && policies.data != null) {
        'privacyModel.policies.eulaUser.content.text${policies.data}'.logD;
        termsPrivcey = PrivacyModel.fromJson(policies.data!);
        final eulaUserPolicy = termsPrivcey?.policies.eulaUser;
        final privacyPolicy = termsPrivcey?.policies.privacy;

// Print their content safely
        '📝 EULA User Policy: ${eulaUserPolicy?.version}'.logD;
        '🔒 Privacy Policy: ${privacyPolicy?.version}'.logD;
        emit(SuccessPolicyState(PrivacyModel.fromJson(policies.data!), policies.data!));
      } else {
        'privacyModel.policies.eulaUser.content.text=='.logD;

        emit(FailedPolicyState('Failed to fetch policies.'));
      }
    } catch (e) {
      'privacyModel.policies.eulaUser.content.text'.logD;

      if (e is AppException) {
        emit(FailedPolicyState(e.message)); // Use the exception's message
      } else {
        emit(FailedPolicyState('Something went wrong, please try again.'));
      }
    }
  }
}
