part of 'policy_cubit.dart';

/// Policy state for handling policy cubit.
sealed class PolicyState {}

/// Initial policy state.
final class InitialPolicyState extends PolicyState {}

/// Loading policy state.
final class LoadingPolicyState extends PolicyState {}

/// Success policy state.
final class SuccessPolicyState extends PolicyState {
  SuccessPolicyState(this.privacyModel, this.rawPrivacy);
  final PrivacyModel privacyModel;
  final Map<String, dynamic> rawPrivacy;
}

/// Error policy state.
final class FailedPolicyState extends PolicyState {
    FailedPolicyState(this.errorMessage);
  final String errorMessage;
}
