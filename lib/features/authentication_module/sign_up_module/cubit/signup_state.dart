part of 'signup_cubit.dart';

@immutable
sealed class SignupState {}

/// Sign-up Initial State
final class SignupInitialState extends SignupState {}

/// Loading Sign-up state
final class SignupLoadingState extends SignupState {}

/// Successful Sign-up state
final class SignupSuccessState extends SignupState {}

/// Failed Sign-up state
final class SignupFailureState extends SignupState {
  SignupFailureState(this.errorMessage);
  final String errorMessage;
}

/// Loading logout state
final class LogOutLoadingState extends SignupState {}

/// Successful logout state
final class LogOutSuccessState extends SignupState {}

/// Failed logout state
final class LogOutFailureState extends SignupState {}

class TimeSuccessState extends SignupState {
  TimeSuccessState(this.timeCounter);
  final int timeCounter;
}

class UserTimeSuccessState extends SignupState {
  UserTimeSuccessState(this.timeCounter);
  final int timeCounter;
}
