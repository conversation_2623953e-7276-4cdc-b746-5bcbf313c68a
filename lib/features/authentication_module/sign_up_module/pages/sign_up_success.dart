import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class SignUpSuccess extends StatelessWidget {
  const SignUpSuccess({required this.email, super.key});
  final String email;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constrains) {
                  return SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(minHeight: constrains.maxHeight),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            children: [
                              CustomBackArrowButton(
                                onTap: () {
                                  'back ===>'.logD;
                                  AppNavigation.replaceScreen(context, const LoginPage());
                                },
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                child: Column(
                                  children: [
                                    AppTextWidget(
                                      CoreLocaleKeys.titlesSignup.tr(),
                                      style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                                    ),
                                    SpaceV(AppSize.h8),
                                    AppTextWidget(
                                      AuthLocaleKeys.signUpSuccessTitle.tr(),
                                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                    ),
                                    SpaceV(AppSize.h10),
                                    const CustomDivider(),
                                    SpaceV(AppSize.h20),
                                    RichText(
                                      textAlign: TextAlign.center,
                                      text: TextSpan(
                                        style: context.textTheme.titleSmall,
                                        children: () {
                                          final textList = DynamicAssetLoader.getNestedValue(
                                            AuthLocaleKeys.signUpSuccessText1,
                                            context,
                                          ) as List;
                                          final spans = <TextSpan>[];
                                          for (var i = 0; i < textList.length; i++) {
                                            final line = textList[i] as String;

                                            spans.add(TextSpan(text: '$line\n\n'));

                                            if (i == 1) {
                                              spans.add(
                                                TextSpan(
                                                  text: '$email\n\n',
                                                  style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                                                ),
                                              );
                                            }
                                          }

                                          return spans;
                                        }(),
                                      ),
                                    ),

                                  ],
                                ),
                              ),
                            ],
                          ),
                          CustomButton(
                            onTap: () {
                              AppNavigation.pushAndRemoveAllScreen(context,  LoginPage());
                            },
                            isBottom: true,
                            title: AuthLocaleKeys.signUpSuccessButton.tr(),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
