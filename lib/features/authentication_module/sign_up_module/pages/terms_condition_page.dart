import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/policy_cubit/policy_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/sign_up_page.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

class TermsAndConditionPage extends StatefulWidget {
  const TermsAndConditionPage({super.key});

  @override
  State<TermsAndConditionPage> createState() => _TermsAndConditionPageState();
}

class _TermsAndConditionPageState extends State<TermsAndConditionPage> {
  final ScrollController _scrollController = ScrollController();
  ValueNotifier<bool> isAtndTrigger = ValueNotifier(false);
  bool hasScrolledToBottom = false;

  @override
  void initState() {
    super.initState();
    // Listen to scroll events to check if the user has scrolled to the bottom
    _scrollController.addListener(_checkIfScrolledToBottom);
  }

  void _checkIfScrolledToBottom() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
      hasScrolledToBottom = true;
      isAtndTrigger.value = true;
      log('At the bottom');
    }
  }



  @override
  void dispose() {
    _scrollController.dispose();
    '>?>?>? TermsAndConditionPage DISPOSED'.logV;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return BlocBuilder<PolicyCubit, PolicyState>(
      builder: (context, policyState) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          body: SafeArea(
            child: Column(
              //crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const CustomBackArrowButton(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                  child: Column(
                    children: [
                      AppTextWidget(
                        CoreLocaleKeys.titlesPolicyUpdates.tr(),
                        style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                      ),
                      SpaceV(AppSize.h8),
                      AppTextWidget(
                        context.read<PolicyCubit>().termsPrivcey?.policies.eulaUser?.title ?? 'Data Sharing Policy',
                        style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                      ),
                      SpaceV(AppSize.h10),
                      const CustomDivider(),
                      SpaceV(AppSize.h20),
                    ],
                  ),
                ),
                if (policyState is SignupInitialState || policyState is LoadingPolicyState) ...{
                  const Expanded(
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                } else if (policyState is FailedPolicyState) ...{
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            policyState.errorMessage,
                            style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(height: 20),
                          CustomButton(
                            title: 'Retry',
                            onTap: () {
                              context.read<SignupCubit>().getPolicies();
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                } else if (policyState is SuccessPolicyState) ...{
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(right: AppSize.w24, left: AppSize.w24),
                      child: LayoutBuilder(
                        builder: (context, constrains) {
                          return CustomRawScrollbar(
                            controller: _scrollController,
                            child: Padding(
                              padding: EdgeInsets.only(right: AppSize.w6),
                              child: SingleChildScrollView(
                                controller: _scrollController,
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                  child: Column(
                                    children: [
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Html(
                                            data:context.read<PolicyCubit>().policyData().join(' <br> <br> '),
                                            onLinkTap: (url, attributes, element) async {
                                              if (url?.isNotEmpty ?? false) {
                                                url = url!.trim();

                                                final locale = context.locale;
                                                var baseUrl = '';

                                                if (locale == const Locale('en', 'CA') || locale == const Locale('fr', 'CA')) {
                                                  baseUrl = 'https://breakingfreeonline.ca';
                                                } else if (locale == const Locale('en', 'GB')) {
                                                  baseUrl = 'https://breakingfreeonline.com';
                                                } else if (locale == const Locale('en', 'US') || locale == const Locale('es', 'US')) {
                                                  baseUrl = 'https://breakingfreeonline.us';
                                                } else if (locale == const Locale('en', 'AU')) {
                                                  baseUrl = 'https://breakingfreeonline.com.au';
                                                }

                                                Uri finalUri;
                                                if (url.startsWith('http')) {
                                                  finalUri = Uri.parse(url);
                                                } else {
                                                  finalUri = Uri.parse('$baseUrl$url');
                                                }

                                                await launchUrl(finalUri);
                                              }
                                            },
                                            style: {
                                              'h1': Style(
                                                fontSize: FontSize(AppSize.sp18),
                                                fontFamily: 'Poppins',
                                                fontWeight: FontWeight.w600,
                                              ),
                                              'p': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                                              'body': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins'),
                                              'b': Style(fontWeight: FontWeight.bold)
                                            },
                                          ),
                                        ],
                                      ),
                                      SpaceV(AppSize.h10),
                                      ValueListenableBuilder(
                                        valueListenable: isAtndTrigger,
                                        builder: (context, value, child) {
                                          return CustomYesNoButton(
                                            padding: EdgeInsets.zero,
                                            isYesNoButton: true,
                                            exitText: CoreLocaleKeys.buttonsExit.tr(),
                                            agreeText: CoreLocaleKeys.buttonsAgree.tr(),
                                            disableColor: !value ? Colors.grey : Colors.white,
                                            onTapYes: () async {
                                              if (hasScrolledToBottom) {
                                                log('Terms accepted');
                                                await AppNavigation.nextScreen(context, const SignUpPage());
                                              } else {
                                                CustomSnackbar.showErrorSnackBar(
                                                  message: 'You must scroll to the bottom to proceed.',
                                                );
                                              }
                                            },
                                            onTapNo: () {
                                              AppNavigation.previousScreen(context);
                                            },
                                          );
                                        },
                                      ),
                                      SpaceV(AppSize.h20),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                } else ...{
                  const SizedBox.shrink(),
                },
                // ValueListenableBuilder(
                //   valueListenable: isAtndTrigger,
                //   builder: (context, value, child) {
                //     return CustomYesNoButton(
                //       isYesNoButton: true,
                //       exitText: 'Exit',
                //       agreeText: 'I Agree',
                //       // inYesProgress: isAtndTrigger,
                //       disableColor: !value ? Colors.grey : Colors.white,
                //       onTapYes: () async {
                //         //      await context.resetLocale();

                //         if (hasScrolledToBottom) {
                //           log('Terms accepted');
                //           await AppNavigation.replaceScreen(context, const SignUpPage());
                //         } else {
                //           CustomSnackbar.showErrorSnackBar(message: 'You must scroll to the bottom to proceed.');
                //         }
                //       },
                //       onTapNo: () {
                //         AppNavigation.previousScreen(context);
                //       },
                //     );
                //   },
                // ),
              ],
            ),
          ),
        );
      },
    );
  }
}
