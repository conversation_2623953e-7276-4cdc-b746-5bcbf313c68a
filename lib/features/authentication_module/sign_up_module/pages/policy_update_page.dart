// ignore_for_file: avoid_dynamic_calls

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/update_polcy_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/warning_data_container.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/widgets/policy_item_view.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PolicyUpdatePage extends StatefulWidget {
  const PolicyUpdatePage({required this.policies, super.key});
  final List<PolicyData> policies;

  @override
  State<PolicyUpdatePage> createState() => _PolicyUpdatePageState();
}

class _PolicyUpdatePageState extends State<PolicyUpdatePage> {
  @override
  void initState() {
    // context.read<LoginCubit>().getPolicies();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: true,
      appBar: CommonAppBar(
        prefixIcon: Icon(
          Icons.logout,
          size: AppSize.sp20,
        ),
        suffixIcon: Assets.icons.icVolumeUpDisabledIcon.image(
          height: AppSize.h22,
          width: AppSize.w22,
        ),
        onPrefixTap: () async {
          await LogOutDialog.showLogOutDialog(context);
        },
      ),
      body: BlocBuilder<LoginCubit, LoginState>(
        builder: (ctx, state) {
          final signUpCubit = ctx.read<LoginCubit>();
          return Column(
            children: [
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return SingleChildScrollView(
                      controller: signUpCubit.scrollController,
                      child: ConstrainedBox(
                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: AppSize.w24, vertical: AppSize.h24),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                children: [
                                  //  SpaceV(AppSize.h16),
                                  AppTextWidget(
                                    CoreLocaleKeys.titlesPolicyUpdates.tr(),
                                    style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  SpaceV(AppSize.h8),
                                  AppTextWidget(
                                    UpdatePolcyLocaleKeys.title.tr(),
                                    textAlign: TextAlign.center,
                                    style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  SpaceV(AppSize.h8),
                                  const CustomDivider(),
                                  SpaceV(AppSize.h16),
                                  AppTextWidget(
                                    widget.policies.length == 1
                                        ? UpdatePolcyLocaleKeys.intro.tr()
                                        : UpdatePolcyLocaleKeys.inroPlural.tr(),
                                    style: context.textTheme.titleSmall,
                                  ),
                                  SpaceV(AppSize.h16),
                                  ValueListenableBuilder(
                                    valueListenable: signUpCubit.expandableIndex,
                                    builder: (context, value, child) {
                                      final requiredPolicyKeys =
                                          (signUpCubit.policy?['policiesRequired'] as List?)?.cast<String>() ?? [];

                                      final acceptedPolicies =
                                          (signUpCubit.policy?['policiesAccepted'] as List?)?.cast<String>() ?? [];

                                      final unacceptedPolicies =
                                          requiredPolicyKeys.where((key) => !acceptedPolicies.contains(key)).toList();
                                      'unacceptedPolicies $unacceptedPolicies'.logV;
                                      return ListView.builder(
                                        //  itemCount: unacceptedPolicies.length,
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        itemCount: widget.policies.length,
                                        itemBuilder: (context, index) {
                                          'policies ${widget.policies.map(
                                            (e) => e.content,
                                          )}'
                                              .logE;

                                          final policyData = widget.policies[index];

                                          final title = '${policyData.title} (Version ${policyData.version})';

                                          // final rawTextList = (policyData.content['text'] as List<dynamic>) ?? [];

                                          // final cleanedTextList = rawTextList
                                          //     .map((item) {
                                          //       if (item is Map && item['content'] is String) {
                                          //         return item['content'] as String;
                                          //       }
                                          //       return '';
                                          //     })
                                          //     .where((e) => e.isNotEmpty)
                                          //     .toList();
                                          // 'cleanedTextList $cleanedTextList'.logV;
                                          // 'ype ${widget.policies[index].content.runtimeType}'.logV;
                                          // cleanedTextList.insert(
                                          //   0,
                                          //   '<div style="text-align:center"><h4>${policyData.content['title'] ?? ''}</h4></div>',
                                          // );

                                          // final policy = cleanedTextList.join(' <br> <br> ');
                                          // 'policy $policy'.logV;
                                          return PolicyItemView(
                                            parentScrollController: signUpCubit.scrollController,
                                            title: title,
                                            policy:
                                                (((widget.policies[index].content as List?)?.whereType<String>().toList() ??
                                                        [])
                                                      ..insert(
                                                        0,
                                                        '<div style="text-align:center"><h4>${widget.policies[index].contentTitle ?? ''}</h4></div>',
                                                      ))
                                                    .join(' <br><br> '),
                                            index: index,
                                            canExpandIndex: signUpCubit.expandableIndex.value,
                                            onExpand: () {
                                              signUpCubit.expandableIndex.value = index;
                                            },
                                            completeCallback: () {
                                              signUpCubit.acceptedPolicyCount.value++;
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                  SpaceV(AppSize.h16),
                                  AppTextWidget(
                                    widget.policies.length == 1
                                        ? UpdatePolcyLocaleKeys.realAll.tr()
                                        : UpdatePolcyLocaleKeys.realAllPlural.tr(),
                                    style: context.textTheme.titleSmall,
                                  ),
                                  SpaceV(AppSize.h16),
                                  _buildWarningContainer(
                                    signUpCubit,
                                    context,
                                    'To use Breaking Free, you must agree to all policies.',
                                  ),
                                ],
                              ), // Warning box handling

                              Column(
                                children: [
                                  SpaceV(AppSize.h10),
                                  ValueListenableBuilder(
                                    valueListenable: signUpCubit.acceptedPolicyCount,
                                    builder: (context, value, child) {
                                      return CustomYesNoButton(
                                        padding: EdgeInsets.zero,
                                        isYesNoButton: true,
                                        inYesProgress: ValueNotifier(state is SendPolicyLoadingState),
                                        exitText: CoreLocaleKeys.buttonsExit.tr(),
                                        agreeText: CoreLocaleKeys.buttonsAgree.tr(),
                                        isYesDisable:
                                            (widget.policies as List).length != signUpCubit.acceptedPolicyCount.value,
                                        onTapYes: () {
                                          if ((widget.policies as List).length == signUpCubit.acceptedPolicyCount.value) {
                                            signUpCubit.sendPolicy(context: context);
                                          } else {
                                            signUpCubit.isPolicyContinue.value = false;
                                          }
                                        },
                                        onTapNo: () {
                                          signUpCubit.showPolicyWarningBox.value = true;
                                          signUpCubit.isPolicyContinue.value = false;
                                        },
                                      );
                                    },
                                  ),
                                  // SpaceV(AppSize.h10),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Warning box handling as a method for better readability
  Widget _buildWarningContainer(LoginCubit signUpCubit, BuildContext context, String titleDetailText) {
    return ValueListenableBuilder(
      valueListenable: signUpCubit.showPolicyWarningBox,
      builder: (context, value, child) {
        return Padding(
          padding: EdgeInsets.only(top: AppSize.h16, bottom: AppSize.h30),
          child: WarningDataContainer(
            titleDetailText: titleDetailText,
            isContinue: signUpCubit.isPolicyContinue,
            visible: value,
            onCloseIconTap: () {
              signUpCubit.showPolicyWarningBox.value = false;
            },
            onGoBackTap: () {
              if (signUpCubit.isPolicyContinue.value == false) {
                signUpCubit.showPolicyWarningBox.value = false;
              } else {
                signUpCubit.isPolicyContinue.value = false;
              }
            },
            onYesTap: () {
              signUpCubit.isPolicyContinue.value = true;
            },
          ),
        );
      },
    );
  }
}
