import 'dart:io';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/services.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/verification_cubit/verification_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignUpAccountVerificationPage extends StatelessWidget {
  const SignUpAccountVerificationPage({
    required this.email,
    super.key,
    this.isVerifiedEmail = false,
    this.isSignUp = true,
  });
  final bool isVerifiedEmail;
  final bool isSignUp;
  final String email;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => VerificationCubit(),
      child: BlocBuilder<VerificationCubit, VerificationState>(
        builder: (context, state) {
          final ref = context.read<VerificationCubit>();
          return WillPopScope(
            onWillPop: () async {
              // if (!isVerifiedEmail) {
              //   // Close the app
              //   if (Platform.isAndroid) {
              //     SystemNavigator.pop();
              //   } else if (Platform.isIOS) {
              //     exit(0);
              //   }
              //   return false;
              // }
              return true;
            },
            child: Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                suffixIcon: Assets.icons.icVolumeUpDisabledIcon.image(
                  height: AppSize.h22,
                  width: AppSize.w22,
                ),
                onPrefixTap: () async {
                  await LogOutDialog.showLogOutDialog(context);
                },
                // onLogoutTap: () {},
              ),
              body: ColoredBox(
                color: context.themeColors.whiteColor,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ColoredBox(
                        color: const Color.fromRGBO(255, 255, 255, 1),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: AppSize.w24, vertical: AppSize.h20),
                          child: LayoutBuilder(
                            builder: (context, constrains) {
                              return SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        children: [
                                          AppTextWidget(
                                            CoreLocaleKeys.titlesVerify.tr(),
                                            style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                                          ),
                                          SpaceV(AppSize.h4),
                                          AppTextWidget(
                                            isVerifiedEmail
                                                ? AuthLocaleKeys.verifiedTitle.tr()
                                                : AuthLocaleKeys.signUpSuccessTitle.tr(),
                                            style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                          ),
                                          SpaceV(AppSize.h10),
                                          const CustomDivider(),
                                          SpaceV(AppSize.h20),
                                          if (isVerifiedEmail) ...[
                                            AppTextWidget(
                                              (DynamicAssetLoader.getNestedValue(
                                                AuthLocaleKeys.verifiedtext,
                                                context,
                                              ) as List)
                                                  .join('\n\n'),
                                              style: context.textTheme.titleSmall,
                                              textAlign: TextAlign.center,
                                            ),

                                          ] else ...[
                                            if(context.locale.countryCode == 'GB' || context.locale.countryCode == 'CA' )
                                             AppTextWidget(
                                              (DynamicAssetLoader.getNestedValue(
                                                AuthLocaleKeys.notVerifiedTextUser,
                                                context,
                                              ) as List)
                                                  .join('\n\n'),
                                              style: context.textTheme.titleSmall,
                                              textAlign: TextAlign.center,
                                            )
                                            else if(context.locale.countryCode == 'AU' || context.locale.countryCode == 'US' )
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AuthLocaleKeys.notVerifiedText,
                                                  context,
                                                ) as List)
                                                    .join('\n\n'),
                                                style: context.textTheme.titleSmall,
                                                textAlign: TextAlign.center,
                                              ),

                                            SpaceV(AppSize.h18),
                                            AppTextWidget(
                                              email,
                                              style:
                                                  context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                              textAlign: TextAlign.center,
                                            ),
                                            SpaceV(AppSize.h14),
                                            if(context.locale.countryCode == 'GB' || context.locale.countryCode == 'CA' )
                                              AppTextWidget(
                                              (DynamicAssetLoader.getNestedValue(
                                                AuthLocaleKeys.notVerifiedTextBottomUser,
                                                context,
                                              ) as List).first.toString(),
                                              style: context.textTheme.titleSmall,
                                              textAlign: TextAlign.center,
                                            )
                                            else if(context.locale.countryCode == 'AU' || context.locale.countryCode == 'US' )
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AuthLocaleKeys.notVerifiedTextBottom,
                                                  context,
                                                ) as List).first.toString(),
                                                style: context.textTheme.titleSmall,
                                                textAlign: TextAlign.center,
                                              ),

                                            SpaceV(AppSize.h14),
                                            if (isSignUp)
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AuthLocaleKeys.signUpSuccessText1,
                                                  context,
                                                ) as List).last.toString(),
                                              style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                                textAlign: TextAlign.center,
                                              )
                                            else
                                              if(context.locale.countryCode == 'GB' || context.locale.countryCode == 'CA' )
                                                AppTextWidget(
                                                  (DynamicAssetLoader.getNestedValue(
                                                    AuthLocaleKeys.notVerifiedTextBottomUser,
                                                    context,
                                                  ) as List).last.toString(), 
                                                  style: context.textTheme.titleSmall,
                                                  textAlign: TextAlign.center,
                                                )
                                              else if(context.locale.countryCode == 'AU' || context.locale.countryCode == 'US' )
                                                AppTextWidget(
                                                  (DynamicAssetLoader.getNestedValue(
                                                    AuthLocaleKeys.notVerifiedTextBottom,
                                                    context,
                                                  ) as List).last.toString(),
                                                  style: context.textTheme.titleSmall,
                                                  textAlign: TextAlign.center,
                                                ),
                                          ],
                                          SpaceV(AppSize.h20),
                                        ],
                                      ),
                                      Column(
                                        children: [
                                          if (!isSignUp && !isVerifiedEmail)... [
                                              CustomButton(
                                                isDisable: ref.isRequestLoading.value,
                                                disableColor: context.themeColors.orangeColor,
                                              isBottom: true,
                                              padding: EdgeInsets.zero,
                                              inProgress: state is LoadingVerificationState,
                                              color: context.themeColors.orangeColor,
                                              isOverflow: true,
                                              // title: 'hdfkgifygihdfigtbynieyirybty7tyeinnytf75eyntb5tktukr',
                                              title: AuthLocaleKeys.verifiedResendButton.tr(),
                                              onTap: () {
                                                context.read<VerificationCubit>().resendVerificationEmail();
                                              },
                                            ),
                                          ],
                                          SpaceV(AppSize.h10),
                                          CustomButton(
                                            isBottom: true,
                                            padding: EdgeInsets.zero,
                                            title: isVerifiedEmail
                                                ? AuthLocaleKeys.verifiedGetstaredButton.tr()
                                                : AuthLocaleKeys.signUpSuccessButton.tr(),
                                            onTap: () {
                                              if (isVerifiedEmail) {
                                                //AppNavigation.pushAndRemoveAllScreen(context, MyDataPage());
                                                AppNavigation.pushAndRemoveAllScreen(context, const LoginPage());
                                              } else {
                                                // context.read<SignupCubit>().logOutApi(context);
                                                // AppNavigation.pushAndRemoveAllScreen(context,  LoginPage());
                                                context.read<SignupCubit>().logOutApi(context).then(
                          (value) async {
                            await clearAllScheduledNotifications();
                            final langugaeList = Injector.instance<AppDB>().langugaeModel?.languages ?? [];
                            final tempLanguageModel = Injector.instance<AppDB>().langugaeModel;
                            final selectedLangugae = Injector.instance<AppDB>().selectedLangugae;
                            final localizedJson = Injector.instance<AppDB>().localizedJson;

                            '====> ++ ${Injector.instance<AppDB>().langugaeModel?.languages}'.logD;
                            //Injector.instance<AppDB>().userModel = null;
                            //Injector.instance<AppDB>().userModelCopy = null;
                            Injector.instance<AppDB>().isAppLogin = false;
                            await Injector.instance<AppDB>().clearData();
                            Navigator.of(context).pop();
                            Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;
                            Injector.instance<AppDB>().langugaeModel = tempLanguageModel;
                            Injector.instance<AppDB>().langugaeModel?.languages = langugaeList;
                            Injector.instance<AppDB>().selectedLangugae = selectedLangugae;
                            Injector.instance<AppDB>().localizedJson = localizedJson;
                            context.read<SignupCubit>().stopTimer();
                            '====>${Injector.instance<AppDB>().langugaeModel?.languages}'.logV;
                            await prefs.clear();
                            WidgetsBinding.instance.addPostFrameCallback(
                              (timeStamp) async {
                                'Locale already set: ${EasyLocalization.of(navigatorKey.currentContext!)?.locale}'.logD;

                                final savedLang = Injector.instance<AppDB>().selectedLangugae;

                                if (savedLang?.isNotEmpty ?? false) {
                                  final localeParts = savedLang!.replaceAll('-', '_').split('_');
                                  final savedLocale =
                                      Locale(localeParts[0], localeParts.length > 1 ? localeParts[1] : null);

                                  final currentLocale = EasyLocalization.of(navigatorKey.currentContext!)?.locale;

                                  if (savedLocale != currentLocale) {
                                    await EasyLocalization.of(navigatorKey.currentContext!)?.setLocale(savedLocale);
                                    'Locale updated to: $savedLocale'.logI;
                                  } else {
                                    'Locale already set: $currentLocale'.logD;
                                  }
                                }
                              },
                            );
                                       // _debouncer.run(ref.accessibilityApi);
                            await AppNavigation.pushAndRemoveAllScreen(context, const LoginPage());
                          },
                        );
                                              }
                                              // context.read<SignupCubit>().logOutApi(context);
                                              // AppNavigation.pushAndRemoveAllScreen(context,  LoginPage());
                                              
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
// AppTextWidget(
//   'Thank you for verifying your email address.',
//   style: context.textTheme.titleSmall,
//   textAlign: TextAlign.center,
// ),
// AppTextWidget(
//   'Your Breaking Free account is now fully active',
//   style: context.textTheme.titleSmall,
//   textAlign: TextAlign.center,
// ),
// SpaceV(AppSize.h14),
// AppTextWidget(
//   'The next screen will explain how Breaking Free uses your data and will ask you for your concent.',
//   style: context.textTheme.titleSmall,
//   textAlign: TextAlign.center,
// ),
// SpaceV(AppSize.h14),
// AppTextWidget(
//   'PLEASE NOTE: You have the right to change your mind at any time about how your data is processed, stored and shared.',
//   style: context.textTheme.titleSmall,
//   textAlign: TextAlign.center,
// ),