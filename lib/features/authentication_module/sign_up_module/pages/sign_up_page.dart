import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/sign_up_success.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/widgets/suggest_password_format.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  bool _showPassword = false;
  bool _showConfirmPassword = false;

  void _togglevisibility() {
    setState(() {
      _showPassword = !_showPassword;
    });
  }

  void _togglevisibility1() {
    setState(() {
      _showConfirmPassword = !_showConfirmPassword;
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    navigatorKey.currentContext!.read<SignupCubit>().clearInput();
  }

  @override
  Widget build(BuildContext context) {
    final signUpNoteList = (DynamicAssetLoader.getNestedValue(
      AuthLocaleKeys.signUpNote,
      context,
    ) as List?)?.cast<String>() ?? [];

    'signUpNoteList $signUpNoteList'.logD;

    // final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    return GestureDetector(
      onTap: (){
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: SafeArea(
          bottom: false,
          child: BlocBuilder<SignupCubit, SignupState>(
            builder: (ctx, state) {
              final signUpCubit = ctx.read<SignupCubit>();
              return BlocListener<SignupCubit, SignupState>(
                listener: (context, state) {
                  if (state is SignupSuccessState) {
                    AppNavigation.pushAndRemoveAllScreen(
                      context,
                      SignUpSuccess(
                        email: signUpCubit.emailController.text,
                      ),
                    );
                  }
                },
                child: ValueListenableBuilder(
                  valueListenable: signUpCubit.isSignUpButtonClicked,
                  builder: (context,value,child) {
                    return IgnorePointer(
                      ignoring: state is SignupLoadingState,
                      child: Form(
                        key: signUpCubit.formKey,
                        child: LayoutBuilder(
                          builder: (context, constrains) {
                            return SingleChildScrollView(
                              key: const Key('scroll_view'),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      children: [
                                        CustomBackArrowButton(
                                          onTap: () {
                                            if (!Navigator.canPop(context)) {
                                              AppNavigation.replaceScreen(context,  const LoginPage());
                                               // signUpCubit.clearInput();
                                            } else {
                                              AppNavigation.previousScreen(context);
                                              // signUpCubit.clearInput();
                                            }
                                          },
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
                                          child: Column(
                                            children: [
                                              AppTextWidget(
                                                CoreLocaleKeys.titlesSignup.tr(),
                                                style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                                              ),
                                              SpaceV(AppSize.h8),
                                              AppTextWidget(
                                                AuthLocaleKeys.signUpTitleText.tr(),
                                                style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                              ),
                                              SpaceV(AppSize.h10),
                                              const CustomDivider(),
                                              SpaceV(AppSize.h20),
                                              ValueListenableBuilder(
                                                valueListenable: signUpCubit.emailError,
                                                builder: (context,value,child) {
                                                  return Column(
                                                    children: [
                                                      CustomOutlinedTextfield(
                                                        textAction: TextInputAction.next,
                                                        key: const Key('email'),
                                                        controller: signUpCubit.emailController,
                                                        autovalidateMode: AutovalidateMode.onUserInteraction,
                                                        isError: value != '' ,
                                                        labelText: AuthLocaleKeys.signUpEmailLabel.tr(),
                                                        onChanged: (p0) {
                                                          signUpCubit.emailValid();
                                                        },
                                                        // onChanged: (_){
                                                        //   signUpCubit.isSignUpButtonClicked.notifyListeners();
                                                        // },
                                                        // validator: ConfirmEmailValidator(
                                                        //   getPassword: () => signUpCubit.emailController.text,
                                                        //   errorText: AuthLocaleKeys.signUpEmailRequired.tr(),
                                                        // ).call,
                                                      ),
                                                      if(signUpCubit.emailError.value != '')
                                                        CustomErrorWidget(errorMessgaeText: signUpCubit.emailError.value)
                                                      else
                                                        ValueListenableBuilder(
                                                          valueListenable:signUpCubit.accessCodeError ,
                                                          builder: (context,value,child){
                                                            if (state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == AuthLocaleKeys.signUpEmailExist.tr()) {
                                                              return CustomErrorWidget(
                                                                errorMessgaeText: state.errorMessage,
                                                              );
                                                            } else {
                                                              return const SizedBox();
                                                            }
                                                          }
                                                      )
                                                    ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h20),
                                              ValueListenableBuilder(
                                                 valueListenable: signUpCubit.confirmEmailError,
                                                 builder: (context,value,child) {
                                                   return Column(
                                                     children: [
                                                       CustomOutlinedTextfield(
                                                            key: const Key('confirm_email'),
                                                            textAction: TextInputAction.next,
                                                            controller: signUpCubit.confirmEmailController,
                                                            autovalidateMode: AutovalidateMode.onUserInteraction,
                                                            isError: value != '',
                                                            labelText: AuthLocaleKeys.signUpConfirmEmailLabel.tr(),
                                                            onChanged: (p0){
                                                              signUpCubit.confirmEmailValid();
                                                            },
                                                            // validator: ConfirmEmailValidator(
                                                            //   getPassword: () => signUpCubit.emailController.text,
                                                            //   errorText: AuthLocaleKeys.signUpEmailRequired.tr(),
                                                            // ).call,

                                                          ),
                                                       if(value != '')
                                                         CustomErrorWidget(errorMessgaeText: signUpCubit.confirmEmailError.value)
                                                     ],
                                                   );
                                                 },
                                               ),
                                              SpaceV(AppSize.h20),
                                              ValueListenableBuilder(
                                                  valueListenable: signUpCubit.passwordError,
                                                  builder: (context,value,child) {
                                                    return Column(
                                                        children: [
                                                          CustomOutlinedTextfield(
                                                            key: const Key('password'),
                                                            textAction: TextInputAction.next,
                                                            controller: signUpCubit.passwordController,
                                                            autovalidateMode: AutovalidateMode.onUserInteraction,
                                                            isError: value != '' ,
                                                            obscureText: !_showConfirmPassword,
                                                            suffixIcon: GestureDetector(
                                                              onTap: _togglevisibility1,
                                                              child: Icon(
                                                                _showConfirmPassword ? Icons.visibility : Icons.visibility_off,
                                                                color: context.themeColors.textfieldTextColor,
                                                              ),
                                                            ),
                                                            onChanged: (p0){
                                                              signUpCubit.passwordValid();
                                                            },
                                                            labelText: AuthLocaleKeys.signUpPasswordLabel.tr(),
                                                            //   obscureText: true,
                                                            // validator: ConfirmPasswordValidator(
                                                            //   getPassword: () => signUpCubit.passwordController.text,
                                                            //   errorText: AuthLocaleKeys.signUpPasswordRequired.tr(),
                                                            // ).call,
                                                          ),
                                                          if(value != '')
                                                            CustomErrorWidget(errorMessgaeText: signUpCubit.passwordError.value)
                                                          else
                                                    ValueListenableBuilder(
                                                      valueListenable:signUpCubit.accessCodeError ,
                                                      builder: (context,value,child){
                                                        if (state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == 'passwordInsecure') {
                                                          return CustomErrorWidget(
                                                            errorMessgaeText: AuthLocaleKeys.signUpPasswordInSecure.tr(), //state.errorMessage,
                                                          );
                                                        } else {
                                                          return const SizedBox();
                                                        }
                                                      }
                                                  )
                                                          // if(value == '' && signUpCubit.unexpectedError.value)
                                                          //   CustomErrorWidget(errorMessgaeText: AuthLocaleKeys.signUpPasswordInSecure.tr()),
                                                        ],
                                                      );
                                                  },
                                                ),
                                              SpaceV(AppSize.h10),
                                              ValueListenableBuilder(
                                                valueListenable: signUpCubit.showPasswordSuggestion,
                                                builder: (context, value, child) {
                                                  return Column(
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.end,
                                                        children: [
                                                          GestureDetector(
                                                            onTap: signUpCubit.togglePasswordSuggestion,
                                                            child: Visibility(
                                                              visible: !signUpCubit.showPasswordSuggestion.value,
                                                              child: Container(
                                                                decoration:
                                                                    BoxDecoration(color: context.themeColors.lightBlueColor),
                                                                child: Padding(
                                                                  padding: EdgeInsets.symmetric(
                                                                    horizontal: AppSize.w6,
                                                                    vertical: AppSize.h4,
                                                                  ),
                                                                  child: Text(
                                                                    AuthLocaleKeys.signUpPasswordSuggesterButtonText.tr(),
                                                                    style: context.textTheme.labelSmall?.copyWith(
                                                                      color: context.themeColors.blueColor,
                                                                      fontSize: AppSize.sp10,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SuggestPasswordFormatWidget(
                                                        visible: signUpCubit
                                                            .showPasswordSuggestion.value, // Show or hide based on state
                                                        onTap: signUpCubit
                                                            .togglePasswordSuggestion, // Close the widget by toggling visibility
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h10),
                                              ValueListenableBuilder(
                                                valueListenable: signUpCubit.confirmPasswordError,
                                                builder: (context,value,child) {
                                                  return Column(
                                                        children: [
                                                          CustomOutlinedTextfield(
                                                            textAction: TextInputAction.next,
                                                            key: const Key('confirm_password'),
                                                            controller: signUpCubit.confirmPasswordController,
                                                            autovalidateMode: AutovalidateMode.onUserInteraction,
                                                            isError: value != '',
                                                            obscureText: !_showPassword,
                                                            suffixIcon: GestureDetector(
                                                              onTap: _togglevisibility,
                                                              child: Icon(
                                                                _showPassword ? Icons.visibility : Icons.visibility_off,
                                                                color: context.themeColors.textfieldTextColor,
                                                              ),
                                                            ),
                                                            onChanged: (p0){
                                                              signUpCubit.confirmPasswordValid();
                                                            },
                                                            // validator: ConfirmPasswordValidator(
                                                            //   getPassword: () => signUpCubit.passwordController.text,
                                                            //   errorText: AuthLocaleKeys.signUpPasswordRequired.tr(),
                                                            // ).call,

                                                            labelText: AuthLocaleKeys.signUpConfirmPasswordLabel.tr(),
                                                          ),
                                                          if(value != '')
                                                            CustomErrorWidget(errorMessgaeText: signUpCubit.confirmPasswordError.value)

                                                          ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h20),
                                              ValueListenableBuilder(
                                                valueListenable: signUpCubit.accessCodeError,
                                                builder: (context,value,child) {
                                                  return Column(
                                                    children: [
                                                      CustomOutlinedTextfield(
                                                        textAction: TextInputAction.done,
                                                        key: const Key('access_code'),
                                                        controller: signUpCubit.accessCodeController,
                                                        autovalidateMode: AutovalidateMode.onUserInteraction,
                                                        isError: value != '',
                                                        onChanged: (_){
                                                          signUpCubit.accessCodeValid();
                                                        },
                                                        labelText: AuthLocaleKeys.signUpAccessCode.tr(),
                                                        // validator: userNameValidator().call,
                                                      ),
                                                      if(signUpCubit.isSignUpButtonClicked.value && value != '')
                                                        CustomErrorWidget(errorMessgaeText: signUpCubit.accessCodeError.value)

                                                      ],
                                                  );
                                                },
                                              ),
                                                ValueListenableBuilder(
                                                    valueListenable:signUpCubit.accessCodeError ,
                                                    builder: (context,value,child){
                                                      if (state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == AuthLocaleKeys.signUpCodeValid.tr()) {
                                                        return CustomErrorWidget(
                                                          errorMessgaeText:  AuthLocaleKeys.signUpCodeValid.tr(),
                                                        );
                                                      }else if(state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == AuthLocaleKeys.signUpCodeInActive.tr()){
                                                        return CustomErrorWidget(
                                                          errorMessgaeText: AuthLocaleKeys.signUpCodeInActive.tr(),
                                                        );
                                                      }else if(state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == AuthLocaleKeys.signUpCodeExpired.tr()){
                                                        return CustomErrorWidget(
                                                          errorMessgaeText: AuthLocaleKeys.signUpCodeExpired.tr(),
                                                        );
                                                      }else if(state is SignupFailureState && signUpCubit.validateAllFields() && state.errorMessage == AuthLocaleKeys.signUpInternalError.tr()){
                                                        return CustomErrorWidget(
                                                          errorMessgaeText: AuthLocaleKeys.signUpInternalError.tr(),
                                                        );
                                                      } else {
                                                        return const SizedBox();
                                                      }
                                                    }
                                                )


                                            ],
                                          ),
                                        ),
                                        SizedBox(height: AppSize.h10,),
                                        if((Injector.instance<AppDB>().selectedLangugae == 'en-CA' || Injector.instance<AppDB>().selectedLangugae == 'fr-CA') && signUpNoteList.isNotEmpty )
                                          Column(
                                            children: [
                                              SizedBox(height: AppSize.h14),
                                              Html(
                                                data:signUpNoteList.join('<br><br>'),
                                                style: {
                                                  'strong': Style(
                                                    padding: HtmlPaddings.symmetric(horizontal: AppSize.w20),
                                                    fontSize: FontSize(AppSize.sp13),
                                                    fontFamily: 'Poppins',
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                  'body': Style(
                                                    padding: HtmlPaddings.symmetric(horizontal: AppSize.w20),
                                                    margin: Margins.zero,
                                                    fontSize: FontSize(AppSize.sp13),
                                                    fontFamily: 'Poppins',
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                },
                                              ),
                                            ],
                                          )
                                        else
                                          const SizedBox.shrink(),

                                        if(Injector.instance<AppDB>().selectedLangugae == 'en-CA' || Injector.instance<AppDB>().selectedLangugae == 'fr-CA')
                                          SizedBox(height: AppSize.h20,),
                                      ],
                                    ),
                                    // if(!isKeyboardVisible)
                                     CustomYesNoButton(
                                          noButtonColor: context.themeColors.orangeColor,
                                           isYesNoButton: true,
                                          exitText: AuthLocaleKeys.logInBackText.tr(),
                                          agreeText: AuthLocaleKeys.signUpCreateAccountText.tr(),
                                          inYesProgress: ValueNotifier(state is SignupLoadingState),
                                          onTapYes: () async{
                                            signUpCubit.isSignUpButtonClicked.value = true;
                                            FocusManager.instance.primaryFocus?.unfocus();
                                            // if (signUpCubit.formKey.currentState!.validate()) {


                                            if(signUpCubit.validateAllFields()) {
                                              signUpCubit.isSignUpButtonClicked.value = false;


                                              await signUpCubit.createAccount(context);
                                            }
                                          },
                                          onTapNo: () async{
                                            AppNavigation.previousScreen(context);
                                            // signUpCubit.clearInput();
                                          },
                                        ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ),
    );
  }

}
