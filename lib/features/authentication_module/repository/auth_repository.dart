import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/pages/reset_password_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/policy_cubit/policy_cubit.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

final class AuthRepository {
  Future<UserModel?> login({
    required String email,
    required String password,
    required BuildContext? context,
  }) async {
    //  try {
        '????? other email = $email'.logD;
        '????? other password = $password'.logD;
        '????? other lang = ${context?.locale.toString().replaceAll('_', '-') ?? 'en-GB'}'.logD;
        '????? other timeZone = ${timeZoneName}'.logD;
    final response = await APIFunction.postAPICall(
      {
        'email': email,
        'password': password,
        'tz': timeZoneName,
        'lang': context?.locale.toString().replaceAll('_', '-') ?? 'en-GB',
      },
      apiName: EndPoints.login,
      context: context,
    );
    '????? other response = ${response.toString()}'.logD;
    debugPrint(response.runtimeType.toString());
    if (response is Response<Map<String, dynamic>>) {
      final data = response.data;

      if (response.statusCode == 200 && data?['success'] == true) {
        // Login successful, save user data or perform any necessary actions
        Injector.instance<AppDB>().cookie = response.headers.map['set-cookie']?[0];
        'data after login :: ${response.data!}'.logD;
        'data after login :: ${response.data!['createdAt']}'.logD;

        return UserModel.fromJson(response.data!);
      } else {
        // if ((data?['message'] as String).contains('invalid')) {
        //   CustomSnackbar.showErrorSnackBar(
        //     message: AuthLocaleKeys.logInInvalid.tr(),
        //   );
        // } else {
        //   CustomSnackbar.showErrorSnackBar(
        //     message: data?['message'] as String,
        //   );
        // }
        'Injector.instance<AppDB>().localizedJson $data'.logV;

        final message = (data?['message'] as String?) ?? 'Something went wrong';
        throw message.contains('invalid') ? AuthLocaleKeys.logInInvalid.tr() : message;

        //return null;
      }
    } else {
      return null;
    }
    // } catch (e) {
    //   '????? other catch = ${e.toString()}'.logD;
    //   debugPrint('Error logging in: $e');
    //   return null;
    // }
  }

  Future<Response<Map<String, dynamic>>?> signUp({
    required String email,
    required String password,
    required String serviceCode,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'email': email,
          'password': password,
          'serviceCode': serviceCode,
          'lang': context?.locale.toString().replaceAll('_', '-') ?? 'en-GB',
          'tz': timeZoneName,
          'policies': [
            {
              'policyName': 'eulaUser',
              'version': context?.read<PolicyCubit>().termsPrivcey?.policies.eulaUser?.version ?? '1.1',
            },
            {
              'policyName': 'privacy',
              'version': context?.read<PolicyCubit>().termsPrivcey?.policies.privacy?.version ?? '1.1',
            },
          ],
        },
        apiName: EndPoints.register,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        final message = data?['message'] as String? ?? 'Something went wrong';

        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          'ELSE'.logV;
          throw AppException(
            message.contains('emailExists')
                ? AuthLocaleKeys.signUpEmailExist.tr()
                : message.contains('codeInvalid')
                    ? AuthLocaleKeys.signUpCodeValid.tr()
                    : message.contains('codeInactive')
                        ? AuthLocaleKeys.signUpCodeInActive.tr()
                        : message.contains('codeExpired')
                            ? AuthLocaleKeys.signUpCodeExpired.tr()
                            : message.contains('internal')
                                ? AuthLocaleKeys.signUpInternalError.tr()
                                : message,
          );
        }
      } else {
        'ELSE ++'.logV;

        if (response is DioException) {
          final responseData = Map<String, dynamic>.from(response.response?.data as Map? ?? {});
          final message = responseData['message'] as String? ?? 'Something went wrong';

          throw AppException(
            message.contains('emailExists')
                ? AuthLocaleKeys.signUpEmailExist.tr()
                : message.contains('codeInvalid')
                    ? AuthLocaleKeys.signUpCodeValid.tr()
                    : message.contains('codeInactive')
                        ? AuthLocaleKeys.signUpCodeInActive.tr()
                        : message.contains('codeExpired')
                            ? AuthLocaleKeys.signUpCodeExpired.tr()
                            : message.contains('internal')
                                ? AuthLocaleKeys.signUpInternalError.tr()
                                : message,
          );
        }

        return null;
      }
    } catch (e) {
      debugPrint('Error signing up: $e');
      // Optional: rethrow AppException so the caller can handle it (like showing inline error message)
      if (e is AppException) rethrow;

      // Generic fallback
      throw AppException('Something went wrong. Please try again.');
    }
  }

  Future<Response<Map<String, dynamic>>?> getPolicies({
    required BuildContext? context,
  }) async {
    try {
      'context?.locale.languageCode ${context?.locale.languageCode}'.logD;
      'context?.locale.languageCode ${context?.locale}'.logD;

      final response = await APIFunction.getAPICall(
        data: {
          'lang': context?.locale.toString().replaceAll('_', '-') ?? 'en-GB',
        },
        apiName: EndPoints.policies,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data != null) {
          return response;
        } else {
          throw AppException(
            data?['message'] as String ?? 'Something went wrong',
          );
        }
      } else {
        if (response is DioException) {
          final message = Map<String, dynamic>.from(
                response.response?.data as Map,
              )['message'] as String? ??
              'Something went wrong';
          throw AppException(message);
        }
        throw AppException('Unexpected error occurred');
      }
    } catch (e) {
      debugPrint('Error fetching policies: $e');
      rethrow;
    }
  }

  Future<Response<Map<String, dynamic>>?> forgotPassword({
    required String email,
    required BuildContext? context,
  }) async {
    'context?.locale.languageCode : ${context?.locale.languageCode}'.logD;
    '????? context?.locale.languageCode = ${context?.locale.languageCode}'.logV;
    try {
      final response = await APIFunction.postAPICall(
        {
          'email': email,
          'role': 'user',
          //if((Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-CA') ?? false) || (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-US') ?? false))
          if(context?.locale.languageCode != 'en')
          'lang': context?.locale.languageCode ?? 'en',
        },
        apiName: EndPoints.forgotPassword,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data != null) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          // CustomSnackbar.showErrorSnackBar(
          //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          // );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> resetPassword({
    required String token,
    required String password,
    required String confirmPassword,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'token': token,
          'password': password,
        },
        apiName: EndPoints.resetPassword,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data != null) {
          return response;
        } else {
          // CustomSnackbar.showErrorSnackBar(
          //   message: data?['message'] as String,
          // );
          return null;
        }
      } else {
        if (response is DioException) {
          // CustomSnackbar.showErrorSnackBar(
          //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          // );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error in reset password: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> validateResetPasswordToken({
    required String token,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'token': token,
        },
        apiName: EndPoints.validateResetPasswordToken,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data != null) {
          '>?>?>? success = ${response.data.toString()}'.logV;
          return response;
        } else {
          '>?>?>? fail'.logV;
          context.read<LoginCubit>().isVerifyResetError.value = true;
          '>?>?>? isVerifyResetError = ${context.read<LoginCubit>().isVerifyResetError.value}'.logV;
          // CustomSnackbar.showErrorSnackBar(
          //   durationInSecond: 6,
          //   message: AuthLocaleKeys.errTokenInvalid.tr(),
          // );
          // CustomSnackbar.showErrorSnackBar(
          //   message: data?['message'] as String,
          // );
          return null;
        }
      } else {
        context.read<LoginCubit>().isVerifyResetError.value = true;
        // CustomSnackbar.showErrorSnackBar(
        //   durationInSecond: 6,
        //     message: AuthLocaleKeys.errTokenInvalid.tr(),
        //   );
        '>?>?>? fail'.logV;
        '>?>?>? isVerifyResetError2 = ${context.read<LoginCubit>().isVerifyResetError.value}'.logV;
        if (response is DioException) {
          // CustomSnackbar.showErrorSnackBar(
          //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          // );
        }
        return null;
      }
    } catch (e) {
      context.read<LoginCubit>().isVerifyResetError.value = true;
      // CustomSnackbar.showErrorSnackBar(
      //       durationInSecond: 6,
      //       message: AuthLocaleKeys.errTokenInvalid.tr(),
      //     );
      '>?>?>? fail'.logV;
      '>?>?>? isVerifyResetError = ${context.read<LoginCubit>().isVerifyResetError.value}'.logV;
      debugPrint('Error in reset password: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> resendVerificationEmail({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        // TODO(dimil): need to add token
        {
          'token': '',
        },
        apiName: EndPoints.resendVerificationEmail,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Map<String, bool>> verifyEmail({
    required BuildContext? context,
    required String token,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'token': token,
        },
        apiName: EndPoints.verifyEmail,
        context: context,
      );
      
      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true && data?['isReverify'] == false) {
          '?|?|?| verifyEmail success'.logV;
          return {AuthLocaleKeys.verifyEmailSuccess.tr(): false};
        } else if (response.statusCode == 200 && data?['success'] == true && data?['isReverify'] == true) {
          '?|?|?| Re verifyEmail success'.logV;
          return {AuthLocaleKeys.reVerifySuccess.tr(): false};
        } else {
          '?|?|?| message = ${response.data!['message']}'.logV;
          return {
            AuthLocaleKeys.verifyEmailError(response.data!['message'] as String): false,
          };
        }
      } else if (response is DioException) {
        return {
          AuthLocaleKeys.verifyEmailError(
            Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          ).tr(): true,
        };
      } else {
        return {'Something went wrong': true};
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return {'Something went wrong': true};
    }
  }

  Future<Response<Map<String, dynamic>>?> logOut({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        <String, dynamic>{},
        apiName: EndPoints.logout,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<UserModel?> getUserData({
    required BuildContext? context,
  }) async {
    // try {
    final response = await APIFunction.getAPICall(
      apiName: EndPoints.getUserData,
      context: context,
    );

    if (response is Response<Map<String, dynamic>>) {
      final data = response.data;
      if (data != null) {
        if (response.statusCode == 200 && data['success'] == true) {
          'response.headers ${response.headers}'.logD;

          Injector.instance<AppDB>().userModel = UserModel.fromJson(data);
          if (Injector.instance<AppDB>().userModelCopy == null) {
            Injector.instance<AppDB>().userModelCopy = UserModel.fromJson(data);
          }
          return UserModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data['message'] as String,
          );
          return null;
        }
      }
    } else {
      if (response is DioException) {
        CustomSnackbar.showErrorSnackBar(
          message: Map<String, dynamic>.from(
            response.response!.data as Map,
          )['message'] as String,
        );
      }
      return null;
    }
    return null;
    // } catch (e) {
    //   debugPrint('Error logging in: $e');
    //   return null;
    // }
    // return null;
  }

  Future<Response<Map<String, dynamic>>?> stats({
    required String type,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'type': type,
        },
        apiName: EndPoints.userStats,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> sendPolicy({
    required List<Map<String, dynamic>> policyVersionsEndorsed,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'policies': policyVersionsEndorsed,
        },
        apiName: EndPoints.userPolicy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> userTimeSpent({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {},
        apiName: EndPoints.userTimeSpent,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(
              response.response!.data as Map,
            )['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
