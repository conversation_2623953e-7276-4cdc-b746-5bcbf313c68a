import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/cubit/data_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/data_processing_container.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/data_sharing_container.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/download_data_container.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/purge_data_container.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../utils/core_locale_keys.dart';

class MyDataPage extends StatelessWidget {
  MyDataPage({super.key, this.showExtraOptions = false});
  bool showExtraOptions;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    '????? datapage = '.logV;
    return BlocProvider(
      create: (context) => DataCubit(),
      child: BlocConsumer<DataCubit, DataState>(
        listener: (context, state) {
          if (state is PurgeSuccesState) {
            AppNavigation.pushAndRemoveAllScreen(context, const LoginPage());
          }
        },
        builder: (BuildContext ctx, DataState state) {
          final dataCubit = ctx.read<DataCubit>();
          return IgnorePointer(
            ignoring: state is LoadingDataState,
            child: Stack(
              children: [
                ValueListenableBuilder(
                  valueListenable: dataCubit.isFromDataPage,
                  builder: (context, value, child) {
                    // 'value ====>${dataCubit.isFromDataPage}'.logV;
                    // 'value ====> showExtraOptions $showExtraOptions'.logV;
                    return AppScaffold(
                      scaffoldKey: _scaffoldKey,
                      resizeToAvoidBottomInset: true,
                      drawer: !showExtraOptions ? null : AppDrawer(scaffoldKey: _scaffoldKey),
                      appBar: !showExtraOptions || !dataCubit.isFromDataPage.value
                          ? CommonAppBar(
                              prefixIcon: Icon(
                                Icons.logout,
                                size: AppSize.sp20,
                              ),
                              suffixIcon: Assets.icons.icVolumeUpDisabledIcon.image(
                                height: AppSize.h22,
                                width: AppSize.w22,
                              ),
                              onPrefixTap: () async {
                                await LogOutDialog.showLogOutDialog(context);
                              },
                              // onLogoutTap: () {},
                              // centerWidget: AppTextWidget(
                              //   'My Data',
                              //   textAlign: TextAlign.center,
                              //   style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
                              // ),
                            )
                          : CommonAppBar(
                              onPrefixTap: () {
                                _scaffoldKey.currentState?.openDrawer();
                              },
                            ),
                      body: ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Column(
                          children: [
                            Expanded(
                              child: AppLoader(
                                isShowLoader: state is LoadingDataState,
                                child: LayoutBuilder(
                                  builder: (context, constrains) {
                                    return ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                      child: Padding(
                                        padding: EdgeInsets.only(right: AppSize.w2, top: AppSize.h2),
                                        child: CustomRawScrollbar(
                                          child: Container(
                                            padding: EdgeInsets.only(right: AppSize.w28, left: AppSize.w22),
                                            child: ListView(
                                              children: [
                                                Column(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Column(
                                                      children: [
                                                        SpaceV(AppSize.h24),
                                                        AppTextWidget(
                                                          CoreLocaleKeys.sideMenuData.tr(),
                                                          style: context.textTheme.titleLarge
                                                              ?.copyWith(fontWeight: FontWeight.w500),
                                                        ),
                                                        SpaceV(AppSize.h8),
                                                        AppTextWidget(
                                                          DataJsonKeys.title.tr(),
                                                          style: context.textTheme.titleSmall
                                                              ?.copyWith(fontWeight: FontWeight.w500),
                                                        ),
                                                        SpaceV(AppSize.h10),
                                                        const CustomDivider(
                                                          color: Color(0x1F000000),
                                                        ),
                                                        // SpaceV(AppSize.h20),
                                                        DataProcessingContainer(
                                                          dataCubit: dataCubit,
                                                          onNoTap: () {
                                                            dataCubit.setDataProcessing(value: false);
                                                            if (showExtraOptions == true) {
                                                              dataCubit.isFromDataPage.value = false;
                                                            }
                                                          },
                                                          onYesTap: () async {
                                                            'value ====> +++ $showExtraOptions'.logV;
                                                            'value ====> +++++ ${dataCubit.isFromDataPage.value}'.logV;
                                                            await dataCubit.setDataProcessing(value: true).then(
                                                              (value) {
                                                                if (showExtraOptions == true) {
                                                                  dataCubit.isFromDataPage.value = true;
                                                                }
                                                              },
                                                            );
                                                          },
                                                        ),
                                                        const CustomDivider(color: Color(0x1F000000)),
                                                        SpaceV(AppSize.h16),
                                                        DataSharingContainer(
                                                          dataCubit: dataCubit,
                                                        ),
                                                        if (showExtraOptions) const SizedBox() else SpaceV(AppSize.h16),
                                                        if (showExtraOptions) ...[
                                                          const CustomDivider(color: Color(0x1F000000)),
                                                          PurgeDataContainer(dataCubit: dataCubit),
                                                          SpaceV(AppSize.h16),
                                                          const CustomDivider(color: Color(0x1F000000)),
                                                          DownloadDataContainer(dataCubit: dataCubit),
                                                          SpaceV(AppSize.h16),
                                                        ],
                                                      ],
                                                    ),
                                                    if (!showExtraOptions) ...[
                                                      // ValueListenableBuilder(
                                                      //   valueListenable: dataCubit.dataProcessingButtonState,
                                                      //   builder: (ctx, snapshot, child) {
                                                      //     return CustomButton(
                                                      //       padding: EdgeInsets.zero,
                                                      //       key: const Key('next_btn'),
                                                      //       isBottom: true,
                                                      //       onTap: () async {
                                                      //         if (snapshot == ButtonState.yesEnabled) {
                                                      //           await context.read<SignupCubit>().getUserData(context);
                                                      //           await AppNavigation.pushAndRemoveAllScreen(
                                                      //             context,
                                                      //             const AssessmentMainPage(),
                                                      //           );
                                                      //         }
                                                      //       },
                                                      //       title: 'Next',
                                                      //       color: context.themeColors.blueColor
                                                      //           .withOpacity(snapshot == ButtonState.yesEnabled ? 1 : 0.5),
                                                      //       // suffix: Icon(
                                                      //       //   Icons.arrow_forward_ios_outlined,
                                                      //       //   color: context.themeColors.whiteColor,
                                                      //       // ),
                                                      //     );
                                                      //   },
                                                      // ),
                                                      ValueListenableBuilder<ButtonState>(
                                                        valueListenable: dataCubit.dataProcessingButtonState,
                                                        builder: (context, processing, _) {
                                                          return ValueListenableBuilder<ButtonState>(
                                                            valueListenable: dataCubit.dataSharingButtonState,
                                                            builder: (context, sharing, _) {
                                                              return ValueListenableBuilder<ButtonState>(
                                                                valueListenable: dataCubit.dataSharingRpiButtonState,
                                                                builder: (context, rpiSharing, _) {
                                                                  final isProcessingYes =
                                                                      processing == ButtonState.yesEnabled;
                                                                  final isSharingSelected =
                                                                      sharing != ButtonState.bothDisabled;
                                                                  final isRpiSharingSelected =
                                                                      rpiSharing != ButtonState.bothDisabled;
                                
                                                                  final isButtonEnabled = isProcessingYes &&
                                                                      isSharingSelected &&
                                                                      isRpiSharingSelected;
                                
                                                                  return CustomButton(
                                                                    padding: EdgeInsets.zero,
                                                                    key: const Key('next_btn'),
                                                                    isBottom: true,
                                                                    onTap: () async {
                                                                      if (isButtonEnabled) {
                                                                        final userModel = Injector.instance<AppDB>().userModel;
                                                                        // Set the fields to the user's actual choices
                                                                        userModel?.user.dataProcessing = processing == ButtonState.yesEnabled;
                                                                        userModel?.user.dataSharing = sharing == ButtonState.yesEnabled;
                                                                        userModel?.user.dataSharingRpiUnderstood = rpiSharing == ButtonState.yesEnabled;
                                                                        Injector.instance<AppDB>().userModel = userModel;
                                                                        Injector.instance<AppDB>().userModelCopy = userModel;
                                                                        await context.read<SignupCubit>().getUserData(context);
                                                                        await AppNavigation.pushAndRemoveAllScreen(
                                                                          context,
                                                                          const AssessmentMainPage(),
                                                                        );
                                                                      } else {
                                                                        null;
                                                                      }
                                                                    },
                                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                                    color: context.themeColors.blueColor
                                                                        .withOpacity(isButtonEnabled ? 1 : 0.5),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          );
                                                        },
                                                      ),
                                
                                                      SpaceV(AppSize.h20),
                                                    ],
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                // if (state is LoadingDataState)
                //   const Center(
                //     child: CircularProgressIndicator(),
                //   ),
              ],
            ),
          );
        },
      ),
    );
  }
}
