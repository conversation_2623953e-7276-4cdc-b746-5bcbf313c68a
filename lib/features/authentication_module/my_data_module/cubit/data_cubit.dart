// ignore_for_file: avoid_dynamic_calls

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/data_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'data_state.dart';

class DataCubit extends Cubit<DataState> {
  DataCubit() : super(InitialDataState()) {
    final data = Injector.instance<AppDB>().userModel?.user;
    dataProcessingButtonState.value = buttonStateFromBool(value: data?.dataProcessing);
    dataSharingButtonState.value = buttonStateFromBool(value: data?.dataSharing);
    dataSharingRpiButtonState.value = buttonStateFromBool(value: data?.dataSharingRpiUnderstood);
    showWarningBox.value = dataProcessingButtonState.value == ButtonState.noEnabled;
    isFromDataPage = ValueNotifier<bool>(
      dataProcessingButtonState.value != ButtonState.noEnabled,
    );
  }

  final DataRepository repository = DataRepository();
  String directoryPath = '';
  ValueNotifier<bool> showWarningBox = ValueNotifier(false); // Track the visibility state
  ValueNotifier<bool> showDataWarningBox = ValueNotifier(false); // Track the visibility state
  ValueNotifier<bool> isContinue = ValueNotifier(false);
  ValueNotifier<bool> isdataContinue = ValueNotifier(false);
  ValueNotifier<bool> showUserUnderstoodRpiWarning = ValueNotifier(false);
  ValueNotifier<ButtonState> dataProcessingButtonState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> dataSharingButtonState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> dataSharingRpiButtonState = ValueNotifier(ButtonState.bothDisabled);

  bool? isDataSharingRpiUnderstood;
  bool? isDataSharingAllowed;
  ValueNotifier<bool> isFromDataPage = ValueNotifier(false);

  Future<void> setDataProcessing({required bool value}) async {
    emit(LoadingDataState());
    try {
      final response = await repository.setDataProcessing(
        value: value,
        context: navigatorKey.currentContext!,
      );
      if (response != null && response.data?['success'] == true) {
        final data = Injector.instance<AppDB>().userModel;
        //  data?['user']['dataProcessing'] = value;
        data?.user.dataProcessing = value;
        Injector.instance<AppDB>().userModel = data;
        Injector.instance<AppDB>().userModelCopy = data;
        if (value) {
          dataProcessingButtonState.value = ButtonState.yesEnabled; // Enable No, disable Yes
          showWarningBox.value = false;
        } else {
          dataProcessingButtonState.value = ButtonState.noEnabled; // Enable No, disable Yes
          showWarningBox.value = true;
        }

        
      }
      emit(InitialDataState());
    } catch (e) {
      emit(InitialDataState());
    }
  }

  Future<bool> purgeUser() async {
    emit(LoadingDataState());
    try {
      final response = await repository.purgeUser(context: navigatorKey.currentContext!);
      if (response != null && response.data != null && response.data!['success'] == true) {
        Injector.instance<AppDB>().userModel = null;
        Injector.instance<AppDB>().userModelCopy = null;
        await Injector.instance<AppDB>().clearData();
        Navigator.of(navigatorKey.currentContext!).pop();
        Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;

        emit(PurgeSuccesState());

        return true;
      } else {
        emit(InitialDataState());
      }
    } catch (e) {
      emit(InitialDataState());
    }
    return false;
  }

  Future<bool> downloadUserData() async {
    emit(LoadingDataState());
    try {
      final response = await repository.downloadUserData(context: navigatorKey.currentContext!);
      if (response != null && response.data != null && response.data!['success'] == true) {
        if (response.data!['csv'] != null) {
          'response.data ${response.data}'.logD;
          final encodedStr = response.data?['csv'] as String;
          try {
            await FilesDownload.downloadAndOpenCsv(encodedStr, '${DataJsonKeys.downloadFileName.tr()}.csv');
          } catch (e) {
            'Error getting directory: ${e.toString().substring(0, 400)}'.logD;
          }
        }
        emit(InitialDataState());
        return true;
      } else {
        emit(InitialDataState());
      }
    } catch (e) {
      emit(InitialDataState());
    }
    return false;
  }

  Future<void> setDataSharing({required bool value}) async {
    emit(LoadingDataState());
    try {
      final response = await repository.dataSharing(value: value, context: navigatorKey.currentContext!);
      if (response != null && response.data?['success'] == true) {
        final data = Injector.instance<AppDB>().userModel;
        //  data?['user']['dataProcessing'] = value;
        data?.user.dataProcessing = value;
        Injector.instance<AppDB>().userModel = data;
        Injector.instance<AppDB>().userModelCopy = data;
        if (value) {
          dataSharingButtonState.value = ButtonState.yesEnabled; // Enable No, disable Yes
        } else {
          dataSharingButtonState.value = ButtonState.noEnabled; // Enable No, disable Yes
        }
      }
      emit(InitialDataState());
    } catch (e) {
      emit(InitialDataState());
    }
  }

  Future<void> setDataSharingRpi({required bool value}) async {
    emit(LoadingDataState());
    try {
      if (dataSharingButtonState.value == ButtonState.yesEnabled && !value) {
        unawaited(setDataSharing(value: false));
        emit(LoadingDataState());
      }
      final response = await repository.dataSharingRpiUnderstood(value: value, context: navigatorKey.currentContext!);
      response?.data.logD;
      if (response != null && response.data?['success'] == true) {
        final data = Injector.instance<AppDB>().userModel;
        //    data?['user']['dataProcessing'] = value;
        data?.user.dataProcessing = value;
        Injector.instance<AppDB>().userModel = data;
        Injector.instance<AppDB>().userModelCopy = data;
        if (value) {
          dataSharingRpiButtonState.value = ButtonState.yesEnabled; // Enable No, disable Yes
        } else {
          dataSharingRpiButtonState.value = ButtonState.noEnabled; // Enable No, disable Yes
        }
      }
      emit(InitialDataState());
    } catch (e) {
      emit(InitialDataState());
    }
  }

  ButtonState buttonStateFromBool({bool? value}) {
    return switch (value) {
      null => ButtonState.bothDisabled,
      true => ButtonState.yesEnabled,
      false => ButtonState.noEnabled,
    };
  }

  @override
  Future<void> close() {
    showWarningBox.dispose();
    isContinue.dispose();
    showUserUnderstoodRpiWarning.dispose();
    dataProcessingButtonState.dispose();
    dataSharingButtonState.dispose();
    dataSharingRpiButtonState.dispose();
    return super.close();
  }
}
