import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class WarningDataContainer extends StatelessWidget {
  const WarningDataContainer({
    this.titleDetailText,
    super.key,
    this.isContinue,
    this.visible = true,
    this.onCloseIconTap,
    this.onYesTap,
    this.onPurgeMyData,
    this.onGoBackTap,
  });
  final bool visible;
  final ValueNotifier<bool>? isContinue;
  final String? titleDetailText;
  final void Function()? onCloseIconTap;
  final void Function()? onYesTap;
  final void Function()? onGoBackTap;
  final void Function()? onPurgeMyData;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isContinue ?? ValueNotifier(false),
      builder: (context, value, child) {
        return Visibility(
          visible: visible,
          child: Stack(
            alignment: Alignment.topRight,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: context.themeColors.lightBrownColor,
                  borderRadius: BorderRadius.circular(AppSize.r10),
                ),
                child: Column(
                  children: [
                    SizedBox(height: AppSize.h10,),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.warning_amber,
                            color: context.themeColors.orangeColor,
                            size: AppSize.sp22,
                          ),
                          Expanded(
                            child: AppTextWidget(
                              textAlign: TextAlign.center,
                              value ? DataJsonKeys.purgeConfirmTitle.tr() : DataJsonKeys.purgePreWarningTitle.tr(),
                              style: context.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: context.themeColors.darkBrownColor,
                              ),
                            ),
                          ),

                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                      child: Column(
                        children: [
                          if (value)
                            const SizedBox()
                          else
                            AppTextWidget(
                              textAlign: TextAlign.center,
                              titleDetailText ??
                                  (DynamicAssetLoader.getNestedValue(
                                    DataJsonKeys.purgeWarningIntro,
                                    context,
                                  ) as List)
                                      .join('\n\n'),
                              style: context.textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: context.themeColors.darkBrownColor,
                              ),
                            ),
                          SpaceV(AppSize.h10),
                          AppTextWidget(
                            textAlign: TextAlign.center,
                            value
                                ? DataJsonKeys.purgeWarningText.tr()
                                : (DynamicAssetLoader.getNestedValue(
                                    DataJsonKeys.purgePreWarningText,
                                    context,
                                  ) as List)
                                    .join('\n\n'),
                            style: context.textTheme.labelSmall?.copyWith(
                              color: context.themeColors.darkBrownColor,
                            ),
                          ),
                          SpaceV(AppSize.h14),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomRoundedButton(
                                title: DataJsonKeys.buttonBack.tr(),
                                onTap: onGoBackTap,
                              ),
                              SpaceH(AppSize.w12),
                              CustomRoundedButton(
                                onTap: value ? onPurgeMyData : onYesTap,
                                title: value ? DataJsonKeys.buttonPurge.tr() : DataJsonKeys.buttonContinue.tr(),
                                fillColor: context.themeColors.redColor,
                              ),
                            ],
                          ),
                          SpaceV(AppSize.h16),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                right: 5,
                top: 5,
                child: InkWell(
                  onTap: onCloseIconTap,
                  child: Icon(
                    Icons.close,
                    color: context.themeColors.darkOrangeColor,
                    size: AppSize.sp18,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
