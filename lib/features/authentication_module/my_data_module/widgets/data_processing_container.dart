import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_launcher.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/cubit/data_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/warning_data_container.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

class DataProcessingContainer extends StatelessWidget {
  const DataProcessingContainer({
    required this.dataCubit,
    required this.onNoTap,
    required this.onYesTap, super.key,
  });
  final DataCubit dataCubit;
  final void Function() onNoTap;
  final void Function() onYesTap;

  @override
  Widget build(BuildContext context) {
    log('buttonState.value ${DataJsonKeys.processingText.tr()}');
    return Container(
      decoration: BoxDecoration(
        color: context.themeColors.whiteColor,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppSize.h10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTextWidget(
              DataJsonKeys.processingBoxTitle.tr(),
              style: context.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.lerp(FontWeight.w500, FontWeight.w600, 0.5),
                fontSize: AppSize.sp13,
              ),
            ),
            SpaceV(AppSize.h16),
            AppTextWidget(
              DataJsonKeys.processingTitle.tr(),
              textAlign: TextAlign.start,
              style: context.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              padding: EdgeInsets.zero,
              margin: EdgeInsets.zero,
              child: Html(
                data: '''
      <p style="margin:0;padding:0;text-align:start;">${DataJsonKeys.processingText.tr()}</p>
    ''',
                style: {
                  'html': Style(
                    padding: HtmlPaddings.zero,
                    margin: Margins.zero,
                  ),
                  'body': Style(
                    padding: HtmlPaddings.zero,
                    margin: Margins.zero,
                  ),
                  'p': Style(
                    padding: HtmlPaddings.zero,
                    margin: Margins.zero,
                    fontSize: FontSize(AppSize.sp12),
                    fontFamily: 'Poppins',
                    color: context.themeTextColors.text,
                    fontWeight: FontWeight.w400,
                  ),
                  'a': Style(
                    color: context.themeColors.linkTextColor,
                    textDecoration: TextDecoration.underline,
                  ),
                },
                onLinkTap: (url, attributes, element) {
                  if(context.locale == const Locale('en','GB')) {
                    CustomLauncher.launchURL('https://breakingfreeonline.com/privacy-policy/');
                  }else if(context.locale == const Locale('en','US') || context.locale == const Locale('es','US')){
                    CustomLauncher.launchURL('https://breakingfreeonline.us/privacy-policy/');
                  }else if(context.locale == const Locale('en','AU')){
                    CustomLauncher.launchURL('https://breakingfreeonline.com.au/privacy-policy/');
                  }else if(context.locale == const Locale('en','CA') || context.locale == const Locale('fr','CA')) {
                    CustomLauncher.launchURL('https://breakingfreeonline.ca/privacy-policy/');
                  }
                  // CustomLauncher.launchURL('https://breakingfreeonline.com/accounts');
                },
              ),
            ),
            SpaceV(AppSize.h6),
            ValueListenableBuilder(
              valueListenable: dataCubit.dataProcessingButtonState,
              builder: (context, value, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomRoundedButton(
                      key: const Key('data_processing_no'),
                      horizontalPadding: AppSize.w24,
                      fillColor: (dataCubit.dataProcessingButtonState.value == ButtonState.noEnabled)
                          ? context.themeColors.greenColor
                          : context.themeColors.disableButtonColor,
                      title: DataJsonKeys.buttonNo.tr(),
                      onTap: onNoTap,
                    ),
                    SpaceH(AppSize.w10),
                    CustomRoundedButton(
                      key: const Key('data_processing_yes'),
                      horizontalPadding: AppSize.w20,
                      title: DataJsonKeys.buttonYes.tr(),
                      fillColor: (dataCubit.dataProcessingButtonState.value == ButtonState.yesEnabled)
                          ? context.themeColors.greenColor
                          : context.themeColors.disableButtonColor,
                      onTap:onYesTap ?? () {
                        dataCubit.setDataProcessing(value: true);
                      },
                    ),
                  ],
                );
              },
            ),
            SpaceV(AppSize.h10),
            ValueListenableBuilder(
              valueListenable: dataCubit.showWarningBox,
              builder: (context, value, child) {
                return Column(
                  children: [
                    Visibility(
                      visible: value,
                      child: Padding(
                        padding: EdgeInsets.only(left: AppSize.w6, right: AppSize.w6, bottom: AppSize.h14),
                        child: AppTextWidget(
                          DataJsonKeys.mustConsent.tr(),
                          textAlign: TextAlign.start,
                          style: context.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: context.themeColors.errorRedColor,
                          ),
                        ),
                      ),
                    ),
                    // SpaceV(AppSize.h14),
                    WarningDataContainer(
                      isContinue: dataCubit.isContinue,
                      visible: value,
                      onCloseIconTap: () {
                        dataCubit.showWarningBox.value = false;
                        dataCubit.dataProcessingButtonState.value = ButtonState.bothDisabled;
                      },
                      onGoBackTap: () {
                        // value = false;
                        log('dataCubit.isContinue.value ${dataCubit.isContinue.value}');

                        log('dataCubit.isContinue.value ${dataCubit.isContinue.value}');
                        log('dataCubit.isContinue.value ${dataCubit.showWarningBox.value}');

                        if (dataCubit.isContinue.value == false) {
                          dataCubit.showWarningBox.value = false;
                          dataCubit.dataProcessingButtonState.value = ButtonState.bothDisabled;
                        } else {
                          dataCubit.isContinue.value = false;
                        }
                      },
                      onYesTap: () {
                        dataCubit.isContinue.value = true;
                      },
                      onPurgeMyData: dataCubit.purgeUser,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
