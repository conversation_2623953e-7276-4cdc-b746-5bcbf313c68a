import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/cubit/data_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/widgets/warning_data_container.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class PurgeDataContainer extends StatelessWidget {
  const PurgeDataContainer({
    required this.dataCubit,
    super.key,
  });
  final DataCubit dataCubit;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromRGBO(255, 255, 255, 1),
        borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric( vertical: AppSize.h10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTextWidget(
              DataJsonKeys.purgeTitle.tr(),
              style: context.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.lerp(FontWeight.w500, FontWeight.w600, 0.5),

              ),
            ),
            ValueListenableBuilder(
              valueListenable: dataCubit.isContinue,
              builder: (context, value, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Html(
                      data: '<p>${(DynamicAssetLoader.getNestedValue(
                        DataJsonKeys.purgeTextUser,
                        context,
                      ) as List).join(' <br> <br> ')}</p>',
                      onLinkTap: (url, attributes, element) {},
                      style: { 'html': Style(
                  padding: HtmlPaddings.zero,
                  margin: Margins.zero,
                ),
                'body': Style(
                  padding: HtmlPaddings.zero,
                  margin: Margins.zero,
                ),
                        'p': Style(
                          fontFamily: 'Poppins',
                          fontSize: FontSize(AppSize.sp12),
                          fontWeight: FontWeight.w400,
                          color: context.themeTextColors.text,
                        ),
                        'a': Style(
                          color: context.themeColors.linkTextColor,
                          textDecoration: TextDecoration.underline,
                        ),
                      },
                    ),
                    SpaceV(AppSize.h10),
                  ],
                );
              },
            ),
            Center(
              child: CustomRoundedButton(
                horizontalPadding: AppSize.w24,
                fillColor: context.themeColors.redColor,
                title: DataJsonKeys.buttonContinue.tr(),
                onTap: () {
                  dataCubit.isdataContinue.value = true;
                },
              ),
            ),
            ValueListenableBuilder(
              valueListenable: dataCubit.isdataContinue,
              builder: (context, value, child) {
                return Column(
                  children: [
                    if (value) SpaceV(AppSize.h12) else const SizedBox(),
                    WarningDataContainer(
                      isContinue: dataCubit.isdataContinue,
                      visible: value,
                      onCloseIconTap: () {
                        dataCubit.isdataContinue.value = false;
                      },
                      onGoBackTap: () {
                        // value = false;
                        log('dataCubit.isContinue.value ${dataCubit.isContinue.value}');

                        log('dataCubit.isContinue.value ${dataCubit.isContinue.value}');
                        log('dataCubit.isContinue.value ${dataCubit.showWarningBox.value}');

                        dataCubit.isdataContinue.value = false;
                      },
                      onYesTap: () {
                        dataCubit.isContinue.value = true;
                      },
                      onPurgeMyData: dataCubit.purgeUser,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
