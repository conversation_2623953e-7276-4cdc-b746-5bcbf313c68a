import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/cubit/data_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class DownloadDataContainer extends StatelessWidget {
  const DownloadDataContainer({
    required this.dataCubit,
    super.key,
  });
  final DataCubit dataCubit;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Color.fromRGBO(255, 255, 255, 1),
        // borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric( vertical: AppSize.h10),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTextWidget(
                  DataJsonKeys.downloadTitle.tr(),
                  style: context.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.lerp(FontWeight.w500, FontWeight.w600, 0.5),

                  ),
                ),
                Html(
                  data: '<p>${DataJsonKeys.downloadText.tr()}</p>',
                  onLinkTap: (url, attributes, element) {},
                  style: {
                    'p': Style(
                      fontFamily: 'Poppins',
                      fontSize: FontSize(AppSize.sp12),
                      fontWeight: FontWeight.w400,
                      color: context.themeTextColors.text,
                    ),
                    'a': Style(
                      color: context.themeColors.linkTextColor,
                      textDecoration: TextDecoration.underline,
                    ),
                  },
                ),
                SpaceV(AppSize.h10),
              ],
            ),
            CustomRoundedButton(
              horizontalPadding: AppSize.w24,
              fillColor: context.themeColors.greenBtnColor,
              title: DataJsonKeys.buttonDownload.tr(),
              onTap: dataCubit.downloadUserData,
            ),
          ],
        ),
      ),
    );
  }
}
