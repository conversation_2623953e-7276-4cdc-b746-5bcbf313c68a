import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_launcher.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/cubit/data_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';

class DataSharingContainer extends StatelessWidget {
  const DataSharingContainer({
    required this.dataCubit,
    super.key,
  });
  final DataCubit dataCubit;

  @override
  Widget build(BuildContext context) {
    log('buttonState.value ${DataJsonKeys.processingText.tr()}');
    return Container(
      decoration: BoxDecoration(
        color: context.themeColors.whiteColor,

        // borderRadius: BorderRadius.circular(AppSize.r10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppTextWidget(
            DataJsonKeys.sharingBoxTitle.tr(),
            style: context.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.lerp(FontWeight.w500, FontWeight.w600, 0.5),
            ),
          ),
          SpaceV(AppSize.h16),
          AppTextWidget(
            DataJsonKeys.sharingTitle.tr(),
            textAlign: TextAlign.start,
            style: context.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          //SpaceV(AppSize.h10),
          Container(
            padding: EdgeInsets.zero,
            margin: EdgeInsets.zero,
            child: Html(
              data: '<p>${DataJsonKeys.sharingPleaseRead.tr()}</p>',
              onLinkTap: (url, attributes, element) {
                if(context.locale == const Locale('en','GB')) {
                  CustomLauncher.launchURL('https://breakingfreeonline.com/research-participant-information/');
                }else if(context.locale == const Locale('en','US') || context.locale == const Locale('es','US')){
                  CustomLauncher.launchURL('https://breakingfreeonline.us/research-participant-information/');
                }else if(context.locale == const Locale('en','AU')){
                  CustomLauncher.launchURL('https://breakingfreeonline.com.au/research-participant-information/');
                }else if(context.locale == const Locale('en','CA') || context.locale == const Locale('fr','CA')) {
                  CustomLauncher.launchURL('https://breakingfreeonline.ca/research-participant-information/');
                }

                // CustomLauncher.launchURL('https://breakingfreeonline.com/accounts');
              },
              style: {
                'html': Style(
                  padding: HtmlPaddings.zero,
                  margin: Margins.zero,
                ),
                'body': Style(
                  padding: HtmlPaddings.zero,
                  margin: Margins.zero,
                ),
                'p': Style(
                  fontFamily: 'Poppins',
                  fontSize: FontSize(AppSize.sp12),
                  fontWeight: FontWeight.w400,
                  color: context.themeTextColors.text,
                ),
                'a': Style(
                  color: context.themeColors.linkTextColor,
                  textDecoration: TextDecoration.underline,
                ),
              },
            ),
          ),
          AppTextWidget(
            DataJsonKeys.sharingText1.tr(),
            textAlign: TextAlign.start,
            style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
          ),

          SpaceV(AppSize.h16),
          ValueListenableBuilder(
            valueListenable: dataCubit.dataSharingRpiButtonState,
            builder: (context, value, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomRoundedButton(
                    key: const Key('data_sharing_rpi_no'),
                    horizontalPadding: AppSize.w24,
                    fillColor: (value == ButtonState.noEnabled)
                        ? context.themeColors.greenColor
                        : context.themeColors.disableButtonColor,
                    title: DataJsonKeys.buttonNo.tr(),
                    onTap: () {
                      dataCubit.setDataSharingRpi(value: false); // Enable No, disable Yes
                    },
                  ),
                  SpaceH(AppSize.w10),
                  CustomRoundedButton(
                    key: const Key('data_sharing_rpi_yes'),
                    horizontalPadding: AppSize.w20,
                    title: DataJsonKeys.buttonYes.tr(),
                    fillColor: (value == ButtonState.yesEnabled)
                        ? context.themeColors.greenColor
                        : context.themeColors.disableButtonColor,
                    onTap: () {
                      dataCubit.showUserUnderstoodRpiWarning.value = false;
                      dataCubit.setDataSharingRpi(value: true);
                    },
                  ),
                ],
              );
            },
          ),

          SpaceV(AppSize.h16),
          AppTextWidget(
            DataJsonKeys.sharingText2.tr(),
            textAlign: TextAlign.start,
            style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
          ),
          SpaceV(AppSize.h16),
          ValueListenableBuilder(
            valueListenable: dataCubit.dataSharingButtonState,
            builder: (context, value, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomRoundedButton(
                    key: const Key('data_sharing_no'),
                    horizontalPadding: AppSize.w24,
                    fillColor: (value == ButtonState.noEnabled)
                        ? context.themeColors.greenColor
                        : context.themeColors.disableButtonColor,
                    title: DataJsonKeys.buttonNo.tr(),
                    onTap: () {
                      // if (dataCubit.dataSharingRpiButtonState.value == ButtonState.noEnabled) {
                      dataCubit.showUserUnderstoodRpiWarning.value = false;
                      // }
                      dataCubit.setDataSharing(value: false); // Enable No, disable Yes
                    },
                  ),
                  SpaceH(AppSize.w10),
                  CustomRoundedButton(
                    key: const Key('data_sharing_yes'),
                    horizontalPadding: AppSize.w20,
                    title: DataJsonKeys.buttonYes.tr(),
                    fillColor: (value == ButtonState.yesEnabled)
                        ? context.themeColors.greenColor
                        : context.themeColors.disableButtonColor,
                    onTap: () {
                      if (dataCubit.dataSharingRpiButtonState.value == ButtonState.noEnabled ||
                          dataCubit.dataSharingRpiButtonState.value == ButtonState.bothDisabled) {
                        dataCubit.showUserUnderstoodRpiWarning.value = true;
                        return;
                      }
                      dataCubit.setDataSharing(value: true); // Enable No, disable Yes
                    },
                  ),
                ],
              );
            },
          ),
          ValueListenableBuilder(
            valueListenable: dataCubit.showUserUnderstoodRpiWarning,
            builder: (context, isUserUnderstoodRpi, child) {
              return isUserUnderstoodRpi
                  ? CustomErrorWidget(
                  errorMessgaeText: DataJsonKeys.mustUnderstandRPI.tr(),
              ) : const SizedBox();
            },
          ),

        ],
      ),
    );
  }
}
