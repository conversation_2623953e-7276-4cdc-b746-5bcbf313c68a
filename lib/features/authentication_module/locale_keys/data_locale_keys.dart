class DataJsonKeys {
  // General keys
  static const String title = 'data.title';

  // Consent keys
  static const String consent = 'data.consent';
  static const String processing = 'data.consent.processing';
  static const String sharing = 'data.consent.sharing';
  static const String mustConsent = 'data.consent.mustConsent';
  static const String mustUnderstandRPI = 'data.consent.mustUnderstandRPI';
  static const String purgeWarningIntro = 'data.consent.purgeWarningIntro';

  // Nested keys for 'processing'
  static const String processingBoxTitle = 'data.consent.processing.boxTitle';
  static const String processingTitle = 'data.consent.processing.title';
  static const String processingText = 'data.consent.processing.text';

  // Nested keys for 'sharing'
  static const String sharingBoxTitle = 'data.consent.sharing.boxTitle';
  static const String sharingTitle = 'data.consent.sharing.title';
  static const String sharingPleaseRead = 'data.consent.sharing.pleaseRead';
  static const String sharingText1 = 'data.consent.sharing.text1';
  static const String sharingText2 = 'data.consent.sharing.text2';

  // Download keys
  static const String download = 'data.download';
  static const String downloadTitle = 'data.download.title';
  static const String downloadText = 'data.download.text';
  static const String downloadFileName = 'data.download.fileName';

  // Purge keys
  static const String purge = 'data.purge';
  static const String purgeTitle = 'data.purge.title';

  // Purge text variants
  static const String purgeTextUser = 'data.purge.text.user';
  static const String purgeTextDashboard = 'data.purge.text.dashboard';
  static const String purgeTextEap = 'data.purge.text.eap';

  // Purge warnings
  static const String purgePreWarningTitle = 'data.purge.preWarning.title';
  static const String purgeConfirmTitle = 'data.purge.preWarning.confirmTitle';
  static const String purgePreWarningText = 'data.purge.preWarning.text';
  static const String purgeConfirmText = 'data.purge.preWarning.confirmText';
  static const String purgeWarningTitle = 'data.purge.warning.title';
  static const String purgeWarningText = 'data.purge.warning.text';

  // Purge disclaimer
  static const String purgeDisclaimerTitle = 'data.purge.warning.disclaimer.title';
  static const String purgeDisclaimerText = 'data.purge.warning.disclaimer.text';
  static const String purgeDisclaimerEmail = 'data.purge.warning.disclaimer.email';

  // RPI (Research Participant Information) keys
  static const String rpi = 'data.rpi';
  static const String rpiTitle = 'data.rpi.title';
  static const String rpiContent = 'data.rpi.content';

  // Buttons
  static const String buttons = 'data.buttons';
  static const String buttonYes = 'data.buttons.yes';
  static const String buttonNo = 'data.buttons.no';
  static const String buttonBack = 'data.buttons.back';
  static const String buttonContinue = 'data.buttons.continue';
  static const String buttonPurge = 'data.buttons.purge';
  static const String buttonDownload = 'data.buttons.download';

  // Errors
  static const String errors = 'data.errors';
  static const String errorPurge = 'data.errors.purge';
  static const String errorDownload = 'data.errors.download';
}
