class AuthLocaleKeys {
  // General texts
  static const heading = 'entry.heading';
  static const organisations = 'entry.organisations';
  static const practitioners = 'entry.practitioners';
  static const loginToAccount = 'entry.loginToAccount';
  static const contactEmail = 'entry.contactEmail';

  // Dashboard headers
  static const dashboardHeader = 'entry.headers.dashboard';
  static const eapHeader = 'entry.headers.eap';

  static const selectLanguage = 'entry.selectLanguage';

  // Initial screen texts
  static const signUpText = 'entry.initial.signUp';
  static const logInText = 'entry.initial.logIn';
  static const ontarioNotice = 'entry.initial.ontario';

  // Log In screen texts
  static const logInEmailLabel = 'entry.logIn.emailLabel';
  static const logInPasswordLabel = 'entry.logIn.passwordLabel';
  static const logInForgetPasswordLabel = 'entry.logIn.forgotPassword';
  static const logInForgotPasswordLabel = 'entry.logIn.forgotPasswordApp';

  // Log In errors
  static const logInEmailRequired = 'entry.logIn.errors.emailRequired';
  static const logInEmailInvalid = 'entry.logIn.errors.emailInvalid';
  static const logInPasswordRequired = 'entry.logIn.errors.passwordRequired';
  static const logInInvalid = 'entry.logIn.errors.invalid';

  // Log In buttons
  static const logInBackText = 'entry.logIn.buttons.back';
  static const logInSubmitText = 'entry.logIn.buttons.submit';
  static const noAccountText = 'entry.noAccount';

  // Forget password screen texts
  static const forgetPasswordText = 'entry.forgotPassword.text';
  static const forgetPasswordEmailLabel = 'entry.forgotPassword.emailLabel';
  static const forgetPasswordLabel = 'entry.logIn.forgotPasswordApp';

  // Forget password buttons
  static const forgetPasswordBackText = 'entry.forgotPassword.buttons.back';
  static const resetText = 'entry.forgotPassword.buttons.submit';

  // Forget password errors
  static const forgetPasswordSuccess = 'entry.forgotPassword.success';
  static const forgetPasswordEmailRequired = 'entry.forgotPassword.errors.emailRequired';
  static const forgetPasswordEmailInvalid = 'entry.forgotPassword.errors.emailInvalid';
  static const forgetPasswordEmailNotFound = 'entry.forgotPassword.errors.emailNotFound';
  static const forgetPasswordInternalError = 'entry.forgotPassword.errors.internal';

  // Sign Up screen texts
  static const signUpTitleText = 'entry.signUp.title';
  static const signUpEmailLabel = 'entry.signUp.emailLabel';
  static const signUpConfirmEmailLabel = 'entry.signUp.confirmEmailLabel';
  static const signUpPasswordLabel = 'entry.signUp.passwordLabel';
  static const signUpConfirmPasswordLabel = 'entry.signUp.confirmPasswordLabel';
  static const signUpAccessCode = 'entry.signUp.serviceCodeLabel';

  // Sign Up note
  static const signUpNote1 = 'entry.signUp.note.0';
  static const signUpNote2 = 'entry.signUp.note.1';
  static const signUpNote = 'entry.signUp.note';

  // Sign Up password suggester texts
  static const signUpPasswordSuggesterButtonText = 'entry.signUp.passwordSuggester.button';
  static const signUpPasswordSuggesterMainText = 'entry.signUp.passwordSuggester.text';
  static const signUpPasswordSuggesterList = 'entry.signUp.passwordSuggester.list';

  // Sign Up error texts
  static const signUpEmailRequired = 'entry.signUp.errors.emailRequired';
  static const signUpEmailNotMatch = 'entry.signUp.errors.emailMatch';
  static const signUpEmailInvalid = 'entry.signUp.errors.emailInvalid';
  static const signUpEmailExist = 'entry.signUp.errors.emailExists';
  static const signUpPasswordRequired = 'entry.signUp.errors.passwordRequired';
  static const signUpPasswordNotMatch = 'entry.signUp.errors.passwordMatch';
  static const signUpPasswordMinLength = 'entry.signUp.errors.passwordMinLength';
  static const signUpPasswordMaxLength = 'entry.signUp.errors.passwordMaxLength';
  static const signUpPasswordInSecure = 'entry.signUp.errors.passwordInsecure';
  static const signUpCodeRequired = 'entry.signUp.errors.serviceCodeRequired';
  static const signUpCodeValid = 'entry.signUp.errors.codeInvalid';
  static const signUpCodeInActive = 'entry.signUp.errors.codeInactive';
  static const signUpCodeExpired = 'entry.signUp.errors.codeExpired';
  static const signUpOntarioNoticePostApr = 'entry.signUp.errors.ontarioNoticePostApr';
  static const signUpInternalError = 'entry.signUp.errors.internal';

  // Sign Up buttons
  static const signUpCreateAccountText = 'entry.signUp.buttons.next';
  static const signUpCreateAccountTextMobile = 'entry.signUp.buttons.nextMobile';

  static const String signUpSuccessTitle = 'entry.signUpSuccess.title';
  static const String signUpSuccessText1 = 'entry.signUpSuccess.text';
  static const String signUpSuccessButton = 'entry.signUpSuccess.button';

  static const String linkDanger = 'entry.links.danger';
  static const String linkFaqContact = 'entry.links.faqContact';
  static const String linkApp = 'entry.links.app';
  static const String linkFaq = 'entry.links.faq';

  static const String footerCopyright = 'entry.footer.copyright';
  static const String footerToolkit = 'entry.footer.toolkit';
  static const String footerOrganisations = 'entry.footer.organisations';
  static const String footerAbout = 'entry.footer.about';
  static const String footerUrl = 'entry.footer.url';
  static const String footerAccessibility = 'entry.footer.accessibility';

  static const String regionDialogGBText1 = 'entry.regionDialog.GB.text.0';
  static const String regionDialogGBText2 = 'entry.regionDialog.GB.text.1';
  static const String regionDialogGBText3 = 'entry.regionDialog.GB.text.2';
  static const String regionDialogGBText4 = 'entry.regionDialog.GB.text.3';
  static const String regionDialogGBButton = 'entry.regionDialog.GB.button.text';
  static const String regionDialogGBButtonMobile = 'entry.regionDialog.GB.button.textMobile';
  static const String regionDialogGBButtonUrl = 'entry.regionDialog.GB.button.url';

  static const String regionDialogUSText1 = 'entry.regionDialog.US.text.0';
  static const String regionDialogUSText2 = 'entry.regionDialog.US.text.1';
  static const String regionDialogUSText3 = 'entry.regionDialog.US.text.2';
  static const String regionDialogUSText4 = 'entry.regionDialog.US.text.3';
  static const String regionDialogUSButton = 'entry.regionDialog.US.button.text';
  static const String regionDialogUSButtonMobile = 'entry.regionDialog.US.button.textMobile';
  static const String regionDialogUSButtonUrl = 'entry.regionDialog.US.button.url';

  static const String faqHeading = 'entry.faq.heading';

  static const String faqSetupTitle = 'entry.faq.content.0.title';
  static const String faqSetupQuestion1 = 'entry.faq.content.0.questions.0.q';
  static const String faqSetupAnswer1 = 'entry.faq.content.0.questions.0.a.0';
  static const String faqSetupAnswer2 = 'entry.faq.content.0.questions.0.a.1';
  static const String faqSetupAnswer3 = 'entry.faq.content.0.questions.0.a.2';
  static const String faqSetupQuestion2 = 'entry.faq.content.0.questions.1.q';
  static const String faqSetupAnswer4 = 'entry.faq.content.0.questions.1.a.0';
  static const String faqSetupQuestion3 = 'entry.faq.content.0.questions.2.q';
  static const String faqSetupAnswer5 = 'entry.faq.content.0.questions.2.a.0';
  static const String faqSetupQuestion4 = 'entry.faq.content.0.questions.3.q';
  static const String faqSetupAnswer6 = 'entry.faq.content.0.questions.3.a.0';
  static const String faqSetupQuestion5 = 'entry.faq.content.0.questions.4.q';
  static const String faqSetupAnswer7 = 'entry.faq.content.0.questions.4.a.0';

  static const String faqAboutTitle = 'entry.faq.content.1.title';
  static const String faqAboutQuestion1 = 'entry.faq.content.1.questions.0.q';
  static const String faqAboutAnswer1 = 'entry.faq.content.1.questions.0.a.0';
  static const String faqAboutQuestion2 = 'entry.faq.content.1.questions.1.q';
  static const String faqAboutAnswer2 = 'entry.faq.content.1.questions.1.a.0';
  static const String faqAboutQuestion3 = 'entry.faq.content.1.questions.2.q';
  static const String faqAboutAnswer3 = 'entry.faq.content.1.questions.2.a.0';
  static const String faqAboutQuestion4 = 'entry.faq.content.1.questions.3.q';
  static const String faqAboutAnswer4 = 'entry.faq.content.1.questions.3.a.0';
  static const String faqAboutQuestion5 = 'entry.faq.content.1.questions.4.q';
  static const String faqAboutAnswer5 = 'entry.faq.content.1.questions.4.a.0';
  static const String faqAboutQuestion6 = 'entry.faq.content.1.questions.5.q';
  static const String faqAboutAnswer6 = 'entry.faq.content.1.questions.5.a.0';
  static const String faqAboutQuestion7 = 'entry.faq.content.1.questions.6.q';
  static const String faqAboutAnswer7 = 'entry.faq.content.1.questions.6.a.0';

  static const String faqUseTitle = 'entry.faq.content.2.title';
  static const String faqUseQuestion1 = 'entry.faq.content.2.questions.0.q';
  static const String faqUseAnswer1 = 'entry.faq.content.2.questions.0.a.0';
  static const String faqUseQuestion2 = 'entry.faq.content.2.questions.1.q';
  static const String faqUseAnswer2 = 'entry.faq.content.2.questions.1.a.0';
  static const String faqUseQuestion3 = 'entry.faq.content.2.questions.2.q';
  static const String faqUseAnswer3 = 'entry.faq.content.2.questions.2.a.0';
  static const String faqUseQuestion4 = 'entry.faq.content.2.questions.3.q';
  static const String faqUseAnswer4 = 'entry.faq.content.2.questions.3.a.0';
  static const String faqUseQuestion5 = 'entry.faq.content.2.questions.4.q';
  static const String faqUseAnswer5 = 'entry.faq.content.2.questions.4.a.0';

  //for verification screen texts
  static const String verifiedTitle = 'verification.verified.title';
  static const String verifiedtext = 'verification.verified.text';
  static const String verifiedGetstaredButton = 'verification.buttons.getStarted';
  static const String verifiedLogOutButton = 'verification.buttons.logOut';
  static const String verifiedResendButton = 'verification.buttons.resend';

  // for not verified screen texts
  static const String notVerifiedTitle = 'verification.notVerified.title';
  static const String notVerifiedText = 'verification.notVerified.text';
  static const String notVerifiedTextUser = 'verification.notVerified.text.user';
  // static const String notVerifiedTextFirst = 'verification.notVerified.text.0';
  // static const String notVerifiedTextSecond = 'verification.notVerified.text.1';
  static const String notVerifiedTextBottomUser = 'verification.notVerified.textBottom.user';
  static const String notVerifiedTextBottom = 'verification.notVerified.textBottom';
  // static const String notVerifiedTextBottomSecond = 'verification.notVerified.textBottom.user.1';

  static const String imageUSFlag = 'entry.images.flagUS.src';

  static const String logo = 'entry.images.logo.src';
  static const String hcpc = 'entry.images.hcpc.src';
  static const String chartered = 'entry.images.chartered.src';
  static const String apple = 'entry.images.apple.src';
  static const String googlePlay = 'entry.images.googleplay.src';
  static const String flagAusSmall = 'entry.images.flagAusSmall.src';
  static const String flagGB = 'entry.images.flagGB.src';
  static const String flagCA = 'entry.images.flagCA.src';
  static const String flagUS = 'entry.images.flagUS.src';
  static const String fieldRequiredError = 'entry.dialogs.resetPassword.errors.required';
  static const String headerFlag = 'entry.images.headerFlag.src';

  static const String resetPassword = 'entry.dialogs.resetPassword.title';
  static const String resetPasswordButton = 'entry.dialogs.resetPassword.buttons.reset';

  static const String errTokenInvalid = 'entry.dialogs.resetPassword.errors.tokenInvalid';
  static const String errTokenExpired = 'entry.dialogs.resetPassword.errors.tokenExpired';

  // for verify email texts
  static const String verifyEmailSuccess = 'entry.notifications.verify';
  static const String reVerifySuccess = 'entry.notifications.reverify';
  static const String emailTokenInvalid = 'entry.notifications.emailTokenInvalid';
  static const String emailTokenUsed = 'entry.notifications.emailTokenUsed';

  static String verifyEmailError(String errorCode) => 'entry.notifications.$errorCode';
}
