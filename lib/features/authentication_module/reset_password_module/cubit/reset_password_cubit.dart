import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/cubit/language_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/cubit/reset_password_state.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/pages/reset_password_page.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/loading_overlay/loading_overlay.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  ResetPasswordCubit({required this.context, required this.resetToken, required this.domain}) : super(InitialResetPasswordState()) {
   WidgetsBinding.instance.addPostFrameCallback((timeStamp) async{ 
     await validateResetPasswordToken(context: context);
     await changeRegionAndLanguage(domain);
   },);
  }

  final AuthRepository authRepository = AuthRepository();

  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  final ValueNotifier<bool> isResetButtonClick = ValueNotifier(false);
  final ValueNotifier<String> passwordError = ValueNotifier('');
  final ValueNotifier<String> confirmPasswordError = ValueNotifier('');
  final ValueNotifier<bool> isRequestLoading = ValueNotifier(false);
  final ValueNotifier<bool> isPasswordVisible = ValueNotifier(false);
  final ValueNotifier<bool> isConfirmPasswordVisible = ValueNotifier(false);

  final formKey = GlobalKey<FormState>();
  final String resetToken;
  final String domain;
  final BuildContext context;

  Future<void> changeRegionAndLanguage(String url) async {
    try {
      LoadingOverlay.instance().show(context: navigatorKey.currentContext!);
      // await Injector.instance<AppDB>().clearData();
      final langCubit = LanguageCubit();
      await langCubit.fetchCountryApi(autoSelect: true, url: url);
      emit(InitialResetPasswordState());
      LoadingOverlay.instance().hide();
    } catch (e) {
      LoadingOverlay.instance().hide();
      'Error verifying email: $e'.logE;
    }
  }

  bool passwordValid({bool buttonClick = false}) {
    if (passwordController.text.trim().isEmpty) {
      passwordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      return false;
    } else if (passwordController.text.trim().length < 8) {
      passwordError.value = AuthLocaleKeys.signUpPasswordMinLength.tr();
      return false;
    } else {
      passwordError.value = '';
      return true;
    }
  }

  bool confirmPasswordValid({bool buttonClick = false}) {
    final confirmPassword = confirmPasswordController.text;
    final password = passwordController.text;

    if (confirmPassword.isEmpty) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordRequired.tr();
      return false;
    }

    if (confirmPassword != password) {
      confirmPasswordError.value = AuthLocaleKeys.signUpPasswordNotMatch.tr();
      return false;
    }

    confirmPasswordError.value = '';
    return true;
  }

  bool validateForm() {
    final isPasswordValid = passwordValid(buttonClick: true);
    final isConfirmPasswordValid = confirmPasswordValid(buttonClick: true);

    return isPasswordValid && isConfirmPasswordValid;
  }

  Future<bool> resetPassword({
    required String password,
    required String confirmPassword,
    required BuildContext context,
  }) async {
    try {
      if (resetToken.isEmpty) {
        emit(InvalidTokenState());
        return false;
      }

      isRequestLoading.value = true;
      emit(LoadingResetPasswordState());

      final result = await authRepository.resetPassword(
        token: resetToken,
        password: password,
        confirmPassword: confirmPassword,
        context: context,
      );
      '>?>?>?>? result = ${result?.data.toString()}'.logV;
      if (result != null) {
        emit(SuccessResetPasswordState());
        return true;
      } else {
        emit(
          ErrorResetPasswordState(
            'Failed to reset password. Please try again.',
          ),
        );
        return false;
      }
    } catch (e) {
      emit(ErrorResetPasswordState('An error occurred. Please try again.'));
      return false;
    } finally {
      isRequestLoading.value = false;
    }
  }

  Future<bool> validateResetPasswordToken({
    required BuildContext context,
  }) async {
    try {
      if (resetToken.isEmpty) {
        emit(InvalidTokenState());
        return false;
      }

      isRequestLoading.value = true;
      emit(LoadingResetPasswordState());

      final result = await authRepository.validateResetPasswordToken(
        token: resetToken,
        context: context,
      );

      if (result != null) {
        isRequestLoading.value = false;
        '>?>?>? navigate to reset'.logV;
        // await AppNavigation.nextScreen(context, ResetPasswordPage(resetToken: resetToken, domain: domain));
        //emit(SuccessResetPasswordState());
        return true;
      } else {
        isRequestLoading.value = false;
        '>?>?>? navigate to login'.logV;
        await AppNavigation.replaceScreen(context, const LoginPage(isResetInvalidTokenError: true,));
         
        // emit(
        //   ErrorResetPasswordState(
        //     'Failed to reset password. Please try again.',
        //   ),
        // );
        return false;
      }
    } catch (e) {
      emit(ErrorResetPasswordState('An error occurred. Please try again.'));
      return false;
    } finally {
      isRequestLoading.value = false;
    }
  }

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  @override
  Future<void> close() {
    passwordController.dispose();
    confirmPasswordController.dispose();
    return super.close();
  }
}
