sealed class ResetPasswordState {}

class InitialResetPasswordState extends ResetPasswordState {}

class LoadingResetPasswordState extends ResetPasswordState {}

class SuccessResetPasswordState extends ResetPasswordState {}

class ErrorResetPasswordState extends ResetPasswordState {
  final String message;
  ErrorResetPasswordState(this.message);
}

class InvalidTokenState extends ResetPasswordState {}
