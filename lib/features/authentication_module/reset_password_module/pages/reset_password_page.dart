import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/cubit/reset_password_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/reset_password_module/cubit/reset_password_state.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResetPasswordPage extends StatelessWidget {
  const ResetPasswordPage({
    required this.resetToken,
    required this.domain,
    super.key,
  });

  final String resetToken;
  final String domain;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          ResetPasswordCubit(resetToken: resetToken, domain: domain,context: context),
      child: BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
        builder: (context, state) {
          final cubit = context.read<ResetPasswordCubit>();
          return BlocListener<ResetPasswordCubit, ResetPasswordState>(
            listener: (context, state) {
              if (state is SuccessResetPasswordState) {
                // Show success message and navigate to login
                // ScaffoldMessenger.of(context).showSnackBar(
                //   const SnackBar(
                //     content: AppTextWidget(
                //       'Password reset successfully! Please login with your new password.',
                //     ),
                //     backgroundColor: Colors.green,
                //   ),
                // );
                '>?>?>? navigate from here 1 '.logV;
                AppNavigation.pushAndRemoveAllScreen(
                  context,
                  const LoginPage(),
                );
              } else if (state is ErrorResetPasswordState) {
                // ScaffoldMessenger.of(context).showSnackBar(
                //   SnackBar(
                //     content: Text(state.message),
                //     backgroundColor: Colors.red,
                //   ),
                // );
              } else if (state is InvalidTokenState) {
                // ScaffoldMessenger.of(context).showSnackBar(
                //   const SnackBar(
                //     content: AppTextWidget(
                //       'Invalid or expired reset token. Please request a new password reset.',
                //     ),
                //     backgroundColor: Colors.red,
                //   ),
                // );
                '>?>?>? navigate from here'.logV;
                AppNavigation.pushAndRemoveAllScreen(
                  context,
                  const LoginPage(),
                );
              }
            },
            child: Scaffold(
              body: SafeArea(
                child: IgnorePointer(
                  ignoring: state is LoadingResetPasswordState,
                  child: ValueListenableBuilder(
                    valueListenable: cubit.isRequestLoading,
                    builder: (context, isRequestLoading, _) {
                      return AppLoader(
                        isShowLoader: cubit.isRequestLoading.value,
                        child: LayoutBuilder(
                          builder: (context, constrains) {
                            return SingleChildScrollView(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  minHeight: constrains.maxHeight,
                                ),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: AppSize.w20,
                                        vertical: AppSize.h20,
                                      ),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              CustomBackArrowButton(
                                                onTap: () {
                                                  AppNavigation.nextScreen(context,const LoginPage());
                                                },
                                                padding: 0,
                                              ),
                                              Expanded(
                                                child: AppTextWidget(
                                                  textAlign: TextAlign.center,
                                                  AuthLocaleKeys.resetPassword.tr(),
                                                  style: context
                                                      .textTheme.headlineMedium
                                                      ?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SpaceV(AppSize.h30),

                                          // Password Field
                                          ValueListenableBuilder(
                                            valueListenable:
                                                cubit.isResetButtonClick,
                                            builder: (context, value, child) {
                                              return ValueListenableBuilder(
                                                valueListenable:
                                                    cubit.passwordError,
                                                builder: (context,
                                                    passwordError, child) {
                                                  return ValueListenableBuilder(
                                                    valueListenable:
                                                        cubit.isPasswordVisible,
                                                    builder: (
                                                      context,
                                                      isPasswordVisible,
                                                      child,
                                                    ) {
                                                      return Column(
                                                        children: [
                                                          CustomOutlinedTextfield(
                                                            controller: cubit
                                                                .passwordController,
                                                            textAction:
                                                                TextInputAction
                                                                    .next,
                                                            labelText:
                                                                AuthLocaleKeys
                                                                    .signUpPasswordLabel
                                                                    .tr(),
                                                            isError: cubit
                                                                    .isResetButtonClick
                                                                    .value &&
                                                                passwordError !=
                                                                    '',
                                                            obscureText:
                                                                !isPasswordVisible,
                                                            suffixIcon:
                                                                GestureDetector(
                                                                  onTap: cubit
                                                                    .togglePasswordVisibility,
                                                                  child: Icon(
                                                                  isPasswordVisible
                                                                      ? Icons
                                                                          .visibility
                                                                      : Icons
                                                                          .visibility_off,
                                                                                                                              ),
                                                                ),
                                                            onChanged: (_) {
                                                              cubit
                                                                  .passwordValid();
                                                            },
                                                          ),
                                                          if (cubit
                                                                  .isResetButtonClick
                                                                  .value &&
                                                              passwordError !=
                                                                  '')
                                                            CustomErrorWidget(
                                                              errorMessgaeText:
                                                                  passwordError,
                                                            ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            },
                                          ),

                                          SpaceV(AppSize.h20),

                                          // Confirm Password Field
                                          ValueListenableBuilder(
                                            valueListenable:
                                                cubit.isResetButtonClick,
                                            builder: (context, value, child) {
                                              return ValueListenableBuilder(
                                                valueListenable:
                                                    cubit.confirmPasswordError,
                                                builder: (
                                                  context,
                                                  confirmPasswordError,
                                                  child,
                                                ) {
                                                  return ValueListenableBuilder(
                                                    valueListenable: cubit
                                                        .isConfirmPasswordVisible,
                                                    builder: (
                                                      context,
                                                      isConfirmPasswordVisible,
                                                      child,
                                                    ) {
                                                      return Column(
                                                        children: [
                                                          CustomOutlinedTextfield(
                                                            controller: cubit
                                                                .confirmPasswordController,
                                                            textAction:
                                                                TextInputAction
                                                                    .done,
                                                            labelText:
                                                                AuthLocaleKeys
                                                                    .signUpConfirmPasswordLabel
                                                                    .tr(),
                                                            isError: cubit
                                                                    .isResetButtonClick
                                                                    .value &&
                                                                confirmPasswordError !=
                                                                    '',
                                                            obscureText:
                                                                !isConfirmPasswordVisible,
                                                            suffixIcon:
                                                                GestureDetector(
                                                                  onTap: cubit
                                                                    .toggleConfirmPasswordVisibility,
                                                                  child: Icon(
                                                                  isConfirmPasswordVisible
                                                                      ? Icons
                                                                          .visibility
                                                                      : Icons
                                                                          .visibility_off,
                                                                                                                                ),
                                                                                                                              ),
                                                            onChanged: (_) {
                                                              cubit
                                                                  .confirmPasswordValid();
                                                            },
                                                          ),
                                                          if (cubit
                                                                  .isResetButtonClick
                                                                  .value &&
                                                              confirmPasswordError !=
                                                                  '')
                                                            CustomErrorWidget(
                                                              errorMessgaeText:
                                                                  confirmPasswordError,
                                                            ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    ValueListenableBuilder(
                                      valueListenable: cubit.isRequestLoading,
                                      builder: (context, value, child) {
                                        return CustomButton(
                                          isDisable:
                                              cubit.isRequestLoading.value,
                                          disableColor:
                                              context.themeColors.greenColor,
                                          inProgress: state
                                              is LoadingResetPasswordState,
                                          title: AuthLocaleKeys.resetPasswordButton.tr(),
                                          onTap: () async {
                                            cubit.isResetButtonClick.value =
                                                true;
                                            if (cubit.validateForm()) {
                                              cubit.isResetButtonClick.value =
                                                  false;
                                              await cubit.resetPassword(
                                                password: cubit
                                                    .passwordController.text,
                                                confirmPassword: cubit
                                                    .confirmPasswordController
                                                    .text,
                                                context: context,
                                              );
                                            }
                                          },
                                          isBottom: true,
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
