import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/forget_password_module/cubit/forget_password_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/forget_password_module/cubit/forget_password_state.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/widgets/custom_textfield.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ForgetPasswordPage extends StatelessWidget {
  const ForgetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgetPasswordCubit(),
      child: BlocBuilder<ForgetPasswordCubit, ForgetPasswordState>(
        builder: (context, state) {
          final cubit = context.read<ForgetPasswordCubit>();
          return GestureDetector(
            onTap: (){
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              resizeToAvoidBottomInset: true,
              body: SafeArea(
                bottom: false,
                child: SingleChildScrollView(
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height - MediaQuery.of(context).viewPadding.top,
                    width: MediaQuery.of(context).size.width,
                    child: Stack(
                      children: [
                        Form(
                          key: cubit.formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const CustomBackArrowButton(),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w20, vertical: AppSize.h20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppTextWidget(
                                      AuthLocaleKeys.forgetPasswordLabel.tr(),
                                      style: context.textTheme.headlineMedium?.copyWith(
                                        height: 1.5,
                                      ),
                                    ),
                                    SpaceV(AppSize.h20),
                                    AppTextWidget(
                                      AuthLocaleKeys.forgetPasswordText.tr(),
                                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                                    ),
                                    SpaceV(AppSize.h20),
                                    // AppTextWidget(
                                    //   AuthLocaleKeys.forgetPasswordEmailLabel.tr(),
                                    //   style: context.textTheme.titleMedium,
                                    //   //style: AppTextStyle.textFieldLabel.copyWith(color: Colors.black, fontSize: 17),
                                    // ),
                                    // SpaceV(AppSize.h6),
                                    ValueListenableBuilder(
                                      valueListenable: cubit.isForgetButtonClick,
                                      builder: (context,value,child) {
                                        return ValueListenableBuilder(
                                          valueListenable: cubit.emailError,
                                          builder: (context,emailError,child) {
                                            return Column(
                                              children: [
                                                CustomOutlinedTextfield(
                                                  controller: cubit.emailController,
                                                  textAction: TextInputAction.done,
                                                  labelText: AuthLocaleKeys.signUpEmailLabel.tr(),
                                                  isError: cubit.isForgetButtonClick.value &&
                                                      emailError != '',
                                                  // validator: emailValidator().call,
                                                  onChanged: (_) {
                                                    cubit.emailValid();
                                                  },
                                                ),
                                                if (cubit.isForgetButtonClick.value &&
                                                    emailError != '')
                                                  CustomErrorWidget(
                                                    errorMessgaeText: emailError,
                                                  ),
                                              ],
                                            );
                                          },
                                        );
                                      },
                                    ),
                                    // CustomOutlinedTextfield(
                                    //   key: const Key('email'),
                                    //   controller: cubit.emailController,
                                    //   validator: emailValidator().call,
                                    //   onChanged: (p0) => cubit.formKey.currentState?.validate(),
                                    // ),
                                    ValueListenableBuilder(
                                        valueListenable:cubit.emailError,
                                        builder: (context,emailError,child) {
                                          if (state is SuccessForgetPasswordState) {
                                            return Column(
                                                children: [
                                                  SizedBox(height: AppSize.h20),
                                                  AppTextWidget(
                                                    AuthLocaleKeys.forgetPasswordSuccess.tr(),
                                                    style: context.textTheme.titleMedium?.copyWith(
                                                      color: context.themeColors.greenColor,
                                                      fontWeight: FontWeight.w500,
                                                      fontSize: AppSize.sp12,
                                                    ),
                                                  )
                                                ],
                                              );
                                          } else {
                                            return const SizedBox();
                                          }

                                        }),
                                    ValueListenableBuilder(
                                        valueListenable:cubit.emailError,
                                        builder: (context,emailError,child) {
                                          if (state is ErrorForgetPasswordState) {
                                            return CustomErrorWidget(errorMessgaeText: AuthLocaleKeys.forgetPasswordEmailNotFound.tr());
                                          } else {
                                            return const SizedBox();
                                          }
                                        }),

                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        ValueListenableBuilder(
                          valueListenable: cubit.isRequestLoading,
                          builder: (context, value, child) {
                            return Positioned(
                              bottom: 0,
                              child: SizedBox(
                                width: MediaQuery.of(context).size.width,
                                child: CustomButton(
                                  isDisable: cubit.isRequestLoading.value,
                                  disableColor: context.themeColors.greenColor,
                                  inProgress: state is LoadingForgetPasswordState,
                                  title: AuthLocaleKeys.resetText.tr(),
                                  onTap: () async {
                                    cubit.isForgetButtonClick.value = true;
                                    if (cubit.emailValid(buttonClick: true)) {
                                      cubit.isForgetButtonClick.value = false;
                                      await cubit.forgotPassword(email: cubit.emailController.text, context: context);
                                    }
                                  },
                                  isBottom: true,
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
