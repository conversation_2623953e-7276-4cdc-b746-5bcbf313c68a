import 'package:breakingfree_v2/features/authentication_module/forget_password_module/cubit/forget_password_state.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ForgetPasswordCubit extends Cubit<ForgetPasswordState> {
  ForgetPasswordCubit() : super(InitialForgetPasswordState());
  final AuthRepository authRepository = AuthRepository();

  TextEditingController emailController = TextEditingController();
  ValueNotifier<bool> isForgetButtonClick = ValueNotifier(false);
  ValueNotifier<String> emailError = ValueNotifier('');

  ValueNotifier<bool> isRequestLoading = ValueNotifier(false);

  final formKey = GlobalKey<FormState>();

  bool emailValid({bool buttonClick = false}) {
    emit(InitialForgetPasswordState());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(emailController.text);
    // Email
    if (emailController.text.trim().isEmpty) {
      emailError.value = AuthLocaleKeys.logInEmailRequired.tr();
      isValid = false;
      if(!buttonClick) {
        emailError.value = '';
      }
    } else if (!emailValid) {
      emailError.value = AuthLocaleKeys.logInEmailInvalid.tr();
      isValid = false;
      if(!buttonClick) {
        emailError.value = '';
      }
    } else {
      emailError.value = '';
    }
    return isValid;
  }

  Future<bool> forgotPassword({required String email, required BuildContext context}) async {
    try {
      isRequestLoading.value = true;
      emit(LoadingForgetPasswordState());
      final result = await authRepository.forgotPassword(email: email, context: context);
      emit(result != null ? SuccessForgetPasswordState() : ErrorForgetPasswordState());
      return result != null;
    } catch (e) {
      emit(ErrorForgetPasswordState());
    } finally {
      isRequestLoading.value = false;
    }
    return false;
  }

  @override
  Future<void> close() {
    emailController.dispose();
    return super.close();
  }
}
