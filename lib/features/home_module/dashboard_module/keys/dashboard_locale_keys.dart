class DashboardLocaleKeys {
  // Titles
  static const title = 'dashboard.title';
  static const titleFirst = 'dashboard.titleFirst';

  // Recovery Progress
  static const recoveryProgressTitle = 'dashboard.recoveryProgress.title';
  static const recoveryProgressSubtitle = 'dashboard.recoveryProgress.subtitle';
  static const recoveryProgressLabelsTabs = 'dashboard.recoveryProgress.labels.tabs';
  static const recoveryProgressLabelsAlcohol = 'dashboard.recoveryProgress.labels.alcohol';
  static const recoveryProgressLabelsAlcoholUnit = 'dashboard.recoveryProgress.labels.alcoholUnits';
  static const recoveryProgressLabelsUnits = 'dashboard.recoveryProgress.labels.units';
  static const recoveryProgressLabelsStart = 'dashboard.recoveryProgress.labels.start';
  static const recoveryProgressLabelsCheckin = 'dashboard.recoveryProgress.labels.checkin';
  static const recoveryProgressLabelsCurrent = 'dashboard.recoveryProgress.labels.current';
  static const recoveryProgressLabelsMyGoal = 'dashboard.recoveryProgress.labels.myGoal';
  static const recoveryProgressLabelsMonthNames = 'dashboard.recoveryProgress.labels.monthNames';

  // Recovery Progress Info Panels
  static const recoveryProgressInfoPanelDrinkingText = 'dashboard.recoveryProgress.infoPanel.drinking.text';
  static const recoveryProgressInfoPanelDrinkingAudio = 'dashboard.recoveryProgress.infoPanel.drinking.audio';
  static const recoveryProgressInfoPanelDrugsText = 'dashboard.recoveryProgress.infoPanel.drugs.text';
  static const recoveryProgressInfoPanelDrugsAudio = 'dashboard.recoveryProgress.infoPanel.drugs.audio';
  static const recoveryProgressInfoPanelDrinkingDrugsText = 'dashboard.recoveryProgress.infoPanel.drinkingDrugs.text';
  static const recoveryProgressInfoPanelDrinkingDrugsAudio = 'dashboard.recoveryProgress.infoPanel.drinkingDrugs.audio';

  // Strengths
  static const strengthsTitle = 'dashboard.strengths.title';
  static const strengthsWellbeingTitle = 'dashboard.strengths.wellbeing.title';
  static const strengthsWellbeingInfoPanelText = 'dashboard.strengths.wellbeing.infoPanel.text';
  static const strengthsWellbeingInfoPanelAudio = 'dashboard.strengths.wellbeing.infoPanel.audio';
  static const strengthsResilienceTitle = 'dashboard.strengths.resilience.title';
  static const strengthsResilienceInfoPanelText = 'dashboard.strengths.resilience.infoPanel.text';
  static const strengthsResilienceInfoPanelAudio = 'dashboard.strengths.resilience.infoPanel.audio';
  static const strengthsQualityTitle = 'dashboard.strengths.quality.title';
  static const strengthsQualityInfoPanelText = 'dashboard.strengths.quality.infoPanel.text';
  static const strengthsQualityInfoPanelAudio = 'dashboard.strengths.quality.infoPanel.audio';

  // Achievements
  static const achievementsTitle = 'dashboard.achievements.title';

  // colourNames
  static const achievementsColourNamesBronze = 'dashboard.achievements.colourNames.bronze';
  static const achievementsColourNamesSilver = 'dashboard.achievements.colourNames.silver';
  static const achievementsColourNamesGold = 'dashboard.achievements.colourNames.gold';
  static const achievementsColourNamesPlatinum = 'dashboard.achievements.colourNames.platinum';



  // Achievements - Goal  
  static const achievementsGoalTitle = 'dashboard.achievements.goal.title';
  static const achievementsGoalSubtitle = 'dashboard.achievements.goal.subtitle';
  static const achievementsGoalTransparentText = 'dashboard.achievements.goal.transparent.text';
  static const achievementsGoalTransparentAudio = 'dashboard.achievements.goal.transparent.audio';
  static const achievementsGoalBronzeText = 'dashboard.achievements.goal.bronze.text';
  static const achievementsGoalBronzeAudio = 'dashboard.achievements.goal.bronze.audio';
  static const achievementsGoalSilverText = 'dashboard.achievements.goal.silver.text';
  static const achievementsGoalSilverAudio = 'dashboard.achievements.goal.silver.audio';
  static const achievementsGoalGoldText = 'dashboard.achievements.goal.gold.text';
  static const achievementsGoalGoldAudio = 'dashboard.achievements.goal.gold.audio';
  static const achievementsGoalPlatinumText = 'dashboard.achievements.goal.platinum.text';
  static const achievementsGoalPlatinumAudio = 'dashboard.achievements.goal.platinum.audio';

  // Achievements - Strategies
  static const achievementsStrategiesTitle = 'dashboard.achievements.strategies.title';
  static const achievementsStrategiesSubtitle = 'dashboard.achievements.strategies.subtitle';
  static const achievementsStrategiesTransparentText = 'dashboard.achievements.strategies.transparent.text';
  static const achievementsStrategiesTransparentAudio = 'dashboard.achievements.strategies.transparent.audio';
  static const achievementsStrategiesBronzeText = 'dashboard.achievements.strategies.bronze.text';
  static const achievementsStrategiesBronzeAudio = 'dashboard.achievements.strategies.bronze.audio';
  static const achievementsStrategiesSilverText = 'dashboard.achievements.strategies.silver.text';
  static const achievementsStrategiesSilverAudio = 'dashboard.achievements.strategies.silver.audio';
  static const achievementsStrategiesGoldText = 'dashboard.achievements.strategies.gold.text';
  static const achievementsStrategiesGoldAudio = 'dashboard.achievements.strategies.gold.audio';
  static const achievementsStrategiesPlatinumText = 'dashboard.achievements.strategies.platinum.text';
  static const achievementsStrategiesPlatinumAudio = 'dashboard.achievements.strategies.platinum.audio';

  // Achievements - Hours
  static const achievementsHoursTitle = 'dashboard.achievements.hours.title';
  static const achievementsHoursSubtitle = 'dashboard.achievements.hours.subtitle';
  static const achievementsHoursTransparentText = 'dashboard.achievements.hours.transparent.text';
  static const achievementsHoursTransparentAudio = 'dashboard.achievements.hours.transparent.audio';
  static const achievementsHoursBronzeText = 'dashboard.achievements.hours.bronze.text';
  static const achievementsHoursBronzeAudio = 'dashboard.achievements.hours.bronze.audio';
  static const achievementsHoursSilverText = 'dashboard.achievements.hours.silver.text';
  static const achievementsHoursSilverAudio = 'dashboard.achievements.hours.silver.audio';
  static const achievementsHoursGoldText = 'dashboard.achievements.hours.gold.text';
  static const achievementsHoursGoldAudio = 'dashboard.achievements.hours.gold.audio';
  static const achievementsHoursPlatinumText = 'dashboard.achievements.hours.platinum.text';
  static const achievementsHoursPlatinumAudio = 'dashboard.achievements.hours.platinum.audio';

  // Achievements - Diagram
  static const achievementsDiagramTitle = 'dashboard.achievements.diagram.title';
  static const achievementsDiagramTransparentText = 'dashboard.achievements.diagram.transparent.text';
  static const achievementsDiagramTransparentAudio = 'dashboard.achievements.diagram.transparent.audio';
  static const achievementsDiagramGreenText = 'dashboard.achievements.diagram.green.text';
  static const achievementsDiagramGreenAudio = 'dashboard.achievements.diagram.green.audio';

  // Progress Report
  static const progressReportTitle = 'dashboard.progressReport.title';
  static const progressReportLatestSubtitle = 'dashboard.progressReport.latest.subtitle';
  static const progressReportLatestLabel = 'dashboard.progressReport.latest.label';
  static const progressReportLatestInfoPanelText = 'dashboard.progressReport.latest.infoPanel.text';
  static const progressReportLatestInfoPanelAudio = 'dashboard.progressReport.latest.infoPanel.audio';
  static const progressReportUpdateSubtitle = 'dashboard.progressReport.update.subtitle';
  static const progressReportUpdateLabel = 'dashboard.progressReport.update.label';
  static const progressReportUpdateInfoPanelText = 'dashboard.progressReport.update.infoPanel.text';
  static const progressReportUpdateInfoPanelAudio = 'dashboard.progressReport.update.infoPanel.audio';
  static const progressReportPdfFileName = 'dashboard.progressReport.pdfFileName';

  // Buttons
  static const buttonsDiagram = 'dashboard.buttons.diagram';
  static const buttonsToolkit = 'dashboard.buttons.toolkit';

  // Errors
  static const errorsNoCheckins = 'dashboard.errors.noCheckins';
}
