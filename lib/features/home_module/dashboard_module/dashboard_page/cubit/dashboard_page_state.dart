part of 'dashboard_page_cubit.dart';

@freezed
class DashboardPageState with _$DashboardPageState {
  const factory DashboardPageState.initial() = _Initial;

   const factory DashboardPageState.downloadPdfLoading() = _DownloadLoadingState;
  const factory DashboardPageState.emailPdfLoading() = _EmailPdfLoadingState;
  const factory DashboardPageState.checkinError(String message) = _CheckinErrorState;

}
