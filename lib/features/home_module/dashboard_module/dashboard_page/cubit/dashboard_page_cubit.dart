import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/checkin_processer_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/repository/my_diagram_repository.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

part 'dashboard_page_cubit.freezed.dart';
part 'dashboard_page_state.dart';

class DashboardPageCubit extends Cubit<DashboardPageState> {
  DashboardPageCubit() : super(const DashboardPageState.initial()) {
    getDashboardData();
    graphData();
    getMyDiaGramColor();
    checkAchievement();
    setUserModelCopy();
  }
  final controller = ScrollController();
  late final Animation<double> scaleAnimation;

  ValueNotifier<bool> showPrivacyPopup = ValueNotifier(false);
  Future<void> navigateToPopup({required bool isEmail, required BuildContext context}) async {
    showPrivacyPopup.value = true;
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        controller.jumpTo(
          controller.position.maxScrollExtent,
          // duration: const Duration(milliseconds: 100),
          // curve: Curves.linear,
        );
      },
    );
    await progressReportActionStrategyforDownloadPdfApi(context: context, isEmail: isEmail);
  }

  ValueNotifier<bool> showRecoveryInfo = ValueNotifier(false);
  ValueNotifier<bool> showTrophyAnimation = ValueNotifier(false);
  String animationTitle = '';
  String animationMainTitle = '';
  String animationSvg = '';
  ValueNotifier<bool> showStEmotionalInfo = ValueNotifier(false);
  ValueNotifier<bool> showStResilienceInfo = ValueNotifier(false);
  ValueNotifier<bool> showStqualityInfo = ValueNotifier(false);
  ValueNotifier<String?> reportInfoText = ValueNotifier(null);
  ValueNotifier<Achievements?> achivementInfoText = ValueNotifier(null);
  ValueNotifier<String?> audioUrl = ValueNotifier(null);
  ValueNotifier<bool> isDownloadFailed = ValueNotifier(false);
  ValueNotifier<bool> isOpen = ValueNotifier(false);
  ValueNotifier<bool> isShowText = ValueNotifier(false);
  ValueNotifier<bool> hundredPersentage = ValueNotifier(false);

  String errorMsg = DashboardLocaleKeys.errorsNoCheckins.tr();

  ValueNotifier<bool> isTooltipBehaviorChange = ValueNotifier(false);
  late TooltipBehavior tooltipBehavior = TooltipBehavior();

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<int> graphIndex = ValueNotifier(0);

  Set<int> triggeredGoalMilestones = {};
  Set<int> triggeredStrategyMilestones = {};
  Set<int> triggeredHourMilestones = {};
  bool diagramMilestoneTriggered = false;

   String formatString(String str) {
  final knownAcronyms = {'GHB', 'PVP', 'MDPV', 'MSJ', 'HGH', 'AH-7921', 'GBL', 'O-PCE'}; // Add all known acronyms
  final wordRegExp = RegExp(r'\b[A-Za-z0-9-]+\b');

  return str.replaceAllMapped(wordRegExp, (match) {
    final word = match.group(0)!;

    final parts = word.split('-');
    final formattedParts = parts.map((part) {
      final upperPart = part.toUpperCase();
      if (knownAcronyms.contains(upperPart)) {
        return upperPart;
      } else {
        return part.toLowerCase();  
      }
    }).toList();

    return formattedParts.join('-');
  });
}

String getLocalizedDrugNameFromKey(String key) {
  final normalizedKey = key.toLowerCase().replaceAll(' ', '-').replaceAll('_', '-');
  final localeKey = drugLocaleKeyMap[normalizedKey];
  return localeKey?.tr() ?? key; // fallback to raw key if not found
}


final Map<String, String> drugLocaleKeyMap = {
  'ah-7921': DrugsLocaleKeys.aH7921Drug,
  'gbl': DrugsLocaleKeys.gbl,
  'ghb': DrugsLocaleKeys.ghb,
  'hgh': DrugsLocaleKeys.hgh,
  'mdpv': DrugsLocaleKeys.mdpv,
  'msj': DrugsLocaleKeys.msj,
  'o-pce': DrugsLocaleKeys.oPce,
  'sustanon': DrugsLocaleKeys.sustanon,
  'testosterone': DrugsLocaleKeys.testosterone,
  'acamprosate': DrugsLocaleKeys.acamprosate,
  'adderall': DrugsLocaleKeys.adderall,
  'alpha-pvp': DrugsLocaleKeys.alphaPvp,
  'ambien': DrugsLocaleKeys.ambien,
  'amitriptyline': DrugsLocaleKeys.amitriptyline,
  'amphetamines': DrugsLocaleKeys.amphetamines,
  'buprenorphine': DrugsLocaleKeys.buprenorphine,
  'butane': DrugsLocaleKeys.butane,
  'clonazepam': DrugsLocaleKeys.clonazepam,
  'co-codamol': DrugsLocaleKeys.coCodamol,
  'cocaine': DrugsLocaleKeys.cocaine,
  'codeine': DrugsLocaleKeys.codeine,
  'crack': DrugsLocaleKeys.crack,
  'demerol': DrugsLocaleKeys.demerol,
  'dexedrine': DrugsLocaleKeys.dexedrine,
  'diazepam': DrugsLocaleKeys.diazepam,
  'disulfiram': DrugsLocaleKeys.disulfiram,
  'ecstasy': DrugsLocaleKeys.ecstasy,
  'ephedrine': DrugsLocaleKeys.ephedrine,
  'etizolam': DrugsLocaleKeys.etizolam,
  'fentanyl': DrugsLocaleKeys.fentanyl,
  'gabapentin': DrugsLocaleKeys.gabapentin,
  'heroin': DrugsLocaleKeys.heroin,
  'hydromorphone': DrugsLocaleKeys.hydromorphone,
  'ketamine': DrugsLocaleKeys.ketamine,
  'khat': DrugsLocaleKeys.khat,
  'lorezepam': DrugsLocaleKeys.lorezepam,
  'lunesta': DrugsLocaleKeys.lunesta,
  'marijuana': DrugsLocaleKeys.marijuana,
  'mephedrone': DrugsLocaleKeys.mephedrone,
  'mephobarbital': DrugsLocaleKeys.mephobarbital,
  'methadone': DrugsLocaleKeys.methadone,
  'methamphetamine': DrugsLocaleKeys.methamphetamine,
  'methoxatamine': DrugsLocaleKeys.methoxatamine,
  'modafinil': DrugsLocaleKeys.modafinil,
  'morphine': DrugsLocaleKeys.morphine,
  'nalmefene': DrugsLocaleKeys.nalmefene,
  'naltrexone': DrugsLocaleKeys.naltrexone,
  'nitrazepam': DrugsLocaleKeys.nitrazepam,
  'nitrous-oxide': DrugsLocaleKeys.nitrousOxide,
  'oxandrolone': DrugsLocaleKeys.oxandrolone,
  'oxycontin': DrugsLocaleKeys.oxycontin,
  'oxymorphone': DrugsLocaleKeys.oxymorphone,
  'pcp': DrugsLocaleKeys.pcp,
  'phenobarbital': DrugsLocaleKeys.phenobarbital,
  'pholcodeine': DrugsLocaleKeys.pholcodeine,
  'pregabalin': DrugsLocaleKeys.pregabalin,
  'ritalin': DrugsLocaleKeys.ritalin,
  'sonata': DrugsLocaleKeys.sonata,
  'suboxone': DrugsLocaleKeys.suboxone,
  'synthetic-cannabis': DrugsLocaleKeys.syntheticCannabis,
  'temazepam': DrugsLocaleKeys.temazepam,
  'tobacco': DrugsLocaleKeys.tobacco,
  'tramadol': DrugsLocaleKeys.tramadol,
  'trenbolone': DrugsLocaleKeys.trenbolone,
  'triazolam': DrugsLocaleKeys.triazolam,
  'vicodin': DrugsLocaleKeys.vicodin,
  'xanax': DrugsLocaleKeys.xanax,
  'zopiclon': DrugsLocaleKeys.zopiclon,
  'alfentanil': DrugsLocaleKeys.alfentanil,
  'alpha-d2pv': DrugsLocaleKeys.alphaD2pv,
  'bromadoline': DrugsLocaleKeys.bromadoline,
  'bromazolam': DrugsLocaleKeys.bromazolam,
  'buvidal': DrugsLocaleKeys.buvidal,
  'caffeine': DrugsLocaleKeys.caffeine,
  'carfentanil': DrugsLocaleKeys.carfentanil,
  'desomorphine': DrugsLocaleKeys.desomorphine,
  'diclazepam': DrugsLocaleKeys.diclazepam,
  'flephedrone': DrugsLocaleKeys.flephedrone,
  'flubromazolam': DrugsLocaleKeys.flubromazolam,
  'hysingla': DrugsLocaleKeys.hysingla,
  'ketazolam': DrugsLocaleKeys.ketazolam,
  'kratom': DrugsLocaleKeys.kratom,
  'meprobamate': DrugsLocaleKeys.meprobamate,
  'metandienone': DrugsLocaleKeys.metandienone,
  'methylone': DrugsLocaleKeys.methylone,
  'midazolam': DrugsLocaleKeys.midazolam,
  'pentazocine': DrugsLocaleKeys.pentazocine,
  'phenibut': DrugsLocaleKeys.phenibut,
  'sufentanil': DrugsLocaleKeys.sufentanil,
  'tapentadol': DrugsLocaleKeys.tapentadol,
  'topiramate': DrugsLocaleKeys.topiramate,
  'u-47700': DrugsLocaleKeys.u47700,
  'vape-marijuana': DrugsLocaleKeys.vapeMarijuana,
  'vape-nicotine': DrugsLocaleKeys.vapeNicotine,
  'vyvanse': DrugsLocaleKeys.vyvanse,
  'xelstrym': DrugsLocaleKeys.xelstrym,
  'xylazine': DrugsLocaleKeys.xylazine,
  'zohydro': DrugsLocaleKeys.zohydro,
};


  bool shouldShowAnimation({required bool isFromProgress}) {
    if (!isFromProgress) return false;

    var shouldAnimate = false;

    // Goal milestones
    const goalMilestones = [25, 50, 75, 100];
    if (goalMilestones.contains(goalPercentage) && !triggeredGoalMilestones.contains(goalPercentage)) {
      triggeredGoalMilestones.add(goalPercentage);
      shouldAnimate = true;
    }

    // Strategies milestones
    const strategyMilestones = [3, 6, 9, 12];
    if (strategyMilestones.contains(myStrategies) && !triggeredStrategyMilestones.contains(myStrategies)) {
      triggeredStrategyMilestones.add(myStrategies!);
      shouldAnimate = true;
    }

    // Hours milestones
    const hourMilestones = [3, 6, 9, 12];
    if (hourMilestones.contains(myHours) && !triggeredHourMilestones.contains(myHours)) {
      triggeredHourMilestones.add(myHours);
      shouldAnimate = true;
    }

    // Diagram milestone
    if (myDiagram == 6 && !diagramMilestoneTriggered) {
      diagramMilestoneTriggered = true;
      shouldAnimate = true;
    }

    return shouldAnimate;
  }

  String tropyImage = Assets.icons.dashboard.trophy.myAchievementsTrophyTransparent;
  String goalInfoText = DashboardLocaleKeys.achievementsGoalTransparentText.tr();
  String goalInfoAudio = DashboardLocaleKeys.achievementsGoalTransparentAudio.tr();
  int goalPercentage = 0;

  int myHours = 0;
  int myDiagram = 0;
  String? hoursImage;
  String? hoursInfoText;
  String? hoursInfoAudio;

  String? myDiagramImage;
  String? myDiagramInfoText;
  String? myDiagramAudio;

  int goalValue = 0;

  int? myStrategies;
  String? strategiesImage;
  String?   strategiesInfoText;
  String? strategiesInfoAudio;
  String popupText = '';
  double emotionalRedTriangle = 0;
  double emotionalGreenTriangle = 0;

  int resilienceRedTriangle = 0;
  int resilienceGreenTriangle = 0;

  int qualityOfRedTriangle = 0;

  int qualityOfLifeGreenTriangle = 0;
  List<FlSpot> dataPoints = <FlSpot>[];
  ValueNotifier<String> dynamicUnitText = ValueNotifier('');
  List<String> xLabels = <String>[];
  final repository = MyDiagramRepository();

  int dsScore = 0;
  int ntScore = 0;
  int eiScore = 0;
  int lsScore = 0;
  int ubScore = 0;
  int psScore = 0;

  final isAlcohol2 = Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 0 || Injector.instance<AppDB>().userModel?.user.assessment?.rp?.specialAddiction == 0;
  final ass2 = Injector.instance<AppDB>().userModel?.user.assessment?.toJson();
  
  
   bool getStartAndGoal(){
    final checkins = Injector.instance<AppDB>().userModel?.user.checkins?.toList();
    '????? checkins = ${checkins?.length ?? 0}'.logD;
    final assessment2 = Assessment.fromJson(ass2 ?? {});
    final substance = isAlcohol2
      ? 'drinking'
      : (assessment2.drugs?.list as Map<String, dynamic>?)?.keys.first ?? 'unknown';
    
    final start2 = isAlcohol2
      ? (assessment2.drinking?.units ?? 0) * (assessment2.drinking?.days ?? 0)
      : (assessment2.drugs?.list?[substance]?.amount ?? 0) *
          (assessment2.drugs?.list?[substance]?.frequency ?? 0);

  // Get GOAL (goal units/week)
  final goal2 = isAlcohol2
      ? (assessment2.drinkingGoal?.units ?? 0) * (7 - (assessment2.drinkingGoal?.freeDays ?? 0))
      : (assessment2.drugsGoal?.drugDetails[substance]?.units ?? 0) *
          (7 - (assessment2.drugsGoal?.drugDetails[substance]?.freeDays ?? 0));
          

        '????? start = $start2 goal = $goal2'.logD;
    return (start2 == goal2) && checkins?.length == 0;
   }


  String getPreposition(String substance) {
  final vowels = ['a', 'e', 'i', 'o', 'u', 'h'];
  final localized = formatString(getLocalizedDrugNameFromKey(substance.toLowerCase()));
  return vowels.contains(localized[0]) ? 'd’' : 'de ';
}

String getLocalizedSubstance(String key) {
  return drugLocaleKeyMap[key]?.tr() ?? key;
}


  // Define goal thresholds (example values; adjust as needed)
  final goalTrophyvalues = <String, dynamic>{
    'goal': {
      'bronze': 25,
      'silver': 50,
      'gold': 75,
      'platinum': 100,
    },
  };

  /// Check if user has finished some kind of achievement to show
  /// compared to previous copy
  void checkAchievement() {

    '>?>?>?>? checkAchievement'.logV;
    final appDB = Injector.instance<AppDB>();
    var userModelOld = appDB.userModelCopy;
    final userModelNew = appDB.userModel;
    '>?>?>?>? userModelOld = ${userModelOld?.user.checkins?.toList()}'.logD;
    '>?>?>?>? userModelNew = ${userModelNew?.user.checkins?.toList()}'.logD;
    if (userModelOld == null) {
      '>?>?>?>? goal ${userModelOld?.toJson()}'.logD;
      userModelOld = userModelNew;
      Injector.instance<AppDB>().userModelCopy = userModelNew;
    }
    final assessmentOld = userModelOld?.user.assessment?.toJson();
    final assessmentNew = userModelNew?.user.assessment?.toJson();
    final latestUpdateOld = CheckinProcessor.processCheckins1<Checkin>(
      <MapEntry<DateTime, Checkin>>[],
      userModelOld?.user.checkins ?? [],
    );
    final latestUpdateNew = CheckinProcessor.processCheckins1<Checkin>(
      <MapEntry<DateTime, Checkin>>[],
      userModelNew?.user.checkins ?? [],
    );
    '>?>?>?>? userModelOld = ${userModelOld?.toJson()}'.logV;
    '>?>?>?>? assessmentOld = $assessmentOld'.logV;
    '>?>?>?>? assessmentNew = $assessmentNew'.logV;
    '>?>?>?>? latestUpdateOld = ${latestUpdateOld.toList()}'.logV;
    '>?>?>?>? latestUpdateNew = ${latestUpdateNew.toList()}'.logV;
    final uniqueStrategiesCountNew = getStrategyLength(userModelNew);
    final uniqueStrategiesCountOld = getStrategyLength(userModelOld);
    const strategyMilestones = [1, 4, 8, 12];
    const hourMilestones = [1, 4, 8, 12];
    
    final timeInHoursOld = (userModelOld?.user.timeSpent ?? 0) ~/ 3600;
    final timeInHoursNew = (userModelNew?.user.timeSpent ?? 0) ~/ 3600;

    '>?>?>?>? uniqueStrategiesCountNew = $uniqueStrategiesCountNew'.logV;
    '>?>?>?>? uniqueStrategiesCountOld = $uniqueStrategiesCountOld'.logV;
    '>?>?>?>? strategyMilestones = $strategyMilestones'.logV;

    '>?>?>?>? timeInHoursNew = $timeInHoursNew'.logV;
    '>?>?>?>? timeInHoursOld = $timeInHoursOld'.logV;
    '>?>?>?>? hourMilestones = $hourMilestones'.logV;

    if (uniqueStrategiesCountNew != uniqueStrategiesCountOld &&
        strategyMilestones.contains(uniqueStrategiesCountNew)) {
          '>?>?>?>? inside strategy if '.logV;
      showTrophyAnimation.value = true;
      animationTitle = strategiesInfoText?.split('\n').first ?? '';
      animationMainTitle = DashboardLocaleKeys.achievementsStrategiesTitle.tr();
      animationSvg = strategiesImage ?? '';
      appDB.userModelCopy = userModelNew;
      return;
    }
    else if (timeInHoursOld != timeInHoursNew && hourMilestones.contains(timeInHoursNew)) {
      '>?>?>?>? inside hour if '.logV;
      showTrophyAnimation.value = true;
      animationTitle = hoursInfoText?.split('\n').first ?? '';
      animationSvg = hoursImage ?? '';
      appDB.userModelCopy = userModelNew;
      animationMainTitle = DashboardLocaleKeys.achievementsHoursTitle.tr();
      return;
    } 
    
    '>?>?>?>? if before'.logV;
    if (userModelOld == null ||
        assessmentOld == null ||
        assessmentNew == null ||
        latestUpdateNew.isEmpty) {
          '>?>?>?>? condition matched '.logV;
      showTrophyAnimation.value = false;
      return;
    }
    '>?>?>?>? if after'.logV;

    // Hours milestones
    
    latestUpdateOld.sort((a, b) => b.key.compareTo(a.key));
    latestUpdateNew.sort((a, b) => b.key.compareTo(a.key));
    final goalOld = latestUpdateOld.isEmpty ? null : getGoal(assessmentOld, latestUpdateOld.first.value.toJson(), goalTrophyvalues);
    final goalNew = getGoal(assessmentNew, latestUpdateNew.first.value.toJson(), goalTrophyvalues);

    '>?>?>?>? uniqueStrategiesCountOld = $uniqueStrategiesCountOld'.logV;
    '>?>?>?>? uniqueStrategiesCountNew = $uniqueStrategiesCountNew'.logV;

    '>>>>>>>> goal old = ${goalOld?.value}'.logV;
    '>>>>>>>> goal new = ${goalNew.value}'.logV;

    if (goalOld?.value != goalNew.value || goalOld == null) {
      showTrophyAnimation.value = true;
      animationTitle = goalNew.text.split('\n').first;
      animationSvg = tropyImage;
      animationMainTitle = DashboardLocaleKeys.achievementsGoalTitle.tr();
    } 
    appDB.userModelCopy = userModelNew;
  }

  int getStrategyLength(UserModel? userModel) {
    final dsAs = userModel?.user.strategies?.dsAs?.length;
    final dsIs = userModel?.user.strategies?.dsIs?.length;
    final ntAs = userModel?.user.strategies?.ntAs?.length;
    final ntIs = userModel?.user.strategies?.ntIs?.length;
    final lsAs = userModel?.user.strategies?.lsAs?.length;
    final lsIs = userModel?.user.strategies?.lsIs?.length;
    final eiAs = userModel?.user.strategies?.eiAs?.length;
    final eiIs = userModel?.user.strategies?.eiIs?.length;
    final psAs = userModel?.user.strategies?.psAs?.length;
    final psIs = userModel?.user.strategies?.psIs?.length;
    final ubAs = userModel?.user.strategies?.ubAs?.length;
    final ubIs = userModel?.user.strategies?.ubIs?.length;
    final strategyLengthsNew = [
      dsAs,
      dsIs,
      ntAs,
      ntIs,
      lsAs,
      lsIs,
      eiAs,
      eiIs,
      psAs,
      psIs,
      ubAs,
      ubIs,
    ];

    return strategyLengthsNew.where((length) => (length ?? 0) > 0).length;
  }
void setUserModelCopy() {
    if(Injector.instance<AppDB>().userModelCopy?.user.checkins?.toList() == null  || Injector.instance<AppDB>().userModelCopy?.user.checkins?.length == 0) {
      Injector.instance<AppDB>().userModelCopy = Injector.instance<AppDB>().userModel;
    }
  }
  String formatDate(DateTime date, {bool isDay = false}) {
    final monthNames = (DynamicAssetLoader.getNestedValue(
      DashboardLocaleKeys.recoveryProgressLabelsMonthNames,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();

    final month = monthNames[date.month - 1];
    final formattedDate = '${date.day} $month';

    return formattedDate;
  }

  Future<void> progressReportActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
  }) async {
    isEmail ? emit(const DashboardPageState.emailPdfLoading()) : emit(const DashboardPageState.downloadPdfLoading());

    //   isEmail ? emit(state.copyWith(isEmailPdfAPILoading: true)) : emit(state.copyWith(isDownloadPdfAPILoading: true));
    try {
      final response = await repository.progressReportActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
        isDashboard: true,
      );
      '/// response = ${response?.data}'.logV;
      '/// response status message = ${response?.statusMessage}'.logV;
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'My progress report.pdf',
            );
          }
        } else {
          // CustomSnackbar.showSucessSnackBar(
          //   message: 'Email sent successfully',
          // );
        }
      } else {
        if(response?.data?['message'] == null){
          errorMsg = DashboardLocaleKeys.errorsNoCheckins.tr();
        }
        else if(response?.data?.containsKey('message') ?? false){
          errorMsg = CoreLocaleKeys.errorsCustom(response?.data!['message'] as String).tr();
        } else {
            '/// err msg = ${response?.data?['message']}'.logV;
          errorMsg = CoreLocaleKeys.errorsDefault.tr();
        }
        isDownloadFailed.value = true;
      }
      emit(const DashboardPageState.initial());
    } catch (e) {
      emit(const DashboardPageState.initial());
    }
  }

 GoalResult getGoal(Map<String, dynamic> assessment, Map<String, dynamic> latestUpdate, Map<String, dynamic> values) {
  final assessment1 = Assessment.fromJson(assessment);
  final latestUpdate1 = Checkin.fromJson(latestUpdate);
  final addictionCase = assessment1.rp!.addictionCase ?? 0;
  final specialAddiction = assessment1.rp!.specialAddiction ?? -1;

  // Determine primary substance
  final isAlcohol = addictionCase == 0 || specialAddiction == 0;
  final substance = isAlcohol
      ? 'drinking'
      : (assessment1.drugs?.list as Map<String, dynamic>?)?.keys.first ?? 'unknown';

  // Get START (assessment units/week)
  final start = isAlcohol
      ? (assessment1.drinking?.units ?? 0) * (assessment1.drinking?.days ?? 0)
      : (assessment1.drugs?.list?[substance]?.amount ?? 0) *
          (assessment1.drugs?.list?[substance]?.frequency ?? 0);

  // Get GOAL (goal units/week)
  final goal = isAlcohol
      ? (assessment1.drinkingGoal?.units ?? 0) * (7 - (assessment1.drinkingGoal?.freeDays ?? 0))
      : (assessment1.drugsGoal?.drugDetails[substance]?.units ?? 0) *
          (7 - (assessment1.drugsGoal?.drugDetails[substance]?.freeDays ?? 0));

  // Get CURRENT (latest checkin units/week) fallback to assessment if missing
  final current = isAlcohol
      ? ((latestUpdate1.drinking?.units ?? assessment1.drinking?.units ?? 0) *
          (latestUpdate1.drinking?.days ?? assessment1.drinking?.days ?? 0))
      : ((latestUpdate1.drugs?.drugDetails[substance]?.amount ??
              assessment1.drugs?.list?[substance]?.amount ??
              0) *
          (latestUpdate1.drugs?.drugDetails[substance]?.frequency ??
              assessment1.drugs?.list?[substance]?.frequency ??
              0));

  // First check if current value is at or below goal - if so, return Platinum
  // if ((current as num) <= (goal as num)) {
  //   return GoalResult(
  //     value: 100,
  //     colour: Assets.icons.dashboard.trophy.myAchievementsTrophyPlatinum,
  //     text: DashboardLocaleKeys.achievementsGoalPlatinumText.tr(),
  //     audio: DashboardLocaleKeys.achievementsGoalPlatinumAudio.tr(),
  //   );
  // }

  // If not at goal yet, calculate progress
  final totalDistance = ((start ?? 0) as num).toDouble() - ((goal ?? 0) as num).toDouble();
  final remainingDistance = ((current ?? 0) as num).toDouble() - ((goal ?? 0) as num).toDouble();

  // final double totalAbs = totalDistance.abs();
  // final double remainingAbs = remainingDistance.abs();

  // final double progress = totalAbs == 0 ? 100.0 : (1 - (remainingAbs / totalAbs)) * 100.0;
  // final double counter = progress.clamp(0.0, 100.0);

// Ensure dynamic values are converted to double
final goalValue = (goal as num).toDouble();
final startValue = (start as num).toDouble();
final currentValue = (current as num).toDouble();

// Calculate absolute distances
final totalAbs = (goalValue - startValue).abs();
final remainingAbs = (goalValue - currentValue).abs();


'??????????????????? totalAbs = $totalAbs, remainingAbs = $remainingAbs'.logV;
// Calculate progress
final progress = currentValue < goalValue ? 100.0 :
  totalAbs == 0
    ? (currentValue <= goalValue ? 100.0 : 0.0)
    : (1 - (remainingAbs / totalAbs)) * 100.0;


// Clamp to 0–100 and cast result
final counter = progress.clamp(0.0, 100.0);

  final goalValues = values['goal'] as Map<String, dynamic>;
  '????? goal values = $goalValues'.logV;
  '????? Start: $start, Goal: $goal, Current: $current'.logV;
  '????? counter = $counter and goal value = ${goalValues['bronze']}'.logV;

  // Trophy logic based on progress
  if (counter < (goalValues['bronze'] as int)) {
    '????? zero'.logV;
    return GoalResult(
      value: 0,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophyTransparent,
      text: DashboardLocaleKeys.achievementsGoalTransparentText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalTransparentAudio.tr(),
    );
  } else if (counter < (goalValues['silver'] as int)) {
    '????? broze'.logV;
    return GoalResult(
      value: 25,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophyBronze,
      text: DashboardLocaleKeys.achievementsGoalBronzeText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalBronzeAudio.tr(),
    );
  } else if (counter < (goalValues['gold'] as int)) {
    '????? silver'.logV;
    return GoalResult(
      value: 50,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophySilver,
      text: DashboardLocaleKeys.achievementsGoalSilverText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalSilverAudio.tr(),
    );
  } else if (counter < (goalValues['platinum'] as int)) {
    '????? gold'.logV;
    return GoalResult(
      value: 75,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophyGold,
      text: DashboardLocaleKeys.achievementsGoalGoldText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalGoldAudio.tr(),
    );
  } else if (counter == (goalValues['platinum'] as int)) {
    '????? platinum'.logV;
    return GoalResult(
      value: 100,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophyPlatinum,
      text: DashboardLocaleKeys.achievementsGoalPlatinumText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalPlatinumAudio.tr(),
    );
  }

  '????? zero'.logV;
  return GoalResult(
      value: 0,
      colour: Assets.icons.dashboard.trophy.myAchievementsTrophyTransparent,
      text: DashboardLocaleKeys.achievementsGoalTransparentText.tr(),
      audio: DashboardLocaleKeys.achievementsGoalTransparentAudio.tr(),
    );
  // return GoalResult(
  //   value: 100,
  //   colour: Assets.icons.dashboard.trophy.myAchievementsTrophyPlatinum,
  //   text: DashboardLocaleKeys.achievementsGoalPlatinumText.tr(),
  //   audio: DashboardLocaleKeys.achievementsGoalPlatinumAudio.tr(),
  // );
}


  String getMedal(int timeInSeconds) {
    // Convert time to hours
    final timeInHours = timeInSeconds ~/ 3600; // 1 hour = 3600 seconds

    String medal;

    if (timeInHours >= 12) {
      myHours = 12;
      hoursInfoText = DashboardLocaleKeys.achievementsHoursPlatinumText.tr();
      hoursInfoAudio = DashboardLocaleKeys.achievementsHoursPlatinumAudio.tr();
      medal = Assets.icons.dashboard.medal.myAchievementsMedalPlatinum;
    } else if (timeInHours >= 8) {
      myHours = timeInHours;
      hoursInfoText = DashboardLocaleKeys.achievementsHoursGoldText.tr();//'${timeInHours - 8}/4';
      hoursInfoAudio = DashboardLocaleKeys.achievementsHoursGoldAudio.tr();
      medal = Assets.icons.dashboard.medal.myAchievementsMedalGold;
    } else if (timeInHours >= 4) {
      myHours = timeInHours;
      hoursInfoText = DashboardLocaleKeys.achievementsHoursSilverText.tr();//'${timeInHours - 4}/4';
      hoursInfoAudio = DashboardLocaleKeys.achievementsHoursSilverAudio.tr();
      medal = Assets.icons.dashboard.medal.myAchievementsMedalSilver;
    } else if (timeInHours >= 1) {
      myHours = timeInHours;
      hoursInfoText = DashboardLocaleKeys.achievementsHoursBronzeText.tr();//'$timeInHours/3';
      hoursInfoAudio = DashboardLocaleKeys.achievementsHoursBronzeAudio.tr();
      medal = Assets.icons.dashboard.medal.myAchievementsMedalBronze;
    } else {
      myHours = 0;
      hoursInfoText = DashboardLocaleKeys.achievementsHoursTransparentText.tr();
      hoursInfoAudio = DashboardLocaleKeys.achievementsHoursTransparentAudio.tr();
      medal = Assets.icons.dashboard.medal.myAchievementsMedalTransparent;
    }

    return medal;
  }

  String monthName(int month) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return monthNames[month - 1]; // Subtract 1 because list indices are 0-based
  }

  String getMyStrategies(int strategiesCount) {
    'strategiesCount $strategiesCount'.logV;
    String strategies;

    if (strategiesCount >= 12) {
      myStrategies = strategiesCount;
      strategiesInfoText = DashboardLocaleKeys.achievementsStrategiesPlatinumText.tr();
      strategiesInfoAudio = DashboardLocaleKeys.achievementsStrategiesPlatinumAudio.tr();
      strategies = Assets.icons.dashboard.rosette.myAchievementsRosettePlatinum;
    } else if (strategiesCount >= 8) {
      myStrategies = strategiesCount;
      strategiesInfoText = DashboardLocaleKeys.achievementsStrategiesGoldText.tr();
      strategiesInfoAudio = DashboardLocaleKeys.achievementsStrategiesGoldAudio.tr();

      strategies = Assets.icons.dashboard.rosette.myAchievementsRosetteGold;
    } else if (strategiesCount >= 4) {
      myStrategies = strategiesCount;

      strategiesInfoText = DashboardLocaleKeys.achievementsStrategiesSilverText.tr();
      strategiesInfoAudio = DashboardLocaleKeys.achievementsStrategiesSilverAudio.tr();

      strategies = Assets.icons.dashboard.rosette.myAchievementsTrosetteSilver;
    } else if (strategiesCount >= 1) {
      myStrategies = strategiesCount;

      strategiesInfoText = DashboardLocaleKeys.achievementsStrategiesBronzeText.tr();
      strategiesInfoAudio = DashboardLocaleKeys.achievementsStrategiesBronzeAudio.tr();

      strategies = Assets.icons.dashboard.rosette.myAchievementsRosetteBronze;
    } else {
      myStrategies = 0;

      strategiesInfoText = DashboardLocaleKeys.achievementsStrategiesTransparentText.tr();
      strategiesInfoAudio = DashboardLocaleKeys.achievementsStrategiesTransparentAudio.tr();

      strategies = Assets.icons.dashboard.rosette.myAchievementsRosetteTransparent;
    }

    return strategies;
  }

  // Function to get the maximum Y value from the data points
  double getMaxYValue() {
    // Assuming ref.dataPoints is a List<FlSpot> and contains the data for the chart
    var maxYValue = double.negativeInfinity; // Start with negative infinity to ensure the first value is greater

    for (final spot in dataPoints) {
      if (spot.y > maxYValue) {
        maxYValue = spot.y;
      }
    }

    // If maxYValue is less than 10, set it to 10
    if (maxYValue < 10) {
      maxYValue = 10;
    } else {
      // Round up to the next multiple of 10
      maxYValue = (maxYValue / 10).ceil() * 10;
    }

    'maxYValue $maxYValue'.logD;
    return maxYValue;
  }

  int getDynamicYInterval() {
    final maxYValue = getMaxYValue(); // Get the maximum Y value dynamically
    int interval;

    // Define custom intervals based on the max Y value
    if (maxYValue <= 10) {
      interval = 5; // Show 1, 2, 3, ... for values between 1 and 10
    } else if (maxYValue <= 40) {
      interval = 10; // Show 0, 5, 10, 15, ... for values between 0 and 40
    } else if (maxYValue <= 60) {
      interval = 10; // Show 0, 10, 20, 30, ... for values between 0 and 60
    } else if (maxYValue <= 100) {
      interval = 20; // Show 0, 10, 20, 30, ... for values between 0 and 100
    } else if (maxYValue <= 1000) {
      interval = 500; // Show 0, 100, 200, 300, ... for values between 0 and 1000
    } else if (maxYValue <= 5000) {
      interval = 1000; // Show 0, 500, 1000, 1500, ... for values between 0 and 5000
    } else if (maxYValue <= 10000) {
      interval = 2000; // Show 0, 500, 1000, 1500, ... for values between 0 and 5000
    } else {
      interval = (maxYValue / 10).round(); // For values greater than 5000, divide by 10 and round
    }

    return interval;
  }

  int getDynamicXInterval() {
    final dataCount = (Injector.instance<AppDB>().userModel?.user.checkins?.length ?? 0) + 1;

    // Ensure there are at least 2 labels on the X-axis
    if (dataCount == 1) {
      return 1; // If only 1 data point, show another label 7 days later
    }

    // Otherwise, show every second point (or adjust based on your data)
    return 1;
  }

  void graphData() {
    var maxValue = 10; // Minimum Y-axis range
    final user = Injector.instance<AppDB>().userModel?.user;

    // Assessment Data (Drug or Alcohol)
    final assessmentDate = DateTime.fromMillisecondsSinceEpoch(user?.assessment?.time ?? 0);
    var assessmentValue = 0;

    if (user?.assessment?.drugs?.list != null) {
      // For drugs, loop through the drug list and calculate total units
      user?.assessment?.drugs?.list?.forEach((key, drug) {
        dynamicUnitText.value = key;
        'dynamicUnitText.value ${dynamicUnitText.value}'.logD;
        final drugUnits = drug.amount ?? 0;
        final drugFrequency = drug.frequency ?? 0;
        final drugTotalUnits = drugUnits * drugFrequency; // Total units for this drug
        assessmentValue += (drugTotalUnits is int
            ? drugTotalUnits
            : (drugTotalUnits as double).toInt()); // Accumulate total units for all drugs
      });
    } else {
      dynamicUnitText.value = 'Unit';
      final alcoholUnits = user?.assessment?.drinking?.units ?? 0;
      final days = user?.assessment?.drinking?.days ?? 0;
      assessmentValue = alcoholUnits * days;
    }

    dataPoints.add(FlSpot(0, assessmentValue.toDouble()));
    xLabels.add('${assessmentDate.day} ${monthName(assessmentDate.month)}');

    // Ensure the value is converted to an integer for comparison
    maxValue = max(maxValue, assessmentValue);

    // Progress Check Data (for drugs and alcohol)
    for (var i = 0; i < (user?.checkins?.length ?? 0); i++) {
      final checkinDate = DateTime.fromMillisecondsSinceEpoch(user?.checkins?[i].time ?? 0);
      var checkinValue = 0;

      if (user?.checkins?[i].drugs?.drugDetails != null) {
        // For drugs, loop through the drug list and calculate total units for each check-in
        user?.checkins?[i].drugs?.drugDetails.forEach((key, drug) {
          final drugUnits = (drug.amount is int) ? drug.amount as int : (drug.amount as num).toInt();
          final drugFrequency = drug.frequency ?? 0;
          final drugTotalUnits = drugUnits * drugFrequency;
          checkinValue += drugTotalUnits;
        });
      } else if (user?.checkins?[i].drinking != null) {
        // For alcohol, calculate the units for each check-in
        final alcoholUnits = user?.checkins?[i].drinking?.units ?? 0;
        final days = user?.checkins?[i].drinking?.days ?? 0;
        checkinValue = alcoholUnits * days;
      }

      dataPoints.add(FlSpot((i + 1).toDouble(), checkinValue.toDouble()));
      xLabels.add('${checkinDate.day} ${monthName(checkinDate.month)}');
      maxValue = max(maxValue, checkinValue);
    }

    // Goal Data (same for drugs or alcohol)
    final goalValue =
        (user?.assessment?.drinkingGoal?.units ?? 0) * (7 - (user?.assessment?.drinkingGoal?.freeDays ?? 0));
    // Add the goal value with the same X-coordinate as the last check-in
    final lastCheckinIndex = dataPoints.length - 1;

    final lastCheckinX =
        dataPoints.length == 1 ? dataPoints[lastCheckinIndex].x + 2 - 0.1 : dataPoints[lastCheckinIndex].x;
    // final lastCheckinX = dataPoints[lastCheckinIndex].x; // Use the same X as the last check-in
    dataPoints.add(FlSpot(lastCheckinX, goalValue.toDouble()));
    xLabels.add('Goal');
    maxValue = max(maxValue, goalValue);
  }

  void getMyDiaGramColor() {
    final checkins = Injector.instance<AppDB>().userModel?.user.checkins;
    final entries1 = <MapEntry<DateTime, Checkin>>[];

    if (checkins != null && checkins.isNotEmpty) {
// Sort the check-ins by date (assuming the Checkin has a 'date' field)
      final data1 = CheckinProcessor.processCheckins1<Checkin>(
        entries1,
        checkins,
      );

// Sort the data by the DateTime in descending order (most recent first)
      data1.sort((a, b) => b.key.compareTo(a.key)); // Sort by DateTime (b is more recent)

      if (data1.isNotEmpty) {
        final mostRecentCheckin = data1.first.value; // Get the Checkin from the most recent entry

        final checkin = mostRecentCheckin.toJson();
        final checkinData = Checkin.fromJson(checkin);

        'b ===>${checkinData.rate?.toJson().values}'.logD;

        // Assign values to scores
        dsScore = checkinData.rate?.ds ?? 0;
        ntScore = checkinData.rate?.nt ?? 0;
        eiScore = checkinData.rate?.ei ?? 0;
        lsScore = checkinData.rate?.ls ?? 0;
        psScore = checkinData.rate?.ps ?? 0;
        ubScore = checkinData.rate?.ub ?? 0;

        'dsScore $dsScore'.logD;
        'eiScore $eiScore'.logD;
        'lsScore $lsScore'.logD;
        'psScore $psScore'.logD;
        'ubScore $ubScore'.logD;
      }
    } else {
      final data = Injector.instance<AppDB>().userModel?.user.assessment;
      dsScore = data?.ds?.rate ?? 0;
      ntScore = data?.nt?.rate ?? 0;
      eiScore = data?.ei?.rate ?? 0;
      lsScore = data?.ls?.rate ?? 0;
      psScore = data?.ps?.rate ?? 0;
      ubScore = data?.ub?.rate ?? 0;
      'dsScore $dsScore'.logD;
      'eiScore $eiScore'.logD;
      'lsScore $lsScore'.logD;
      'psScore $psScore'.logD;
      'ubScore $ubScore'.logD;
    }

    if ([dsScore, ntScore, eiScore, lsScore, psScore, ubScore].any(
      (element) => element <= 2,
    )) {
      myDiagram = [dsScore, ntScore, eiScore, lsScore, psScore, ubScore]
          .where(
            (element) => element <= 2,
          )
          .length;
      myDiagramImage = Assets.icons.dashboard.garland.myAchievementsGarlandGreen;
      myDiagramAudio = DashboardLocaleKeys.achievementsDiagramGreenAudio.tr();
      myDiagramInfoText = DashboardLocaleKeys.achievementsDiagramGreenText.tr();
    } else {
      myDiagram = 0;
      myDiagramAudio = DashboardLocaleKeys.achievementsDiagramTransparentAudio.tr();
      myDiagramInfoText = DashboardLocaleKeys.achievementsDiagramTransparentText.tr();

      myDiagramImage = Assets.icons.dashboard.garland.myAchievementsGarlandTransparent;
    }
    'Scores ===> ${[dsScore, ntScore, eiScore, lsScore, psScore, ubScore].any(
      (element) => element <= 2,
    )}'
        .logD;
  }

  void getDashboardData() {

    // final goalValNew = getGoal(Injector.instance<AppDB>().userModel?.user.assessment?.toJson() ?? {}, Injector.instance<AppDB>().userModel?.user.checkins?.last.toJson() ?? {}, goalTrophyvalues);
    // final goalValOld = getGoal(Injector.instance<AppDB>().userModel?.user.assessment?.toJson() ?? {}, Injector.instance<AppDB>().userModelCopy?.user.checkins?.last.toJson() ?? {}, goalTrophyvalues);

    // '>?>?>?>? == goalValNew = ${goalValNew.value}'.logD;
    // '>?>?>?>? == goalValOld = ${goalValOld.value}'.logD;

    final userData = Injector.instance<AppDB>().userModel?.user;

    final entries1 = <MapEntry<DateTime, Checkin>>[];
    final entries = <MapEntry<DateTime, CheckinEi>>[];
    final lifeEntries = <MapEntry<DateTime, CheckinLife>>[];
    final rateEntries = <MapEntry<DateTime, CheckinRate>>[];
    final drugEntries = <MapEntry<DateTime, CheckinDrugs>>[];
    final userModel = Injector.instance<AppDB>().userModel;
    final assessment = userModel?.user.assessment?.toJson();
    final dsAs = userModel?.user.strategies?.dsAs?.length;
    final dsIs = userModel?.user.strategies?.dsIs?.length;
    final ntAs = userModel?.user.strategies?.ntAs?.length;
    final ntIs = userModel?.user.strategies?.ntIs?.length;
    final lsAs = userModel?.user.strategies?.lsAs?.length;
    final lsIs = userModel?.user.strategies?.lsIs?.length;
    final eiAs = userModel?.user.strategies?.eiAs?.length;
    final eiIs = userModel?.user.strategies?.eiIs?.length;
    final psAs = userModel?.user.strategies?.psAs?.length;
    final psIs = userModel?.user.strategies?.psIs?.length;
    final ubAs = userModel?.user.strategies?.ubAs?.length;
    final ubIs = userModel?.user.strategies?.ubIs?.length;
    final strategyLengths = [
      dsAs,
      dsIs,
      ntAs,
      ntIs,
      lsAs,
      lsIs,
      eiAs,
      eiIs,
      psAs,
      psIs,
      ubAs,
      ubIs,
    ];

    final uniqueStrategiesCount = strategyLengths.where((length) => (length ?? 0) > 0).length;

    hoursImage = getMedal(Injector.instance<AppDB>().userModel?.user.timeSpent ?? 0);
    strategiesImage = getMyStrategies(uniqueStrategiesCount);
    if (assessment == null) {
      return;
    }

    final data1 = CheckinProcessor.processCheckins1<Checkin>(
      entries1,
      userData?.checkins ?? [],
    );

    // Sort check-ins by date in descending order (most recent first)
    data1.sort((a, b) => b.key.compareTo(a.key));
    '????? data1 = $data1'.logV;
    // Only process the most recent check-in for goal calculation
    if (data1.isNotEmpty) {
      final mostRecentCheckin = data1.first.value.toJson();
      final goal = getGoal(assessment, mostRecentCheckin, goalTrophyvalues);
      '>?>?>?>? goal.value = ${goal.value}'.logV;
      tropyImage = goal.colour;
      goalPercentage = goal.value;
      goalInfoText = goal.text;
      goalInfoAudio = goal.audio;
      '>>>>> Most recent check-in goal calculation:'.logV;
      '>>>>> tropyImage = ${goal.colour}'.logV;
      '>>>>> goalPercentage = ${goal.value}'.logV;
      '>>>>> goalInfoText = ${goal.text}'.logV;
    }

    // Process Checkins for each type (Ei, Life, Rate, Drugs)
    CheckinProcessor.processCheckins<CheckinEi>(
      entries,
      userData?.checkins ?? [],
      (checkin) => checkin.ei ?? CheckinEi(),
    );
    CheckinProcessor.processCheckins<CheckinLife>(
      lifeEntries,
      userData?.checkins ?? [],
      (checkin) => checkin.life ?? CheckinLife(),
    );
    CheckinProcessor.processCheckins<CheckinRate>(
      rateEntries,
      userData?.checkins ?? [],
      (checkin) => checkin.rate ?? CheckinRate(),
    );
    CheckinProcessor.processCheckins<CheckinDrugs>(
      drugEntries,
      userData?.checkins ?? [],
      (checkin) => checkin.drugs ?? CheckinDrugs(drugDetails: {}),
    );

    // Print the most recent entry for each type
    final eiData = CheckinProcessor.printMostRecentEntry(entries, 'Ei') ?? {};
    final lifeStyleData = CheckinProcessor.printMostRecentEntry(lifeEntries, 'Life') ?? {};
    CheckinProcessor.printMostRecentEntry(rateEntries, 'Rate');
    CheckinProcessor.printMostRecentEntry(drugEntries, 'Drugs');

    final nervous = userData?.assessment?.ei?.nervous ?? 0;
    final worry = userData?.assessment?.ei?.worry ?? 0;
    final interest = userData?.assessment?.ei?.interest ?? 0;
    final down = userData?.assessment?.ei?.down ?? 0;

    final checkInnervous = CheckinEi.fromJson(eiData).nervous ?? 0;
    final checkInworry = CheckinEi.fromJson(eiData).worry ?? 0;
    final checkIninterest = CheckinEi.fromJson(eiData).interest ?? 0;
    final checkIndown = CheckinEi.fromJson(eiData).down ?? 0;
    final checkInLifeDifficulties = CheckinLife.fromJson(lifeStyleData).difficulties ?? 0;
    final checkInLifeRate = CheckinLife.fromJson(lifeStyleData).rate ?? 0;

    final emotionalRedTriangleData = ((nervous + worry + interest + down) * 10) / 12;
    final emotionalGreenTriangleData = ((checkInnervous + checkInworry + checkIninterest + checkIndown) * 10) / 12;

    emotionalRedTriangle = 10 - emotionalRedTriangleData.floorToDouble();
    emotionalGreenTriangle = 10 - emotionalGreenTriangleData.floorToDouble();
    resilienceRedTriangle = userData?.assessment?.life?.difficulties ?? 0;
    resilienceGreenTriangle = checkInLifeDifficulties;
    'emotionalGreenTriangle $emotionalGreenTriangleData'.logD;
    'emotionalGreenTriangle $checkInnervous'.logD;
    'emotionalGreenTriangle $eiData'.logD;
    qualityOfRedTriangle = userData?.assessment?.life?.rate ?? 0;
    qualityOfLifeGreenTriangle = checkInLifeRate;
  }
}

class GoalContent {
  GoalContent({required this.text, required this.audio});
  final String text;
  final String audio;
}

class DrugDataPoint {
  DrugDataPoint({required this.spot, this.drugName});
  final FlSpot spot;
  final String? drugName;
}
