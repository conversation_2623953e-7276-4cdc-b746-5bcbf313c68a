import 'dart:math';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_card_widget.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/cubit/dashboard_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/achievement_tile.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/achievement_overlay_animation.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/card_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/custom_graph_data.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/my_goal_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/my_hours_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/my_strategies_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/report_icon_button.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/single_chart_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/strength_pannel_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/two_tile_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/privacy_message_popup.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/pages/my_recovery_toolkit_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/features/progress_check_module/pages/progress_check_welcome_back_page.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({
    super.key,
    this.isFromProgress,
    this.goalPercentage,
    this.myDiagram,
    this.myHour,
    this.myStrategies,
  });
  final bool? isFromProgress;
  final int? goalPercentage;
  final int? myStrategies;
  final int? myHour;
  final int? myDiagram;

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final GlobalKey _buttonKey = GlobalKey();

  Map<String, dynamic> drugsList = {};

  @override
  void initState() {
    super.initState();

    final user = Injector.instance<AppDB>().userModel?.user;

    'drinking ${user?.assessment?.drinking}'.logD;
    'drugs ${user?.assessment?.drugs?.list}'.logD;
    // Set flags based on available substances

    drugsList = Injector.instance<AppDB>().userModel?.user.assessment?.drugs?.list ?? {};
  }

  @override
  Widget build(BuildContext context) {
    '>?>?>? langiuge = ${Injector.instance<AppDB>().userModel?.user.lang.toString()}'.logV;
    '========/ checkins = ${Injector.instance<AppDB>().userModel?.user.checkins}'.logV;

    '======== addiction case = ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}'.logV;
    '======== special addiction case = ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.specialAddiction}'.logV;
    final assessment = Injector.instance<AppDB>().userModel?.user.assessment;
    final hasDrinking = assessment?.drinking != null;
    final hasDrugs = assessment?.drugs != null;

    'assessment?.drinking ${assessment?.drinking?.toJson()}'.logV;
    'assessment?.drinking ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}'.logV;
    'assessment $hasDrinking'.logV;
    'assessment $hasDrugs'.logV;

// Determine the correct audio or text key
    final key = (Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 2)
        ? DashboardLocaleKeys.recoveryProgressInfoPanelDrinkingDrugsText
        : Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 0
            ? DashboardLocaleKeys.recoveryProgressInfoPanelDrinkingText
            : DashboardLocaleKeys.recoveryProgressInfoPanelDrugsText;

    final result = (DynamicAssetLoader.getNestedValue(key, context) as List).join('\n\n');

    'result ++++ $result'.logV;

    return BlocProvider(
      create: (context) => DashboardPageCubit(),
      child: BlocBuilder<DashboardPageCubit, DashboardPageState>(
        builder: (context, state) {
          final ref = context.read<DashboardPageCubit>();
          'dataPoints. time ${ref.dataPoints}'.logD;

          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return Stack(
                children: [
                  AppScaffold(
                    scaffoldKey: _scaffoldKey,
                    isAudioPanelVisible: ref.isAudioPannelVisible,
                    infoAudioUrl: ref.infoAudioUrl,
                    appBar: CommonAppBar(
                      onPrefixTap: () {
                        _scaffoldKey.currentState?.openDrawer();
                      },
                      onSuffixTap: () {
                        if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                          ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                        }
                      },
                    ),
                    drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                    body: GestureDetector(
                      onTap: () {
                        ref.tooltipBehavior.hide();
                      },
                      child: ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Padding(
                              padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                              child: SingleChildScrollView(
                                controller: ref.controller,
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: AppSize.h24),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            AppTextWidget(
                                              CoreLocaleKeys.sideMenuDashboard.tr(),
                                              textAlign: TextAlign.center,
                                              style:
                                                  context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                                            ),
                                            SpaceV(AppSize.h8),
                                            AppTextWidget(
                                              '${((Injector.instance<AppDB>().userModel?.user.loginTokens?.length == 1) || (Injector.instance<AppDB>().userModel?.user.loginTokens?.isEmpty ?? false)) ? DashboardLocaleKeys.titleFirst.tr() : DashboardLocaleKeys.title.tr()}${Injector.instance<AppDB>().userModel?.user.nameOnCertificate == '' ? '' : ','} ${Injector.instance<AppDB>().userModel?.user.nameOnCertificate}',
                                              textAlign: TextAlign.center,
                                              style: context.textTheme.titleSmall,
                                            ),
                                            SpaceV(AppSize.h14),
                                            Divider(
                                              height: 1,
                                              color: context.themeColors.greyColor.withOpacity(.6),
                                            ),
                                            SpaceV(AppSize.h16),
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                CardHeadingWidget(
                                                  name: DashboardLocaleKeys.recoveryProgressTitle.tr(),
                                                  textAlign: TextAlign.center,
                                                ),
                                                SpaceV(AppSize.h20),
                                                if (Injector.instance<AppDB>()
                                                            .userModel
                                                            ?.user
                                                            .assessment
                                                            ?.rp
                                                            ?.addictionCase ==
                                                        1 &&
                                                    ((Injector.instance<AppDB>()
                                                                    .userModel
                                                                    ?.user
                                                                    .assessment
                                                                    ?.drugs
                                                                    ?.list
                                                                    ?.length ??
                                                                0) >
                                                            1 &&
                                                        (Injector.instance<AppDB>().userModel?.user.assessment?.drugs !=
                                                                null ||
                                                            (Injector.instance<AppDB>()
                                                                    .userModel
                                                                    ?.user
                                                                    .checkins
                                                                    ?.any((e) => e.drugs != null) ??
                                                                false))))
                                                  ValueListenableBuilder(
                                                    valueListenable: ref.graphIndex,
                                                    builder: (context, selectedIndex, child) {
                                                      return LayoutBuilder(
                                                        builder: (context, constraints) {
                                                          final tabCount =
                                                              (drugsList.isNotEmpty ? drugsList.length : 0);
                                                          final tabWidth =
                                                              constraints.maxWidth / tabCount; // Dynamic width per tab
                                                          return Row(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              // Add Alcohol tab if applicable
                                                              // Add tabs for each drug in the drugs list
                                                              if (drugsList.isNotEmpty)
                                                                ...List.generate(drugsList.length, (index) {
                                                                  final drugName = drugsList.keys
                                                                      .elementAt(index); // Get the drug name by index
                                                                  final tabIndex =
                                                                      index; // Adjust index if Alcohol tab exists

                                                                  return InkWell(
                                                                    onTap: () {
                                                                      '---> graph index: ${ref.graphIndex.value}'.logD;
                                                                      ref.graphIndex.value = tabIndex;

                                                                      '---> tabIndex: $tabIndex'.logD;
                                                                    },
                                                                    child: Container(
                                                                      decoration: BoxDecoration(
                                                                        borderRadius: tabIndex == 0
                                                                            ? BorderRadius.only(
                                                                                topLeft: Radius.circular(AppSize.r16),
                                                                                bottomLeft:
                                                                                    Radius.circular(AppSize.r16),
                                                                              )
                                                                            : tabIndex == drugsList.length - 1
                                                                                ? BorderRadius.only(
                                                                                    topRight:
                                                                                        Radius.circular(AppSize.r16),
                                                                                    bottomRight:
                                                                                        Radius.circular(AppSize.r16),
                                                                                  )
                                                                                : BorderRadius.circular(0),
                                                                        color: selectedIndex == tabIndex
                                                                            ? const Color.fromRGBO(
                                                                                0,
                                                                                119,
                                                                                14,
                                                                                1,
                                                                              )
                                                                            : const Color.fromRGBO(
                                                                                147,
                                                                                195,
                                                                                153,
                                                                                1,
                                                                              ), // Change color based on selection
                                                                      ),
                                                                      height: AppSize.h40,
                                                                      width: tabWidth,
                                                                      alignment: Alignment.center,
                                                                      child: Padding(
                                                                        padding: EdgeInsets.all(AppSize.h2),
                                                                        child: Text(
                                                                          drugName.toUpperCase(),
                                                                          style: context.textTheme.titleSmall?.copyWith(
                                                                            color: selectedIndex == tabIndex
                                                                                ? Colors.white
                                                                                : Colors.black,
                                                                            fontSize: AppSize.sp12,
                                                                          ),
                                                                          maxLines: 1,
                                                                          overflow: TextOverflow.ellipsis,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  );
                                                                }),
                                                            ],
                                                          );
                                                        },
                                                      );
                                                    },
                                                  ),
                                                if (Injector.instance<AppDB>()
                                                        .userModel
                                                        ?.user
                                                        .assessment
                                                        ?.rp
                                                        ?.addictionCase ==
                                                    2)
                                                  if (((Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .checkins
                                                                  ?.map((e) => e.drinking)
                                                                  .where((v) => v != null)
                                                                  .toList()
                                                                  .isNotEmpty ??
                                                              false) &&
                                                          (Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .checkins
                                                                  ?.map((e) => e.drugs)
                                                                  .where((v) => v != null)
                                                                  .toList()
                                                                  .isNotEmpty ??
                                                              false)) ||
                                                      (Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .assessment
                                                                  ?.drinking !=
                                                              null &&
                                                          Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .assessment
                                                                  ?.drugs !=
                                                              null)) ...[
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.graphIndex,
                                                      builder: (context, selectedIndex, child) {
                                                        return LayoutBuilder(
                                                          builder: (context, constraints) {
                                                            '----- addictionCase = ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}'
                                                                .logV;
                                                            final alcoholTabExists = Injector.instance<AppDB>()
                                                                    .userModel
                                                                    ?.user
                                                                    .assessment
                                                                    ?.drinking !=
                                                                null;
                                                            final tabCount = (alcoholTabExists ? 1 : 0) +
                                                                (drugsList.isNotEmpty ? drugsList.length : 0);
                                                            final tabWidth = constraints.maxWidth /
                                                                tabCount; // Dynamic width per tab

                                                            return Container(
                                                              decoration: BoxDecoration(
                                                                borderRadius: BorderRadius.circular(50),
                                                              ),
                                                              child: Row(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  // Add Alcohol tab if applicable
                                                                  if (alcoholTabExists)
                                                                    InkWell(
                                                                      onTap: () {
                                                                        ref.graphIndex.value =
                                                                            0; // Set selected index to 0 for Alcohol
                                                                      },
                                                                      child: Container(
                                                                        decoration: BoxDecoration(
                                                                          color: selectedIndex == 0
                                                                              ? const Color.fromRGBO(
                                                                                  0,
                                                                                  119,
                                                                                  14,
                                                                                  1,
                                                                                )
                                                                              : const Color.fromRGBO(
                                                                                  147,
                                                                                  195,
                                                                                  153,
                                                                                  1,
                                                                                ),
                                                                          borderRadius: BorderRadius.only(
                                                                            topLeft: Radius.circular(AppSize.r16),
                                                                            bottomLeft: Radius.circular(AppSize.r16),
                                                                          ),
                                                                        ),
                                                                        height: AppSize.h40,
                                                                        width: tabWidth,
                                                                        // Chan// Change color based on selection
                                                                        alignment: Alignment.center,
                                                                        child: Padding(
                                                                          padding: EdgeInsets.all(AppSize.h2),
                                                                          child: Text(
                                                                            DrugsLocaleKeys.alcohol.tr().toUpperCase(),
                                                                            style:
                                                                                context.textTheme.titleSmall?.copyWith(
                                                                              color: selectedIndex == 0
                                                                                  ? Colors.white
                                                                                  : Colors.black,
                                                                              fontSize: AppSize.sp12,
                                                                            ),
                                                                            maxLines: 1,
                                                                            overflow: TextOverflow.ellipsis,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  // Add tabs for each drug in the drugs list
                                                                  if (drugsList.isNotEmpty)
                                                                    ...List.generate(drugsList.length, (index) {
                                                                      final drugName = drugsList.keys.elementAt(
                                                                        index,
                                                                      ); // Get the drug name by index
                                                                      final tabIndex = alcoholTabExists
                                                                          ? index + 1
                                                                          : index; // Adjust index if Alcohol tab exists

                                                                      return InkWell(
                                                                        onTap: () {
                                                                          ref.graphIndex.value =
                                                                              tabIndex; // Set selected index for the drug
                                                                        },
                                                                        child: Container(
                                                                          decoration: BoxDecoration(
                                                                            borderRadius: tabIndex == drugsList.length
                                                                                ? BorderRadius.only(
                                                                                    topRight:
                                                                                        Radius.circular(AppSize.r16),
                                                                                    bottomRight:
                                                                                        Radius.circular(AppSize.r16),
                                                                                  )
                                                                                : BorderRadius.circular(0),
                                                                            color: selectedIndex == tabIndex
                                                                                ? const Color.fromRGBO(
                                                                                    0,
                                                                                    119,
                                                                                    14,
                                                                                    1,
                                                                                  )
                                                                                : const Color.fromRGBO(
                                                                                    147,
                                                                                    195,
                                                                                    153,
                                                                                    1,
                                                                                  ), // Change color based on selection
                                                                          ),
                                                                          height: AppSize.h40,
                                                                          width: tabWidth,
                                                                          alignment: Alignment.center,
                                                                          child: Padding(
                                                                            padding: EdgeInsets.all(AppSize.h2),
                                                                            child: Text(
                                                                              ref.getLocalizedDrugNameFromKey(drugName).toUpperCase(),
                                                                              style: context.textTheme.titleSmall
                                                                                  ?.copyWith(
                                                                                color: selectedIndex == tabIndex
                                                                                    ? Colors.white
                                                                                    : Colors.black,
                                                                                fontSize: AppSize.sp12,
                                                                              ),
                                                                              maxLines: 1,
                                                                              overflow: TextOverflow.ellipsis,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      );
                                                                    }),
                                                                ],
                                                              ),
                                                            );
                                                          },
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h10),
                                                  ] else
                                                    const SizedBox(),
                                                SpaceV(AppSize.h10),
                                                ValueListenableBuilder(
                                                  valueListenable: ref.dynamicUnitText,
                                                  builder: (context, dynamicUnitText, child) {
                                                    final localizedSubstance = ref.getLocalizedSubstance(dynamicUnitText); // e.g. "amitriptyline"
final preposition = ref.getPreposition(dynamicUnitText); // e.g. "d’"

final subtitle = DashboardLocaleKeys.recoveryProgressSubtitle.tr(namedArgs: {
  '{preOf}': preposition,
  '{substance}': ref.formatString(ref.getLocalizedDrugNameFromKey(localizedSubstance)),
});
'===>$subtitle'.logV;
                                                    return Align(
                                                      alignment: Alignment.centerLeft,
                                                      child: HeadingWidget(
                                                        heading: subtitle,
                                                        // AppCommonFunctions.getFormattedTranslation(
                                                        //   DashboardLocaleKeys.recoveryProgressSubtitle,
                                                        //   {
                                                        //     'substance':
                                                        //         dynamicUnitText == 'unit' || dynamicUnitText == 'Unit'
                                                        //             ? 'alcohol'
                                                        //             : ref.formatString(ref.getLocalizedDrugNameFromKey(dynamicUnitText.toLowerCase())),
                                                        //   },
                                                        // ),
                                                        // AppCommonFunctions.getFormattedTranslation(
                                                        //   DashboardLocaleKeys.recoveryProgressSubtitle,
                                                        //   {
                                                        //     'substance':
                                                        //         dynamicUnitText == 'unit' || dynamicUnitText == 'Unit'
                                                        //             ? 'alcohol'
                                                        //             : ref.formatString(dynamicUnitText),
                                                        //   },
                                                        // ),
                                                        textAlign: TextAlign.left,
                                                        onInfoTap: () {
                                                          ref.showRecoveryInfo.value = !ref.showRecoveryInfo.value;

                                                          if (ref.showRecoveryInfo.value) {
                                                            ref.infoAudioUrl.value = (Injector.instance<AppDB>()
                                                                        .userModel
                                                                        ?.user
                                                                        .assessment
                                                                        ?.rp
                                                                        ?.addictionCase ==
                                                                    2)
                                                                ? DashboardLocaleKeys
                                                                    .recoveryProgressInfoPanelDrinkingDrugsAudio
                                                                    .tr()
                                                                : Injector.instance<AppDB>()
                                                                            .userModel
                                                                            ?.user
                                                                            .assessment
                                                                            ?.rp
                                                                            ?.addictionCase ==
                                                                        0
                                                                    ? DashboardLocaleKeys
                                                                        .recoveryProgressInfoPanelDrinkingAudio
                                                                        .tr()
                                                                    : DashboardLocaleKeys
                                                                        .recoveryProgressInfoPanelDrugsAudio
                                                                        .tr();
                                                          } else if (ref.infoAudioUrl.value ==
                                                              (() {
                                                                final addictionCase = Injector.instance<AppDB>()
                                                                    .userModel
                                                                    ?.user
                                                                    .assessment
                                                                    ?.rp
                                                                    ?.addictionCase;

                                                                return (addictionCase == 2)
                                                                    ? DashboardLocaleKeys
                                                                        .recoveryProgressInfoPanelDrinkingDrugsAudio
                                                                        .tr()
                                                                    : (addictionCase == 0)
                                                                        ? DashboardLocaleKeys
                                                                            .recoveryProgressInfoPanelDrinkingAudio
                                                                            .tr()
                                                                        : DashboardLocaleKeys
                                                                            .recoveryProgressInfoPanelDrugsAudio
                                                                            .tr();
                                                              })()) {
                                                            ref.infoAudioUrl.value = null;
                                                          }
                                                        },
                                                      ),
                                                    );
                                                  },
                                                ),
                                                SpaceV(AppSize.h8),
                                                ValueListenableBuilder(
                                                  valueListenable: ref.showRecoveryInfo,
                                                  builder: (context, showRecoveryInfoV, _) {
                                                    return CustomInfoWidget(
                                                      onCloseTap: () {
                                                        ref.showRecoveryInfo.value = false;
                                                        if (ref.infoAudioUrl.value ==
                                                            DashboardLocaleKeys.recoveryProgressInfoPanelDrugsAudio
                                                                .tr()) {
                                                          ref.infoAudioUrl.value = null;
                                                        } else {
                                                          ref.infoAudioUrl.value = null;
                                                        }
                                                      },
                                                      visible: showRecoveryInfoV,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      padding: EdgeInsets.only(
                                                        left: AppSize.w8,
                                                        right: AppSize.w8,
                                                      ),
                                                      bodyText: (Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .assessment
                                                                  ?.rp
                                                                  ?.addictionCase ==
                                                              2)
                                                          ? (DynamicAssetLoader.getNestedValue(
                                                              DashboardLocaleKeys
                                                                  .recoveryProgressInfoPanelDrinkingDrugsText,
                                                              context,
                                                            ) as List)
                                                              .join('\n\n')
                                                          : Injector.instance<AppDB>()
                                                                      .userModel
                                                                      ?.user
                                                                      .assessment
                                                                      ?.rp
                                                                      ?.addictionCase ==
                                                                  0
                                                              ? (DynamicAssetLoader.getNestedValue(
                                                                  DashboardLocaleKeys
                                                                      .recoveryProgressInfoPanelDrinkingText,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n')
                                                              : (DynamicAssetLoader.getNestedValue(
                                                                  DashboardLocaleKeys
                                                                      .recoveryProgressInfoPanelDrugsText,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n'),
                                                    );
                                                  },
                                                ),
                                                SpaceV(AppSize.h8),
                                                SizedBox(
                                                  height: AppSize.h220,
                                                  child: (Injector.instance<AppDB>()
                                                              .userModel
                                                              ?.user
                                                              .assessment
                                                              ?.rp
                                                              ?.addictionCase ==
                                                          2)
                                                      ? (((Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.map((e) => e.drinking)
                                                                          .where(
                                                                            (value) => value != null,
                                                                          ) // Filter out null values
                                                                          .toList()
                                                                          .isNotEmpty ??
                                                                      false) &&
                                                                  (Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.map((e) => e.drugs)
                                                                          .where(
                                                                            (value) => value != null,
                                                                          ) // Filter out null values
                                                                          .toList()
                                                                          .isNotEmpty ??
                                                                      false)) ||
                                                              (Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .assessment
                                                                          ?.drinking !=
                                                                      null &&
                                                                  Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .assessment
                                                                          ?.drugs !=
                                                                      null))
                                                          ? ValueListenableBuilder<int>(
                                                              valueListenable: ref.graphIndex,
                                                              builder: (context, selectedIndex, child) {
                                                                return (selectedIndex == 0)
                                                                    ? _buildAlcoholGraph(0, ref)
                                                                    : _buildDrugGraph(selectedIndex, drugsList, ref);
                                                              },
                                                            )
                                                          : ((Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.any((e) => e.drinking != null) ??
                                                                      false) ||
                                                                  (Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.any((e) => e.drugs != null) ??
                                                                      false))
                                                              ? SingleChartWidget(
                                                                  ref: ref,
                                                                  timestamps: formatCheckInDates(
                                                                    // First, add the assessment time if it exists, then add the check-in times
                                                                    [
                                                                      if (Injector.instance<AppDB>()
                                                                              .userModel
                                                                              ?.user
                                                                              .assessment
                                                                              ?.time !=
                                                                          null)
                                                                        Injector.instance<AppDB>()
                                                                                .userModel
                                                                                ?.user
                                                                                .assessment
                                                                                ?.time ??
                                                                            0,
                                                                      ...?Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.map((e) => e.time)
                                                                          .whereType<int>(),
                                                                    ],
                                                                  ),
                                                                )
                                                              : Container()
                                                      : (Injector.instance<AppDB>()
                                                                      .userModel
                                                                      ?.user
                                                                      .assessment
                                                                      ?.rp
                                                                      ?.addictionCase ==
                                                                  1 &&
                                                              ((Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .checkins
                                                                          ?.map((e) => e.drugs)
                                                                          .where((v) => v != null)
                                                                          .isNotEmpty ??
                                                                      false) ||
                                                                  Injector.instance<AppDB>().userModel?.user.assessment?.drugs != null))
                                                          ? ValueListenableBuilder<int>(
                                                              valueListenable: ref.graphIndex,
                                                              builder: (context, selectedIndex, child) {
                                                                return _buildDrugGraph(
                                                                  selectedIndex + 1,
                                                                  drugsList,
                                                                  ref,
                                                                );
                                                              },
                                                            )
                                                          : SingleChartWidget(
                                                              ref: ref,
                                                              timestamps: formatCheckInDates(
                                                                // First, add the assessment time if it exists, then add the check-in times
                                                                [
                                                                  if (Injector.instance<AppDB>()
                                                                          .userModel
                                                                          ?.user
                                                                          .assessment
                                                                          ?.time !=
                                                                      null)
                                                                    Injector.instance<AppDB>()
                                                                            .userModel
                                                                            ?.user
                                                                            .assessment
                                                                            ?.time ??
                                                                        0,
                                                                  ...?Injector.instance<AppDB>()
                                                                      .userModel
                                                                      ?.user
                                                                      .checkins
                                                                      ?.map((e) => e.time)
                                                                      .whereType<int>(),
                                                                ],
                                                              ),
                                                            ),
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h14),
                                            Divider(
                                              height: 1,
                                              color: context.themeColors.greyColor.withOpacity(.6),
                                            ),
                                            SpaceV(AppSize.h16),
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                CardHeadingWidget(
                                                  name: DashboardLocaleKeys.strengthsTitle.tr(),
                                                  textAlign: TextAlign.center,
                                                ),
                                                SpaceV(AppSize.h20),
                                                StrengthPannelWidget(
                                                  heading: DashboardLocaleKeys.strengthsWellbeingTitle.tr(),
                                                  iconPath: Assets.icons.dashboard.emotionalWellbeing,
                                                  greenVal:
                                                      (Injector.instance<AppDB>().userModel?.user.checkins != null &&
                                                              Injector.instance<AppDB>()
                                                                  .userModel!
                                                                  .user
                                                                  .checkins!
                                                                  .isNotEmpty)
                                                          ? ref.emotionalGreenTriangle
                                                          : null,
                                                  redVal: ref.emotionalRedTriangle,
                                                  isShowPopUp: ref.showStEmotionalInfo,
                                                  infoAudioUrl: ref.infoAudioUrl,
                                                  audioUrl: DashboardLocaleKeys.strengthsWellbeingInfoPanelAudio.tr(),
                                                  infoText: (DynamicAssetLoader.getNestedValue(
                                                    DashboardLocaleKeys.strengthsWellbeingInfoPanelText,
                                                    context,
                                                  ) as List)
                                                      .join('\n\n'),
                                                ),
                                                SpaceV(AppSize.h20),
                                                StrengthPannelWidget(
                                                  heading: DashboardLocaleKeys.strengthsResilienceTitle.tr(),
                                                  iconPath: Assets.icons.dashboard.myResilience,
                                                  greenVal:
                                                      (Injector.instance<AppDB>().userModel?.user.checkins != null &&
                                                              Injector.instance<AppDB>()
                                                                  .userModel!
                                                                  .user
                                                                  .checkins!
                                                                  .isNotEmpty)
                                                          ? ref.resilienceGreenTriangle.toDouble()
                                                          : null,
                                                  redVal: ref.resilienceRedTriangle.toDouble(),
                                                  isShowPopUp: ref.showStResilienceInfo,
                                                  infoAudioUrl: ref.infoAudioUrl,
                                                  audioUrl: DashboardLocaleKeys.strengthsResilienceInfoPanelAudio.tr(),
                                                  infoText: (DynamicAssetLoader.getNestedValue(
                                                    DashboardLocaleKeys.strengthsResilienceInfoPanelText,
                                                    context,
                                                  ) as List)
                                                      .join('\n\n'),
                                                ),
                                                SpaceV(AppSize.h20),
                                                StrengthPannelWidget(
                                                  heading: DashboardLocaleKeys.strengthsQualityTitle.tr(),
                                                  iconPath: Assets.icons.dashboard.myQuality,
                                                  greenVal:
                                                      (Injector.instance<AppDB>().userModel?.user.checkins != null &&
                                                              Injector.instance<AppDB>()
                                                                  .userModel!
                                                                  .user
                                                                  .checkins!
                                                                  .isNotEmpty)
                                                          ? ref.qualityOfLifeGreenTriangle.toDouble()
                                                          : null,
                                                  redVal: ref.qualityOfRedTriangle.toDouble(),
                                                  isShowPopUp: ref.showStqualityInfo,
                                                  infoAudioUrl: ref.infoAudioUrl,
                                                  audioUrl: DashboardLocaleKeys.strengthsQualityInfoPanelAudio.tr(),
                                                  infoText: (DynamicAssetLoader.getNestedValue(
                                                    DashboardLocaleKeys.strengthsQualityInfoPanelText,
                                                    context,
                                                  ) as List)
                                                      .join('\n\n'),
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h20),
                                            Divider(
                                              height: 1,
                                              color: context.themeColors.greyColor.withOpacity(.6),
                                            ),
                                            SpaceV(AppSize.h20),
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                CardHeadingWidget(
                                                  name: DashboardLocaleKeys.achievementsTitle.tr(),
                                                ),
                                                SpaceV(AppSize.h20),
                                                Column(
                                                  children: [
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.hundredPersentage,
                                                      builder: (context, value, child) {
                                                        '????? ref.goalPercentage.toString() = ${ref.goalPercentage.toString()}'.logV;
                                                        '????? ref.hundredPersentage.value = ${ref.hundredPersentage.value}'.logV;
                                                        '????? UI: ref.hundredPersentage = ${ref.hundredPersentage.value}, ref.goalPercentage = ${ref.goalPercentage}'.logV;

                                                        return ValueListenableBuilder(
                                                          valueListenable: ref.achivementInfoText,
                                                          builder: (context, achivementInfoTextV, _) {
                                                            return AchievementTile(
                                                              infoWidget: achivementInfoTextV == Achievements.myGoal
                                                                  ? MyGoalInfoWidget(goalSelectedText: ref.goalInfoText)
                                                                  : achivementInfoTextV == Achievements.myStrategies
                                                                      ? MyStrategiesInfoWidget(
                                                                          strategiesInfoText: ref.strategiesInfoText,
                                                                        )
                                                                      : null,
                                                              tile1: Achievement(
                                                                heading: DashboardLocaleKeys.achievementsGoalTitle.tr(),
                                                                largeText: ref.getStartAndGoal() ? '100' : ref.goalPercentage.toString(),
                                                                smallText: '%',
                                                                iconColor: Colors.transparent,
                                                                iconPath: ref.getStartAndGoal() ? Assets.icons.dashboard.trophy.myAchievementsTrophyPlatinum : ref.tropyImage,
                                                                onInfoTap: () {
                                                                  ref.achivementInfoText.value =
                                                                      ref.achivementInfoText.value == Achievements.myGoal
                                                                          ? null
                                                                          : Achievements.myGoal;
                                                        
                                                                  if (ref.achivementInfoText.value == Achievements.myGoal) {
                                                                    ref.infoAudioUrl.value = ref.goalInfoAudio;
                                                                  } else if (ref.infoAudioUrl.value == ref.goalInfoAudio) {
                                                                    ref.infoAudioUrl.value = null;
                                                                  }
                                                                },
                                                              ),
                                                              tile2: Achievement(
                                                                heading:
                                                                    DashboardLocaleKeys.achievementsStrategiesTitle.tr(),
                                                                largeText: '${ref.myStrategies}/',
                                                                smallText: '12',
                                                                iconPath: ref.strategiesImage ?? '',
                                                                iconColor: context.themeColors.redColor,
                                                                isIconFilled: true,
                                                                onInfoTap: () {
                                                                  ref.achivementInfoText.value =
                                                                      ref.achivementInfoText.value ==
                                                                              Achievements.myStrategies
                                                                          ? null
                                                                          : Achievements.myStrategies;
                                                                  'ref.strategiesInfoAudio ${ref.strategiesInfoAudio}'.logV;
                                                                  if (ref.achivementInfoText.value ==
                                                                      Achievements.myStrategies) {
                                                                    ref.infoAudioUrl.value = ref.strategiesInfoAudio?.tr();
                                                                  } else if (ref.infoAudioUrl.value ==
                                                                      ref.strategiesInfoAudio?.tr()) {
                                                                    ref.infoAudioUrl.value = null;
                                                                  }
                                                                },
                                                              ),
                                                              onCloseInfoTap: () {
                                                                if (ref.achivementInfoText.value == Achievements.myGoal ||
                                                                    ref.achivementInfoText.value ==
                                                                        Achievements.myStrategies) {
                                                                  ref.infoAudioUrl.value = null;
                                                                }
                                                                ref.achivementInfoText.value = null;
                                                              },
                                                            );
                                                          },
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.achivementInfoText,
                                                      builder: (context, achivementInfoTextV, _) {
                                                        return AchievementTile(
                                                          infoWidget: achivementInfoTextV == Achievements.myHours
                                                              ? MyHoursInfoWidget()
                                                              : achivementInfoTextV == Achievements.myDiagram
                                                                  ? InfoTextWidget(
                                                                      text: ref.myDiagramInfoText ??
                                                                          DashboardLocaleKeys
                                                                              .achievementsDiagramTransparentText
                                                                              .tr(),
                                                                    )
                                                                  : null,
                                                          tile1: Achievement(
                                                            heading: DashboardLocaleKeys.achievementsHoursTitle.tr(),
                                                            largeText: '${ref.myHours}/',
                                                            smallText: '12',
                                                            iconPath: ref.hoursImage ?? '',
                                                            iconColor: Colors.transparent,
                                                            isIconFilled: true,
                                                            onInfoTap: () {
                                                              ref.achivementInfoText.value =
                                                                  ref.achivementInfoText.value == Achievements.myHours
                                                                      ? null
                                                                      : Achievements.myHours;

                                                              if (ref.achivementInfoText.value ==
                                                                  Achievements.myHours) {
                                                                ref.infoAudioUrl.value = ref.hoursInfoAudio;
                                                              } else if (ref.infoAudioUrl.value == ref.hoursInfoAudio) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                          ),
                                                          tile2: Achievement(
                                                            heading: DashboardLocaleKeys.achievementsDiagramTitle.tr(),
                                                            largeText: '${ref.myDiagram}/',
                                                            smallText: '6',
                                                            iconPath: ref.myDiagramImage ?? '',
                                                            onInfoTap: () {
                                                              ref.achivementInfoText.value =
                                                                  ref.achivementInfoText.value == Achievements.myDiagram
                                                                      ? null
                                                                      : Achievements.myDiagram;

                                                              if (ref.achivementInfoText.value ==
                                                                  Achievements.myDiagram) {
                                                                ref.infoAudioUrl.value = ref.myDiagramAudio;
                                                              } else if (ref.infoAudioUrl.value == ref.myDiagramAudio) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                            isIconFilled: true,
                                                          ),
                                                          onCloseInfoTap: () {
                                                            if (ref.achivementInfoText.value == Achievements.myHours ||
                                                                ref.achivementInfoText.value ==
                                                                    Achievements.myDiagram) {
                                                              ref.infoAudioUrl.value = null;
                                                            }
                                                            ref.achivementInfoText.value = null;
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h20),
                                            Divider(
                                              height: 1,
                                              color: context.themeColors.greyColor.withOpacity(.6),
                                            ),
                                            SpaceV(AppSize.h20),
                                            AppCardWidget(
                                              padding: EdgeInsets.zero,
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  CardHeadingWidget(
                                                    name: DashboardLocaleKeys.progressReportTitle.tr(),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Column(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      TwoTileHeadingWidget(
                                                        heading1: DashboardLocaleKeys.progressReportLatestSubtitle.tr(),
                                                        heading2: DashboardLocaleKeys.progressReportUpdateSubtitle.tr(),
                                                        onInfo1Tap: () {
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            DashboardLocaleKeys.progressReportLatestInfoPanelText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');

                                                          ref.reportInfoText.value =
                                                              (ref.reportInfoText.value.isNotEmptyAndNotNull &&
                                                                      ref.reportInfoText.value == info)
                                                                  ? null
                                                                  : info;
                                                          if (ref.reportInfoText.value == info) {
                                                            ref.infoAudioUrl.value = DashboardLocaleKeys
                                                                .progressReportLatestInfoPanelAudio
                                                                .tr();
                                                          } else if (ref.infoAudioUrl.value ==
                                                              DashboardLocaleKeys.progressReportLatestInfoPanelAudio
                                                                  .tr()) {
                                                            ref.infoAudioUrl.value = null;
                                                          }
                                                        },
                                                        onInfo2Tap: () {
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            DashboardLocaleKeys.progressReportUpdateInfoPanelText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          ref.reportInfoText.value =
                                                              (ref.reportInfoText.value.isNotEmptyAndNotNull &&
                                                                      ref.reportInfoText.value == info)
                                                                  ? null
                                                                  : info;
                                                          if (ref.reportInfoText.value == info) {
                                                            ref.infoAudioUrl.value = DashboardLocaleKeys
                                                                .progressReportUpdateInfoPanelAudio
                                                                .tr();
                                                          } else if (ref.infoAudioUrl.value ==
                                                              DashboardLocaleKeys.progressReportUpdateInfoPanelAudio
                                                                  .tr()) {
                                                            ref.infoAudioUrl.value = null;
                                                          }
                                                        },
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      ValueListenableBuilder(
                                                        valueListenable: ref.reportInfoText,
                                                        builder: (context, reportInfoTextV, _) {
                                                          return CustomInfoWidget(
                                                            onCloseTap: () {
                                                              ref.reportInfoText.value = null;
                                                              if (ref.infoAudioUrl.value ==
                                                                      DashboardLocaleKeys
                                                                          .progressReportLatestInfoPanelAudio
                                                                          .tr() ||
                                                                  ref.infoAudioUrl.value ==
                                                                      DashboardLocaleKeys
                                                                          .progressReportUpdateInfoPanelAudio
                                                                          .tr()) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                            visible: reportInfoTextV.isNotEmptyAndNotNull,
                                                            margin: EdgeInsets.symmetric(
                                                              vertical: AppSize.h8,
                                                            ),
                                                            padding: EdgeInsets.only(
                                                              left: AppSize.w8,
                                                              right: AppSize.w8,
                                                            ),
                                                            bodyText: reportInfoTextV,
                                                          );
                                                        },
                                                      ),
                                                      SpaceV(AppSize.h4),
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: state.maybeWhen(
                                                                      downloadPdfLoading: () => true,
                                                                      orElse: () => false,
                                                                    ) ||
                                                                    state.maybeWhen(
                                                                      emailPdfLoading: () => true,
                                                                      orElse: () => false,
                                                                    )
                                                                ? SizedBox(
                                                                    height: AppSize.w30,
                                                                    width: AppSize.w30,
                                                                    child: const Center(
                                                                      child: CircularProgressIndicator(),
                                                                    ),
                                                                  )
                                                                : ReportIconButton(
                                                                    key: _buttonKey,
                                                                    onTap: () {
                                                                      ref.infoAudioUrl.value = null;

                                                                      final renderBox = _buttonKey.currentContext
                                                                          ?.findRenderObject() as RenderBox?;
                                                                      final offset =
                                                                          renderBox?.localToGlobal(Offset.zero);
                                                                      //  'offset $offset'.logD;
                                                                      CustomDownloadPopup.buildPopupMenu(
                                                                        context: context,
                                                                        offset: offset,
                                                                        onDownLoadPdf: () async {
                                                                          ref.isDownloadFailed.value = false;
                                                                          await ref.navigateToPopup(
                                                                            context: context,
                                                                            isEmail: false,
                                                                          );
                                                                        },
                                                                        onEmailDownload: () async {
                                                                          ref.isDownloadFailed.value = false;
                                                                          await ref.navigateToPopup(
                                                                            context: context,
                                                                            isEmail: true,
                                                                          );
                                                                        },
                                                                      );
                                                                    },
                                                                    iconPath: Assets.icons.dashboard.myReport,
                                                                  ),
                                                          ),
                                                          SpaceH(AppSize.w16),
                                                          Expanded(
                                                            child: ReportIconButton(
                                                              onTap: () {
                                                                ref.infoAudioUrl.value = null;
                                                                AppNavigation.nextScreen(
                                                                  context,
                                                                  const ProgressCheckWelcomeBackPage(
                                                                    isFromDashboardPage: true,
                                                                  ),
                                                                );
                                                                ref.isDownloadFailed.value = false;
                                                              },
                                                              iconPath: Assets.icons.dashboard.updateReport,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      ValueListenableBuilder(
                                                        valueListenable: ref.isDownloadFailed,
                                                        builder: (context, isDownloadFailed, _) {
                                                          if (isDownloadFailed) {
                                                            return CustomErrorWidget(
                                                              errorMessgaeText: ref.errorMsg,
                                                                  // DashboardLocaleKeys.errorsNoCheckins.tr(),
                                                            );
                                                          } else {
                                                            return const SizedBox.shrink();
                                                          }
                                                        },
                                                      ),
                                                      ValueListenableBuilder(
                                                        valueListenable: ref.showPrivacyPopup,
                                                        builder: (context, showPrivacyPopupV, _) {
                                                          return Column(
                                                            children: [
                                                              SpaceV(AppSize.h30),
                                                              PrivacyMessagePopup(
                                                                visible: showPrivacyPopupV,
                                                                onCloseTap: () {
                                                                  ref.showPrivacyPopup.value = false;
                                                                },
                                                              ),
                                                              SpaceV(AppSize.h20),
                                                            ],
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            //SpaceV(AppSize.h10),
                                          ],
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  '>?>?>? 16'.logV;
                                                  AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: AppSize.w16,
                                                    vertical: AppSize.h8,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: context.themeColors.blueColor,
                                                    borderRadius: BorderRadius.circular(AppSize.r32),
                                                  ),
                                                  child: Text(
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                    CoreLocaleKeys.sideMenuDiagram.tr(),
                                                    style: context.textTheme.titleMedium?.copyWith(
                                                      fontSize: AppSize.sp12,
                                                      color: context.themeColors.whiteColor,
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SpaceH(AppSize.w10),
                                            Expanded(
                                              child: GestureDetector(
                                                onTap: () {
                                                  AppNavigation.pushAndRemoveAllScreen(
                                                    context,
                                                    const MyRecoveryToolkitPage(),
                                                  );
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: AppSize.w16,
                                                    vertical: AppSize.h8,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: context.themeColors.blueColor,
                                                    borderRadius: BorderRadius.circular(AppSize.r32),
                                                  ),
                                                  child: Text(
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                    CoreLocaleKeys.sideMenuToolkit.tr(),
                                                    style: context.textTheme.titleMedium?.copyWith(
                                                      fontSize: AppSize.sp12,
                                                      color: context.themeColors.whiteColor,
                                                      fontWeight: FontWeight.w500,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  ValueListenableBuilder(
                    valueListenable: ref.showTrophyAnimation,
                    builder: (context, value, child) {
                      'hoursInfoText ${ref.hoursInfoText}'.logD;
                      if (!value) return Container();
                      return Positioned.fill(
                        child: AchievementOverlay(
                          assetIcon: ref.animationSvg,
                          onClose: () {
                            ref.showTrophyAnimation.value = false;
                          },
                          title: ref.animationTitle, 
                          mainTitle: ref.animationMainTitle,
                          ref: ref,
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class GoalResult {
  GoalResult({
    required this.value,
    required this.colour,
    required this.audio,
    required this.text,
  });
  final int value;
  final String colour;
  final String audio;
  final String text;
}

// Function to build the Alcohol graph
Widget _buildAlcoholGraph(int selectedIndex, DashboardPageCubit ref) {
  var maxValue = 10; // Minimum Y-axis range
  final dataPoints = <FlSpot>[];

  final xLabels = <String>[];
  final user = Injector.instance<AppDB>().userModel?.user;
  var goalValue = 0;
  // Assessment Data (Drug or Alcohol)
  final assessmentDate = DateTime.fromMillisecondsSinceEpoch(user?.assessment?.time ?? 0);
  var assessmentValue = 0;
  'user?.assessment?.drinking?.units ${user?.assessment?.drinking?.units}'.logD;
  'user?.assessment?.drinking?.days ${user?.assessment?.drinking?.days}'.logD;
  final alcoholUnits = user?.assessment?.drinking?.units ?? 0;
  final days = user?.assessment?.drinking?.days ?? 0;
  assessmentValue = alcoholUnits * days;
  dataPoints.add(FlSpot(0, assessmentValue.toDouble()));
  xLabels.add('${assessmentDate.day} ${ref.monthName(assessmentDate.month)}');
  WidgetsBinding.instance.addPostFrameCallback(
    (timeStamp) async {
      ref.dynamicUnitText.value = 'alcohol';
    },
  );
  // Ensure the value is converted to an integer for comparison
  maxValue = max(maxValue, assessmentValue);
  for (var i = 0; i < (user?.checkins?.length ?? 0); i++) {
    final checkinDate = DateTime.fromMillisecondsSinceEpoch(user?.checkins?[i].time ?? 0);
    '========/ checkinData for alc = ${checkinDate}'.logV; 
    var checkinValue = 0;

    // For alcohol, calculate the units for each check-in
    final alcoholUnits = user?.checkins?[i].drinking?.units ?? 0;
    final days = user?.checkins?[i].drinking?.days ?? 0;
    checkinValue = alcoholUnits * days;

    dataPoints.add(FlSpot((i + 1).toDouble(), checkinValue.toDouble()));
    xLabels.add('${checkinDate.day} ${ref.monthName(checkinDate.month)}');
    maxValue = max(maxValue, checkinValue);
  }

  goalValue = (user?.assessment?.drinkingGoal?.units ?? 0) * (7 - (user?.assessment?.drinkingGoal?.freeDays ?? 0));
  // Add the goal value with the same X-coordinate as the last check-in

  final lastCheckinIndex = dataPoints.length - 1;

  final lastCheckinX =
      dataPoints.length == 1 ? dataPoints[lastCheckinIndex].x + 2 - 0.1 : dataPoints[lastCheckinIndex].x;

  // //final lastCheckinX = dataPoints[lastCheckinIndex].x; // Use the same X as the last check-in
  dataPoints.add(FlSpot(lastCheckinX, goalValue.toDouble()));
  xLabels.add('Goal');
  maxValue = max(maxValue, goalValue);

  double getMaxYValue() {
    // Assuming ref.dataPoints is a List<FlSpot> and contains the data for the chart
    var maxYValue = double.negativeInfinity; // Start with negative infinity to ensure the first value is greater

    for (final spot in dataPoints) {
      if (spot.y > maxYValue) {
        maxYValue = spot.y;
      }
    }

    // If maxYValue is less than 10, set it to 10
    if (maxYValue < 10) {
      maxYValue = 10;
    } else {
      // Round up to the next multiple of 10
      maxYValue = (maxYValue / 10).ceil() * 10;
    }

    'maxYValue $maxYValue'.logD;
    return maxYValue;
  }

  '======== dataPoint from first dashboard = ${dataPoints}'.logV;
  // Build the Drug graph
  return CustomGraph(
    ref: ref,
    isForAlcohol: true,
    graphIndex: ref.graphIndex.value,
    dataPoints: dataPoints,
    xLabels: xLabels,
    dynamicUnitText: DashboardLocaleKeys.recoveryProgressLabelsAlcoholUnit.tr(),
    goalValue: goalValue.toString(),
    // maxX: (Injector.instance<AppDB>().userModel?.user.checkins?.length ?? 0) + 0.1,
    maxY: getMaxYValue(),
    timestamps: formatCheckInDates(
      // First, add the assessment time if it exists, then add the check-in times
      [
        if (Injector.instance<AppDB>().userModel?.user.assessment?.time != null)
          Injector.instance<AppDB>().userModel?.user.assessment?.time ?? 0,
        ...?Injector.instance<AppDB>().userModel?.user.checkins?.map((e) => e.time).whereType<int>(),
      ],
    ),
  );
}

List<String> formatCheckInDates(List<int> timestamps) {
  final dateFormat = DateFormat('dd MMM');

  // Sort the timestamps to ensure they are in ascending order
  timestamps.sort();

  // Format the dates
  final formattedDates = timestamps.map((timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return dateFormat.format(dateTime);
  }).toList();

  // Ensure at least three labels on the x-axis when only one date is present
  if (formattedDates.length == 1) {
    final firstDate = DateTime.fromMillisecondsSinceEpoch(timestamps.first);

    // Add two more dates within the next 7 days, spaced by 2 days
    for (var i = 1; i <= 2; i++) {
      final nextDate = firstDate.add(Duration(days: i * 2)); // Add 2 days incrementally
      if (nextDate.isBefore(firstDate.add(const Duration(days: 7)))) {
        formattedDates.add(dateFormat.format(nextDate));
      }
    }
  }

  // If there are more than 3 dates, distribute the labels evenly
  // if (formattedDates.length > 3) {
  //   final firstDate = DateTime.fromMillisecondsSinceEpoch(timestamps.first);
  //   final lastDate = DateTime.fromMillisecondsSinceEpoch(timestamps.last);

  //   // Calculate the middle date
  //   final middleDate = DateTime(
  //     firstDate.year,
  //     firstDate.month + (lastDate.month - firstDate.month) ~/ 2,
  //     (firstDate.day + lastDate.day) ~/ 2,
  //   );

  //   // Add the first, middle, and last date
  //   formattedDates
  //     ..clear()
  //     ..add(dateFormat.format(firstDate))
  //     ..add(dateFormat.format(middleDate))
  //     ..add(dateFormat.format(lastDate));
  // }

  return formattedDates;
}

Widget _buildDrugGraph(int selectedIndex, Map<String, dynamic> drugsList, DashboardPageCubit ref) {
  var maxValue = 10; // Minimum Y-axis range
  final dataPoints = <FlSpot>[];
  //var dynamicUnitText = '';
  var unit = '';

  final xLabels = <String>[];
  final user = Injector.instance<AppDB>().userModel?.user;

  final drugName = drugsList.keys.elementAt(selectedIndex - 1); // Adjusted to use the correct index
  final drugData = user?.assessment?.drugs?.list?[drugName];

  '//////// drug name = ${drugsList.keys.elementAt(selectedIndex - 1)}'.logV;
  '//////// drugData = ${drugData}'.logV;

  WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    ref.dynamicUnitText.value = drugName;
  });

  if (drugData != null) {
    // Assign drugName from the unit property of ListButane
    unit = DrugAndUnitList.units[drugData.unit] ?? ''; // 'microgram' from ListButane
    'ListButane details: $drugData'.logD;
    'ListButane unit: $unit'.logD; // Log the unit

    // Set the drugName as unit (microgram)
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // ref.dynamicUnitText.value = unit ?? ''; // Set the drug name to the unit (e.g., microgram)
      //type = unit ?? '';
    });
  } else {
    'No drug data found for $drugName'.logD;
  }
// Assessment Data (Drug or Alcohol)
  final assessmentDate = DateTime.fromMillisecondsSinceEpoch(user?.assessment?.time ?? 0);
  var assessmentValue = 0;
  final drugUnits = drugData?.amount ?? 0;
  final drugFrequency = drugData?.frequency ?? 0;

  // assessmentValue = ((drugUnits) * drugFrequency);
  assessmentValue = (drugUnits is int ? drugUnits : (drugUnits as double ?? 0.0).toInt()) *
      (drugFrequency is int ? drugFrequency : (drugFrequency as double ?? 0.0).toInt());

  'assessmentValue $assessmentValue'.logD;
  'assessmentValue ${drugData?.toJson()}'.logD;
  dataPoints.add(FlSpot(0, assessmentValue.toDouble()));
  xLabels.add('${assessmentDate.day} ${ref.monthName(assessmentDate.month)}');

// Ensure the value is converted to an integer for comparison
  maxValue = max(maxValue, assessmentValue);

// Loop through the check-ins, but filter by the selected drug
  for (var i = 0; i < (user?.checkins?.length ?? 0); i++) {
    final checkinDate = DateTime.fromMillisecondsSinceEpoch(user?.checkins?[i].time ?? 0);
    '//////// checkinDate for drug = ${checkinDate}'.logV;
    var checkinValue = 0;
    '========/ checkin Date drug = ${checkinDate}'.logV;
    // Only process the selected drug in the check-ins
    final checkinDrugData = user?.checkins?[i].drugs?.drugDetails[drugName]; 
    //final checkinDrugData = Injector.instance<AppDB>().userModel?.user.checkins?.last.drugs;
    '========/ checkinDrugData = ${user?.checkins?[i].drugs?.drugDetails[drugName]?.amount}'.logV;
    if (checkinDrugData != null) {
      'ref.dynamicUnitText.value ${ref.dynamicUnitText.value}'.logD;
      final checkinDrugUnits =
          (checkinDrugData.amount is int) ? checkinDrugData.amount as int : (checkinDrugData.amount as num).toInt();
      final checkinDrugFrequency = checkinDrugData.frequency ?? 0;
      '======== checkinDrugUnits = ${checkinDrugUnits}'.logV;
      '======== checkinDrugFrequency = ${checkinDrugFrequency}'.logV;
      final checkinDrugTotalUnits = checkinDrugUnits * checkinDrugFrequency;
      checkinValue += checkinDrugTotalUnits;
    }
    '======== checkinDrugTotalUnits = ${checkinValue}'.logV;
    '======== checkinValue = ${checkinValue}'.logV;
    // Add the check-in data point
    dataPoints.add(FlSpot((i + 1).toDouble(), checkinValue.toDouble()));
    '========/= datapoints = ${dataPoints}'.logV;
    xLabels.add('${checkinDate.day} ${ref.monthName(checkinDate.month)}');
    maxValue = max(maxValue, checkinValue);
  }

  final goalValue = (user?.assessment?.drugsGoal?.drugDetails[user.assessment?.drugs?.list?[drugName]?.drug]?.units ??
          0) *
      (7 - (user?.assessment?.drugsGoal?.drugDetails[user.assessment?.drugs?.list?[drugName]?.drug]?.freeDays ?? 0));
  // Add the goal value with the same X-coordinate as the last check-in
  'goalValue $goalValue'.logD;
  '/goalValue ${user?.assessment?.drugsGoal?.drugDetails[drugData?.drug]?.units}'.logD;
  '//goalValue ${user?.assessment?.drugsGoal?.drugDetails[drugData?.drug]?.freeDays}'.logD;
  '///goalValue ${user?.assessment?.drugs?.list?[drugName]?.drug}'.logD;
  final lastCheckinIndex = dataPoints.length - 1;
  final lastCheckinX =
      dataPoints.length == 1 ? dataPoints[lastCheckinIndex].x + 2 - 0.1 : dataPoints[lastCheckinIndex].x;
  'lastCheckinX$lastCheckinX'.logD;
  dataPoints.add(FlSpot(lastCheckinX, goalValue.toDouble()));
  xLabels.add('Goal');
  maxValue = max(maxValue, goalValue);

  double getMaxYValue() {
    // Assuming ref.dataPoints is a List<FlSpot> and contains the data for the chart
    var maxYValue = double.negativeInfinity; // Start with negative infinity to ensure the first value is greater

    for (final spot in dataPoints) {
      if (spot.y > maxYValue) {
        maxYValue = spot.y;
      }
    }

    // If maxYValue is less than 10, set it to 10
    if (maxYValue < 10) {
      maxYValue = 10;
    } else {
      // Round up to the next multiple of 10
      maxYValue = (maxYValue / 10).ceil() * 10;
    }

    'maxYValue $maxYValue'.logD;
    return maxYValue;
  }
  '======== dataPoint from second dashboard = ${dataPoints}'.logV;
  // Build the Drug graph
  return CustomGraph(
    ref: ref,
    drugData: drugData,
    graphIndex: ref.graphIndex.value,
    dataPoints: dataPoints,
    xLabels: xLabels,
    dynamicUnitText: unit,
    goalValue: goalValue.toString(),
    //  maxX: (Injector.instance<AppDB>().userModel?.user.checkins?.length ?? 0) + 0.1,
    maxY: getMaxYValue(),
    timestamps: formatCheckInDates(
      // First, add the assessment time if it exists, then add the check-in times
      [
        if (Injector.instance<AppDB>().userModel?.user.assessment?.time != null)
          Injector.instance<AppDB>().userModel?.user.assessment?.time ?? 0,
        ...?Injector.instance<AppDB>().userModel?.user.checkins?.map((e) => e.time).whereType<int>(),
      ],
    ),
  );
}
