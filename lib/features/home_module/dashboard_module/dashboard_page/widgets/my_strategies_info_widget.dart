import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_key_value_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class MyStrategiesInfoWidget extends StatelessWidget {
  const MyStrategiesInfoWidget({
    super.key, this.strategiesInfoText,
  });
  final String? strategiesInfoText;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InfoTextWidget(
          text:strategiesInfoText ?? DashboardLocaleKeys.achievementsStrategiesSilverText.tr(),
        ),
        SpaceV(AppSize.h12),
        InfoTextWidget(
          text:
              '${DashboardLocaleKeys.achievementsStrategiesTitle.tr()} - ${DashboardLocaleKeys.achievementsStrategiesSubtitle.tr()}',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesBronze.tr(),
          value: '1',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesSilver.tr(),
          value: '4',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesGold.tr(),
          value: '8',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesPlatinum.tr(),
          value: '12',
        ),
      ],
    );
  }
}
