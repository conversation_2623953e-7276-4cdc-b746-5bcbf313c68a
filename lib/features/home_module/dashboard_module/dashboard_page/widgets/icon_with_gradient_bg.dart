import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class IconWithGradientBg extends StatelessWidget {
  const IconWithGradientBg({
    required this.iconPath,
    required this.gradientColor,
    this.isFilled = false,
    super.key,
  });
  final String iconPath;
  final Color gradientColor;
  final bool isFilled;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // gradient: isFilled
        //     ? LinearGradient(
        //         begin: Alignment.topRight,
        //         end: Alignment.bottomLeft,
        //         colors: [
        //           ...List.generate(3, (index) => gradientColor),
        //           gradientColor.withOpacity(.6),
        //           ...List.generate(4, (index) => gradientColor),
        //         ],
        //       )
        //     : null,
        border: isFilled ? null : Border.all(color: gradientColor),
        shape: BoxShape.circle,
      ),
      child: ShaderMask(
        shaderCallback: (Rect bounds) {
          return LinearGradient(
            colors: isFilled
                ? [Colors.white, Colors.white]
                : [
                    ...List.generate(3, (index) => gradientColor),
                    gradientColor.withOpacity(.6),
                    ...List.generate(4, (index) => gradientColor),
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ).createShader(bounds);
        },
        blendMode: BlendMode.srcIn,
        child: AppSvgAsset(
          svgAsset: iconPath,
          size: AppSize.h44,
        ),
      ),
    );
  }
}
