import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_key_value_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class MyGoalInfoWidget extends StatelessWidget {
  const MyGoalInfoWidget({
    super.key,
    this.goalSelectedText,
  });
  final String? goalSelectedText;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InfoTextWidget(
          text: goalSelectedText ?? DashboardLocaleKeys.achievementsGoalTransparentText.tr(),
        ),
        SpaceV(AppSize.h12),
        InfoTextWidget(
          text:
              '${DashboardLocaleKeys.achievementsGoalTitle.tr()} - ${DashboardLocaleKeys.achievementsGoalSubtitle.tr()}',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesBronze.tr(),
          value: '25',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesSilver.tr(),
          value: '50',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesGold.tr(),
          value: '75',
        ),
         InfoKeyValueWidget(
          name: DashboardLocaleKeys.achievementsColourNamesPlatinum.tr(),
          value: '100',
        ),
      ],
    );
  }
}
