import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/gradient_slider_graph_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/heading_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class StrengthPannelWidget extends StatelessWidget {
  const StrengthPannelWidget({
    required this.heading,
    required this.iconPath,
    required this.redVal,
    required this.infoText,
    required this.isShowPopUp,
    required this.infoAudioUrl,
    required this.audioUrl,
    super.key,
    this.minVal = 0,
    this.maxVal = 10,
    this.greenVal,
  });

  final String heading;
  final String infoText;
  final String iconPath;
  final double redVal;
  final int minVal;
  final int maxVal;
  final double? greenVal;
  final ValueNotifier<bool> isShowPopUp;
  final ValueNotifier<String?> infoAudioUrl;
  final String audioUrl;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: HeadingWidget(
            heading: heading,
            onInfoTap: () {
              isShowPopUp.value = !isShowPopUp.value;
              if (isShowPopUp.value) {
                infoAudioUrl.value = audioUrl;
              } else if (infoAudioUrl.value == audioUrl) {
                infoAudioUrl.value = null;
              }
            },
          ),
        ),
        SpaceV(AppSize.h8),
        ValueListenableBuilder(
          valueListenable: isShowPopUp,
          builder: (context, isShowPopUpV, _) {
            return CustomInfoWidget(
              onCloseTap: () {
                isShowPopUp.value = false;
                if (infoAudioUrl.value == audioUrl) {
                  infoAudioUrl.value = null;
                }
              },
              padding: EdgeInsets.only(
                left: AppSize.w4,
                right: AppSize.w4,
              ),
              visible: isShowPopUpV,
              margin: EdgeInsets.symmetric(vertical: AppSize.h8),
              bodyText: infoText,
            );
          },
        ),
        SpaceV(AppSize.h8),
        Row(
          children: [
            AppSvgAsset(
              svgAsset: iconPath,
              size: AppSize.h44,
            ),
            SpaceH(AppSize.w30),
            GradientSliderGraphWidget(
              redVal: redVal,
              greenVal: greenVal,
              maxVal: maxVal,
              minVal: minVal,
            ),
          ],
        ),
      ],
    );
  }
}
