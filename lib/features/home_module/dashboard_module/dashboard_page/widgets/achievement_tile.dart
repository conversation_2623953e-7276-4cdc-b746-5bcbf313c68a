import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/icon_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/two_tile_heading_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class Achievement {
  Achievement({
    required this.iconPath,
    required this.heading,
    required this.largeText,
    required this.smallText,
    this.isIconFilled = false,
    this.onInfoTap,
    this.iconColor,
  });
  final String heading;
  final String largeText;
  final String smallText;
  final String iconPath;
  final bool isIconFilled;
  final Color? iconColor;
  final void Function()? onInfoTap;
}

class AchievementTile extends StatelessWidget {
  const AchievementTile({
    required this.tile1,
    required this.tile2,
    this.infoWidget,
    super.key,
    this.onCloseInfoTap,
  });
  final Widget? infoWidget;
  final Achievement tile1;
  final Achievement tile2;
  final void Function()? onCloseInfoTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TwoTileHeadingWidget(
          heading1: tile1.heading,
          onInfo1Tap: tile1.onInfoTap,
          heading2: tile2.heading,
          onInfo2Tap: tile2.onInfoTap,
        ),
        SpaceV(AppSize.h4),
        CustomInfoWidget(
          visible: infoWidget != null,
          margin: EdgeInsets.symmetric(vertical: AppSize.h8),
          padding: EdgeInsets.only(
            left: AppSize.w8,
            right: AppSize.w8,
          ),
          customWidget: infoWidget,
          onCloseTap: onCloseInfoTap,
        ),
        SpaceV(AppSize.h4),
        Row(
          children: [
            IconTextWidget(
              iconPath: tile1.iconPath,
              largeText: tile1.largeText,
              smallText: tile1.smallText,
              //  color: tile1.iconColor,
              isFilled: tile1.isIconFilled,
            ),
            IconTextWidget(
              iconPath: tile2.iconPath,
              largeText: tile2.largeText,
              smallText: tile2.smallText,
              // color: tile2.iconColor,
              isFilled: tile2.isIconFilled,
            ),
          ],
        ),
      ],
    );
  }
}
