import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class GraphAxisText extends StatelessWidget {
  const GraphAxisText({
    required this.axisTitle,
    super.key, this.color,
  });

  final String axisTitle;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return AppTextWidget(
      axisTitle,
      style: context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp6, color: color),
    );
  }
}
