import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:flutter/material.dart';

class InfoTextWidget extends StatelessWidget {
  const InfoTextWidget({
    required this.text,
    super.key,
  });
  
  final String text;
  @override
  Widget build(BuildContext context) {
    return AppTextWidget(
      text,
      style: context.textTheme.titleSmall?.copyWith(
        color: context.themeColors.darkOrangeColor,
      ),
    );
  }
}
