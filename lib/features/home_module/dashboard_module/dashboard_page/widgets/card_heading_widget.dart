import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class CardHeadingWidget extends StatelessWidget {
  const CardHeadingWidget({
    required this.name,
    super.key, this.textAlign,
  });
  final String name;
  final TextAlign? textAlign;
  @override
  Widget build(BuildContext context) {
    return AppTextWidget(
      name,
      textAlign:textAlign?? TextAlign.center,
      style: context.textTheme.labelSmall?.copyWith(
        color: context.themeColors.blackColor,
        fontWeight: FontWeight.w600,
        fontSize: AppSize.sp14,
      ),
    );
  }
}
