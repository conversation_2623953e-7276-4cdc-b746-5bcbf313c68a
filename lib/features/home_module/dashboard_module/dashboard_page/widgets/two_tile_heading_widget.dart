

import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/heading_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class TwoTileHeadingWidget extends StatelessWidget {
  const TwoTileHeadingWidget({
    required this.heading1,
    required this.heading2,
    super.key,
    this.onInfo1Tap,
    this.onInfo2Tap,
  });

  final String heading1;
  final String heading2;
  final void Function()? onInfo1Tap;
  final void Function()? onInfo2Tap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: HeadingWidget(
            heading: heading1,
            onInfoTap: onInfo1Tap,
          ),
        ),
        SpaceH(AppSize.w16),
        Expanded(
          child: HeadingWidget(
            heading: heading2,
            onInfoTap: onInfo2Tap,
          ),
        ),
      ],
    );
  }
}
