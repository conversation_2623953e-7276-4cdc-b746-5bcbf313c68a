import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/res/logger.dart';

class CheckinProcessor {
  // A function to process and sort entries for each type

  static List<MapEntry<DateTime, Checkin>> processCheckins1<T>(
    List<MapEntry<DateTime, Checkin>> entries,
    List<Checkin> checkins,
  ) {
    for (final element in checkins) {
      if (element.time != null) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(element.time!);
        entries.add(MapEntry(dateTime, element));
      }
    }

    // Sort entries by DateTime in descending order
    entries.sort((a, b) => b.key.compareTo(a.key));
    // Log all processed entries
    for (final entry in entries) {
      'Date: ${entry.key}, Checkin Data: ${entry.value}'.logD;
    }
    return entries;
  }

  static void processCheckins<T>(
    List<MapEntry<DateTime, T>> entries,
    List<Checkin> checkins,
    T Function(Checkin) getType,
  ) {
    for (final element in checkins) {
      if (element.time != null) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(element.time!);
        final type = getType(element);
        if (type != null) {
          entries.add(MapEntry(dateTime, type));
        }
      }
    }

    // Sort entries by DateTime in descending order
    entries.sort((a, b) => b.key.compareTo(a.key));
  }

  // A function to handle the conversion and printing of the most recent entry
  static Map<String, dynamic>? printMostRecentEntry<T>(
    List<MapEntry<DateTime, T>> entries,
    String entryTypeName,
  ) {
    if (entries.isNotEmpty) {
      final mostRecentEntry = entries.first;

      // Try calling toJson() on the value and print it
      final jsonData = _getJsonData(mostRecentEntry.value);

      if (jsonData != null) {
        'Most Recent $entryTypeName Entry Time: ${mostRecentEntry.key}'.logD;
        'Most Recent $entryTypeName Entry Data: $jsonData'.logD;
        jsonData.forEach((key, value) {
          '$entryTypeName Data: $key: $value'.logD;
        });
        return jsonData; // Return the model data as Map<String, dynamic>
      } else {
        'Error: The object does not have a toJson() method'.logD;
        return null; // Return null if no toJson() method is found
      }
    }
    return null; // Return null if the entries list is empty
  }

  // Helper function to call toJson dynamically
  static Map<String, dynamic>? _getJsonData(dynamic value) {
    try {
      if (value is Map<String, dynamic>) {
        return value; // Already a Map
      } else if (value is CheckinEi) {
        return value.toJson();
      } else if (value is CheckinLife) {
        return value.toJson();
      } else if (value is CheckinRate) {
        return value.toJson();
      } else if (value is CheckinDrugs) {
        return value.toJson();
      }
      return null; // If no toJson() method is available
    } catch (e) {
      'Error calling toJson: $e'.logD;
      return null;
    }
  }
}
