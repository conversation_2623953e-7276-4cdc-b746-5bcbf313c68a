import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/info_text_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class InfoKeyValueWidget extends StatelessWidget {
  const InfoKeyValueWidget({
    required this.name,
    required this.value,
    super.key,
  });
  final String name;
  final String value;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SpaceV(AppSize.h20),
        Row(
          children: [
            Expanded(
              child: Padding(
                padding:  EdgeInsets.only(left: AppSize.w10),
                child: InfoTextWidget(
                  text: name,
                ),
              ),
            ),
            Expanded(
              child: InfoTextWidget(text: value),
            ),
          ],
        ),
        SpaceV(AppSize.h8),
        Divider(
          height: 1,
          color: context.themeColors.dividerColor,
        ),
      ],
    );
  }
}
