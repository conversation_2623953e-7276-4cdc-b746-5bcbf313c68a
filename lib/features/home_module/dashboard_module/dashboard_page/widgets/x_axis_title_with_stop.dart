import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/graph_axis_text.dart';
import 'package:flutter/material.dart';

class XAxisTitleWithStop extends StatelessWidget {
  const XAxisTitleWithStop({
    required this.xAxisTitle,
    super.key,
    this.color,
  });
  final String xAxisTitle;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (color != Colors.white)
          Container(
            height: 3,
            width: 2,
            color: Colors.black,
          )
        else
          const SizedBox(),
        GraphAxisText(
          axisTitle: xAxisTitle,
          color: color,
        ),
      ],
    );
  }
}
