import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:flutter/material.dart';

class ScoreTextWidget extends StatelessWidget {
  const ScoreTextWidget({
    required this.smallText,
    required this.largeText,
    super.key,
  });
  final String smallText;
  final String largeText;
  @override
  Widget build(BuildContext context) {
    return Text.rich(
      maxLines: 1,
      style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
      textAlign: TextAlign.center,
      TextSpan(
        children: [
          TextSpan(
            text: largeText,
          ),
          TextSpan(
            text: ' $smallText',
            style: context.textTheme.labelSmall,
          ),
        ],
      ),
    );
  }
}
