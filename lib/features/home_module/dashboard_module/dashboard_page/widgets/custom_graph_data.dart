import 'dart:math';
import 'dart:isolate';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/cubit/dashboard_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/x_axis_title_with_stop.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';

class CustomGraph extends StatefulWidget {
  CustomGraph({
    required this.dataPoints,
    required this.xLabels,
    required this.graphIndex,
    super.key,
    this.maxX,
    this.maxY,
    this.dynamicUnitText,
    this.goalValue,
    this.timestamps,
    this.interval, 
    this.drugData,
    this.isForAlcohol = false,
    required this.ref,
  });
  final double? maxX;
  final double? interval;
  final double? maxY;
  final List<dynamic> dataPoints;
  final List<String> xLabels;
  final String? dynamicUnitText;
  final String? goalValue;
  List<String>? timestamps; 
  final int graphIndex;
  final ListButane? drugData;
  final bool isForAlcohol;
  final DashboardPageCubit ref;

  @override
  State<CustomGraph> createState() => _CustomGraphState();
  initState() {
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //   // Use GestureDetector to detect taps outside the chart
  //   GestureBinding.instance.pointerRouter.addGlobalRoute((PointerEvent event) {
  //     if (event is PointerDownEvent && _tooltipBehavior.isVisible) {
  //       _tooltipBehavior.hide(); // Hide the tooltip
  //     }
  //   });
  // });
  }
}

class _CustomGraphState extends State<CustomGraph> {
  @override
  Widget build(BuildContext context) {
   'goalValue :=== ${widget.goalValue}'.logV;

   final Map<String, int> dateUsageMap = {}; // Track how many times a date is reused

final List<ChartData> chartDataRaw = List.generate(widget.dataPoints.length, (index) {
  final spot = widget.dataPoints[index];
  DateTime date;
  bool isGoalPoint = widget.xLabels.isNotEmpty &&
                     index < widget.xLabels.length &&
                     widget.xLabels[index] == 'Goal';

  if (isGoalPoint) {
    // Temporarily set to placeholder; will be corrected below
    date = DateTime(1900);
  } else {
    try {
      if (widget.xLabels.isNotEmpty && index < widget.xLabels.length) {
        final parts = widget.xLabels[index].split(' ');
        if (parts.length == 2) {
          final day = int.tryParse(parts[0]) ?? 1;
          final month = _getMonthNumber(parts[1]);
          final now = DateTime.now();
          int year = now.year;
          if (month > now.month || (month == now.month && day > now.day)) {
            year = now.year - 1;
          }
    date = DateTime(year, month, day);

          // Avoid overlapping timestamps
          final key = date.toIso8601String().substring(0, 10);
          int count = dateUsageMap[key] ?? 0;
          date = date.add(Duration(minutes: count * 5));
          dateUsageMap[key] = count + 1;
        } else {
          final assessmentTime = Injector.instance<AppDB>().userModel?.user.assessment?.time ?? DateTime.now().millisecondsSinceEpoch;
          date = DateTime.fromMillisecondsSinceEpoch(assessmentTime).add(Duration(days: index));
        }
      } else {
        final assessmentTime = Injector.instance<AppDB>().userModel?.user.assessment?.time ?? DateTime.now().millisecondsSinceEpoch;
        date = DateTime.fromMillisecondsSinceEpoch(assessmentTime).add(Duration(days: index));
      }
    } catch (e, stacktrace) {
      final assessmentTime = Injector.instance<AppDB>().userModel?.user.assessment?.time ?? DateTime.now().millisecondsSinceEpoch;
      date = DateTime.fromMillisecondsSinceEpoch(assessmentTime).add(Duration(days: index));
    }
  }

  final double yValue = ((spot?.y ?? 0.0) as num).toDouble();
  return ChartData(date, yValue, isGoalPoint: isGoalPoint);
});

/// ✅ Fix placeholder goal date
final int goalIndex = widget.xLabels.lastIndexOf('Goal');
if (goalIndex != -1 && goalIndex > 0 && goalIndex < chartDataRaw.length) {
  final DateTime lastCheckinDate = chartDataRaw[goalIndex - 1].date;

  final ChartData originalGoal = chartDataRaw[goalIndex];

  final DateTime goalDate = lastCheckinDate.add(const Duration(seconds: 1)); // Nudge to avoid collision

  chartDataRaw[goalIndex] = ChartData(
    goalDate,
    originalGoal.value,
    isGoalPoint: true,
  );

} else if (goalIndex == 0) {
} else {
}

final List<ChartData> chartData = List.from(chartDataRaw);
chartData.sort((a, b) => a.date.compareTo(b.date));

    // Determine min/max dates for the axis
    DateTime? minDate;
    DateTime? maxDate;
    if (chartData.isNotEmpty) {
      minDate = chartData.first.date;
      maxDate = chartData.last.date;
    } else {
        final now = DateTime.now();
        minDate = now.subtract(const Duration(days: 30));
        maxDate = now;
    }
    'minDate: $minDate, maxDate: $maxDate'.logV;

    // Determine Y-axis maximum
    double calculatedMaxY = 10.0;
    if (widget.dataPoints.isNotEmpty) {
        double maxValFromData = 0.0; 
        widget.dataPoints.forEach((point) {
            double currentY = 0.0;
            try {
                if (point != null && point.y != null) {
                   currentY = (point.y as num).toDouble();
                } 
            } catch (e) {
                // Handle cases where point is null, doesn't have 'y', or 'y' isn't num
                 'Error accessing point.y: $e, point: $point'.logW;
            }
             maxValFromData = max(maxValFromData, currentY);
        });

        // Calculate appropriate max value based on the data range
        if (maxValFromData > 0) {
            // For values less than 10, use 10 as max
            if (maxValFromData <= 10) {
                calculatedMaxY = 10.0;
            } 
            // For values between 10 and 100, round up to next multiple of 10
            else if (maxValFromData <= 100) {
                calculatedMaxY = (maxValFromData / 10).ceil() * 10.0;
            }
            // For values between 100 and 1000, round up to next multiple of 100
            else if (maxValFromData <= 1000) {
                calculatedMaxY = (maxValFromData / 100).ceil() * 100.0;
            }
            // For values greater than 1000, round up to next multiple of 1000
            else {
                calculatedMaxY = (maxValFromData / 1000).ceil() * 1000.0;
            }
        }
    }
    final double finalMaxY = calculatedMaxY;
    'finalMaxY for axis: $finalMaxY'.logV;
    
    // Calculate appropriate interval based on the max value
    double yInterval;
    if (finalMaxY <= 10) {
        yInterval = 2.0;
    } else if (finalMaxY <= 100) {
        yInterval = (finalMaxY / 5).ceilToDouble();
    } else if (finalMaxY <= 1000) {
        yInterval = (finalMaxY / 5).ceilToDouble();
    } else {
        yInterval = (finalMaxY / 5).ceilToDouble();
    }
    'yInterval: $yInterval'.logV;
    final int dataPointsLength = widget.dataPoints.length;

DateTimeIntervalType intervalType;
double? xInterval;

final DateTime firstDate = chartData.first.date;
final DateTime lastDate = chartData.last.date;
final int differenceInDays = lastDate.difference(firstDate).inDays;
final int differenceInHours = lastDate.difference(firstDate).inHours;
'===== differenceInDays = $differenceInDays'.logV;
maxDate = maxDate.difference(minDate).inDays == 0 ? maxDate.add(Duration(minutes: 0)) : maxDate;
'/// data pt = ${widget.dataPoints.length}'.logV;
if(widget.dataPoints.length == 2){
  '///point 2'.logV;
  intervalType = DateTimeIntervalType.milliseconds;
  xInterval = 250;
}  else if(differenceInDays == 0){
  intervalType = DateTimeIntervalType.hours;
  xInterval = 6;
} else if(differenceInDays <= 4){
  intervalType = DateTimeIntervalType.days;
  xInterval = 1;
}else if(differenceInDays < 8){
  intervalType = DateTimeIntervalType.days;
  xInterval = 2;
} else if (differenceInDays >= 8 && differenceInDays < 20) {
  intervalType = DateTimeIntervalType.days;
  xInterval = 5;
} else if (differenceInDays >= 20 && differenceInDays <= 31) {
  intervalType = DateTimeIntervalType.days;
  xInterval = 7;
} else if(differenceInDays > 31 && differenceInDays < 120){
  intervalType = DateTimeIntervalType.months;
  xInterval = 1;
} else if(differenceInDays >= 120 && differenceInDays <= 240){
  intervalType = DateTimeIntervalType.months;
  xInterval = 2;
} else {
  intervalType = DateTimeIntervalType.months;
  xInterval = 3;
}

    List<ChartData> dashedLineData = [];
    // Use the xLabels from parameter to check for goal
    if (goalIndex != -1 && goalIndex > 0 && goalIndex < chartData.length) {
        final actualGoalData = chartData.last;
        final lastCheckinData = chartData[chartData.length - 2];
        final DateTime goalDate = actualGoalData.date;
        final double goalValueData = actualGoalData.value; // Renamed to avoid conflict
        final double lastCheckinValue = lastCheckinData.value;
        dashedLineData = [
            ChartData(goalDate, lastCheckinValue),
            ChartData(goalDate, goalValueData),
        ];
    }
  final double goalYValue = widget.goalValue != null
    ? double.tryParse(widget.goalValue.toString()) ?? 0.0
    : chartData.firstWhere((data) => data.isGoalPoint).value;
   late final TrackballBehavior _trackballBehavior = TrackballBehavior(
    
  enable: true,
  activationMode: ActivationMode.singleTap,
  tooltipSettings: const InteractiveTooltip(enable: false,),
  builder: (BuildContext context, TrackballDetails details) {
    final int pi = details.pointIndex!;
    final ChartData d = chartData[pi];
    String tooltipText;
    Color borderColor;
    if (d.isGoalPoint) {
      tooltipText = DashboardLocaleKeys.recoveryProgressLabelsMyGoal.tr(); //'My Goal';
      borderColor = context.themeColors.greenBtnColor;
    } else if (pi == 0) {
      tooltipText = DashboardLocaleKeys.recoveryProgressLabelsStart.tr();  //'Where I started';
      borderColor = context.themeColors.redColor;
    } else if (pi == chartData.length - 1) {
      tooltipText = DashboardLocaleKeys.recoveryProgressLabelsCurrent.tr(); //'Where I am now';
      borderColor = context.themeColors.greenBtnColor;
    } else {
      tooltipText = DashboardLocaleKeys.recoveryProgressLabelsCheckin.tr(); //'Check-in point';
      borderColor = context.themeColors.blueColor;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(tooltipText, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text('${d.value.toInt()} ${widget.dynamicUnitText}'),
          Text(DateFormat('dd MMM yyyy').format(d.date)),
        ],
      ),
    );
  },
);
Color popUpColor = Colors.black;
widget.ref.tooltipBehavior = TooltipBehavior( 
  
  duration: 500000,
  enable: true,
  canShowMarker: false,
  builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
    final ChartData d = data as ChartData;

    // Default values
    String tooltipText = DashboardLocaleKeys.recoveryProgressLabelsCheckin.tr();//'Check-in point';
    Color borderColor = Color.fromRGBO(51, 51, 51,1);

 final isGoal = chartData.any((data) =>
  data.isGoalPoint &&
  (d.value - data.value).abs() < 0.001 &&
  d.date.difference(data.date).inSeconds.abs() < 60
);

final nonGoalData = chartData.where((data) => !data.isGoalPoint).toList();
final firstNonGoal = nonGoalData.isNotEmpty ? nonGoalData.first : null;
final lastNonGoal = nonGoalData.isNotEmpty ? nonGoalData.last : null;

// Prefer checking identity instead of value/date comparisons
bool isGoalPoint = isGoal;
bool isStartPoint = firstNonGoal != null && identical(d, firstNonGoal);
bool isLastCheckin = lastNonGoal != null && identical(d, lastNonGoal);

// Now apply correct tooltip logic
if (isStartPoint) {
  tooltipText = DashboardLocaleKeys.recoveryProgressLabelsStart.tr(); //'Where I started';
  borderColor = const Color.fromRGBO(153, 23, 0, 1);
  popUpColor = const Color.fromRGBO(153, 23, 0, 1);
} else if (isGoalPoint) {
  tooltipText = DashboardLocaleKeys.recoveryProgressLabelsMyGoal.tr(); //'My Goal';
  borderColor = const Color.fromRGBO(3, 82, 0, 1);
  popUpColor = const Color.fromRGBO(3, 82, 0, 1);
} else if (isLastCheckin) {
  tooltipText = DashboardLocaleKeys.recoveryProgressLabelsCurrent.tr(); //'Where I am now';
  borderColor = const Color.fromRGBO(197, 97, 0, 1);
  popUpColor = const Color.fromRGBO(197, 97, 0, 1);
} else {
  tooltipText = DashboardLocaleKeys.recoveryProgressLabelsCheckin.tr(); //'Check-in point';
  borderColor = const Color.fromRGBO(51, 51, 51, 1);
  popUpColor = const Color.fromRGBO(51, 51, 51, 1);
}


    return Container(
      padding: EdgeInsets.all(AppSize.sp12),
      decoration: BoxDecoration(
        color: borderColor,
        borderRadius: BorderRadius.circular(AppSize.r2)
        // border: Border.all(color: borderColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(tooltipText, style: TextStyle(color: context.themeColors.whiteColor,)),
          SpaceV(AppSize.h5),
           Injector.instance<AppDB>().userModel?.user.lang.toString() == 'fr-CA' ? 
           Text('${d.value.toInt()} ${widget.dynamicUnitText?.toLowerCase()}',style: TextStyle(color: context.themeColors.whiteColor)) 
           : Text('${d.value.toInt()} ${widget.dynamicUnitText}',style: TextStyle(color: context.themeColors.whiteColor)),
          isGoal ? SizedBox():Text(widget.ref.formatDate(d.date),style: TextStyle(color: context.themeColors.whiteColor)),
        ],
      ),
    );
  },
  color: popUpColor,
);

  return GestureDetector(
    onTap: () {
        widget.ref.tooltipBehavior.hide();
        '======== dataPoint = ${widget.dataPoints}'.logV;
    },
    behavior: HitTestBehavior.opaque,
    child: SfCartesianChart(
    enableAxisAnimation: false,
    tooltipBehavior: widget.ref.tooltipBehavior,
    primaryXAxis: DateTimeAxis(
            minimum: minDate,
            maximum: maxDate,
            intervalType: intervalType,
            interval: xInterval,
            // dateFormat: DateFormat('d MMM'),
            axisLabelFormatter: (AxisLabelRenderDetails details) {
              final date = DateTime.fromMillisecondsSinceEpoch(details.value.toInt());
              final formattedLabel = widget.ref.formatDate(date); // e.g., '21 May'
              return ChartAxisLabel(formattedLabel, context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp9));
            },
            majorGridLines: const MajorGridLines(width: 0),
            edgeLabelPlacement: EdgeLabelPlacement.shift,
             labelIntersectAction: AxisLabelIntersectAction.rotate45,
         ),
    primaryYAxis: NumericAxis(
      title: AxisTitle(
        text: Injector.instance<AppDB>().userModel?.user.lang.toString() == 'fr-CA' ? widget.dynamicUnitText?.toLowerCase() : widget.dynamicUnitText,
        textStyle: context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp9)
      ),
      minimum: 0,
      maximum: finalMaxY,
      interval: yInterval,
      majorGridLines: const MajorGridLines(width: 0),
      // axisLine: const AxisLine(width: 0) 
      labelFormat: '{value}',
      axisLabelFormatter: (AxisLabelRenderDetails args) {
        return ChartAxisLabel(args.text, context.textTheme.labelSmall?.copyWith(fontSize: AppSize.sp9));
      },
      plotBands: [
        if (widget.graphIndex <= 1) PlotBand(
          borderColor: context.themeColors.greenBtnColor,
          end: goalYValue + 0.01,
        ),
        if (widget.graphIndex <= 1) PlotBand(
          end: goalYValue,
          color: Color.fromRGBO(218, 245, 209, 1),
        ),
      ],
    ),
    series: _buildChartSeries(context, chartData, goalYValue, widget.goalValue),
    plotAreaBorderWidth: 0,
    ),
  );

  }

  // Helper function moved outside build method
  List<CartesianSeries<dynamic, dynamic>> _buildChartSeries(
      BuildContext context, List<ChartData> chartData, double goalYValue, dynamic goalValueRef) {

    // --- Data Preparation ---
    final nonGoalData = chartData.where((data) => !data.isGoalPoint).toList();
    final goalData = chartData.where((data) => data.isGoalPoint).toList();
    final startPoint = nonGoalData.isNotEmpty ? nonGoalData.first : null;
    final beforeGoalPoint = nonGoalData.length > 1 ? nonGoalData.last : null; // Point before goal (if exists)
    
    '????? addiction case = ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}'.logV;
    '????? special addiction case = ${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.specialAddiction}'.logV;

    '>?>?>? ${ chartData.where((data) => data.isGoalPoint).toList()}'.logV;
    '>>>>> nonGoal = ${chartData}'.logE;
    '>>>>> goaldata = ${chartData.where((data) => data.isGoalPoint).toList()}'.logE;
    '>>>>> startPoint = ${ nonGoalData.isNotEmpty ? nonGoalData.first : null}'.logE;
    '>>>>> beforeGoalpoint ${nonGoalData.length > 1 ? nonGoalData.last : null}'.logE;
     '>?>?>? goalData = ${goalData[0].value}'.logV;
    // --- End Data Preparation ---  

    final List<CartesianSeries<dynamic, dynamic>> series = [
      // Main LineSeries connecting all non-goal points
      LineSeries<ChartData, DateTime>(
        dataSource: nonGoalData, 
        xValueMapper: (ChartData data, _) => data.date,
        yValueMapper: (ChartData data, _) => data.value,
        color: context.themeColors.graphLine, 
        markerSettings: const MarkerSettings(
          isVisible: true,
          width: 12, 
          height: 12,
          borderWidth: 0,
          shape: DataMarkerType.circle,
          color: Color.fromRGBO(51, 51,51,1), // Set default color to blue
        ), 
        name: 'MainData',
      ),

      // Separate Series for the Start Point Marker (will draw OVER the blue one)
      if (startPoint != null)
        LineSeries<ChartData, DateTime>(
          dataSource: [startPoint],
          xValueMapper: (ChartData data, _) => data.date,
          yValueMapper: (ChartData data, _) => data.value,
          width: 0, // No line
          markerSettings: MarkerSettings(
            isVisible: true,
            width: 15, 
            height: 15,
            borderWidth: 0,
            shape: DataMarkerType.circle,
            color: context.themeColors.redColor, // RED fill color
          ),
          name: 'StartPoint',
        ),

      // Separate Series for the Point Before Goal Marker
      if (beforeGoalPoint != null)
        LineSeries<ChartData, DateTime>(
          dataSource: [beforeGoalPoint],
          xValueMapper: (ChartData data, _) => data.date,
          yValueMapper: (ChartData data, _) => data.value,
          width: 0, // No line
          markerSettings: MarkerSettings(
            isVisible: true,
            width: 15, 
            height: 15,
            borderWidth: 0,
            shape: DataMarkerType.circle,
            color: context.themeColors.orangeColor, // ORANGE fill color
          ),
          name: 'BeforeGoalPoint',
        ),
       
      // Separate MarkerSeries for the Goal Point
      if (goalData.isNotEmpty && widget.graphIndex <= 1 && (Injector.instance<AppDB>().userModel?.user.assessment?.drugsGoal?.drugDetails[widget.drugData?.drug]?.units != null || widget.isForAlcohol == true))
        LineSeries<ChartData, DateTime>(
          dataSource: goalData,
          xValueMapper: (ChartData data, _) => data.date,
          yValueMapper: (ChartData data, _) => data.value,
          width: 0, // No line, just the marker
          markerSettings: MarkerSettings(
            isVisible: true,
            width: 15,
            height: 15,
            shape: DataMarkerType.circle,
            color: context.themeColors.greenBtnColor, // GREEN fill color for goal
            borderWidth: 0, // Remove border
          ),
          name: 'GoalPoint',
        ),
    ];
    
    if (goalData.isNotEmpty && nonGoalData.isNotEmpty && widget.graphIndex <= 1 && (Injector.instance<AppDB>().userModel?.user.assessment?.drugsGoal?.drugDetails[widget.drugData?.drug]?.units != null || widget.isForAlcohol == true)) {
      final ChartData goalPoint = goalData.first;
      final double startY = nonGoalData.first.value;
      final double lastY = nonGoalData.last.value;
      final double targetY = max(startY, lastY);
      final DateTime xCoord = goalPoint.date;
      final double yMin = min(goalPoint.value, targetY);
      final double yMax = max(goalPoint.value, targetY);
      series.add(LineSeries<ChartData, DateTime>(
        dataSource: [ChartData(xCoord, yMin), ChartData(xCoord, yMax)],
        xValueMapper: (ChartData data, _) => data.date,
        yValueMapper: (ChartData data, _) => data.value,
        color: context.themeColors.blackColor,
        width: 2,
        dashArray: <double>[5, 8],
        name: 'GoalToPointVerticalLine',
      ));
    }

    return series;
  }

  // Helper function to get month number from abbreviation (same as in CustomGraph)
  int _getMonthNumber(String monthAbbr) {
    final months = {
      'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
      'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
    };
    // Add lowercase variants for robustness if needed
     final lowerMonths = months.map((key, value) => MapEntry(key.toLowerCase(), value));
     return months[monthAbbr] ?? lowerMonths[monthAbbr.toLowerCase()] ?? 1; // Fallback to Jan
  }
}

class ChartData {
  final DateTime date;
  final double value;
  final bool isGoalPoint;

  ChartData(this.date, this.value, {this.isGoalPoint = false});

}
