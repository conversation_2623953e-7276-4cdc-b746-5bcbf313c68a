import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:flutter/material.dart';

class TriangleShapePainter extends CustomPainter {
  TriangleShapePainter({this.color});
  final Color? color;
  @override
  void paint(Canvas canvas, Size size) {
    // Paint for the fill
    final fillPaint = Paint()
      ..color = color ?? navigatorKey.currentContext!.themeColors.redColor
      ..style = PaintingStyle.fill;

    // Paint for the border
    final borderPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    // Define the triangle path
    final path = Path()
      ..moveTo(size.width / 2, 0) // Top-center point of the triangle
      ..lineTo(0, size.height) // Bottom-left point of the triangle
      ..lineTo(size.width, size.height) // Bottom-right point of the triangle
      ..close(); // Close the path to form a triangle

    canvas
      ..drawPath(path, fillPaint) // Draw the filled triangle
      ..drawPath(path, borderPaint); // Draw the triangle's border
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
