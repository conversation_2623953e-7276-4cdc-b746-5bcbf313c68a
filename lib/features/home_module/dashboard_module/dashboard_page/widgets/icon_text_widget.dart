import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/score_text_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class IconTextWidget extends StatelessWidget {
  const IconTextWidget({
    required this.smallText,
    required this.largeText,
    required this.iconPath,
    this.color,
    this.isFilled = false,
    super.key,
  });
  final String smallText;
  final String largeText;
  final String iconPath;
  final bool isFilled;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          // IconWithGradientBg(
          //   gradientColor: color??context.themeColors.gradientGreenColor,
          //   iconPath: iconPath,
          //   isFilled: isFilled,
          // ),
          SpaceV(AppSize.h8),
          CustomIconButton(
            iconPath: iconPath,
            size: AppSize.sp64,
          ),
          SpaceV(AppSize.h8),
          ScoreTextWidget(
            largeText: largeText,
            smallText: smallText,
          ),
        ],
      ),
    );
  }
}
