import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/triangle_shape_painter.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class GradientSliderGraphWidget extends StatelessWidget {
  const GradientSliderGraphWidget({
    required this.redVal,
    super.key,
    this.minVal = 0,
    this.maxVal = 10,
    this.greenVal,
  });
  final int minVal;
  final int maxVal;
  final double redVal;
  final double? greenVal;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Padding(
        padding: EdgeInsets.only(top: AppSize.h16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: AppSize.h28,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppSize.r6),
                gradient: LinearGradient(
                  colors: [
                    context.themeColors.redColor,
                    const Color.fromRGBO(240, 104, 0, 1),
                    context.themeColors.greenColor,
                  ],
                ),
              ),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final containerWidth = constraints.maxWidth;
                  final redPointPosition =
                      (redVal - minVal) / (maxVal - minVal) * (containerWidth-AppSize.w30);
                  final greenPointPosition = greenVal != null
                      ? (((greenVal ?? -1) - minVal) /
                          (maxVal - minVal) *
                          (containerWidth-AppSize.w30))
                      : null;
                  return Stack(
                    clipBehavior: Clip.none,
                    children: [
                      if (redVal >= minVal && redVal <= maxVal) ...[
                        Positioned(
                          left: redPointPosition,
                          top: -AppSize.h16,
                          child: SizedBox(
                            width: AppSize.w30,
                            child: Center(
                              child: AppTextWidget(
                                '${redVal.toInt()}',
                                maxLines: 1,
                                style: context.textTheme.labelSmall?.copyWith(
                                  color: context.themeColors.redColor,
                                  fontSize: AppSize.sp14,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: redPointPosition ,
                          bottom: -AppSize.h6,
                          child: CustomPaint(
                            size: Size(AppSize.w30, AppSize.h20),
                            painter: TriangleShapePainter(),
                          ),
                        ),
                      ],
                      if (greenPointPosition != null &&
                          (greenVal ?? -1) >= minVal &&
                          (greenVal ?? -1) <= maxVal) ...[
                        Positioned(
                          left: greenVal == redVal ? greenPointPosition + AppSize.w10 : greenPointPosition,
                          top: -AppSize.h16,
                          child: SizedBox(
                            width: AppSize.w30,
                            child: Center(
                              child: AppTextWidget(
                                '${greenVal?.toInt() ?? -1}',
                                maxLines: 1,
                                style: context.textTheme.labelSmall?.copyWith(
                                  color: context.themeColors.greenColor,
                                  fontSize: AppSize.sp14,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          left: greenVal == redVal ? greenPointPosition + AppSize.w10 : greenPointPosition,
                          bottom: -AppSize.h6,
                          child: CustomPaint(
                            size: Size(AppSize.w30, AppSize.h20),
                            painter: TriangleShapePainter(
                              color: context.themeColors.greenColor,
                            ),
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
            SpaceV(AppSize.h4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: AppSize.w12),
                  child: AppTextWidget(
                    '$minVal',
                    style: context.textTheme.labelSmall?.copyWith(
                      color: context.themeColors.blackColor,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: AppSize.w8),
                  child: AppTextWidget(
                    '$maxVal',
                    textAlign: TextAlign.right,
                    style: context.textTheme.labelSmall?.copyWith(
                      color: context.themeColors.blackColor,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
