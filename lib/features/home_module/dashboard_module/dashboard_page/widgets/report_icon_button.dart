import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class ReportIconButton extends StatelessWidget {
  const ReportIconButton({
    required this.iconPath,
    super.key,
    this.onTap,
  });

  final void Function()? onTap;
  final String iconPath;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AppSvgAsset(
        svgAsset: iconPath,
        size: AppSize.sp64,
      ),
    );
  }
}
