import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class HeadingWidget extends StatelessWidget {
  const HeadingWidget({
    required this.heading,
    super.key,
    this.onInfoTap,
    this.textAlign,
  });
  final String heading;
  final void Function()? onInfoTap;
  final TextAlign? textAlign;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: AppTextWidget(
            heading,
            maxLines: 2,
            textAlign: textAlign ?? TextAlign.center,
            style: context.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: AppSize.sp13,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        SpaceH(AppSize.w8),
        CustomIconButton(
          onTap: onInfoTap,
          assetIcon: Assets.icons.infoIcon,
        ),
      ],
    );
  }
}
