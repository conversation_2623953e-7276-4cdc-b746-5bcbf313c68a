import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/graph_axis_text.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class YAxisTitleWithStop extends StatelessWidget {
  const YAxisTitleWithStop({
    required this.yAxisTitle,
    super.key,
  });
  final String yAxisTitle;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GraphAxisText(axisTitle: yAxisTitle),
        SpaceH(AppSize.w4),
        Container(
          width: 4,
          height: 2,
          color: Colors.black,
        ),
      ],
    );
  }
}
