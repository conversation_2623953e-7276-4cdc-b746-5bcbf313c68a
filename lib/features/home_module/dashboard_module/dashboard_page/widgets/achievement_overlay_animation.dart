import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/cubit/dashboard_page_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class AchievementOverlay extends StatefulWidget {
  const AchievementOverlay({
    required this.title,
    required this.mainTitle,
    required this.assetIcon,
    required this.onClose,
    required this.ref,
    super.key,
  });
  final String title;
  final String mainTitle;
  final String assetIcon;
  final VoidCallback onClose;
  final DashboardPageCubit ref;

  @override
  State<AchievementOverlay> createState() => _AchievementOverlayState();
}

class _AchievementOverlayState extends State<AchievementOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _showText = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(seconds: 2));
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _controller.forward().whenComplete(() => setState(() => _showText = true));
      },
    );
  }

  void _skipAnimation() {
    if (!_showText) {
      _controller.stop();
      setState(() => _showText = true);
    }
    else{
      widget.ref.showTrophyAnimation.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: _skipAnimation,
        child: ColoredBox(
          color: const Color(0x9963c6ae),
          child: SafeArea(
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: widget.onClose,
                  ),
                ),
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ScaleTransition(
                        scale: Tween(begin: 0.5, end: 1.2)
                            .animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut)),
                        child: AppSvgAsset(
                          svgAsset: widget.assetIcon,
                          size: AppSize.h70,
                        ),
                      ),
                      const SizedBox(height: 20),
                      AnimatedOpacity(
                        opacity: _showText ? 1 : 0,
                        duration: const Duration(milliseconds: 300),
                        child: Column(
                          children: [
                            Text(widget.mainTitle,
                              style: TextStyle(
                                  fontSize: AppSize.sp16,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                            ),
                            SpaceV(AppSize.h10),
                            Padding(
                              padding:  EdgeInsets.symmetric(horizontal: AppSize.w40),
                              child: Text(
                                widget.title,
                                style: TextStyle(
                                  fontSize: AppSize.sp14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
