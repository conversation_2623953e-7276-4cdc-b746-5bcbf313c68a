import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_clock_module/cubit/my_recovery_clock_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_clock_module/keys/clock_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MyRecoveryClockPage extends StatelessWidget {
  const MyRecoveryClockPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          MyRecoveryClockCubit()..convertSecondsToTime(Injector.instance<AppDB>().userModel?.user.timeSpent ?? 0),
      child: BlocBuilder<MyRecoveryClockCubit, MyRecoveryClockState>(
        builder: (ctx, state) {
          final ref = ctx.read<MyRecoveryClockCubit>();

          return AppScaffold(
            resizeToAvoidBottomInset: false,
            scaffoldKey: ref.scaffoldKey,
            isAudioPanelVisible: ref.isAudioPannelVisible,
            infoAudioUrl: ref.infoAudioUrl,
            drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
            appBar: CommonAppBar(
              onPrefixTap: () {
                ref.scaffoldKey.currentState?.openDrawer();
              },
              onSuffixTap: () {
                if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                  ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                }
              },
            ),
            body: ValueListenableBuilder(
              valueListenable: ref.hoursMinute,
              builder: (context, value, child) {
                final digits = (value ?? '').split(''); // Split the string into individual digits

                return ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      SingleChildScrollView(
                        child: Padding(
                          padding: EdgeInsets.only(
                            left: AppSize.w24,
                            right: AppSize.w24,
                            bottom: AppSize.h20,
                            top: AppSize.h24,
                          ),
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: Column(
                              children: [
                                AppTextWidget(
                                  ClockLocaleKeys.title.tr(),
                                  style: context.textTheme.titleSmall?.copyWith(
                                    fontSize: AppSize.sp14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SpaceV(AppSize.h14),
                                Divider(
                                  height: 1,
                                  color: context.themeColors.greyColor.withOpacity(.6),
                                ),
                                SpaceV(AppSize.h16),

                                AppTextWidget(
                                  (DynamicAssetLoader.getNestedValue(
                                    ClockLocaleKeys.text,
                                    context,
                                  ) as List)
                                      .join('\n\n'),
                                  style: context.textTheme.titleSmall,
                                ),
                                SpaceV(AppSize.h36),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w16,
                                  ),
                                  child: Column(
                                    children: [
                                      FittedBox(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            // Group 1: First two digits
                                            Row(
                                              children:
                                                  digits.take(2).map((digit) => NumberWidget(value: digit)).toList(),
                                            ),
                                            // Space between the groups
                                            SizedBox(width: AppSize.w14), // Adjust width as needed for spacing
                                            // Group 2: Last two digits
                                            Row(
                                              children:
                                                  digits.skip(2).map((digit) => NumberWidget(value: digit)).toList(),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SpaceV(AppSize.h24),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: SizedBox(
                                              width: MediaQuery.of(context).size.width,
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  AppTextWidget(
                                                    ClockLocaleKeys.hours.tr(),
                                                    style: context.textTheme.titleSmall?.copyWith(
                                                      fontSize: AppSize.sp14,
                                                      fontWeight: FontWeight.w600,
                                                      color: AppColors.greyColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          SpaceH(AppSize.w14),
                                          Expanded(
                                            child: SizedBox(
                                              width: MediaQuery.of(context).size.width,
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  AppTextWidget(
                                                    ClockLocaleKeys.minutes.tr(),
                                                    style: context.textTheme.titleSmall?.copyWith(
                                                      fontSize: AppSize.sp13,
                                                      fontWeight: FontWeight.w600,
                                                      color: AppColors.greyColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                SpaceV(AppSize.h16),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class NumberWidget extends StatelessWidget {
  const NumberWidget({
    required this.value,
    super.key,
  });
  final String value;
  @override
  Widget build(BuildContext context) {
    return Container(
      //   width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppColors.lightBlueColor,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      margin: const EdgeInsets.all(2),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppSize.h20, horizontal: AppSize.w20),
        child: Center(
          child: AppTextWidget(
            value,
            style: context.textTheme.headlineLarge?.copyWith(
              fontSize: AppSize.sp40,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
