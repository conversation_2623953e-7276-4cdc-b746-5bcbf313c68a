import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_clock_module/keys/clock_locale_keys.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'my_recovery_clock_cubit.freezed.dart';
part 'my_recovery_clock_state.dart';

class MyRecoveryClockCubit extends Cubit<MyRecoveryClockState> {
  MyRecoveryClockCubit() : super(const MyRecoveryClockState.initial()) {
    infoAudioUrl.value = ClockLocaleKeys.audio.tr();
  }

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<String?> hoursMinute = ValueNotifier(null);

  final scaffoldKey = GlobalKey<ScaffoldState>();

  String convertSecondsToTime(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    // final secondsFormatted = (duration.inSeconds % 60).toString().padLeft(2, '0');
    hoursMinute.value = '$hours$minutes';
    '$hours$minutes'.logD;
    return '$hours:$minutes';
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    hoursMinute.dispose();
    return super.close();
  }
}
