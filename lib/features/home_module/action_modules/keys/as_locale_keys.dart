import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';

class AsLocaleKeys {
  static const eiTitle = 'as.ei.title';
  static const eiInfoPanelsInformationText = 'as.ei.infoPanels.information.text';
  static const lsDsLocation = 'as.ds.labels.search';

  static const dsnoLocationPermission = 'as.ds.noLocationPermission';

  static const eiInfoPanelsInformationAudio = 'as.ei.infoPanels.information.audio';
  static const eiInfoPanelsLearnText = 'as.ei.infoPanels.learn.text';
  static const eiInfoPanelsLearnText1 = 'as.ei.infoPanels.learn.text.1';
  static const eiInfoPanelsLearnText2 = 'as.ei.infoPanels.learn.text.2';
  static const eiInfoPanelsLearnText3 = 'as.ei.infoPanels.learn.text.3';
  static const eiInfoPanelsLearnText4 = 'as.ei.infoPanels.learn.text.4';
  static const eiInfoPanelsLearnText5 = 'as.ei.infoPanels.learn.text.5';
  static const eiInfoPanelsLearnAudio = 'as.ei.infoPanels.learn.audio';
  static const eiRateBeforeAudio = 'as.ei.rateBefore.audio';
  static const eiRateBeforeDescription0 = 'as.ei.rateBefore.description';
  static const eiRateBeforeDescription1 = 'as.ei.rateBefore.description.1';
  static const eiRateBeforeAriaLabel = 'as.ei.rateBefore.ariaLabel';
  static const eiRateBeforeLabels0 = 'as.ei.rateBefore.labels';
  static const eiRateBeforeLabels1 = 'as.ei.rateBefore.labels.1';
  static const eiSelectVideoAudio = 'as.ei.selectVideo.audio';
  static const eiSelectVideoDescription = 'as.ei.selectVideo.description';
  static const eiSelectVideoLabelsBeach = 'as.ei.selectVideo.labels.beach';
  static const eiSelectVideoLabelsPark = 'as.ei.selectVideo.labels.park';
  static const eiRateAfterAudio = 'as.ei.rateAfter.audio';
  static const eiRateAfterDescription = 'as.ei.rateAfter.description';
  static const eiRateAfterAriaLabel = 'as.ei.rateAfter.ariaLabel';
  static const eiRateAfterLabels0 = 'as.ei.rateAfter.labels.0';
  static const eiRateAfterLabels1 = 'as.ei.rateAfter.labels.1';
  static const eiCompareAudioImproved = 'as.ei.compare.audio.improved';
  static const eiCompareAudioNotImproved = 'as.ei.compare.audio.notImproved';
  static const eiComparePreText = 'as.ei.compare.preText';
  static const eiCompareBefore = 'as.ei.compare.before';
  static const eiCompareAfter = 'as.ei.compare.after';
  static const eiComparePostTextImproved = 'as.ei.compare.postText.improved';
  static const eiComparePostTextNotImproved = 'as.ei.compare.postText.notImproved';
  static const eiVideoParkSrc = 'as.ei.video.park.src';
  static const eiVideoParkPoster = 'as.ei.video.park.poster';
  static const eiVideoParkPreview = 'as.ei.video.park.preview';
  static const eiVideoBeachSrc = 'as.ei.video.beach.src';
  static const eiVideoBeachPoster = 'as.ei.video.beach.poster';
  static const eiVideoBeachPreview = 'as.ei.video.beach.preview';
  static const eiSummaryTitle = 'as.ei.summary.title';
  static const eiSummaryAudio = 'as.ei.summary.audio';
  static const eiSummaryText = 'as.ei.summary.text';

  static const eiActionPlanTitle = 'as.ei.actionPlan.title';
  static const eiActionPlanAudio = 'as.ei.actionPlan.audio';
  static const eiActionPlanText = 'as.ei.actionPlan.text';
  static const eiActionPlanBullets = 'as.ei.actionPlan.bullets';
  static const eiActionPlanBullets1 = 'as.ei.actionPlan.bullets.1';
  static const eiActionPlanBullets2 = 'as.ei.actionPlan.bullets.2';
  static const eiActionPlanBullets3 = 'as.ei.actionPlan.bullets.3';
  static const eiActionPlanBullets4 = 'as.ei.actionPlan.bullets.4';
  static const eiActionPlanBullets5 = 'as.ei.actionPlan.bullets.5';
  static const eiActionPlanBullets6 = 'as.ei.actionPlan.bullets.6';
  static const eiErrorsRequired = 'as.ei.errors.required';
  static const eiErrorsVideo = 'as.ei.errors.video';

  static const psTitle = 'as.ps.title';
  static const psRateBeforeAudio = 'as.ps.rateBefore.audio';
  static const psRateBeforeDescription0 = 'as.ps.rateBefore.description';
  static const psRateBeforeDescription1 = 'as.ps.rateBefore.description.1';
  static const psRateBeforeAriaLabel = 'as.ps.rateBefore.ariaLabel';
  static const psRateBeforeLabels0 = 'as.ps.rateBefore.labels';
  static const psRateBeforeLabels1 = 'as.ps.rateBefore.labels.1';
  static const psVideoTitles = 'as.ps.videoTitles';

  static const psRateAfterAudio = 'as.ps.rateAfter.audio';
  static const psRateAfterDescription = 'as.ps.rateAfter.description';
  static const psRateAfterAriaLabel = 'as.ps.rateAfter.ariaLabel';
  static const psRateAfterLabels0 = 'as.ps.rateAfter.labels.0';
  static const psRateAfterLabels1 = 'as.ps.rateAfter.labels.1';
  static const psCompareAudioImproved = 'as.ps.compare.audio.improved';
  static const psCompareAudioNotImproved = 'as.ps.compare.audio.notImproved';
  static const psComparePreText = 'as.ps.compare.preText';
  static const psCompareBefore = 'as.ps.compare.before';
  static const psCompareAfter = 'as.ps.compare.after';
  static const psComparePostTextImproved = 'as.ps.compare.postText.improved';
  static const psComparePostTextNotImproved = 'as.ps.compare.postText.notImproved';
  static const psInfoPanelsInformationText = 'as.ps.infoPanels.information.text';

  static const psInfoPanelsInformationAudio = 'as.ps.infoPanels.information.audio';
  static const psInfoPanelsLearnText = 'as.ps.infoPanels.learn.text';
  static const psInfoPanelsLearnText1 = 'as.ps.infoPanels.learn.text.1';
  static const psInfoPanelsLearnText2 = 'as.ps.infoPanels.learn.text.2';
  static const psInfoPanelsLearnText3 = 'as.ps.infoPanels.learn.text.3';
  static const psInfoPanelsLearnAudio = 'as.ps.infoPanels.learn.audio';
  static const psVideosSrc = 'as.ps.videos';
  static const psVideosPoster = 'as.ps.videos.poster';
  static const psVideos1Src = 'as.ps.videos.1.src';
  static const psVideos1Poster = 'as.ps.videos.1.poster';
  static const psVideos2Src = 'as.ps.videos.2.src';
  static const psVideos2Poster = 'as.ps.videos.2.poster';
  static const psSummaryTitle = 'as.ps.summary.title';
  static const psSummaryAudio = 'as.ps.summary.audio';
  static const psSummaryText = 'as.ps.summary.text';

  static const psActionPlanTitle = 'as.ps.actionPlan.title';
  static const psActionPlanAudio = 'as.ps.actionPlan.audio';
  static const psActionPlanText = 'as.ps.actionPlan.text';
  static const psActionPlanBullets = 'as.ps.actionPlan.bullets';

  static const psErrorsRequired = 'as.ps.errors.required';

  static const lsTitle = 'as.ls.title';
  static const lsInfoPanelsInformationText = 'as.ls.infoPanels.information.text';

  static const lsInfoPanelsInformationAudio = 'as.ls.infoPanels.information.audio';
  static const lsInfoPanelsLearnText = 'as.ls.infoPanels.learn.text';

  static const lsInfoPanelsLearnAudio = 'as.ls.infoPanels.learn.audio';
  static const lsDays0 = 'as.ls.days.0';
  static const lsDays1 = 'as.ls.days.1';
  static const lsDays2 = 'as.ls.days.2';
  static const lsDays3 = 'as.ls.days.3';
  static const lsDays4 = 'as.ls.days.4';
  static const lsDays5 = 'as.ls.days.5';
  static const lsDays6 = 'as.ls.days.6';
  static const lsDays = 'as.ls.days';
  static const lsMountainsTitle = 'as.ls.mountains.title';
  static const lsMountainsAudio = 'as.ls.mountains.audio';
  static const lsMountainsText = 'as.ls.mountains.text';
  static const lsMountainsOptionsEverestLabel = 'as.ls.mountains.options.Everest.label';
  static const lsMountainsOptionsEverestImg = 'as.ls.mountains.options.Everest.img';
  static const lsMountainsOptionsKilimanjaroLabel = 'as.ls.mountains.options.Kilimanjaro.label';
  static const lsMountainsOptionsKilimanjaroImg = 'as.ls.mountains.options.Kilimanjaro.img';
  static const lsMountainsOptionsMachuLabel = 'as.ls.mountains.options.Machu Picchu.label';
  static const lsMountainsOptionsMachuImg = 'as.ls.mountains.options.Machu Picchu.img';
  static const lsMountainsOptionsMatterhornLabel = 'as.ls.mountains.options.Matterhorn.label';
  static const lsMountainsOptionsMatterhornImg = 'as.ls.mountains.options.Matterhorn.img';
  static const lsIdentifyGoalTitle = 'as.ls.identifyGoal.title';
  static const lsIdentifyGoalAudio = 'as.ls.identifyGoal.audio';
  static const lsIdentifyGoalCheck = 'as.ls.identifyGoal.check';
  static const lsIdentifyGoalOptions0 = 'as.ls.identifyGoal.options.0';
  static const lsIdentifyGoalOptions1 = 'as.ls.identifyGoal.options.1';
  static const lsQuestionsLifeGoalLabel = 'as.ls.identifyGoal.questions.lifeGoal.label';
  static const lsQuestionsLifeGoalInfoPanelAudio = 'as.ls.identifyGoal.questions.lifeGoal.infoPanel.audio';
  static const lsQuestionsLifeGoalInfoPanelHtml = 'as.ls.identifyGoal.questions.lifeGoal.infoPanel.html';

  static const lsQuestionsNextStepLabel = 'as.ls.identifyGoal.questions.nextStep.label';
  static const lsQuestionsNextStepInfoPanelAudio = 'as.ls.identifyGoal.questions.nextStep.infoPanel.audio';
  static const lsQuestionsNextStepInfoPanelHtml = 'as.ls.identifyGoal.questions.nextStep.infoPanel.html';

  static const lsQuestionsQ1Label = 'as.ls.identifyGoal.questions.q1.label';
  static const lsQuestionsQ1Err = 'as.ls.identifyGoal.questions.q1.err';
  static const lsQuestionsQ1Audio = 'as.ls.identifyGoal.questions.q1.audio';

  static const lsQuestionsQ2Label = 'as.ls.identifyGoal.questions.q2.label';
  static const lsQuestionsQ2Err = 'as.ls.identifyGoal.questions.q2.err';
  static const lsQuestionsQ2Audio = 'as.ls.identifyGoal.questions.q2.audio';

  static const lsQuestionsQ3Label = 'as.ls.identifyGoal.questions.q3.label';
  static const lsQuestionsQ3Err = 'as.ls.identifyGoal.questions.q3.err';
  static const lsQuestionsQ3Audio = 'as.ls.identifyGoal.questions.q3.audio';

  static const lsQuestionsQ4Label = 'as.ls.identifyGoal.questions.q4.label';
  static const lsQuestionsQ4Err = 'as.ls.identifyGoal.questions.q4.err';
  static const lsQuestionsQ4Audio = 'as.ls.identifyGoal.questions.q4.audio';

  static const lsIdentifyBarrierTitle = 'as.ls.identifyBarrier.title';
  static const lsIdentifyBarrierAudio = 'as.ls.identifyBarrier.audio';
  static const lsIdentifyBarrierText = 'as.ls.identifyBarrier.text';
  static const lsIdentifyBarrierOptions = 'as.ls.identifyBarrier.options';

  static const lsOvercomeBarrierTitle = 'as.ls.overcomeBarrier.title';
  static const lsOvercomeBarrierAudio = 'as.ls.overcomeBarrier.audio';

  static const lsOvercomeBarrierCanSay = 'as.ls.overcomeBarrier.canSay';
  static const lsOvercomeBarrierCanDo = 'as.ls.overcomeBarrier.canDo';
  static const lsOvercomeBarrierCommit = 'as.ls.overcomeBarrier.commit';

  static const lsOvercomeBarrierCommitText = 'as.ls.overcomeBarrier.commitText';
  static const lsOvercomeBarrierCommitText1 = 'as.ls.overcomeBarrier.commitText.1';

  static const lsOvercomeBarrierSelectDay = 'as.ls.overcomeBarrier.selectDay';
  static const lsOvercomeBarrierSelectTime = 'as.ls.overcomeBarrier.selectTime';

  static const lsOvercomeBarrierLabelsSelectDay = 'as.ls.overcomeBarrier.labels.selectDay';
  static const lsOvercomeBarrierLabelsSelectHour = 'as.ls.overcomeBarrier.labels.selectHour';
  static const lsOvercomeBarrierLabelsSelectMinute = 'as.ls.overcomeBarrier.labels.selectMinute';
  static const lsOvercomeBarrierLabelsAddDay = 'as.ls.overcomeBarrier.labels.addDay';
  static const lsOvercomeBarrierLabelsRemoveDay = 'as.ls.overcomeBarrier.labels.removeDay';
  static const lsOvercomeBarrierLabelsAddHour = 'as.ls.overcomeBarrier.labels.addHour';
  static const lsOvercomeBarrierLabelsRemoveHour = 'as.ls.overcomeBarrier.labels.removeHour';
  static const lsOvercomeBarrierLabelsAddMinute = 'as.ls.overcomeBarrier.labels.addMinute';
  static const lsOvercomeBarrierLabelsRemoveMinute = 'as.ls.overcomeBarrier.labels.removeMinute';

  static const lsOvercomeBarrierBarriers = 'as.ls.overcomeBarrier.barriers';
  static const lsOvercomeBarrierBarriers0CanSay1 = 'as.ls.overcomeBarrier.barriers.0.canSay.1';
  static const lsOvercomeBarrierBarriers0CanDo0 = 'as.ls.overcomeBarrier.barriers.0.canDo.0';
  static const lsOvercomeBarrierBarriers0CanDo1 = 'as.ls.overcomeBarrier.barriers.0.canDo.1';

  static const lsOvercomeBarrierBarriers1CanSay0 = 'as.ls.overcomeBarrier.barriers.1.canSay.0';
  static const lsOvercomeBarrierBarriers1CanSay1 = 'as.ls.overcomeBarrier.barriers.1.canSay.1';
  static const lsOvercomeBarrierBarriers1CanDo0 = 'as.ls.overcomeBarrier.barriers.1.canDo.0';
  static const lsOvercomeBarrierBarriers1CanDo1 = 'as.ls.overcomeBarrier.barriers.1.canDo.1';

  static const lsOvercomeBarrierBarriers2CanSay0 = 'as.ls.overcomeBarrier.barriers.2.canSay.0';
  static const lsOvercomeBarrierBarriers2CanSay1 = 'as.ls.overcomeBarrier.barriers.2.canSay.1';
  static const lsOvercomeBarrierBarriers2CanDo0 = 'as.ls.overcomeBarrier.barriers.2.canDo.0';
  static const lsOvercomeBarrierBarriers2CanDo1 = 'as.ls.overcomeBarrier.barriers.2.canDo.1';
  static const lsOvercomeBarrierBarriers2CanDo2 = 'as.ls.overcomeBarrier.barriers.2.canDo.2';
  static const lsOvercomeBarrierBarriers2CanDo3 = 'as.ls.overcomeBarrier.barriers.2.canDo.3';

  static const lsOvercomeBarrierBarriers3CanSay0 = 'as.ls.overcomeBarrier.barriers.3.canSay.0';
  static const lsOvercomeBarrierBarriers3CanSay1 = 'as.ls.overcomeBarrier.barriers.3.canSay.1';
  static const lsOvercomeBarrierBarriers3CanDo0 = 'as.ls.overcomeBarrier.barriers.3.canDo.0';
  static const lsOvercomeBarrierBarriers3CanDo1 = 'as.ls.overcomeBarrier.barriers.3.canDo.1';
  static const lsOvercomeBarrierBarriers3CanDo2 = 'as.ls.overcomeBarrier.barriers.3.canDo.2';

  static const lsOvercomeBarrierBarriers4CanSay0 = 'as.ls.overcomeBarrier.barriers.4.canSay.0';
  static const lsOvercomeBarrierBarriers4CanSay1 = 'as.ls.overcomeBarrier.barriers.4.canSay.1';
  static const lsOvercomeBarrierBarriers4CanDo0 = 'as.ls.overcomeBarrier.barriers.4.canDo.0';
  static const lsOvercomeBarrierBarriers4CanDo1 = 'as.ls.overcomeBarrier.barriers.4.canDo.1';

  static const lsOvercomeBarrierBarriers5CanSay0 = 'as.ls.overcomeBarrier.barriers.5.canSay.0';
  static const lsOvercomeBarrierBarriers5CanSay1 = 'as.ls.overcomeBarrier.barriers.5.canSay.1';
  static const lsOvercomeBarrierBarriers5CanDo0 = 'as.ls.overcomeBarrier.barriers.5.canDo.0';
  static const lsOvercomeBarrierBarriers5CanDo1 = 'as.ls.overcomeBarrier.barriers.5.canDo.1';

  static const lsOvercomeBarrierBarriers6CanSay0 = 'as.ls.overcomeBarrier.barriers.6.canSay.0';
  static const lsOvercomeBarrierBarriers6CanSay1 = 'as.ls.overcomeBarrier.barriers.6.canSay.1';
  static const lsOvercomeBarrierBarriers6CanDo0 = 'as.ls.overcomeBarrier.barriers.6.canDo.0';
  static const lsOvercomeBarrierBarriers6CanDo1 = 'as.ls.overcomeBarrier.barriers.6.canDo.1';

  static const lsOvercomeBarrierBarriers7CanSay0 = 'as.ls.overcomeBarrier.barriers.7.canSay.0';
  static const lsOvercomeBarrierBarriers7CanSay1 = 'as.ls.overcomeBarrier.barriers.7.canSay.1';
  static const lsOvercomeBarrierBarriers7CanDo0 = 'as.ls.overcomeBarrier.barriers.7.canDo.0';
  static const lsOvercomeBarrierBarriers7CanDo1 = 'as.ls.overcomeBarrier.barriers.7.canDo.1';

  static const lsQuestionsWhyTitle = 'as.ls.overcomeBarrier.questions.why.title';
  static const lsQuestionsWhyText = 'as.ls.overcomeBarrier.questions.why.text';

  static const lsQuestionsWhatTitle = 'as.ls.overcomeBarrier.questions.what.title';
  static const lsQuestionsWhatText = 'as.ls.overcomeBarrier.questions.what.text';

  static const lsQuestionsWhoTitle = 'as.ls.overcomeBarrier.questions.who.title';
  static const lsQuestionsWhoText = 'as.ls.overcomeBarrier.questions.who.text';

  static const lsMotivateTitle = 'as.ls.motivate.title';
  static const lsMotivateAudio = 'as.ls.motivate.audio';

  static const lsMotivateText = 'as.ls.motivate.text';
  static const lsMotivateText1 = 'as.ls.motivate.text.1';

  static const lsMotivateBenefits = 'as.ls.motivate.benefits';

  static const lsMotivateOther = 'as.ls.motivate.other';

  static const lsSummaryTitle = 'as.ls.summary.title';
  static const lsSummaryAudio = 'as.ls.summary.audio';

  static const lsSummaryText = 'as.ls.summary.text';
  static const lsSummaryTextApp = 'as.ls.summary.textApp';
  static const lsSummaryAudioApp = 'as.ls.summary.audioApp';

  static const lsActionPlanTitle = 'as.ls.actionPlan.title';
  static const lsActionPlanAudio = 'as.ls.actionPlan.audio';

  static const lsActionPlanText = 'as.ls.actionPlan.text';

  static const lsActionPlanLifeGoal = 'as.ls.actionPlan.lifeGoal';
  static const lsActionPlanNextStep = 'as.ls.actionPlan.nextStep';
  static const lsActionPlanBarrier = 'as.ls.actionPlan.barrier';
  static const lsActionPlanPlanned = 'as.ls.actionPlan.planned';
  static const lsActionPlanCommit = 'as.ls.actionPlan.commit';
  static const lsActionPlanBenefits = 'as.ls.actionPlan.benefits';

  static const lsErrorsSelectMountain = 'as.ls.errors.selectMountain';
  static const lsErrorsRequired = 'as.ls.errors.required';
  static const lsErrorsYesRequired = 'as.ls.errors.yesRequired';
  static const lsErrorsFuture = 'as.ls.errors.future';
  static const lsErrorsMinimumOne = 'as.ls.errors.minimumOne';

  static const lsNtTitle = 'as.nt.title';

  static const lsNtInfoPanelsInformationAudio = 'as.nt.infoPanels.information.audio';
  static const lsNtInfoPanelsInformationText = 'as.nt.infoPanels.information.text';

  static const lsNtInfoPanelsLearnAudio = 'as.nt.infoPanels.learn.audio';
  static const lsNtInfoPanelsLearnText = 'as.nt.infoPanels.learn.text';

  static const lsNtMindTrapsVideoSrc = 'as.nt.mindTraps.video.src';
  static const lsNtMindTrapsVideoPoster = 'as.nt.mindTraps.video.poster';

  static const lsNtMindTrapsTitle = 'as.nt.mindTraps.title';
  static const lsNtMindTrapsAudio = 'as.nt.mindTraps.audio';

  static const lsNtMindTrapsOptionsText = 'as.nt.mindTraps.options';

  static const lsNtUnderstandBlameTitle = 'as.nt.understand.blame.title';
  static const lsNtUnderstandBlameAudio = 'as.nt.understand.blame.audio';
  static const lsNtUnderstandBlameText = 'as.nt.understand.blame.text';

  static const lsNtUnderstandHelplessTitle = 'as.nt.understand.helpless.title';
  static const lsNtUnderstandHelplessAudio = 'as.nt.understand.helpless.audio';
  static const lsNtUnderstandHelplessText = 'as.nt.understand.helpless.text';

  static const lsNtUnderstandCatastropheTitle = 'as.nt.understand.catastrophe.title';
  static const lsNtUnderstandCatastropheAudio = 'as.nt.understand.catastrophe.audio';
  static const lsNtUnderstandCatastropheText0 = 'as.nt.understand.catastrophe.text';

  static const lsNtUnderstandGuiltTitle = 'as.nt.understand.guilt.title';
  static const lsNtUnderstandGuiltAudio = 'as.nt.understand.guilt.audio';
  static const lsNtUnderstandGuiltText = 'as.nt.understand.guilt.text';

  static const lsNtUnderstandAllOrNothingTitle = 'as.nt.understand.all-or-nothing.title';
  static const lsNtUnderstandAllOrNothingAudio = 'as.nt.understand.all-or-nothing.audio';
  static const lsNtUnderstandAllOrNothingText = 'as.nt.understand.all-or-nothing.text';

  static const lsNtLookTitle = 'as.nt.look.title';
  static const lsNtLookSubtitle = 'as.nt.look.subtitle';

  static const lsNtLookOptions0 = 'as.nt.look.options.0';
  static const lsNtLookOptions1 = 'as.nt.look.options.1';

  static const lsNtLookBlameAudio = 'as.nt.look.blame.audio';
  static const lsNtLookBlameText = 'as.nt.look.blame.text';

  static const lsNtLookBlameQ1 = 'as.nt.look.blame.questions.q1';
  static const lsNtLookBlameQ2 = 'as.nt.look.blame.questions.q2';
  static const lsNtLookBlameQ3 = 'as.nt.look.blame.questions.q3';
  static const lsNtLookBlameQ4 = 'as.nt.look.blame.questions.q4';
  static const lsNtLookBlameOther = 'as.nt.look.blame.questions.other';

  static const lsNtLookHelplessAudio = 'as.nt.look.helpless.audio';
  static const lsNtLookHelplessText = 'as.nt.look.helpless.text';

  static const lsNtLookHelplessQ1 = 'as.nt.look.helpless.questions.q1';
  static const lsNtLookHelplessQ2 = 'as.nt.look.helpless.questions.q2';
  static const lsNtLookHelplessQ3 = 'as.nt.look.helpless.questions.q3';
  static const lsNtLookHelplessQ4 = 'as.nt.look.helpless.questions.q4';
  static const lsNtLookHelplessOther = 'as.nt.look.helpless.questions.other';

  static const lsNtLookCatastropheAudio = 'as.nt.look.catastrophe.audio';
  static const lsNtLookCatastropheText = 'as.nt.look.catastrophe.text';

  static const lsNtLookCatastropheQ1 = 'as.nt.look.catastrophe.questions.q1';
  static const lsNtLookCatastropheQ2 = 'as.nt.look.catastrophe.questions.q2';
  static const lsNtLookCatastropheQ3 = 'as.nt.look.catastrophe.questions.q3';
  static const lsNtLookCatastropheQ4 = 'as.nt.look.catastrophe.questions.q4';
  static const lsNtLookCatastropheOther = 'as.nt.look.catastrophe.questions.other';

  static const lsNtLookGuiltAudio = 'as.nt.look.guilt.audio';
  static const lsNtLookGuiltText = 'as.nt.look.guilt.text';

  static const lsNtLookGuiltQ1 = 'as.nt.look.guilt.questions.q1';
  static const lsNtLookGuiltQ2 = 'as.nt.look.guilt.questions.q2';
  static const lsNtLookGuiltQ3 = 'as.nt.look.guilt.questions.q3';
  static const lsNtLookGuiltQ4 = 'as.nt.look.guilt.questions.q4';
  static const lsNtLookGuiltOther = 'as.nt.look.guilt.questions.other';

  static const lsNtLookAllOrNothingAudio = 'as.nt.look.all-or-nothing.audio';
  static const lsNtLookAllOrNothingText = 'as.nt.look.all-or-nothing.text';

  static const lsNtLookAllOrNothingQ1 = 'as.nt.look.all-or-nothing.questions.q1';
  static const lsNtLookAllOrNothingQ2 = 'as.nt.look.all-or-nothing.questions.q2';
  static const lsNtLookAllOrNothingQ3 = 'as.nt.look.all-or-nothing.questions.q3';
  static const lsNtLookAllOrNothingQ4 = 'as.nt.look.all-or-nothing.questions.q4';
  static const lsNtLookAllOrNothingOther = 'as.nt.look.all-or-nothing.questions.other';

  static const lsNtSetTitle = 'as.nt.set.title';

  static const lsNtSetOptions0 = 'as.nt.set.options.0';
  static const lsNtSetOptions1 = 'as.nt.set.options.1';

  static const lsNtSetSubtitlesCanAsk = 'as.nt.set.subtitles.canAsk';
  static const lsNtSetSubtitlesCanDo = 'as.nt.set.subtitles.canDo';

  static const lsNtSetBlameTitle = 'as.nt.set.blame.title';
  static const lsNtSetBlameAudio = 'as.nt.set.blame.audio';

  static const lsNtSetBlameQ1 = 'as.nt.set.blame.questions.q1';
  static const lsNtSetBlameQ2 = 'as.nt.set.blame.questions.q2';
  static const lsNtSetBlameCanDo = 'as.nt.set.blame.questions.canDo';

  static const lsNtSetHelplessTitle = 'as.nt.set.helpless.title';
  static const lsNtSetHelplessAudio = 'as.nt.set.helpless.audio';

  static const lsNtSetHelplessQ1 = 'as.nt.set.helpless.questions.q1';
  static const lsNtSetHelplessQ2 = 'as.nt.set.helpless.questions.q2';
  static const lsNtSetHelplessCanDo = 'as.nt.set.helpless.questions.canDo';

  static const lsNtSetCatastropheTitle = 'as.nt.set.catastrophe.title';
  static const lsNtSetCatastropheAudio = 'as.nt.set.catastrophe.audio';

  static const lsNtSetCatastropheQ1 = 'as.nt.set.catastrophe.questions.q1';
  static const lsNtSetCatastropheQ2 = 'as.nt.set.catastrophe.questions.q2';
  static const lsNtSetCatastropheCanDo = 'as.nt.set.catastrophe.questions.canDo';

  static const lsNtSetGuiltTitle = 'as.nt.set.guilt.title';
  static const lsNtSetGuiltAudio = 'as.nt.set.guilt.audio';

  static const lsNtSetGuiltQ1 = 'as.nt.set.guilt.questions.q1';
  static const lsNtSetGuiltQ2 = 'as.nt.set.guilt.questions.q2';
  static const lsNtSetGuiltCanDo = 'as.nt.set.guilt.questions.canDo';

  static const lsNtSetAllOrNothingTitle = 'as.nt.set.all-or-nothing.title';
  static const lsNtSetAllOrNothingAudio = 'as.nt.set.all-or-nothing.audio';

  static const lsNtSetAllOrNothingQ1 = 'as.nt.set.all-or-nothing.questions.q1';
  static const lsNtSetAllOrNothingQ2 = 'as.nt.set.all-or-nothing.questions.q2';
  static const lsNtSetAllOrNothingCanDo = 'as.nt.set.all-or-nothing.questions.canDo';

  static const lsNtSaySubtitle = 'as.nt.say.subtitle';

  static const lsNtSayBlameTitle = 'as.nt.say.blame.title';
  static const lsNtSayBlameAudio = 'as.nt.say.blame.audio';

  static const lsNtSayBlameText = 'as.nt.say.blame.text';

  static const lsNtHelplessTitle = 'as.nt.say.helpless.title';
  static const lsNtHelplessAudio = 'as.nt.say.helpless.audio';

  static const lsNtHelplessText = 'as.nt.say.helpless.text';

  static const lsNtCatastropheTitle = 'as.nt.say.catastrophe.title';
  static const lsNtCatastropheAudio = 'as.nt.say.catastrophe.audio';

  static const lsNtCatastropheText = 'as.nt.say.catastrophe.text';

  static const lsNtGuiltTitle = 'as.nt.guilt.title';
  static const lsNtGuiltAudio = 'as.nt.guilt.audio';

  static const lsNtGuiltText = 'as.nt.say.guilt.text';
  static const lsNtGiltSayAudio = 'as.nt.say.guilt.audio';

  static const lsNtGuiltTitle2 = 'as.nt.say.guilt.title';

  static const lsNtAllOrNothingTitle = 'as.nt.say.all-or-nothing.title';
  static const lsNtAllOrNothingAudio = 'as.nt.say.all-or-nothing.audio';
  static const lsNtAllOrNothingText = 'as.nt.say.all-or-nothing.text';

  static const lsNtSummaryTitle = 'as.nt.summary.title';
  static const lsNtSummaryAudio = 'as.nt.summary.audio';

  static const lsNtSummaryText = 'as.nt.summary.text';

  static const lsNtActionPlanTitle = 'as.nt.actionPlan.title';
  static const lsNtActionPlanAudio = 'as.nt.actionPlan.audio';

  static const lsNtActionPlanText = 'as.nt.actionPlan.text';

  static const lsNtActionPlanSubtitlesAsk = 'as.nt.actionPlan.subtitles.ask';
  static const lsNtActionPlanSubtitlesEvidence = 'as.nt.actionPlan.subtitles.evidence';
  static const lsNtActionPlanSubtitlesSay = 'as.nt.actionPlan.subtitles.say';

  static const lsNtErrorsSelectMindTrap = 'as.nt.errors.selectMindTrap';
  static const lsNtErrorsRequired = 'as.nt.errors.required';

  static const lsDsTitle = 'as.ds.title';

  static const lsDsInfoPanelsInformationAudio = 'as.ds.infoPanels.information.audio';
  static const lsDsInfoPanelsInformationText = 'as.ds.infoPanels.information.text';

  static const lsDsInfoPanelsInformationVideo = 'as.ds.infoPanels.information.video.src';
  static const lsDsInfoPanelsInformationVideoposter = 'as.ds.infoPanels.information.video.poster';

  static const lsDsInfoPanelsLearnAudio = 'as.ds.infoPanels.learn.audio';
  static const lsDsInfoPanelsLearnText = 'as.ds.infoPanels.learn.text';

  static const lsDsInfoPanelsPrisonsInformationAudio = 'as.ds.infoPanelsPrisons.information.audio';
  static const lsDsInfoPanelsPrisonsInformationText0 = 'as.ls.ds.infoPanelsPrisons.information.text.0';
  static const lsDsInfoPanelsPrisonsInformationText1 = 'as.ls.ds.infoPanelsPrisons.information.text.1';
  static const lsDsInfoPanelsPrisonsInformationText2 = 'as.ls.ds.infoPanelsPrisons.information.text.2';
  static const lsDsInfoPanelsPrisonsInformationText3 = 'as.ls.ds.infoPanelsPrisons.information.text.3';
  static const lsDsInfoPanelsPrisonsInformationText4 = 'as.ls.ds.infoPanelsPrisons.information.text.4';

  static const lsDsInfoPanelsPrisonsLearnAudio = 'as.ls.ds.infoPanelsPrisons.learn.audio';
  static const lsDsInfoPanelsPrisonsLearnText0 = 'as.ls.ds.infoPanelsPrisons.learn.text.0';
  static const lsDsInfoPanelsPrisonsLearnText1 = 'as.ls.ds.infoPanelsPrisons.learn.text.1';
  static const lsDsInfoPanelsPrisonsLearnText2 = 'as.ls.ds.infoPanelsPrisons.learn.text.2';
  static const lsDsInfoPanelsPrisonsLearnText3 = 'as.ls.ds.infoPanelsPrisons.learn.text.3';
  static const lsDsInfoPanelsPrisonsLearnText4 = 'as.ls.ds.infoPanelsPrisons.learn.text.4';

  static const lsDsRecognise = 'as.ls.ds.recognise';
  static const lsDsAvoid = 'as.ls.ds.avoid';
  static const lsDsCope = 'as.ls.ds.cope';
  static const lsDsClickToAdd = 'as.ds.clickToAdd';
  static const lsDsAddRiskyPlace = 'as.ds.addRiskyPlace';

  static const lsDsQuestionsWhat = 'as.ds.questions.what';

  static const lsDsQuestionsWhyLabel = 'as.ds.questions.why.label';
  static const lsDsQuestionsWhyCustomLabel = 'as.ds.questions.why.customLabel';
  static const lsDsQuestionsWhyOptions = 'as.ds.questions.why.options';

  static const lsDsQuestionsHowLabel = 'as.ds.questions.how.label';
  static const lsDsQuestionsHowCustomLabel = 'as.ds.questions.how.customLabel';
  static const lsDsQuestionsHowIconLabel = 'as.ds.questions.how.iconLabel';
  static const lsDsQuestionsHowInfoPanelAudio = 'as.ds.questions.how.infoPanel.audio';
  static const lsDsQuestionsHowInfoPanelHtml = 'as.ds.questions.how.infoPanel.html';

  static const lsDsQuestionsHowOptions = 'as.ds.questions.how.options';

  static const lsDsLabelsEdit = 'as.ls.ds.labels.edit';
  static const lsDsLabelsSave = 'as.ls.ds.labels.save';
  static const lsDsLabelsDelete = 'as.ls.ds.labels.delete';
  static const lsDsLabelsSearch = 'as.ls.ds.labels.search';

  static const lsDsButtonsSave = 'as.ds.buttons.save';
  static const lsDsButtonsCancel = 'as.ls.ds.buttons.cancel';
  static const lsDsButtonsClear = 'as.ds.buttons.clear';

  static const lsDsSummaryTitle = 'as.ds.summary.title';
  static const lsDsSummaryAudio = 'as.ds.summary.audio';
  static const lsDsSummaryText = 'as.ds.summary.text';
  static const lsDsSummaryTextApp = 'as.ds.summary.textApp';
  static const lsDsSummaryAudioApp = 'as.ds.summary.audioApp';

  static const lsDsActionPlanTitle = 'as.ds.actionPlan.title';
  static const lsDsActionPlanAudio = 'as.ds.actionPlan.audio';
  static const lsDsActionPlanText = 'as.ds.actionPlan.text';

  static const lsDsErrorsMinimumOne = 'as.ds.errors.minimumOne';
  static const lsDsErrorsRequired = 'as.ds.errors.required';

  // static const lsDsLocation = 'as.ds.labels.search';

  static const lsUbTitle = 'as.ub.title';

  static const lsUbSubtitlesCalendar = 'as.ub.subtitles.calendar';
  static const lsUbSubtitlesActivityForEnjoyment = 'as.ub.subtitles.activityForEnjoyment';
  static const lsUbSubtitlesActivityForAchievement = 'as.ub.subtitles.activityForAchievement';

  static const lsUbInfoPanelsInformationAudio = 'as.ub.infoPanels.information.audio';
  static const lsUbInfoPanelsInformationText = 'as.ub.infoPanels.information.text';

  static const lsUbInfoPanelsInformationVideoSrc = 'as.ub.infoPanels.information.video.src';
  static const lsUbInfoPanelsInformationVideoPoster = 'as.ub.infoPanels.information.video.poster';

  static const lsUbInfoPanelsLearnAudio = 'as.ub.infoPanels.learn.audio';
  static const lsUbInfoPanelsLearnText = 'as.ub.infoPanels.learn.text';

  static const lsUbEnjoyment = 'as.ub.enjoyment';
  static const lsUbAchievement = 'as.ub.achievement';
  static const lsUbAddNew = 'as.ub.addNew';
  static const lsUbAddAnother = 'as.ub.addAnother';
  static const lsUbSelectTime = 'as.ub.selectTime';
  static const lsUbSelectActivity = 'as.ub.selectActivity';
  static const lsUbAddMyOwn = 'as.ub.addMyOwn';
  static const lsUbSelectOrAddOwn = 'as.ub.selectOrAddOwn';
  static const lsUbSelectWhatTime = 'as.ub.selectWhatTime';
  static const lsUbAddYourOwn = 'as.ub.addYourOwn';
  static const lsUbActivityName = 'as.ub.activityName';
  static const lsUbChooseColour = 'as.ub.chooseColour';
  static const lsUbYourActivity = 'as.ub.yourActivity';
  static const lsUbFuture = 'as.ub.future';
  static const lsUbCustomLogo = 'as.ub.customLogo';

  static const lsUbDays = 'as.ub.days';


  static const lsUbDaysMon = 'as.ub.days.MON';
  static const lsUbDaysTue = 'as.ub.days.TUE';
  static const lsUbDaysWed = 'as.ub.days.WED';
  static const lsUbDaysThur = 'as.ub.days.THUR';
  static const lsUbDaysFri = 'as.ub.days.FRI';
  static const lsUbDaysSat = 'as.ub.days.SAT';
  static const lsUbDaysSun = 'as.ub.days.SUN';

  static const lsUbLabelsBack = 'as.ls.ub.labels.back';
  static const lsUbLabelsSelectHour = 'as.ls.ub.labels.selectHour';
  static const lsUbLabelsSelectMinute = 'as.ls.ub.labels.selectMinute';
  static const lsUbLabelsAddHour = 'as.ls.ub.labels.addHour';
  static const lsUbLabelsRemoveHour = 'as.ls.ub.labels.removeHour';
  static const lsUbLabelsAddMinute = 'as.ls.ub.labels.addMinute';
  static const lsUbLabelsRemoveMinute = 'as.ls.ub.labels.removeMinute';

  static const lsUbButtonsAdd = 'as.ls.ub.buttons.add';
  static const lsUbButtonsCancel = 'as.ls.ub.buttons.cancel';
  static const lsUbButtonsSave = 'as.ls.ub.buttons.save';
  static const lsUbButtonsClear = 'as.ub.buttons.clear';
  static const lsUbButtonsSelectActivity = 'as.ub.buttons.selectActivity';
  static const lsUbButtonsAddMyOwn = 'as.ub.buttons.addMyOwn';

  static const lsUbSummaryTitle = 'as.ub.summary.title';
  static const lsUbSummaryAudio = 'as.ub.summary.audio';
  static const lsUbSummaryText = 'as.ub.summary.text';
  static const lsUbSummaryTextApp = 'as.ub.summary.textApp';
  static const lsUbSummaryAudioApp = 'as.ub.summary.audioApp';

  static const lsUbActionPlanTitle = 'as.ub.actionPlan.title';
  static const lsUbActionPlanAudio = 'as.ub.actionPlan.audio';
  static const lsUbActionPlanText0 = 'as.ub.actionPlan.text';

  // Activities - Enjoyment
  static const lsUbActivitiesEnjoymentListenToMusicName = 'as.ub.activities.enjoyment.Listen to music.name';
  static const lsUbactivities1 = 'as.ub.activities';
  static const lsUbActivitiesEnjoymentListenToMusicImg = 'as.ub.activities.enjoyment.Listen to music.img';
  static const lsUbActivitiesEnjoymentListenToMusicImgFull = 'as.ub.activities.enjoyment.Listen to music.imgFull';

  static const lsUbActivitiesEnjoymentTakeAWalkName = 'as.ub.activities.enjoyment.Take a walk.name';
  static const lsUbActivitiesEnjoymentTakeAWalkImg = 'as.ub.activities.enjoyment.Take a walk.img';
  static const lsUbActivitiesEnjoymentTakeAWalkImgFull = 'as.ub.activities.enjoyment.Take a walk.imgFull';

  static const lsUbActivitiesEnjoymentEatAGoodMealName = 'as.ub.activities.enjoyment.Eat a good meal.name';
  static const lsUbActivitiesEnjoymentEatAGoodMealImg = 'as.ub.activities.enjoyment.Eat a good meal.img';
  static const lsUbActivitiesEnjoymentEatAGoodMealImgFull = 'as.ub.activities.enjoyment.Eat a good meal.imgFull';

  static const lsUbActivitiesEnjoymentPlaySomeSportName = 'as.ub.activities.enjoyment.Play some sport.name';
  static const lsUbActivitiesEnjoymentPlaySomeSportImg = 'as.ub.activities.enjoyment.Play some sport.img';
  static const lsUbActivitiesEnjoymentPlaySomeSportImgFull = 'as.ub.activities.enjoyment.Play some sport.imgFull';

  static const lsUbActivitiesEnjoymentCallSomeoneName = 'as.ub.activities.enjoyment.Call someone.name';
  static const lsUbActivitiesEnjoymentCallSomeoneImg = 'as.ub.activities.enjoyment.Call someone.img';
  static const lsUbActivitiesEnjoymentCallSomeoneImgFull = 'as.ub.activities.enjoyment.Call someone.imgFull';

  static const lsUbActivitiesEnjoymentMeetAFriendName = 'as.ub.activities.enjoyment.Meet a friend.name';
  static const lsUbActivitiesEnjoymentMeetAFriendImg = 'as.ub.activities.enjoyment.Meet a friend.img';
  static const lsUbActivitiesEnjoymentMeetAFriendImgFull = 'as.ub.activities.enjoyment.Meet a friend.imgFull';

  static const lsUbActivitiesEnjoymentSeeMyFamilyName = 'as.ub.activities.enjoyment.See my family.name';
  static const lsUbActivitiesEnjoymentSeeMyFamilyImg = 'as.ub.activities.enjoyment.See my family.img';
  static const lsUbActivitiesEnjoymentSeeMyFamilyImgFull = 'as.ub.activities.enjoyment.See my family.imgFull';

  static const lsUbActivitiesEnjoymentWatchTVName = 'as.ub.activities.enjoyment.Watch TV.name';
  static const lsUbActivitiesEnjoymentWatchTVImg = 'as.ub.activities.enjoyment.Watch TV.img';
  static const lsUbActivitiesEnjoymentWatchTVImgFull = 'as.ub.activities.enjoyment.Watch TV.imgFull';

  static const lsUbActivitiesEnjoymentReadAMagazineName = 'as.ub.activities.enjoyment.Read a magazine.name';
  static const lsUbActivitiesEnjoymentReadAMagazineImg = 'as.ub.activities.enjoyment.Read a magazine.img';
  static const lsUbActivitiesEnjoymentReadAMagazineImgFull = 'as.ub.activities.enjoyment.Read a magazine.imgFull';

  static const lsUbActivitiesEnjoymentReadABookName = 'as.ub.activities.enjoyment.Read a book.name';
  static const lsUbActivitiesEnjoymentReadABookImg = 'as.ub.activities.enjoyment.Read a book.img';
  static const lsUbActivitiesEnjoymentReadABookImgFull = 'as.ub.activities.enjoyment.Read a book.imgFull';

  static const lsUbActivitiesEnjoymentGoToACafeName = 'as.ub.activities.enjoyment.Go to a cafe.name';
  static const lsUbActivitiesEnjoymentGoToACafeImg = 'as.ub.activities.enjoyment.Go to a cafe.img';
  static const lsUbActivitiesEnjoymentGoToACafeImgFull = 'as.ub.activities.enjoyment.Go to a cafe.imgFull';

  static const lsUbActivitiesEnjoymentGoToAParkName = 'as.ub.activities.enjoyment.Go to a park.name';
  static const lsUbActivitiesEnjoymentGoToAParkImg = 'as.ub.activities.enjoyment.Go to a park.img';
  static const lsUbActivitiesEnjoymentGoToAParkImgFull = 'as.ub.activities.enjoyment.Go to a park.imgFull';

  static const lsUbActivitiesEnjoymentGoShoppingName = 'as.ub.activities.enjoyment.Go shopping.name';
  static const lsUbActivitiesEnjoymentGoShoppingImg = 'as.ub.activities.enjoyment.Go shopping.img';
  static const lsUbActivitiesEnjoymentGoShoppingImgFull = 'as.ub.activities.enjoyment.Go shopping.imgFull';

  static const lsUbActivitiesEnjoymentEatATakeawayName = 'as.ub.activities.enjoyment.Eat a takeaway.name';
  static const lsUbActivitiesEnjoymentEatATakeawayImg = 'as.ub.activities.enjoyment.Eat a takeaway.img';
  static const lsUbActivitiesEnjoymentEatATakeawayImgFull = 'as.ub.activities.enjoyment.Eat a takeaway.imgFull';

  static const lsUbActivitiesEnjoymentSurfOnlineName = 'as.ub.activities.enjoyment.Surf online.name';
  static const lsUbActivitiesEnjoymentSurfOnlineImg = 'as.ub.activities.enjoyment.Surf online.img';
  static const lsUbActivitiesEnjoymentSurfOnlineImgFull = 'as.ub.activities.enjoyment.Surf online.imgFull';

  static const lsUbActivitiesEnjoymentConnectOnlineName = 'as.ub.activities.enjoyment.Connect online.name';
  static const lsUbActivitiesEnjoymentConnectOnlineImg = 'as.ub.activities.enjoyment.Connect online.img';
  static const lsUbActivitiesEnjoymentConnectOnlineImgFull = 'as.ub.activities.enjoyment.Connect online.imgFull';

  static const lsUbActivitiesEnjoymentEmailSomeoneName = 'as.ub.activities.enjoyment.Email someone.name';
  static const lsUbActivitiesEnjoymentEmailSomeoneImg = 'as.ub.activities.enjoyment.Email someone.img';
  static const lsUbActivitiesEnjoymentEmailSomeoneImgFull = 'as.ub.activities.enjoyment.Email someone.imgFull';

  static const lsUbActivitiesEnjoymentDoSomeGamingName = 'as.ub.activities.enjoyment.Do some gaming.name';
  static const lsUbActivitiesEnjoymentDoSomeGamingImg = 'as.ub.activities.enjoyment.Do some gaming.img';
  static const lsUbActivitiesEnjoymentDoSomeGamingImgFull = 'as.ub.activities.enjoyment.Do some gaming.imgFull';

  static const lsUbActivitiesEnjoymentPamperMyselfName = 'as.ub.activities.enjoyment.Pamper myself.name';
  static const lsUbActivitiesEnjoymentPamperMyselfImg = 'as.ub.activities.enjoyment.Pamper myself.img';
  static const lsUbActivitiesEnjoymentPamperMyselfImgFull = 'as.ub.activities.enjoyment.Pamper myself.imgFull';

  static const lsUbActivitiesEnjoymentTakeAHotBathName = 'as.ub.activities.enjoyment.Take a hot bath.name';
  static const lsUbActivitiesEnjoymentTakeAHotBathImg = 'as.ub.activities.enjoyment.Take a hot bath.img';
  static const lsUbActivitiesEnjoymentTakeAHotBathImgFull = 'as.ub.activities.enjoyment.Take a hot bath.imgFull';

  static const lsUbActivitiesEnjoymentPlayAGameName = 'as.ub.activities.enjoyment.Play a game.name';
  static const lsUbActivitiesEnjoymentPlayAGameImg = 'as.ub.activities.enjoyment.Play a game.img';
  static const lsUbActivitiesEnjoymentPlayAGameImgFull = 'as.ub.activities.enjoyment.Play a game.imgFull';

  static const lsUbActivitiesEnjoymentGoSwimmingName = 'as.ub.activities.enjoyment.Go swimming.name';
  static const lsUbActivitiesEnjoymentGoSwimmingImg = 'as.ub.activities.enjoyment.Go swimming.img';
  static const lsUbActivitiesEnjoymentGoSwimmingImgFull = 'as.ub.activities.enjoyment.Go swimming.imgFull';

  static const lsUbActivitiesEnjoymentSeeAMovieName = 'as.ub.activities.enjoyment.See a movie.name';
  static const lsUbActivitiesEnjoymentSeeAMovieImg = 'as.ub.activities.enjoyment.See a movie.img';
  static const lsUbActivitiesEnjoymentSeeAMovieImgFull = 'as.ub.activities.enjoyment.See a movie.imgFull';

  static const lsUbActivitiesEnjoymentDoGardeningName = 'as.ub.activities.enjoyment.Do gardening.name';
  static const lsUbActivitiesEnjoymentDoGardeningImg = 'as.ub.activities.enjoyment.Do gardening.img';
  static const lsUbActivitiesEnjoymentDoGardeningImgFull = 'as.ub.activities.enjoyment.Do gardening.imgFull';

  static const lsUbActivitiesEnjoymentGoBowlingName = 'as.ub.activities.enjoyment.Go bowling.name';
  static const lsUbActivitiesEnjoymentGoBowlingImg = 'as.ub.activities.enjoyment.Go bowling.img';
  static const lsUbActivitiesEnjoymentGoBowlingImgFull = 'as.ub.activities.enjoyment.Go bowling.imgFull';

  static const lsUbActivitiesEnjoymentGoDancingName = 'as.ub.activities.enjoyment.Go dancing.name';
  static const lsUbActivitiesEnjoymentGoDancingImg = 'as.ub.activities.enjoyment.Go dancing.img';
  static const lsUbActivitiesEnjoymentGoDancingImgFull = 'as.ub.activities.enjoyment.Go dancing.imgFull';

  static const lsUbActivitiesEnjoymentWatchSportName = 'as.ub.activities.enjoyment.Watch sport.name';
  static const lsUbActivitiesEnjoymentWatchSportImg = 'as.ub.activities.enjoyment.Watch sport.img';
  static const lsUbActivitiesEnjoymentWatchSportImgFull = 'as.ub.activities.enjoyment.Watch sport.imgFull';

  static const lsUbActivitiesEnjoymentRideABikeName = 'as.ub.activities.enjoyment.Ride a bike.name';
  static const lsUbActivitiesEnjoymentRideABikeImg = 'as.ub.activities.enjoyment.Ride a bike.img';
  static const lsUbActivitiesEnjoymentRideABikeImgFull = 'as.ub.activities.enjoyment.Ride a bike.imgFull';

  static const lsUbActivitiesEnjoymentGoFishingName = 'as.ub.activities.enjoyment.Go fishing.name';
  static const lsUbActivitiesEnjoymentGoFishingImg = 'as.ub.activities.enjoyment.Go fishing.img';
  static const lsUbActivitiesEnjoymentGoFishingImgFull = 'as.ub.activities.enjoyment.Go fishing.imgFull';

  static const lsUbActivitiesEnjoymentWalkADogName = 'as.ub.activities.enjoyment.Walk a dog.name';
  static const lsUbActivitiesEnjoymentWalkADogImg = 'as.ub.activities.enjoyment.Walk a dog.img';
  static const lsUbActivitiesEnjoymentWalkADogImgFull = 'as.ub.activities.enjoyment.Walk a dog.imgFull';

  static const lsUbActivitiesEnjoymentVisitATownName = 'as.ub.activities.enjoyment.Visit a town.name';
  static const lsUbActivitiesEnjoymentVisitATownImg = 'as.ub.activities.enjoyment.Visit a town.img';
  static const lsUbActivitiesEnjoymentVisitATownImgFull = 'as.ub.activities.enjoyment.Visit a town.imgFull';

  static const lsUbActivitiesEnjoymentGetOutdoorsName = 'as.ub.activities.enjoyment.Get outdoors.name';
  static const lsUbActivitiesEnjoymentGetOutdoorsImg = 'as.ub.activities.enjoyment.Get outdoors.img';
  static const lsUbActivitiesEnjoymentGetOutdoorsImgFull = 'as.ub.activities.enjoyment.Get outdoors.imgFull';

  static const lsUbActivitiesEnjoymentVisitALandmarkName = 'as.ub.activities.enjoyment.Visit a landmark.name';
  static const lsUbActivitiesEnjoymentVisitALandmarkImg = 'as.ub.activities.enjoyment.Visit a landmark.img';
  static const lsUbActivitiesEnjoymentVisitALandmarkImgFull = 'as.ub.activities.enjoyment.Visit a landmark.imgFull';

  static const lsUbActivitiesEnjoymentVisitABuildingName = 'as.ub.activities.enjoyment.Visit a building.name';
  static const lsUbActivitiesEnjoymentVisitABuildingImg = 'as.ub.activities.enjoyment.Visit a building.img';
  static const lsUbActivitiesEnjoymentVisitABuildingImgFull = 'as.ub.activities.enjoyment.Visit a building.imgFull';

  static const lsUbActivitiesEnjoymentGoToABeachName = 'as.ub.activities.enjoyment.Go to a beach.name';
  static const lsUbActivitiesEnjoymentGoToABeachImg = 'as.ub.activities.enjoyment.Go to a beach.img';
  static const lsUbActivitiesEnjoymentGoToABeachImgFull = 'as.ub.activities.enjoyment.Go to a beach.imgFull';

  static const lsUbActivitiesEnjoymentGoCampingName = 'as.ub.activities.enjoyment.Go camping.name';
  static const lsUbActivitiesEnjoymentGoCampingImg = 'as.ub.activities.enjoyment.Go camping.img';
  static const lsUbActivitiesEnjoymentGoCampingImgFull = 'as.ub.activities.enjoyment.Go camping.imgFull';

  static const lsUbActivitiesEnjoymentSeeAComedianName = 'as.ub.activities.enjoyment.See a comedian.name';
  static const lsUbActivitiesEnjoymentSeeAComedianImg = 'as.ub.activities.enjoyment.See a comedian.img';
  static const lsUbActivitiesEnjoymentSeeAComedianImgFull = 'as.ub.activities.enjoyment.See a comedian.imgFull';

  static const lsUbActivitiesEnjoymentPlaySomeMusicName = 'as.ub.activities.enjoyment.Play some music.name';
  static const lsUbActivitiesEnjoymentPlaySomeMusicImg = 'as.ub.activities.enjoyment.Play some music.img';
  static const lsUbActivitiesEnjoymentPlaySomeMusicImgFull = 'as.ub.activities.enjoyment.Play some music.imgFull';

  static const lsUbActivitiesEnjoymentGoToAConcertName = 'as.ub.activities.enjoyment.Go to a concert.name';
  static const lsUbActivitiesEnjoymentGoToAConcertImg = 'as.ub.activities.enjoyment.Go to a concert.img';
  static const lsUbActivitiesEnjoymentGoToAConcertImgFull = 'as.ub.activities.enjoyment.Go to a concert.imgFull';

  static const lsUbActivitiesEnjoymentDrawOrPaintName = 'as.ub.activities.enjoyment.Draw or paint.name';
  static const lsUbActivitiesEnjoymentDrawOrPaintImg = 'as.ub.activities.enjoyment.Draw or paint.img';
  static const lsUbActivitiesEnjoymentDrawOrPaintImgFull = 'as.ub.activities.enjoyment.Draw or paint.imgFull';

  static const lsUbActivitiesEnjoymentDoPhotographyName = 'as.ub.activities.enjoyment.Do photography.name';
  static const lsUbActivitiesEnjoymentDoPhotographyImg = 'as.ub.activities.enjoyment.Do photography.img';
  static const lsUbActivitiesEnjoymentDoPhotographyImgFull = 'as.ub.activities.enjoyment.Do photography.imgFull';

  static const lsUbActivitiesEnjoymentWriteALetterName = 'as.ub.activities.enjoyment.Write a letter.name';
  static const lsUbActivitiesEnjoymentWriteALetterImg = 'as.ub.activities.enjoyment.Write a letter.img';
  static const lsUbActivitiesEnjoymentWriteALetterImgFull = 'as.ub.activities.enjoyment.Write a letter.imgFull';

  static const lsUbActivitiesEnjoymentPraiseMyFaithName = 'as.ub.activities.enjoyment.Praise my faith.name';
  static const lsUbActivitiesEnjoymentPraiseMyFaithImg = 'as.ub.activities.enjoyment.Praise my faith.img';
  static const lsUbActivitiesEnjoymentPraiseMyFaithImgFull = 'as.ub.activities.enjoyment.Praise my faith.imgFull';

  static const lsUbActivitiesEnjoymentMeditateName = 'as.ub.activities.enjoyment.Meditate.name';
  static const lsUbActivitiesEnjoymentMeditateImg = 'as.ub.activities.enjoyment.Meditate.img';
  static const lsUbActivitiesEnjoymentMeditateImgFull = 'as.ub.activities.enjoyment.Meditate.imgFull';

  static const lsUbActivitiesEnjoymentBeMindfulName = 'as.ub.activities.enjoyment.Be mindful.name';
  static const lsUbActivitiesEnjoymentBeMindfulImg = 'as.ub.activities.enjoyment.Be mindful.img';
  static const lsUbActivitiesEnjoymentBeMindfulImgFull = 'as.ub.activities.enjoyment.Be mindful.imgFull';

  static const lsUbActivitiesEnjoymentDoArtsOrCraftsName = 'as.ub.activities.enjoyment.Do arts or crafts.name';
  static const lsUbActivitiesEnjoymentDoArtsOrCraftsImg = 'as.ub.activities.enjoyment.Do arts or crafts.img';
  static const lsUbActivitiesEnjoymentDoArtsOrCraftsImgFull = 'as.ub.activities.enjoyment.Do arts or crafts.imgFull';

  static const lsUbActivitiesEnjoymentGoToAMallName = 'as.ub.activities.enjoyment.Go to a mall.name';
  static const lsUbActivitiesEnjoymentGoToAMallImg = 'as.ub.activities.enjoyment.Go to a mall.img';
  static const lsUbActivitiesEnjoymentGoToAMallImgFull = 'as.ub.activities.enjoyment.Go to a mall.imgFull';

  static const lsUbActivitiesEnjoymentSeeAPlayName = 'as.ub.activities.enjoyment.See a play.name';
  static const lsUbActivitiesEnjoymentSeeAPlayImg = 'as.ub.activities.enjoyment.See a play.img';
  static const lsUbActivitiesEnjoymentSeeAPlayImgFull = 'as.ub.activities.enjoyment.See a play.imgFull';

  static const lsUbActivitiesEnjoymentVisitAMuseumName = 'as.ub.activities.enjoyment.Visit a museum.name';
  static const lsUbActivitiesEnjoymentVisitAMuseumImg = 'as.ub.activities.enjoyment.Visit a museum.img';
  static const lsUbActivitiesEnjoymentVisitAMuseumImgFull = 'as.ub.activities.enjoyment.Visit a museum.imgFull';

  static const lsUbActivitiesEnjoymentVisitAGalleryName = 'as.ub.activities.enjoyment.Visit a gallery.name';
  static const lsUbActivitiesEnjoymentVisitAGalleryImg = 'as.ub.activities.enjoyment.Visit a gallery.img';
  static const lsUbActivitiesEnjoymentVisitAGalleryImgFull = 'as.ub.activities.enjoyment.Visit a gallery.imgFull';

  static const lsUbAchievementsDoBreakingFreeName = 'as.ub.activities.achievement.Do Breaking Free.name';
  static const lsUbAchievementsDoBreakingFreeImg = 'as.ub.activities.achievement.Do Breaking Free.img';
  static const lsUbAchievementsDoBreakingFreeImgFull = 'as.ub.activities.achievement.Do Breaking Free.imgFull';

  static const lsUbAchievementsDoExerciseName = 'as.ub.activities.achievement.Do exercise.name';
  static const lsUbAchievementsDoExerciseImg = 'as.ub.activities.achievement.Do exercise.img';
  static const lsUbAchievementsDoExerciseImgFull = 'as.ub.activities.achievement.Do exercise.imgFull';

  static const lsUbAchievementsCleanMyHomeName = 'as.ub.activities.achievement.Clean my home.name';
  static const lsUbAchievementsCleanMyHomeImg = 'as.ub.activities.achievement.Clean my home.img';
  static const lsUbAchievementsCleanMyHomeImgFull = 'as.ub.activities.achievement.Clean my home.imgFull';

  static const lsUbAchievementsDoSomeIroningName = 'as.ub.activities.achievement.Do some ironing.name';
  static const lsUbAchievementsDoSomeIroningImg = 'as.ub.activities.achievement.Do some ironing.img';
  static const lsUbAchievementsDoSomeIroningImgFull = 'as.ub.activities.achievement.Do some ironing.imgFull';

  static const lsUbAchievementsShopForFoodName = 'as.ub.activities.achievement.Shop for food.name';
  static const lsUbAchievementsShopForFoodImg = 'as.ub.activities.achievement.Shop for food.img';
  static const lsUbAchievementsShopForFoodImgFull = 'as.ub.activities.achievement.Shop for food.imgFull';

  static const lsUbAchievementsCookAMealName = 'as.ub.activities.achievement.Cook a meal.name';
  static const lsUbAchievementsCookAMealImg = 'as.ub.activities.achievement.Cook a meal.img';
  static const lsUbAchievementsCookAMealImgFull = 'as.ub.activities.achievement.Cook a meal.imgFull';

  static const lsUbAchievementsAlterMyHomeName = 'as.ub.activities.achievement.Alter my home.name';
  static const lsUbAchievementsAlterMyHomeImg = 'as.ub.activities.achievement.Alter my home.img';
  static const lsUbAchievementsAlterMyHomeImgFull = 'as.ub.activities.achievement.Alter my home.imgFull';

  static const lsUbAchievementsDoSomeDIYName = 'as.ub.activities.achievement.Do some DIY.name';
  static const lsUbAchievementsDoSomeDIYImg = 'as.ub.activities.achievement.Do some DIY.img';
  static const lsUbAchievementsDoSomeDIYImgFull = 'as.ub.activities.achievement.Do some DIY.imgFull';

  static const lsUbAchievementsAttendWorkName = 'as.ub.activities.achievement.Attend work.name';
  static const lsUbAchievementsAttendWorkImg = 'as.ub.activities.achievement.Attend work.img';
  static const lsUbAchievementsAttendWorkImgFull = 'as.ub.activities.achievement.Attend work.imgFull';

  static const lsUbAchievementsAttendCollegeName = 'as.ub.activities.achievement.Attend college.name';
  static const lsUbAchievementsAttendCollegeImg = 'as.ub.activities.achievement.Attend college.img';
  static const lsUbAchievementsAttendCollegeImgFull = 'as.ub.activities.achievement.Attend college.imgFull';

  static const lsUbAchievementsHelpSomeoneName = 'as.ub.activities.achievement.Help someone.name';
  static const lsUbAchievementsHelpSomeoneImg = 'as.ub.activities.achievement.Help someone.img';
  static const lsUbAchievementsHelpSomeoneImgFull = 'as.ub.activities.achievement.Help someone.imgFull';

  static const lsUbAchievementsDoVolunteeringName = 'as.ub.activities.achievement.Do volunteering.name';
  static const lsUbAchievementsDoVolunteeringImg = 'as.ub.activities.achievement.Do volunteering.img';
  static const lsUbAchievementsDoVolunteeringImgFull = 'as.ub.activities.achievement.Do volunteering.imgFull';

  static const lsUbAchievementsWriteAListName = 'as.ub.activities.achievement.Write a list.name';
  static const lsUbAchievementsWriteAListImg = 'as.ub.activities.achievement.Write a list.img';
  static const lsUbAchievementsWriteAListImgFull = 'as.ub.activities.achievement.Write a list.imgFull';

  static const lsUbAchievementsWashUpName = 'as.ub.activities.achievement.Wash up.name';
  static const lsUbAchievementsWashUpImg = 'as.ub.activities.achievement.Wash up.img';
  static const lsUbAchievementsWashUpImgFull = 'as.ub.activities.achievement.Wash up.imgFull';

  static const lsUbAchievementsTidyUpName = 'as.ub.activities.achievement.Tidy up.name';
  static const lsUbAchievementsTidyUpImg = 'as.ub.activities.achievement.Tidy up.img';
  static const lsUbAchievementsTidyUpImgFull = 'as.ub.activities.achievement.Tidy up.imgFull';

  static const lsUbAchievementsWashSomethingName = 'as.ub.activities.achievement.Wash something.name';
  static const lsUbAchievementsWashSomethingImg = 'as.ub.activities.achievement.Wash something.img';
  static const lsUbAchievementsWashSomethingImgFull = 'as.ub.activities.achievement.Wash something.imgFull';

  static const lsUbAchievementsWriteABlogName = 'as.ub.activities.achievement.Write a blog.name';
  static const lsUbAchievementsWriteABlogImg = 'as.ub.activities.achievement.Write a blog.img';
  static const lsUbAchievementsWriteABlogImgFull = 'as.ub.activities.achievement.Write a blog.imgFull';

  static const lsUbAchievementsGo1DaySoberName = 'as.ub.activities.achievement.Go 1 day sober.name';
  static const lsUbAchievementsGo1DaySoberImg = 'as.ub.activities.achievement.Go 1 day sober.img';
  static const lsUbAchievementsGo1DaySoberImgFull = 'as.ub.activities.achievement.Go 1 day sober.imgFull';

  static const lsUbAchievementsGo7DaysSoberName = 'as.ub.activities.achievement.Go 7 days sober.name';
  static const lsUbAchievementsGo7DaysSoberImg = 'as.ub.activities.achievement.Go 7 days sober.img';
  static const lsUbAchievementsGo7DaysSoberImgFull = 'as.ub.activities.achievement.Go 7 days sober.imgFull';

  static const lsUbAchievementsGo30DaysSoberName = 'as.ub.activities.achievement.Go 30 days sober.name';
  static const lsUbAchievementsGo30DaysSoberImg = 'as.ub.activities.achievement.Go 30 days sober.img';
  static const lsUbAchievementsGo30DaysSoberImgFull = 'as.ub.activities.achievement.Go 30 days sober.imgFull';

  static const lsUbAchievementsBookAMeetingName = 'as.ub.activities.achievement.Book a meeting.name';
  static const lsUbAchievementsBookAMeetingImg = 'as.ub.activities.achievement.Book a meeting.img';
  static const lsUbAchievementsBookAMeetingImgFull = 'as.ub.activities.achievement.Book a meeting.imgFull';

  static const lsUbAchievementsPlanMyFinancesName = 'as.ub.activities.achievement.Plan my finances.name';
  static const lsUbAchievementsPlanMyFinancesImg = 'as.ub.activities.achievement.Plan my finances.img';
  static const lsUbAchievementsPlanMyFinancesImgFull = 'as.ub.activities.achievement.Plan my finances.imgFull';

  static const lsUbAchievementsCreateACVName = 'as.ub.activities.achievement.Create a CV.name';
  static const lsUbAchievementsCreateACVImg = 'as.ub.activities.achievement.Create a CV.img';
  static const lsUbAchievementsCreateACVImgFull = 'as.ub.activities.achievement.Create a CV.imgFull';

  static const lsUbAchievementsApplyForAJobName = 'as.ub.activities.achievement.Apply for a job.name';
  static const lsUbAchievementsApplyForAJobImg = 'as.ub.activities.achievement.Apply for a job.img';
  static const lsUbAchievementsApplyForAJobImgFull = 'as.ub.activities.achievement.Apply for a job.imgFull';

  static const lsUbAchievementsDoAnInterviewName = 'as.ub.activities.achievement.Do an interview.name';
  static const lsUbAchievementsDoAnInterviewImg = 'as.ub.activities.achievement.Do an interview.img';
  static const lsUbAchievementsDoAnInterviewImgFull = 'as.ub.activities.achievement.Do an interview.imgFull';

  static const lsUbAchievementsSeeADoctorName = 'as.ub.activities.achievement.See a doctor.name';
  static const lsUbAchievementsSeeADoctorImg = 'as.ub.activities.achievement.See a doctor.img';
  static const lsUbAchievementsSeeADoctorImgFull = 'as.ub.activities.achievement.See a doctor.imgFull';

  static const lsUbAchievementsSeeAPharmacistName = 'as.ub.activities.achievement.See a pharmacist.name';
  static const lsUbAchievementsSeeAPharmacistImg = 'as.ub.activities.achievement.See a pharmacist.img';
  static const lsUbAchievementsSeeAPharmacistImgFull = 'as.ub.activities.achievement.See a pharmacist.imgFull';

  static const lsUbAchievementsSeeADentistName = 'as.ub.activities.achievement.See a dentist.name';
  static const lsUbAchievementsSeeADentistImg = 'as.ub.activities.achievement.See a dentist.img';
  static const lsUbAchievementsSeeADentistImgFull = 'as.ub.activities.achievement.See a dentist.imgFull';

  static const lsUbAchievementsSeeAnOpticianName = 'as.ub.activities.achievement.See an optician.name';
  static const lsUbAchievementsSeeAnOpticianImg = 'as.ub.activities.achievement.See an optician.img';
  static const lsUbAchievementsSeeAnOpticianImgFull = 'as.ub.activities.achievement.See an optician.imgFull';

  static const lsUbAchievementsSeeACounsellorName = 'as.ub.activities.achievement.See a counsellor.name';
  static const lsUbAchievementsSeeACounsellorImg = 'as.ub.activities.achievement.See a counsellor.img';
  static const lsUbAchievementsSeeACounsellorImgFull = 'as.ub.activities.achievement.See a counsellor.imgFull';

  static const lsUbAchievementsAttendAGroupName = 'as.ub.activities.achievement.Attend a group.name';
  static const lsUbAchievementsAttendAGroupImg = 'as.ub.activities.achievement.Attend a group.img';
  static const lsUbAchievementsAttendAGroupImgFull = 'as.ub.activities.achievement.Attend a group.imgFull';

  static const lsUbAchievementsHaveAcupunctureName = 'as.ub.activities.achievement.Have acupuncture.name';
  static const lsUbAchievementsHaveAcupunctureImg = 'as.ub.activities.achievement.Have acupuncture.img';
  static const lsUbAchievementsHaveAcupunctureImgFull = 'as.ub.activities.achievement.Have acupuncture.imgFull';

  static const lsUbAchievementsSeeALawyerName = 'as.ub.activities.achievement.See a lawyer.name';
  static const lsUbAchievementsSeeALawyerImg = 'as.ub.activities.achievement.See a lawyer.img';
  static const lsUbAchievementsSeeALawyerImgFull = 'as.ub.activities.achievement.See a lawyer.imgFull';

  static const lsUbAchievementsGoToABankName = 'as.ub.activities.achievement.Go to a bank.name';
  static const lsUbAchievementsGoToABankImg = 'as.ub.activities.achievement.Go to a bank.img';
  static const lsUbAchievementsGoToABankImgFull = 'as.ub.activities.achievement.Go to a bank.imgFull';

  static const lsUbAchievementsFindANewHomeName = 'as.ub.activities.achievement.Find a new home.name';
  static const lsUbAchievementsFindANewHomeImg = 'as.ub.activities.achievement.Find a new home.img';
  static const lsUbAchievementsFindANewHomeImgFull = 'as.ub.activities.achievement.Find a new home.imgFull';

  static const lsUbAchievementsGoToALibraryName = 'as.ub.activities.achievement.Go to a library.name';
  static const lsUbAchievementsGoToALibraryImg = 'as.ub.activities.achievement.Go to a library.img';
  static const lsUbAchievementsGoToALibraryImgFull = 'as.ub.activities.achievement.Go to a library.imgFull';

  static const lsUbAchievementsAttendACourseName = 'as.ub.activities.achievement.Attend a course.name';
  static const lsUbAchievementsAttendACourseImg = 'as.ub.activities.achievement.Attend a course.img';
  static const lsUbAchievementsAttendACourseImgFull = 'as.ub.activities.achievement.Attend a course.imgFull';

  static const lsUbAchievementsGetAHaircutName = 'as.ub.activities.achievement.Get a haircut.name';
  static const lsUbAchievementsGetAHaircutImg = 'as.ub.activities.achievement.Get a haircut.img';
  static const lsUbAchievementsGetAHaircutImgFull = 'as.ub.activities.achievement.Get a haircut.imgFull';

  static const lsUbAchievementsHaveAShaveName = 'as.ub.activities.achievement.Have a shave.name';
  static const lsUbAchievementsHaveAShaveImg = 'as.ub.activities.achievement.Have a shave.img';
  static const lsUbAchievementsHaveAShaveImgFull = 'as.ub.activities.achievement.Have a shave.imgFull';

  static const lsUbAchievementsGrowPlantsName = 'as.ub.activities.achievement.Grow plants.name';
  static const lsUbAchievementsGrowPlantsImg = 'as.ub.activities.achievement.Grow plants.img';
  static const lsUbAchievementsGrowPlantsImgFull = 'as.ub.activities.achievement.Grow plants.imgFull';

  static const lsUbAchievementsGrowVegetablesName = 'as.ub.activities.achievement.Grow vegetables.name';
  static const lsUbAchievementsGrowVegetablesImg = 'as.ub.activities.achievement.Grow vegetables.img';
  static const lsUbAchievementsGrowVegetablesImgFull = 'as.ub.activities.achievement.Grow vegetables.imgFull';

  static const lsUbAchievementsAidMyCommunityName = 'as.ub.activities.achievement.Aid my community.name';
  static const lsUbAchievementsAidMyCommunityImg = 'as.ub.activities.achievement.Aid my community.img';
  static const lsUbAchievementsAidMyCommunityImgFull = 'as.ub.activities.achievement.Aid my community.imgFull';

  static const lsUbActivitiesPrisonsEnjoymentWatchTVName = 'as.ub.activities.prisons.enjoyment.Watch TV.name';
  static const lsUbActivitiesPrisonsEnjoymentWatchTVImg = 'as.ub.activities.prisons.enjoyment.Watch TV.img';

  static const lsUbActivitiesPrisonsEnjoymentPlaySomePoolName =
      'as.ub.activities.prisons.enjoyment.Play some pool.name';
  static const lsUbActivitiesPrisonsEnjoymentPlaySomePoolImg = 'as.ub.activities.prisons.enjoyment.Play some pool.img';

  static const lsUbActivitiesPrisonsEnjoymentGoToTheGymName = 'as.ub.activities.prisons.enjoyment.Go to the gym.name';
  static const lsUbActivitiesPrisonsEnjoymentGoToTheGymImg = 'as.ub.activities.prisons.enjoyment.Go to the gym.img';

  static const lsUbActivitiesPrisonsEnjoymentTalkToSomeoneName =
      'as.ub.activities.prisons.enjoyment.Talk to someone.name';
  static const lsUbActivitiesPrisonsEnjoymentTalkToSomeoneImg =
      'as.ub.activities.prisons.enjoyment.Talk to someone.img';

  static const lsUbActivitiesPrisonsEnjoymentHaveATreatName = 'as.ub.activities.prisons.enjoyment.Have a treat.name';
  static const lsUbActivitiesPrisonsEnjoymentHaveATreatImg = 'as.ub.activities.prisons.enjoyment.Have a treat.img';

  static const lsUbActivitiesPrisonsEnjoymentPhoneSomeoneName = 'as.ub.activities.prisons.enjoyment.Phone someone.name';
  static const lsUbActivitiesPrisonsEnjoymentPhoneSomeoneImg = 'as.ub.activities.prisons.enjoyment.Phone someone.img';

  static const lsUbActivitiesPrisonsEnjoymentArrangeAVisitName =
      'as.ub.activities.prisons.enjoyment.Arrange a visit.name';
  static const lsUbActivitiesPrisonsEnjoymentArrangeAVisitImg =
      'as.ub.activities.prisons.enjoyment.Arrange a visit.img';

  static const lsUbActivitiesPrisonsEnjoymentReadAPaperName = 'as.ub.activities.prisons.enjoyment.Read a paper.name';
  static const lsUbActivitiesPrisonsEnjoymentReadAPaperImg = 'as.ub.activities.prisons.enjoyment.Read a paper.img';

  static const lsUbActivitiesPrisonsEnjoymentDoExerciseName = 'as.ub.activities.prisons.enjoyment.Do exercise.name';
  static const lsUbActivitiesPrisonsEnjoymentDoExerciseImg = 'as.ub.activities.prisons.enjoyment.Do exercise.img';

  static const lsUbActivitiesPrisonsEnjoymentTakeAShowerName = 'as.ub.activities.prisons.enjoyment.Take a shower.name';
  static const lsUbActivitiesPrisonsEnjoymentTakeAShowerImg = 'as.ub.activities.prisons.enjoyment.Take a shower.img';

  static const lsUbActivitiesPrisonsEnjoymentPlayCardsName = 'as.ub.activities.prisons.enjoyment.Play cards.name';
  static const lsUbActivitiesPrisonsEnjoymentPlayCardsImg = 'as.ub.activities.prisons.enjoyment.Play cards.img';

  static const lsUbActivitiesPrisonsEnjoymentDoAPuzzleName = 'as.ub.activities.prisons.enjoyment.Do a puzzle.name';
  static const lsUbActivitiesPrisonsEnjoymentDoAPuzzleImg = 'as.ub.activities.prisons.enjoyment.Do a puzzle.img';

  static const lsUbActivitiesPrisonsEnjoymentWatchAMovieName = 'as.ub.activities.prisons.enjoyment.Watch a movie.name';
  static const lsUbActivitiesPrisonsEnjoymentWatchAMovieImg = 'as.ub.activities.prisons.enjoyment.Watch a movie.img';

  static const lsUbActivitiesPrisonsEnjoymentPlayMusicName = 'as.ub.activities.prisons.enjoyment.Play music.name';
  static const lsUbActivitiesPrisonsEnjoymentPlayMusicImg = 'as.ub.activities.prisons.enjoyment.Play music.img';

  static const lsUbActivitiesPrisonsEnjoymentDoArtsOrCraftsName =
      'as.ub.activities.prisons.enjoyment.Do arts or crafts.name';
  static const lsUbActivitiesPrisonsEnjoymentDoArtsOrCraftsImg =
      'as.ub.activities.prisons.enjoyment.Do arts or crafts.img';

  static const lsUbActivitiesPrisonsAchievementTidyMyCellName =
      'as.ub.activities.prisons.achievement.Tidy my cell.name';
  static const lsUbActivitiesPrisonsAchievementTidyMyCellImg = 'as.ub.activities.prisons.achievement.Tidy my cell.img';

  static const lsUbActivitiesPrisonsAchievementWorkName = 'as.ub.activities.prisons.achievement.Work.name';
  static const lsUbActivitiesPrisonsAchievementWorkImg = 'as.ub.activities.prisons.achievement.Work.img';

  static const lsUbActivitiesPrisonsAchievementEducationName = 'as.ub.activities.prisons.achievement.Education.name';
  static const lsUbActivitiesPrisonsAchievementEducationImg = 'as.ub.activities.prisons.achievement.Education.img';

  static const lsUbActivitiesPrisonsAchievementDrugAwarenessName =
      'as.ub.activities.prisons.achievement.Drug awareness.name';
  static const lsUbActivitiesPrisonsAchievementDrugAwarenessImg =
      'as.ub.activities.prisons.achievement.Drug awareness.img';

  static const lsUbActivitiesPrisonsAchievementKeyworkSessionName =
      'as.ub.activities.prisons.achievement.Keywork session.name';
  static const lsUbActivitiesPrisonsAchievementKeyworkSessionImg =
      'as.ub.activities.prisons.achievement.Keywork session.img';

  static const lsUbActivitiesPrisonsAchievementGroupSessionName =
      'as.ub.activities.prisons.achievement.Group session.name';
  static const lsUbActivitiesPrisonsAchievementGroupSessionImg =
      'as.ub.activities.prisons.achievement.Group session.img';

  static const lsUbActivitiesPrisonsAchievementReviewTreatmentName =
      'as.ub.activities.prisons.achievement.Review treatment.name';
  static const lsUbActivitiesPrisonsAchievementReviewTreatmentImg =
      'as.ub.activities.prisons.achievement.Review treatment.img';

  static const lsUbActivitiesPrisonsAchievementPlanSentenceName =
      'as.ub.activities.prisons.achievement.Plan sentence.name';
  static const lsUbActivitiesPrisonsAchievementPlanSentenceImg =
      'as.ub.activities.prisons.achievement.Plan sentence.img';

  static const lsUbActivitiesPrisonsAchievementPlanForReleaseName =
      'as.ub.activities.prisons.achievement.Plan for release.name';
  static const lsUbActivitiesPrisonsAchievementPlanForReleaseImg =
      'as.ub.activities.prisons.achievement.Plan for release.img';

  static const lsUbActivitiesPrisonsAchievementVictimAwarenessName =
      'as.ub.activities.prisons.achievement.Victim awareness.name';
  static const lsUbActivitiesPrisonsAchievementVictimAwarenessImg =
      'as.ub.activities.prisons.achievement.Victim awareness.img';

  static const lsUbActivitiesPrisonsAchievementCounsellingName =
      'as.ub.activities.prisons.achievement.Counselling.name';
  static const lsUbActivitiesPrisonsAchievementCounsellingImg = 'as.ub.activities.prisons.achievement.Counselling.img';

  static const lsUbActivitiesPrisonsAchievementMutualAidGroupName =
      'as.ub.activities.prisons.achievement.Mutual aid group.name';
  static const lsUbActivitiesPrisonsAchievementMutualAidGroupImg =
      'as.ub.activities.prisons.achievement.Mutual aid group.img';

  static const lsUbActivitiesPrisonsAchievementParentingCourseName =
      'as.ub.activities.prisons.achievement.Parenting course.name';
  static const lsUbActivitiesPrisonsAchievementParentingCourseImg =
      'as.ub.activities.prisons.achievement.Parenting course.img';

  static const lsUbActivitiesPrisonsAchievementSmokingCourseName =
      'as.ub.activities.prisons.achievement.Smoking course.name';
  static const lsUbActivitiesPrisonsAchievementSmokingCourseImg =
      'as.ub.activities.prisons.achievement.Smoking course.img';

  static const lsUbActivitiesPrisonsAchievementHousingMeetingName =
      'as.ub.activities.prisons.achievement.Housing meeting.name';
  static const lsUbActivitiesPrisonsAchievementHousingMeetingImg =
      'as.ub.activities.prisons.achievement.Housing meeting.img';

  static const lsUbActivitiesPrisonsAchievementCreateACvName = 'as.ub.activities.prisons.achievement.Create a cv.name';
  static const lsUbActivitiesPrisonsAchievementCreateACvImg = 'as.ub.activities.prisons.achievement.Create a cv.img';

  static const lsUbActivitiesPrisonsAchievementUseTheLibraryName =
      'as.ub.activities.prisons.achievement.Use the library.name';
  static const lsUbActivitiesPrisonsAchievementUseTheLibraryImg =
      'as.ub.activities.prisons.achievement.Use the library.img';

  static const lsUbActivitiesPrisonsAchievementDoGardeningName =
      'as.ub.activities.prisons.achievement.Do gardening.name';
  static const lsUbActivitiesPrisonsAchievementDoGardeningImg = 'as.ub.activities.prisons.achievement.Do gardening.img';

  // Errors
  static const lsUbErrorsMinimumOne = 'as.ub.errors.minimumOne';
  static const lsUbErrorsMaxCharacters = 'as.ub.errors.maxCharacters';

  // Buttons
  static const lsUbButtonsFinish = 'as.buttons.finish';
  static const lsUbButtonsActionPlan = 'as.buttons.actionPlan';

  static const selectDateAndTime = 'as.ls.overcomeBarrier.selectDayAndTime';
  
  // traps
  static const ntTrapsblame = 'as.nt.traps.blame';
  static const ntTrapsHelpless = 'as.nt.traps.helpless';
  static const ntTrapsCatastrophe = 'as.nt.traps.catastrophe';
  static const ntTrapsGuilt = 'as.nt.traps.guilt';
  static const ntTrapsAllOrNothing = 'as.nt.traps.all-or-nothing';

}
