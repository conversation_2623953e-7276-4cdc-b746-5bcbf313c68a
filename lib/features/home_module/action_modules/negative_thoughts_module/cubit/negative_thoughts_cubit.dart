import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/negative_thoughts_repository/negative_thoughts_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoudhts_understand_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_look_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_well_done_page.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'negative_thoughts_cubit.freezed.dart';
part 'negative_thoughts_state.dart';

class NegativeThoughtsCubit extends Cubit<NegativeThoughtsState> {
  NegativeThoughtsCubit() : super(NegativeThoughtsState()) {
    questionButtonStates = ValueNotifier<List<ValueNotifier<ButtonState>>>(
      List.generate(
        4,
        (index) => ValueNotifier<ButtonState>(ButtonState.bothDisabled), // Initialize with ValueNotifier
      ),
    );
    questionSetButtonStates = ValueNotifier<List<ValueNotifier<ButtonState>>>(
      List.generate(
        2,
        (index) => ValueNotifier<ButtonState>(ButtonState.bothDisabled), // Initialize with ValueNotifier
      ),
    );
    // infoAudioUrl.value = AsLocaleKeys.lsNtMindTrapsAudio.tr();
  }

  //audio variable for situation screen
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AsLocaleKeys.lsNtMindTrapsAudio.tr());
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<String?> infounderstandAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isUnderstandManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelUnderstandVisible = ValueNotifier(false);
  // ValueNotifier<bool> isAudioPanelUnderstandVisible = ValueNotifier(false);

  ValueNotifier<String?> infoLookAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isLookManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelLookVisible = ValueNotifier(false);

  ValueNotifier<String?> infoMySetAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isMySetManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelMySetVisible = ValueNotifier(false);

  ValueNotifier<String?> infoCanSayAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isCanSayManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelCanSayVisible = ValueNotifier(false);

  ValueNotifier<String?> infoWelDoneAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isWellDoneManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelWellDoneVisible = ValueNotifier(false);

  ValueNotifier<String?> infoActionPlanAudioUrl = ValueNotifier(AsLocaleKeys.lsNtActionPlanAudio.tr());
  // ValueNotifier<bool> isAudioPanelActionPlanVisible = ValueNotifier(false);

  ValueNotifier<List<ValueNotifier<ButtonState>>> questionButtonStates =
      ValueNotifier<List<ValueNotifier<ButtonState>>>([]);
  ValueNotifier<List<ValueNotifier<ButtonState>>> questionSetButtonStates =
      ValueNotifier<List<ValueNotifier<ButtonState>>>([]);

  ValueNotifier<String> selectMindTrapValue = ValueNotifier('');
  ValueNotifier<String> selectedMindTrapValue = ValueNotifier('');

  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final scaffoldUnderstandKey = GlobalKey<ScaffoldState>();
  final scaffoldLookKey = GlobalKey<ScaffoldState>();
  final scaffoldSetKey = GlobalKey<ScaffoldState>();
  final scaffoldCanSayKey = GlobalKey<ScaffoldState>();
  final scaffoldWellDoneKey = GlobalKey<ScaffoldState>();

  final thoughtLookKey = GlobalKey();

  ValueNotifier<ButtonState> blameQuestion1Value = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> blameQuestion2Value = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> blameQuestion3Value = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> blameQuestion4Value = ValueNotifier(ButtonState.bothDisabled);
  final TextEditingController otherThoughtController = TextEditingController();
  final TextEditingController setCanDoThoughtController = TextEditingController();

  NegativeThoughtsRepository negativeThoughtsRepository = NegativeThoughtsRepository();

  String getStringFromjson(String text) {
  if (text.isEmpty) {
    return '';
  }
  else if(text == 'blame'){
    return AsLocaleKeys.ntTrapsblame.tr();
  }
  else if(text == 'helpless'){
    return AsLocaleKeys.ntTrapsHelpless.tr();
  }
  else if(text == 'catastrophe'){
    return AsLocaleKeys.ntTrapsCatastrophe.tr();
  }
  else if(text == 'guilt'){
    return AsLocaleKeys.ntTrapsGuilt.tr();
  }
  else if(text == 'all-or-nothing'){
    return AsLocaleKeys.ntTrapsAllOrNothing.tr();
  }
  return '';
}


  Future<void> ntActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required ValueNotifier<bool> isEmailPdfAPILoading,
    required ValueNotifier<bool> isDownloadPdfAPILoading,
  }) async {
    isEmail ? isEmailPdfAPILoading.value = true : isDownloadPdfAPILoading.value = true;
    try {
      final response = await negativeThoughtsRepository.ntActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Escaping your mind trap.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    }
  }

  Future<void> negativeThoughtStrategyAPI({
    required BuildContext context,
    required NegativeThoughtsCubit ref,
  }) async {
    emit(state.copyWith(isApiLoading: true));
    try {
      final questions = List<int>.filled(4, 0);

      for (var i = 0; i < ref.questionButtonStates.value.length; i++) {
        questions[i] = ref.questionButtonStates.value[i].value == ButtonState.yesEnabled ? 1 : 0;
        '>?>?>?>? question ${questions[i]}'.logD;
      }
      final q1 = questions[0];
      final q2 = questions[1];
      final q3 = questions[2];
      final q4 = questions[3];

      

      final response = await negativeThoughtsRepository.negativeThoughtStrategy(
        context: context,
        canDo: ref.setCanDoThoughtController.text,
        mindTrap: ref.selectedMindTrapValue.value,
        other: ref.otherThoughtController.text,
        q1: q1,
        q2: q2,
        q3: q3,
        q4: q4,
      );
      // response.logD;
      FocusManager.instance.primaryFocus?.unfocus();
      ref.headerInfoText.value = null;
      ref.infoCanSayAudioUrl.value = null;
      if (response != null && (response.success ?? false) == true) {
        if (response.strategies != null) {
          Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;
          'User data ===> ${Injector.instance<AppDB>().userModel?.user.strategies?.ntAs}'.logD;
          await AppNavigation.nextScreen(
            context,
            BlocProvider.value(value: ref, child: const MyThoughtsWellDonePage()),
          );
        }
      } else {
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(value: ref, child: const MyThoughtsWellDonePage()),
        );
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  final List<Map<String, String>> mindTraps = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsNtMindTrapsOptionsText,
    navigatorKey.currentContext!,
  ) as List<dynamic>)
      .map(
        (e) => {
          'text': e['text'] as String,
          'value': e['value'] as String,
        },
      )
      .toList();

  // void getMindTrapValue(String value) {
  //   switch (value) {
  //     case 'BLAME trap: Thinking others are to blame for everything':
  //       selectedMindTrapValue.value = 'blame';
  //     case 'HELPLESS trap: Thinking we cannot change things':
  //       selectedMindTrapValue.value = 'helpless';
  //     case 'CATASTROPHE trap: Thinking the worst will happen':
  //       selectedMindTrapValue.value = 'catastrophe';
  //     case 'GUILT trap: Thinking everything is our fault':
  //       selectedMindTrapValue.value = 'guilt';
  //     case 'ALL OR NOTHING trap: Thinking things are totally one way or another':
  //       selectedMindTrapValue.value = 'all-or-nothing';
  //     default:
  //       selectedMindTrapValue.value = ''; // Handle default case
  //   }
  // }
  void getMindTrapValue(String value) {
    final trap = mindTraps.firstWhere(
      (trap) => trap['text'] == value,
      orElse: () => {'value': ''},
    );
    selectedMindTrapValue.value = trap['value'] ?? '';
    'selectedMindTrapValue.value ${selectedMindTrapValue.value}'.logD;
  }

  final Map<String, MindTrapInfo> mindTrapData = {
    'blame': MindTrapInfo(
      title: AsLocaleKeys.lsNtUnderstandBlameTitle.tr(),
      audio: AsLocaleKeys.lsNtUnderstandBlameAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtUnderstandBlameText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'helpless': MindTrapInfo(
      title: AsLocaleKeys.lsNtUnderstandHelplessTitle.tr(),
      audio: AsLocaleKeys.lsNtUnderstandHelplessAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtUnderstandHelplessText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'catastrophe': MindTrapInfo(
      title: AsLocaleKeys.lsNtUnderstandCatastropheTitle.tr(),
      audio: AsLocaleKeys.lsNtUnderstandCatastropheAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtUnderstandCatastropheText0,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'guilt': MindTrapInfo(
      title: AsLocaleKeys.lsNtUnderstandGuiltTitle.tr(),
      audio: AsLocaleKeys.lsNtUnderstandGuiltAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtUnderstandGuiltText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'all-or-nothing': MindTrapInfo(
      title: AsLocaleKeys.lsNtUnderstandAllOrNothingTitle.tr(),
      audio: AsLocaleKeys.lsNtUnderstandAllOrNothingAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtUnderstandAllOrNothingText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
  };

  final Map<String, MindTrapLookInfo> mindLookData = {
    'blame': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtLookBlameAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtLookBlameQ1.tr(),
        AsLocaleKeys.lsNtLookBlameQ2.tr(),
        AsLocaleKeys.lsNtLookBlameQ3.tr(),
        AsLocaleKeys.lsNtLookBlameQ4.tr(),
        AsLocaleKeys.lsNtLookBlameOther.tr(),
      ],
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtLookBlameText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'helpless': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtLookHelplessAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtLookHelplessQ1.tr(),
        AsLocaleKeys.lsNtLookHelplessQ2.tr(),
        AsLocaleKeys.lsNtLookHelplessQ3.tr(),
        AsLocaleKeys.lsNtLookHelplessQ4.tr(),
        AsLocaleKeys.lsNtLookHelplessOther.tr(),
      ],
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtLookHelplessText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'catastrophe': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      questions: [
        AsLocaleKeys.lsNtLookCatastropheQ1.tr(),
        AsLocaleKeys.lsNtLookCatastropheQ2.tr(),
        AsLocaleKeys.lsNtLookCatastropheQ3.tr(),
        AsLocaleKeys.lsNtLookCatastropheQ4.tr(),
        AsLocaleKeys.lsNtLookCatastropheOther.tr(),
      ],
      audio: AsLocaleKeys.lsNtLookCatastropheAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtLookCatastropheText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'guilt': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtLookGuiltQ1.tr(),
        AsLocaleKeys.lsNtLookGuiltQ2.tr(),
        AsLocaleKeys.lsNtLookGuiltQ3.tr(),
        AsLocaleKeys.lsNtLookGuiltQ4.tr(),
        AsLocaleKeys.lsNtLookGuiltOther.tr(),
      ],
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtLookGuiltAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtLookGuiltText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'all-or-nothing': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtLookAllOrNothingQ1.tr(),
        AsLocaleKeys.lsNtLookAllOrNothingQ2.tr(),
        AsLocaleKeys.lsNtLookAllOrNothingQ3.tr(),
        AsLocaleKeys.lsNtLookAllOrNothingQ4.tr(),
        AsLocaleKeys.lsNtLookAllOrNothingOther.tr(),
      ],
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtLookAllOrNothingAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtLookAllOrNothingText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
  };

  final Map<String, MindTrapLookInfo> mindSetData = {
    'blame': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtSetBlameAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtSetBlameQ1.tr(),
        AsLocaleKeys.lsNtSetBlameQ2.tr(),
        AsLocaleKeys.lsNtSetBlameCanDo.tr(),
      ],
      text: [
        AsLocaleKeys.lsNtSetBlameTitle.tr(),
      ].join('\n\n'),
    ),
    'helpless': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtSetHelplessAudio.tr(),//AsLocaleKeys.lsNtLookHelplessAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtSetHelplessQ1.tr(),
        AsLocaleKeys.lsNtSetHelplessQ2.tr(),
        AsLocaleKeys.lsNtSetHelplessCanDo.tr(),
      ],
      text: [
        AsLocaleKeys.lsNtSetHelplessTitle.tr(),
      ].join('\n\n'),
    ),
    'catastrophe': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      questions: [
        AsLocaleKeys.lsNtSetCatastropheQ1.tr(),
        AsLocaleKeys.lsNtSetCatastropheQ2.tr(),
        AsLocaleKeys.lsNtSetCatastropheCanDo.tr(),
      ],
      audio: AsLocaleKeys.lsNtSetCatastropheAudio.tr(),//AsLocaleKeys.lsNtLookCatastropheAudio.tr(),
      text: [
        AsLocaleKeys.lsNtSetCatastropheTitle.tr(),
      ].join('\n\n'),
    ),
    'guilt': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtSetGuiltQ1.tr(),
        AsLocaleKeys.lsNtSetGuiltQ2.tr(),
        AsLocaleKeys.lsNtSetGuiltCanDo.tr(),
      ],
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtSetGuiltAudio.tr(),//AsLocaleKeys.lsNtLookGuiltAudio.tr(),
      text: [
        AsLocaleKeys.lsNtSetGuiltTitle.tr(),
      ].join('\n\n'),
    ),
    'all-or-nothing': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtSetAllOrNothingQ1.tr(),
        AsLocaleKeys.lsNtSetAllOrNothingQ2.tr(),
        AsLocaleKeys.lsNtSetAllOrNothingCanDo.tr(),
      ],
      title: AsLocaleKeys.lsNtLookTitle.tr(),
      audio: AsLocaleKeys.lsNtSetAllOrNothingAudio.tr(),//AsLocaleKeys.lsNtLookAllOrNothingAudio.tr(),
      text: [
        AsLocaleKeys.lsNtSetAllOrNothingTitle.tr(),
      ].join('\n\n'),
    ),
  };

  final Map<String, MindTrapLookInfo> mindCanSayData = {
    'blame': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtSayBlameTitle.tr(),
      audio: AsLocaleKeys.lsNtSayBlameAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtSetBlameQ1.tr(),
        AsLocaleKeys.lsNtSetBlameQ2.tr(),
        AsLocaleKeys.lsNtSetBlameCanDo.tr(),
      ],
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtSayBlameText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'helpless': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtHelplessTitle.tr(),
      audio: AsLocaleKeys.lsNtHelplessAudio.tr(),
      questions: [
        AsLocaleKeys.lsNtSetHelplessQ1.tr(),
        AsLocaleKeys.lsNtSetHelplessQ2.tr(),
        AsLocaleKeys.lsNtSetHelplessCanDo.tr(),
      ],
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtHelplessText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'catastrophe': MindTrapLookInfo(
      title: AsLocaleKeys.lsNtCatastropheTitle.tr(),
      questions: [
        AsLocaleKeys.lsNtSetCatastropheQ1.tr(),
        AsLocaleKeys.lsNtSetCatastropheQ2.tr(),
        AsLocaleKeys.lsNtSetCatastropheCanDo.tr(),
      ],
      audio: AsLocaleKeys.lsNtCatastropheAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtCatastropheText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'guilt': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtSetGuiltQ1.tr(),
        AsLocaleKeys.lsNtSetGuiltQ2.tr(),
        AsLocaleKeys.lsNtSetGuiltCanDo.tr(),
      ],
      title: AsLocaleKeys.lsNtGuiltTitle2.tr(),
      audio: AsLocaleKeys.lsNtGiltSayAudio.tr(),//AsLocaleKeys.lsNtGuiltAudio.tr(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtGuiltText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
    'all-or-nothing': MindTrapLookInfo(
      questions: [
        AsLocaleKeys.lsNtSetAllOrNothingQ1.tr(),
        AsLocaleKeys.lsNtSetAllOrNothingQ2.tr(),
        AsLocaleKeys.lsNtSetAllOrNothingCanDo.tr(),
      ],
      title: AsLocaleKeys.lsNtAllOrNothingTitle.tr(),
      audio: AsLocaleKeys.lsNtAllOrNothingAudio.tr(),//AsLocaleKeys.lsNtAllOrNothingAudio.tr(),
      // text: [].join(),
      text: (DynamicAssetLoader.getNestedValue(
        AsLocaleKeys.lsNtAllOrNothingText,
        navigatorKey.currentContext!,
      ) as List)
          .join('\n\n'),
    ),
  };

  @override
  Future<void> close() {
    isManuallyPaused.dispose();
    isAudioPanelUnderstandVisible.dispose();
    isLookManuallyPaused.dispose();
    isAudioPanelLookVisible.dispose();
    isMySetManuallyPaused.dispose();
    isAudioPanelMySetVisible.dispose();
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    headerInfoText.dispose();
    blameQuestion1Value.dispose();
    blameQuestion2Value.dispose();
    blameQuestion3Value.dispose();
    blameQuestion4Value.dispose();
    otherThoughtController.dispose();
    setCanDoThoughtController.dispose();
    selectMindTrapValue.dispose();
    selectedMindTrapValue.dispose();
    questionButtonStates.dispose();
    questionSetButtonStates.dispose();
    return super.close();
  }
}
