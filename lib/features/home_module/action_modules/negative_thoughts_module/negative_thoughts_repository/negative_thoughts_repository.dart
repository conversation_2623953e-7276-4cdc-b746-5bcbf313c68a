import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class NegativeThoughtsRepository {
  Future<StrategiesModel?> negativeThoughtStrategy({
    required String mindTrap,
    required int q1,
    required int q2,
    required int q3,
    required int q4,
    required String other,
    required String canDo,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'type': 'ntAS',
          'data': {
            'mindTrap': mindTrap,
            'lookAtYourThoughts': {'q1': q1, 'q2': q2, 'q3': q3, 'q4': q4, if (other.isNotEmpty) 'other': other},
            if (canDo.isNotEmpty) 'canDo': canDo,
          },
        },
        apiName: EndPoints.strategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return StrategiesModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

    Future<Response<Map<String, dynamic>>?> ntActionStrategyforDownload({
    required BuildContext context,
    required bool isEmail,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isEmail ? '${EndPoints.ntActionStrategy}?email=true' : EndPoints.ntActionStrategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
