import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:easy_localization/easy_localization.dart';

class MindActionPlanInfo {
  MindActionPlanInfo({
    required this.audio,
    required this.questions,
    required this.text,
  });
  final String audio;
  final List<String> questions;
  final String text;
}

final Map<String, MindActionPlanInfo> mindActionPlanData = {
  'blame': MindActionPlanInfo(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsNtSayBlameText,
      navigatorKey.currentContext!,
    ) as List)
        .join('\n\n'),
    audio: AsLocaleKeys.lsNtSetBlameAudio.tr(),
    questions: [
      AsLocaleKeys.lsNtSetBlameQ1.tr(),
      AsLocaleKeys.lsNtSetBlameQ2.tr(),
    ],
  ),
  'helpless': MindActionPlanInfo(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsNtHelplessText,
      navigatorKey.currentContext!,
    ) as List)
        .join('\n\n'),
    audio: AsLocaleKeys.lsNtLookHelplessAudio.tr(),
    questions: [
      AsLocaleKeys.lsNtSetHelplessQ1.tr(),
      AsLocaleKeys.lsNtSetHelplessQ2.tr(),
    ],
  ),
  'catastrophe': MindActionPlanInfo(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsNtCatastropheText,
      navigatorKey.currentContext!,
    ) as List)
        .join('\n\n'),
    questions: [
      AsLocaleKeys.lsNtSetCatastropheQ1.tr(),
      AsLocaleKeys.lsNtSetCatastropheQ2.tr(),
    ],
    audio: AsLocaleKeys.lsNtLookCatastropheAudio.tr(),
  ),
  'guilt': MindActionPlanInfo(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsNtGuiltText,
      navigatorKey.currentContext!,
    ) as List)
        .join('\n\n'),
    questions: [
      AsLocaleKeys.lsNtSetGuiltQ1.tr(),
      AsLocaleKeys.lsNtSetGuiltQ2.tr(),
    ],
    audio: AsLocaleKeys.lsNtLookGuiltAudio.tr(),
  ),
  'all-or-nothing': MindActionPlanInfo(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsNtAllOrNothingText,
      navigatorKey.currentContext!,
    ) as List)
        .join('\n\n'),
    questions: [
      AsLocaleKeys.lsNtSetAllOrNothingQ1.tr(),
      AsLocaleKeys.lsNtSetAllOrNothingQ2.tr(),
    ],
    audio: AsLocaleKeys.lsNtLookAllOrNothingAudio.tr(),
  ),
};
