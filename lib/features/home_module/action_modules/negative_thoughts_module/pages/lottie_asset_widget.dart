import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LottieAssetWidget extends StatefulWidget {
  const LottieAssetWidget({required this.assetPath, super.key});
  final String assetPath;

  @override
  State<LottieAssetWidget> createState() => _LottieAssetWidgetState();
}

class _LottieAssetWidgetState extends State<LottieAssetWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    // Set your desired duration
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4), // change duration here
    );

    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Lottie.asset(
      controller: _controller,
      repeat: false, // Ensures the animation plays only once
      animate: false,
      widget.assetPath,
      //  fit: BoxFit.cover,
    );
  }
}
