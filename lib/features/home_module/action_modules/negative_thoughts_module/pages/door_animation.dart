import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class DoorTextAnimation extends StatefulWidget {
  const DoorTextAnimation({super.key, this.title});
  final String? title;

  @override
  State<DoorTextAnimation> createState() => _DoorTextAnimationState();
}

class _DoorTextAnimationState extends State<DoorTextAnimation> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _doorAnimation;
  late Animation<double> _textPositionAnimation;
  late Animation<double> _textOpacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3), // Duration of each animation cycle
    );

    // // Define the animation for the door (moving from right to left)
    // _doorAnimation = Tween<double>(begin: 117, end: 16).animate(
    //   CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    // );

    _doorAnimation = Tween<double>(begin: 102, end: 16).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Define the animation for the text (moving from left to right)
    _textPositionAnimation = Tween<double>(begin: 50, end: 200).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Define the opacity animation for the text (fade in and fade out)
    _textOpacityAnimation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Start the animation
    _controller
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
        } else if (status == AnimationStatus.dismissed) {
          _controller.forward();
        }
      })
      ..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            AnimatedBuilder(
              animation: Listenable.merge([_textPositionAnimation, _textOpacityAnimation]),
              builder: (context, child) {
                return Positioned(
                  top: AppSize.h18,
                  left: _textPositionAnimation.value,
                  bottom: 22,
                  child: Opacity(
                    opacity: _textOpacityAnimation.value,
                    child: Transform.rotate(
                      angle: 50,
                      child: Align(
                        child: Text(
                          widget.title?.toUpperCase() ?? 'BLAME',
                          style: TextStyle(
                            fontFamily: 'Anton',
                            letterSpacing: -1,
                            fontSize: widget.title?.toUpperCase() != null && (widget.title?.length ?? 0) > 7
                                ? AppSize.sp28
                                : AppSize.sp45,
                                // ? AppSize.sp24
                                // : AppSize.sp36,
                            fontWeight: FontWeight.bold,
                            color: const Color.fromRGBO(164, 164, 255, 1), // Main text color
                            shadows: const [
                              Shadow(
                                offset: Offset(-3, 3), // Equivalent to -3px 3px
                                color: Color.fromRGBO(97, 93, 233, 1),
                              ),
                              Shadow(
                                offset: Offset(-2, 2), // Equivalent to -2px 2px
                                color: Color.fromRGBO(97, 93, 233, 1),
                              ),
                              Shadow(
                                offset: Offset(-1, 1), // Equivalent to -1px 1px
                                color: Color.fromRGBO(97, 93, 233, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),

            // Door (Animating from right to left)
            AnimatedBuilder(
              animation: _doorAnimation,
              builder: (BuildContext context, Widget? child) {
                return Lottie.asset(
          repeat: false, // Ensures the animation plays only once
          animate: true,
          'assets/icons/action_icons/gate_openning_animation.json',
          //  fit: BoxFit.cover,
        );
              },
            ),
          ],
        );
      },
    );
  }
}
