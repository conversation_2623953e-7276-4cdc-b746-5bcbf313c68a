import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class BlameTextAnimation extends StatefulWidget {
  const BlameTextAnimation({super.key, this.title});
  final String? title;

  @override
  State<BlameTextAnimation> createState() => _BlameTextAnimationState();
}

class _BlameTextAnimationState extends State<BlameTextAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _blameTextAnimation;
  int _repeatCount = 0; // To track how many times the animation has played
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2), // Duration of each animation cycle
    );

    _blameTextAnimation = Tween<double>(begin: 10, end: 25).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed && _repeatCount < 3) {
          _repeatCount++;
          _controller.reverse();
        } else if (status == AnimationStatus.dismissed && _repeatCount < 3) {
          _controller.forward();
        }
      })
      ..forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: _blameTextAnimation,
          builder: (context, child) {
            return Positioned(
              top: _blameTextAnimation.value - 10,
              //   left: AppSize.sp40,
              // bottom: 20,
              bottom: 0, left: -0, right: 0,
              child: Transform.rotate(
                angle: 50,
                child: Align(
                  child: Text(
                    widget.title?.toUpperCase() ?? 'BLAME',
                    style: TextStyle(
                      fontFamily: 'Anton',
                      fontSize: widget.title?.toUpperCase() != null &&
                              (widget.title?.length ?? 0) > 7
                          ? AppSize.sp28
                          : AppSize.sp45,
                      fontWeight: FontWeight.bold,
                      color: const Color.fromRGBO(
                          164, 164, 255, 1), // Main text color
                      shadows: const [
                        Shadow(
                          offset: Offset(-3, 3), // Equivalent to -3px 3px
                          color: Color.fromRGBO(97, 93, 233, 1),
                        ),
                        Shadow(
                          offset: Offset(-2, 2), // Equivalent to -2px 2px
                          color: Color.fromRGBO(97, 93, 233, 1),
                        ),
                        Shadow(
                          offset: Offset(-1, 1), // Equivalent to -1px 1px
                          color: Color.fromRGBO(97, 93, 233, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        Lottie.asset(
          repeat: false, // Ensures the animation plays only once
          animate: false,
          'assets/icons/action_icons/gate_closed_state_animation.json',
          //  fit: BoxFit.cover,
        ),
        // AppSvgAsset(
        //   svgAsset: Assets.icons.actionIcons.thoughtImage,
        //   size: AppSize.sp120,
        // ),
      ],
    );
  }
}
