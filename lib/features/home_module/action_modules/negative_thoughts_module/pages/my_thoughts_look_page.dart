import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/cubit/negative_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/blame_animation.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/lottie_asset_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_set_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:lottie/lottie.dart';

class MyThoughtsLookPage extends StatefulWidget {
  const MyThoughtsLookPage({super.key});

  @override
  State<MyThoughtsLookPage> createState() => _MyThoughtsLookPageState();
}

class _MyThoughtsLookPageState extends State<MyThoughtsLookPage> {
  @override
  void initState() {
    final ref = context.read<NegativeThoughtsCubit>();
    ref.infoLookAudioUrl.value = ref.mindLookData[ref.selectedMindTrapValue.value]?.audio;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NegativeThoughtsCubit, NegativeThoughtsState>(
      builder: (ctx, state) {
        final ref = ctx.read<NegativeThoughtsCubit>();
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              ref.headerInfoText.value = null;
              ref.infounderstandAudioUrl.value = ref.mindTrapData[ref.selectedMindTrapValue.value]?.audio;
            }
          },
          child: ValueListenableBuilder(
            valueListenable: ref.infoLookAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                //resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldLookKey,
                isAudioPanelVisible: ref.isAudioPanelLookVisible,
                infoAudioUrl: ref.infoLookAudioUrl,
                isManuallyPaused: ref.isLookManuallyPaused,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldLookKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldLookKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoLookAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelLookVisible.value = !ref.isAudioPanelLookVisible.value;
                    }
                  },
                ),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: ColoredBox(
                                      color: context.themeColors.whiteColor,
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w24,
                                          right: AppSize.w24,
                                          bottom: AppSize.h20,
                                          top: AppSize.h24,
                                        ),
                                        child: ValueListenableBuilder(
                                          valueListenable: ref.selectedMindTrapValue,
                                          builder: (context, value, child) {
                                            return Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  children: [
                                                    InformationPageHeadingWidget(
                                                      onBackArrowTap: () {
                                                        ref.headerInfoText.value = null;

                                                        ref.infounderstandAudioUrl.value =
                                                            ref.mindLookData[ref.selectedMindTrapValue.value]?.audio;
                                                        Navigator.pop(context);
                                                      },
                                                      title: CoreLocaleKeys.titlesInformationStrategiesNegativeThoughts
                                                          .tr(),
                                                      subtitle: AsLocaleKeys.lsNtTitle.tr(),
                                                      icon: Assets.icons.actionIcons.negativeThoughts,
                                                      onInfoTap: () {
                                                        final info = (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsNtInfoPanelsInformationText,
                                                          context,
                                                        ) as List)
                                                            .join('<br/><br/>');
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isLookManuallyPaused.value = true;
                                                          ref.infoLookAudioUrl.value =
                                                              ref.mindLookData[ref.selectedMindTrapValue.value]?.audio;
                                                          ref.headerInfoText.value = null;
                                                          ref.isAudioPanelLookVisible.value = false;
                                                        } else {
                                                          ref.isLookManuallyPaused.value = false;
                                                          ref.infoLookAudioUrl.value =
                                                              AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr();
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      onLearnTap: () {
                                                        final info = (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsNtInfoPanelsLearnText,
                                                          context,
                                                        ) as List)
                                                            .join('<br/><br/>');
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isLookManuallyPaused.value = true;
                                                          ref.infoLookAudioUrl.value =
                                                              ref.mindLookData[ref.selectedMindTrapValue.value]?.audio;
                                                          ref.headerInfoText.value = null;
                                                          ref.isAudioPanelLookVisible.value = false;
                                                        } else {
                                                          ref.isLookManuallyPaused.value = false;
                                                          ref.infoLookAudioUrl.value =
                                                              AsLocaleKeys.lsNtInfoPanelsLearnAudio.tr();
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      infoWidget: ValueListenableBuilder(
                                                        valueListenable: ref.headerInfoText,
                                                        builder: (context, headerPlanInfoTextV, _) {
                                                          return CustomInfoWidget(
                                                            customWidget: Column(
                                                              children: [
                                                                Html(
                                                                  data: ref.headerInfoText.value ?? '',
                                                                  style: {
                                                                    'strong': Style(
                                                                      fontSize: FontSize(AppSize.sp13),
                                                                      color: context.themeColors.darkOrangeColor,
                                                                      fontWeight: FontWeight.bold,
                                                                      fontFamily: 'Poppins',
                                                                    ),
                                                                    'body': Style(
                                                                      fontSize: FontSize(AppSize.sp13),
                                                                      color: context.themeColors.darkOrangeColor,
                                                                      fontFamily: 'Poppins',
                                                                    ),
                                                                  },
                                                                ),
                                                              ],
                                                            ),
                                                            onCloseTap: () {
                                                              ref.isLookManuallyPaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoLookAudioUrl.value = ref
                                                                  .mindLookData[ref.selectedMindTrapValue.value]?.audio;
                                                            },
                                                            visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                            margin: EdgeInsets.symmetric(
                                                              vertical: AppSize.h8,
                                                            ),
                                                            bodyText: headerPlanInfoTextV,
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    
                                                    SizedBox(
                                                      height: AppSize.h120,
                                                      child: Row(
                                                        crossAxisAlignment: CrossAxisAlignment
                                                            .stretch, // Ensure both widgets stretch to the same height
                                                        children: [
                                                          Expanded(
                                                            flex: 6, // 60% width
                                                            child: Align(
                                                              child: SizedBox(
                                                                width: double
                                                                    .infinity, // Ensures it takes the full width of the expanded space
                                                                child: BlameTextAnimation(
                                                                  title: ref.getStringFromjson(ref.selectedMindTrapValue.value),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          SpaceH(AppSize.w10),
                                                          Expanded(
                                                            flex: 3, // 40% width
                                                            child: Align(
                                                              child: Padding(
                                                                padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                                child: SizedBox(
                                                                  height: AppSize.h120,
                                                                  //  color: Colors.red,
                                                                  child: const LottieAssetWidget(
                                                                    assetPath:
                                                                        'assets/icons/action_icons/key_one_animation.json',
                                                                    //  fit: BoxFit.cover,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Center(
                                                      child: AppTextWidget(
                                                        ref.mindLookData[ref.selectedMindTrapValue.value]?.title ??
                                                            'Select a Mind Trap',
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Center(
                                                      child: AppTextWidget(
                                                        ref.mindLookData[ref.selectedMindTrapValue.value]?.text ??
                                                            'Select a Mind Trap',
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    AppTextWidget(
                                                      AsLocaleKeys.lsNtLookSubtitle.tr(),
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.isButtonClicked,
                                                      builder: (context, value, child) {
                                                        return ListView.builder(
                                                          shrinkWrap: true,
                                                          physics: const NeverScrollableScrollPhysics(),
                                                          itemCount: ref.mindLookData[ref.selectedMindTrapValue.value]
                                                                  ?.questions.length ??
                                                              0,
                                                          itemBuilder: (context, index) {
                                                            final question = ref
                                                                    .mindLookData[ref.selectedMindTrapValue.value]
                                                                    ?.questions[index] ??
                                                                '';
                                                            return Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              children: [
                                                                QuestionRowWidget(questionText: question),
                                                                SpaceV(AppSize.h20),
                                                                if (index ==
                                                                    (ref.mindLookData[ref.selectedMindTrapValue.value]
                                                                                ?.questions.length ?? 0) - 1)
                                                                  Padding(
                                                                    padding: EdgeInsets.only(
                                                                      left: AppSize.w34,
                                                                      right: AppSize.w28,
                                                                    ),
                                                                    child: CustomOutlinedTextfield(
                                                                      hintText:
                                                                          CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                                      controller: ref.otherThoughtController,
                                                                    ),
                                                                  )
                                                                else
                                                                  Padding(
                                                                    padding: EdgeInsets.only(
                                                                      left: AppSize.w34,
                                                                      right: AppSize.w28,
                                                                    ),
                                                                    child: CustomAssessmentButton(
                                                                      switchPosition: false,
                                                                      selectedValue: ref.isButtonClicked.value,
                                                                      buttonFirstText: DataJsonKeys.buttonYes.tr(),
                                                                      buttonSecondText: DataJsonKeys.buttonNo.tr(),
                                                                      currentState: ref.questionButtonStates.value[
                                                                          index], // Pass ValueNotifier<ButtonState>
                                                                      onNoTap: () {
                                                                        ref.questionButtonStates.value[index].value =
                                                                            ButtonState.yesEnabled;
                                                                        // ref.blameQuestion3Value.value = ButtonState.yesEnabled;
                                                                      },

                                                                      onYesTap: () {
                                                                        ref.questionButtonStates.value[index].value =
                                                                            ButtonState.noEnabled;
                                                                        '>?>?>? lookAtYourThoughts ${ref.questionButtonStates.value[index].value}'
                                                                            .logD;
                                                                      },
                                                                    ),
                                                                  ),
                                                                SpaceV(AppSize.h20),
                                                              ],
                                                            );
                                                          },
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                                ColoredBox(
                                                  color: context.themeColors.whiteColor,
                                                  child: CustomButton(
                                                    padding: EdgeInsets.zero,
                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                    onTap: () async {
                                                      if (ref.questionButtonStates.value.every(
                                                        (element) => element.value != ButtonState.bothDisabled,
                                                      )) {
                                                        log('ref.infoAudioUrl.value ${ref.infoAudioUrl.value}');
                                                        ref.headerInfoText.value = null;
                                                        ref.infoLookAudioUrl.value = null;

                                                        //  ref.isAudioPanelLookVisible.value = false;
                                                        //                                               ref.infoLookAudioUrl.value = null;
                                                        ref.infoMySetAudioUrl.value =
                                                            ref.mindSetData[ref.selectedMindTrapValue.value]?.audio;
                                                        ref.isButtonClicked.value = false;

                                                        '***${ref.selectMindTrapValue.value}'.logD;
                                                        await AppNavigation.nextScreen(
                                                          context,
                                                          BlocProvider.value(
                                                            value: ref,
                                                            child: const MyThoughtsSetPage(),
                                                          ),
                                                        );
                                                      } else {
                                                        ref.isButtonClicked.value = true;
                                                        // if(ref.questionButtonStates.value.every(
                                                        //       (element) => element.value == ButtonState.bothDisabled,
                                                        // )) {
                                                        //   await AppCommonFunctions.scrollToKey(ref.thoughtLookKey);
                                                        //   return;
                                                        // }
                                                      }
                                                    },
                                                    isBottom: true,
                                                    color: context.themeColors.blueColor,
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

class MindTrapLookInfo {
  MindTrapLookInfo({
    required this.title,
    required this.audio,
    required this.text,
    required this.questions,
  });
  final String title;
  final String audio;
  final String text;
  final List<String> questions;
}
