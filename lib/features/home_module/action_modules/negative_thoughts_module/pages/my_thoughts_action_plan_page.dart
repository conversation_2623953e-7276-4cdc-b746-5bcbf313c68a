import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/custom_action_plan_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/cubit/negative_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/models/mind_action_plan_info.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/negative_thoughts_repository/negative_thoughts_repository.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/assets_path.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class MyThoughtsActionPlanPage extends StatefulWidget {
  const MyThoughtsActionPlanPage({
    required this.selectedMindTrapValue,
    super.key,
    this.negativeThougthtCubit,
    this.state,
    this.isFromToolKit = false,
    this.recoveryCubit,
    this.time,
  });
  final ValueNotifier<String> selectedMindTrapValue;
  final NegativeThoughtsCubit? negativeThougthtCubit;
  final NegativeThoughtsState? state;
  final MyRecoveryToolKitCubit? recoveryCubit;
  final bool isFromToolKit;
  final int? time;

  @override
  State<MyThoughtsActionPlanPage> createState() => _MyThoughtsActionPlanPageState();
}

class _MyThoughtsActionPlanPageState extends State<MyThoughtsActionPlanPage> {
  final scaffoldActionPlanKey = GlobalKey<ScaffoldState>();

  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isEmailPdfAPILoading = ValueNotifier(false);
  ValueNotifier<bool> isDownloadPdfAPILoading = ValueNotifier(false);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  NegativeThoughtsRepository negativeThoughtsRepository = NegativeThoughtsRepository();


    Future<void> ntActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required ValueNotifier<bool> isEmailPdfAPILoading,
    required ValueNotifier<bool> isDownloadPdfAPILoading,
  }) async {
    isEmail ? isEmailPdfAPILoading.value = true : isDownloadPdfAPILoading.value = true;
    try {
      final response = await negativeThoughtsRepository.ntActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Escaping your mind trap.pdf',
            );
          }
        } else {
        }
      }
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    } catch (e) {
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    }
  }


  @override
  void initState() {
    super.initState();
    AsLocaleKeys.lsNtActionPlanAudio.tr().logD;
    if (widget.isFromToolKit) {
      // headerInfoText.value = 'Default header info for tool kit'; // Set your default value here
      infoAudioUrl.value = AsLocaleKeys.lsNtActionPlanAudio.tr(); // Set your default audio URL hereanel
    } else {
      // Use the values from the Cubit if it's not from the tool kit
      infoAudioUrl.value = AsLocaleKeys.lsNtActionPlanAudio.tr();
    }
  }

  @override
  void dispose() {
    headerInfoText.dispose();
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    isEmailPdfAPILoading.dispose();
    isDownloadPdfAPILoading.dispose();
    isManuallyPaused.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: infoAudioUrl,
      builder: (context, value, child) {
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              // ref.infoAudioUrl.value = null;
              headerInfoText.value = null;

              infoAudioUrl.value = AsLocaleKeys.lsNtSummaryAudio.tr();
              if (widget.isFromToolKit) {
                // headerInfoText.value = 'Default header info for tool kit'; // Set your default value here
              } else {
                // Use the values from the Cubit if it's not from the tool kit
                headerInfoText = ValueNotifier(null);
                widget.negativeThougthtCubit?.infoWelDoneAudioUrl.value = AsLocaleKeys.lsNtSummaryAudio.tr();
                isAudioPanelVisible = ValueNotifier(false);
              }
              log('ref.infoAudioUrl.value ${infoAudioUrl.value}');
            }
          },
          child: AppScaffold(
            resizeToAvoidBottomInset: false,
            scaffoldKey: scaffoldActionPlanKey,
            isAudioPanelVisible: isAudioPanelVisible,
            isManuallyPaused: isManuallyPaused,
            infoAudioUrl: infoAudioUrl,
            drawer: AppDrawer(scaffoldKey: scaffoldActionPlanKey),
            appBar: CommonAppBar(
              onPrefixTap: () {
                scaffoldActionPlanKey.currentState?.openDrawer();
              },
              onSuffixTap: () {
                if (infoAudioUrl.value.isNotEmptyAndNotNull) {
                  isAudioPanelVisible.value = !isAudioPanelVisible.value;
                }
              },
            ),
            body: ColoredBox(
              color: context.themeColors.whiteColor,
              child: Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(right: AppSize.w4),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return CustomRawScrollbar(
                            child: SingleChildScrollView(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    left: AppSize.w24,
                                    right: AppSize.w24,
                                    bottom: AppSize.h20,
                                    top: AppSize.h24,
                                  ),
                                  child: ValueListenableBuilder(
                                    valueListenable: widget.selectedMindTrapValue,
                                    builder: (context, value, child) {
                                      return Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              InformationPageHeadingWidget(
                                                onBackArrowTap: () {
                                                  // ref.infoAudioUrl.value = null;
                                                  headerInfoText.value = null;

                                                  infoAudioUrl.value = AsLocaleKeys.lsNtSummaryAudio.tr();
                                                  if (widget.isFromToolKit) {
                                                    // headerInfoText.value = 'Default header info for tool kit'; // Set your default value here
                                                  } else {
                                                    // Use the values from the Cubit if it's not from the tool kit
                                                    headerInfoText = ValueNotifier(null);
                                                    widget.negativeThougthtCubit?.infoWelDoneAudioUrl.value =
                                                        AsLocaleKeys.lsNtSummaryAudio.tr();
                                                    isAudioPanelVisible = ValueNotifier(false);
                                                  }
                                                  log('ref.infoAudioUrl.value ${infoAudioUrl.value}');
                                                  Navigator.pop(context);
                                                },
                                                title: CoreLocaleKeys.titlesInformationStrategiesNegativeThoughts.tr(),
                                                subtitle: AsLocaleKeys.lsNtTitle.tr(),
                                                icon: Assets.icons.actionIcons.negativeThoughts,
                                                onInfoTap: () {
                                                  // Use the values from the Cubit if it's not from the tool kit
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsNtInfoPanelsInformationText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (headerInfoText.value.isNotEmptyAndNotNull &&
                                                      headerInfoText.value == info) {
                                                    isManuallyPaused.value = true;
                                                    infoAudioUrl.value = AsLocaleKeys.lsNtActionPlanAudio.tr();
                                                    headerInfoText.value = null;
                                                    isAudioPanelVisible.value = false;
                                                  } else {
                                                    isManuallyPaused.value = false;
                                                    infoAudioUrl.value =
                                                        AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr();
                                                    headerInfoText.value = info;
                                                  }
                                                },
                                                onLearnTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsNtInfoPanelsLearnText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (headerInfoText.value.isNotEmptyAndNotNull &&
                                                      headerInfoText.value == info) {
                                                    isManuallyPaused.value = true;
                                                    infoAudioUrl.value = AsLocaleKeys.lsNtActionPlanAudio.tr();
                                                    headerInfoText.value = null;
                                                    isAudioPanelVisible.value = false;
                                                  } else {
                                                    isManuallyPaused.value = false;
                                                    infoAudioUrl.value = AsLocaleKeys.lsNtInfoPanelsLearnAudio.tr();
                                                    headerInfoText.value = info;
                                                  }
                                                },
                                                infoWidget: ValueListenableBuilder(
                                                  valueListenable: headerInfoText,
                                                  builder: (context, headerPlanInfoTextV, _) {
                                                    return CustomInfoWidget(
                                                      customWidget: Column(
                                                        children: [
                                                          Html(
                                                            data: headerInfoText.value ?? '',
                                                            style: {
                                                              'strong': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontWeight: FontWeight.bold,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                              'body': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      onCloseTap: () {
                                                        isManuallyPaused.value = true;
                                                        headerInfoText.value = null;
                                                        infoAudioUrl.value = AsLocaleKeys.lsNtActionPlanAudio.tr();
                                                      },
                                                      visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerPlanInfoTextV,
                                                    );
                                                  },
                                                ),
                                              ),
                                              SpaceV(AppSize.h10),
                                              Center(
                                                child: AppTextWidget(
                                                  AsLocaleKeys.lsNtActionPlanTitle.tr(),
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                              SpaceV(AppSize.h20),
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsNtActionPlanText,
                                                  context,
                                                ) as List)
                                                    .join('\n\n'),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h20),
                                              AppTextWidget(
                                                AsLocaleKeys.lsNtActionPlanSubtitlesAsk.tr(),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              //SpaceV(AppSize.h10),
                                              ListView.builder(
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                itemCount: mindActionPlanData[widget.selectedMindTrapValue.value]
                                                        ?.questions
                                                        .length ??
                                                    0,
                                                itemBuilder: (context, index) {
                                                  final question =
                                                      mindActionPlanData[widget.selectedMindTrapValue.value]
                                                              ?.questions[index] ??
                                                          '';
                                                  return Column(
                                                    children: [
                                                      SpaceV(AppSize.h10),
                                                      CustomActionPlanTextWidget(question: question),
                                                    ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h14),
                                              if (widget.isFromToolKit) ...{
                                                if (Injector.instance<AppDB>()
                                                        .userModel
                                                        ?.user
                                                        .strategies
                                                        ?.ntAs
                                                        ?.firstWhere(
                                                          (element) =>
                                                              element.time ==
                                                              widget.time!, // Replace 'desiredTime' with your condition
                                                        )
                                                        .data
                                                        ?.canDo
                                                        ?.isNotEmpty ??
                                                    false) ...[
                                                  //SpaceV(AppSize.h20),
                                                  AppTextWidget(
                                                    AsLocaleKeys.lsNtActionPlanSubtitlesEvidence.tr(),
                                                    style: context.textTheme.titleSmall,
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const Padding(
                                                        padding: EdgeInsets.only(top: 10),
                                                        child: AppCachedNetworkImage(
                                                          imageUrl: AssetsPath.greenBulletPoint,
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.w10),
                                                      Expanded(
                                                        child: AppTextWidget(
                                                          Injector.instance<AppDB>()
                                                                  .userModel
                                                                  ?.user
                                                                  .strategies
                                                                  ?.ntAs
                                                                  ?.firstWhere(
                                                                    (element) =>
                                                                        element.time ==
                                                                        widget
                                                                            .time!, // Replace 'desiredTime' with your condition
                                                                  )
                                                                  .data
                                                                  ?.canDo ??
                                                              '',
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                ],
                                              } else ...{
                                                if (widget.negativeThougthtCubit?.setCanDoThoughtController.text
                                                        .isNotEmpty ??
                                                    false) ...[
                                                  //SpaceV(AppSize.h20),
                                                  AppTextWidget(
                                                    AsLocaleKeys.lsNtActionPlanSubtitlesEvidence.tr(),
                                                    style: context.textTheme.titleSmall,
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const Padding(
                                                        padding: EdgeInsets.only(top: 10),
                                                        child: AppCachedNetworkImage(
                                                          imageUrl: AssetsPath.greenBulletPoint,
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.w10),
                                                      Expanded(
                                                        child: AppTextWidget(
                                                          widget.negativeThougthtCubit?.setCanDoThoughtController
                                                                  .text ??
                                                              '',
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                      
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                ],
                                              },
                                              //SpaceV(AppSize.h20),
                                              AppTextWidget(
                                                AsLocaleKeys.lsNtActionPlanSubtitlesSay.tr(),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h10),
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: mindActionPlanData[widget.selectedMindTrapValue.value]
                                                        ?.text
                                                        .split('\n\n')
                                                        .asMap()
                                                        .entries
                                                        .map((line) {
                                                      final index = line.key;
                                                      final line1 = line.value;
                                                      return Row(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          Padding(
                                                            padding: EdgeInsets.only(
                                                              top: index > 0 ? AppSize.h20 : AppSize.h10,
                                                            ), // Adjust for vertical alignment
                                                            child: const AppCachedNetworkImage(
                                                              imageUrl: AssetsPath.greenBulletPoint,
                                                            ),
                                                          ),
                                                          SpaceH(AppSize.w10),
                                                          Expanded(
                                                            child: Padding(
                                                              padding:
                                                                  EdgeInsets.only(top: index > 0 ? AppSize.h10 : 0),
                                                              child: AppTextWidget(
                                                                line1,
                                                                style: context.textTheme.titleSmall?.copyWith(
                                                                  fontWeight: FontWeight.w600,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    }).toList() ??
                                                    [], // Fallback to an empty list if null
                                              ),
                                            ],
                                          ),
                                          SpaceV(AppSize.h12),
                                          MultiValueListenableBuilder(
                                            valueListenables: [isDownloadPdfAPILoading, isEmailPdfAPILoading],
                                            builder: (context, List<dynamic> values, child) {
                                              return CustomYesNoButton(
                                                padding: EdgeInsets.zero,
                                                isDownLoad: true,
                                                isYesNoButton: true,
                                                exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                                agreeText: IsLocaleKeys.buttonsFinish.tr(),
                                                inNoProgress: ValueNotifier(
                                                  isDownloadPdfAPILoading.value || isEmailPdfAPILoading.value,
                                                ),
                                                //    inYesProgress: isEmailPdfAPILoading,
                                                onDownloadTap: () {
                                                  CustomDownloadPopup.buildPopupMenu(
                                                    context: context,
                                                    onDownLoadPdf: () {
                                                      if (!widget.isFromToolKit) {
                                                        widget.negativeThougthtCubit?.ntActionStrategyforDownloadPdfApi(
                                                          context: context,
                                                          isEmail: false,
                                                          isDownloadPdfAPILoading: isDownloadPdfAPILoading,
                                                          isEmailPdfAPILoading: isEmailPdfAPILoading,
                                                        );
                                                     }else {
                                                      ntActionStrategyforDownloadPdfApi(
                                                          context: context,
                                                          isEmail: false,
                                                          isDownloadPdfAPILoading: isDownloadPdfAPILoading,
                                                          isEmailPdfAPILoading: isEmailPdfAPILoading,
                                                        );
                                                     }
                                                    },
                                                    onEmailDownload: () {
                                                     if (!widget.isFromToolKit) {
                                                        widget.negativeThougthtCubit?.ntActionStrategyforDownloadPdfApi(
                                                          context: context,
                                                          isEmail: true,
                                                          isDownloadPdfAPILoading: isDownloadPdfAPILoading,
                                                          isEmailPdfAPILoading: isEmailPdfAPILoading,
                                                        );
                                                      }else {
                                                        ntActionStrategyforDownloadPdfApi(
                                                          context: context,
                                                          isEmail: true,
                                                          isDownloadPdfAPILoading: isDownloadPdfAPILoading,
                                                          isEmailPdfAPILoading: isEmailPdfAPILoading,
                                                        );
                                                      }
                                                    },
                                                  );
                                                },
                                                onTapYes: () {
                                                  infoAudioUrl.value = null;
                                                  //  infoAudioUrl.value = null;
                                                  //  isAudioPanelVisible.dispose();
                                                  '>?>?>? 13'.logV;
                                                  AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                                },
                                                onTapNo: () {},
                                                noButtonColor: context.themeColors.orangeColor,
                                              );
                                            },
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // SafeArea(
                    //   child: CustomButton(
                    //     title: CoreLocaleKeys.surveyThankYouFinishButton.tr(),
                    //     onTap: () {

                    //     },
                    //     isBottom: true,
                    //     color: context.themeColors.blueColor,
                    //   ),
                    // ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
