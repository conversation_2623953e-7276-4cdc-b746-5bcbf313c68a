import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/cubit/negative_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/blame_animation.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/lottie_asset_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyThoughtsCanSayPage extends StatefulWidget {
  const MyThoughtsCanSayPage({super.key});

  @override
  State<MyThoughtsCanSayPage> createState() => _MyThoughtsCanSayPageState();
}

class _MyThoughtsCanSayPageState extends State<MyThoughtsCanSayPage> {
  @override
  void initState() {
    final ref = context.read<NegativeThoughtsCubit>();
    ref.infoCanSayAudioUrl.value = ref.mindCanSayData[ref.selectedMindTrapValue.value]?.audio;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NegativeThoughtsCubit, NegativeThoughtsState>(
      builder: (ctx, state) {
        final ref = ctx.read<NegativeThoughtsCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoCanSayAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.headerInfoText.value = null;

                  ref.infoMySetAudioUrl.value = ref.mindSetData[ref.selectedMindTrapValue.value]?.audio;
                }
              },
              child: AbsorbPointer(
                absorbing: state.isApiLoading,
                child: AppScaffold(
                  resizeToAvoidBottomInset: false,
                  scaffoldKey: ref.scaffoldSetKey,
                  isAudioPanelVisible: ref.isAudioPanelCanSayVisible,
                  infoAudioUrl: ref.infoCanSayAudioUrl,
                  isManuallyPaused: ref.isCanSayManuallyPaused,
                  drawer: AppDrawer(scaffoldKey: ref.scaffoldSetKey),
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      ref.scaffoldSetKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoCanSayAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPanelCanSayVisible.value = !ref.isAudioPanelCanSayVisible.value;
                      }
                    },
                  ),
                  body: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(right: AppSize.w4),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return CustomRawScrollbar(
                                  child: SingleChildScrollView(
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(
                                        minHeight: constraints.maxHeight,
                                      ),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w24,
                                          right: AppSize.w24,
                                          bottom: AppSize.h20,
                                          top: AppSize.h24,
                                        ),
                                        child: ValueListenableBuilder(
                                          valueListenable: ref.selectedMindTrapValue,
                                          builder: (
                                            context,
                                            value,
                                            child,
                                          ) {
                                            return Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  children: [
                                                    InformationPageHeadingWidget(
                                                      onBackArrowTap: () {
                                                        ref.headerInfoText.value = null;
                                                        ref.infoMySetAudioUrl.value =
                                                            ref.mindSetData[ref.selectedMindTrapValue.value]?.audio;
                                                        Navigator.pop(context);
                                                      },
                                                      title: CoreLocaleKeys.titlesInformationStrategiesNegativeThoughts
                                                          .tr(),
                                                      subtitle: AsLocaleKeys.lsNtTitle.tr(),
                                                      icon: Assets.icons.actionIcons.negativeThoughts,
                                                      onInfoTap: () {
                                                        final info = (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsNtInfoPanelsInformationText,
                                                          context,
                                                        ) as List)
                                                            .join('<br/><br/>');
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isCanSayManuallyPaused.value = true;
                                                          ref.infoCanSayAudioUrl.value = ref
                                                              .mindCanSayData[ref.selectedMindTrapValue.value]?.audio;
                                                          ref.headerInfoText.value = null;
                                                          ref.isAudioPanelCanSayVisible.value = false;
                                                        } else {
                                                          ref.isCanSayManuallyPaused.value = false;
                                                          ref.infoCanSayAudioUrl.value =
                                                              AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr();
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      onLearnTap: () {
                                                        final info = (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsNtInfoPanelsLearnText,
                                                          context,
                                                        ) as List)
                                                            .join('<br/><br/>');
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isCanSayManuallyPaused.value = true;
                                                          ref.infoCanSayAudioUrl.value = ref
                                                              .mindCanSayData[ref.selectedMindTrapValue.value]?.audio;
                                                          ref.headerInfoText.value = null;
                                                          ref.isAudioPanelCanSayVisible.value = false;
                                                        } else {
                                                          ref.isCanSayManuallyPaused.value = false;
                                                          ref.infoCanSayAudioUrl.value =
                                                              AsLocaleKeys.lsNtInfoPanelsLearnAudio.tr();
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      infoWidget: ValueListenableBuilder(
                                                        valueListenable: ref.headerInfoText,
                                                        builder: (
                                                          context,
                                                          headerPlanInfoTextV,
                                                          _,
                                                        ) {
                                                          return CustomInfoWidget(
                                                            customWidget: Column(
                                                              children: [
                                                                Html(
                                                                  data: ref.headerInfoText.value ?? '',
                                                                  style: {
                                                                    'strong': Style(
                                                                      fontSize: FontSize(AppSize.sp13),
                                                                      color: context.themeColors.darkOrangeColor,
                                                                      fontWeight: FontWeight.bold,
                                                                      fontFamily: 'Poppins',
                                                                    ),
                                                                    'body': Style(
                                                                      fontSize: FontSize(AppSize.sp13),
                                                                      color: context.themeColors.darkOrangeColor,
                                                                      fontFamily: 'Poppins',
                                                                    ),
                                                                  },
                                                                ),
                                                              ],
                                                            ),
                                                            onCloseTap: () {
                                                              ref.isCanSayManuallyPaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoCanSayAudioUrl.value = ref
                                                                  .mindCanSayData[ref.selectedMindTrapValue.value]
                                                                  ?.audio;
                                                            },
                                                            visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                            margin: EdgeInsets.symmetric(
                                                              vertical: AppSize.h8,
                                                            ),
                                                            bodyText: headerPlanInfoTextV,
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      //    color: Colors.pink,
                                                      height: AppSize.h120,
                                                      child: Row(
                                                        crossAxisAlignment: CrossAxisAlignment.stretch,
                                                        // Ensure both widgets stretch to the same height
                                                        children: [
                                                          Expanded(
                                                            flex: 6,
                                                            // 60% width
                                                            child: Align(
                                                              child: SizedBox(
                                                                width: double.infinity,
                                                                // Ensures it takes the full width of the expanded space
                                                                child: BlameTextAnimation(
                                                                  title: ref.getStringFromjson(ref.selectedMindTrapValue.value),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          SpaceH(
                                                            AppSize.w10,
                                                          ),
                                                          Expanded(
                                                            flex: 3,
                                                            // 40% width
                                                            child: Align(
                                                              child: Padding(
                                                                padding: EdgeInsets.symmetric(
                                                                  vertical: AppSize.h10,
                                                                ),
                                                                child: SizedBox(
                                                                  height: AppSize.h120,
                                                                  //  color: Colors.red,
                                                                  child: const LottieAssetWidget(
                                                                    assetPath:
                                                                        'assets/icons/action_icons/key_three_animation.json',
                                                                    //  fit: BoxFit.cover,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Center(
                                                      child: AppTextWidget(
                                                        ref.mindCanSayData[ref.selectedMindTrapValue.value]?.title ??
                                                            'Select a Mind Trap',
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    SpaceV(
                                                      AppSize.h20,
                                                    ),
                                                    AppTextWidget(
                                                      AsLocaleKeys.lsOvercomeBarrierCanSay.tr(),
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                    SpaceV(
                                                      AppSize.h20,
                                                    ),
                                                    AppTextWidget(
                                                      ref.mindCanSayData[ref.selectedMindTrapValue.value]?.text ??
                                                          'Select a Mind Trap',
                                                      style: context.textTheme.titleSmall,
                                                    ),
                                                    SpaceV(
                                                      AppSize.h20,
                                                    ),
                                                  ],
                                                ),
                                                CustomButton(
                                                  padding: EdgeInsets.zero,
                                                  title: CoreLocaleKeys.buttonsNext.tr(),
                                                  inProgress: state.isApiLoading,
                                                  onTap: () async {
                                                    if (ref.questionSetButtonStates.value.every(
                                                      (
                                                        element,
                                                      ) =>
                                                          element.value != ButtonState.bothDisabled,
                                                    )) {
                                                      // ref.infoWelDoneAudioUrl.value =
                                                      //     AsLocaleKeys.lsNtSummaryAudio.tr();
                                                      log('ref.infoAudioUrl.value ${ref.infoAudioUrl.value}');

                                                      // ref
                                                      //     .isAudioPanelVisible
                                                      //     .value =
                                                      // false;
                                                      await ref.negativeThoughtStrategyAPI(
                                                        ref: ref,
                                                        context: context,
                                                      );
                                                    } else {
                                                      CustomSnackbar.showErrorSnackBar(
                                                        message: AsLocaleKeys.lsNtErrorsRequired.tr(),
                                                      );
                                                    }
                                                  },
                                                  isBottom: true,
                                                  color: context.themeColors.blueColor,
                                                ),
                                              ],
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
