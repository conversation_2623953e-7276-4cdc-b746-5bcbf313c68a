import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/cubit/negative_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoudhts_understand_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyThoughtsPage extends StatelessWidget {
  const MyThoughtsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NegativeThoughtsCubit(),
      child: BlocBuilder<NegativeThoughtsCubit, NegativeThoughtsState>(
        builder: (ctx, state) {
          final ref = ctx.read<NegativeThoughtsCubit>();
          return PopScope(
            onPopInvokedWithResult: (didPop, result) {
              if (didPop) {
                ref.isManuallyPaused.value = false;
                ref.headerInfoText.value = null;
                ref.infoAudioUrl.value = null;
              }
            },
            child: ValueListenableBuilder(
              valueListenable: ref.infoAudioUrl,
              builder: (context, value, child) {
                return AppScaffold(
                  resizeToAvoidBottomInset: false,
                  scaffoldKey: ref.scaffoldKey,
                  isAudioPanelVisible: ref.isAudioPanelVisible,
                  infoAudioUrl: ref.infoAudioUrl,
                  isManuallyPaused: ref.isManuallyPaused,
                  drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      ref.scaffoldKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                      }
                    },
                  ),
                  body: ValueListenableBuilder(
                    valueListenable: ref.isButtonClicked,
                    builder: (context, value, child) {
                      return ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Column(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    return CustomRawScrollbar(
                                      child: SingleChildScrollView(
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                          child: ColoredBox(
                                            color: context.themeColors.whiteColor,
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                left: AppSize.w24,
                                                right: AppSize.w24,
                                                bottom: AppSize.h20,
                                                top: AppSize.h24,
                                              ),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    children: [
                                                      InformationPageHeadingWidget(
                                                        onBackArrowTap: () {
                                                          ref.infoAudioUrl.value = null;
                                                          Navigator.pop(context);
                                                        },
                                                        title: CoreLocaleKeys
                                                            .titlesInformationStrategiesNegativeThoughts
                                                            .tr(),
                                                        subtitle: AsLocaleKeys.lsNtTitle.tr(),
                                                        icon: Assets.icons.actionIcons.negativeThoughts,
                                                        onInfoTap: () {
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsNtInfoPanelsInformationText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                              ref.headerInfoText.value == info) {
                                                            ref.isManuallyPaused.value = true;
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.lsNtMindTrapsAudio.tr();
                                                            ref.headerInfoText.value = null;
                                                            ref.isAudioPanelVisible.value = false;
                                                          } else {
                                                            ref.isManuallyPaused.value = false;
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr();
                                                            ref.headerInfoText.value = info;
                                                          }
                                                          // if (ref.infoAudioUrl.value ==
                                                          //     AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr()) {
                                                          //   ref.infoAudioUrl.value =
                                                          //       AsLocaleKeys.lsNtMindTrapsAudio.tr();
                                                          // } else {
                                                          //   ref.infoAudioUrl.value =
                                                          //       AsLocaleKeys.lsNtInfoPanelsInformationAudio.tr();
                                                          // }
                                                        },
                                                        onLearnTap: () {
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsNtInfoPanelsLearnText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                              ref.headerInfoText.value == info) {
                                                            ref.isManuallyPaused.value = true;
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.lsNtMindTrapsAudio.tr();
                                                            ref.headerInfoText.value = null;
                                                            ref.isAudioPanelVisible.value = false;
                                                          } else {
                                                            ref.isManuallyPaused.value = false;
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.lsNtInfoPanelsLearnAudio.tr();
                                                            ref.headerInfoText.value = info;
                                                          }
                                                          // final audio = AsLocaleKeys.lsNtInfoPanelsLearnAudio.tr();
                                                          // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                                        },
                                                        infoWidget: ValueListenableBuilder(
                                                          valueListenable: ref.headerInfoText,
                                                          builder: (context, headerPlanInfoTextV, _) {
                                                            return CustomInfoWidget(
                                                              customWidget: Column(
                                                                children: [
                                                                  Html(
                                                                    data: ref.headerInfoText.value ?? '',
                                                                    style: {
                                                                      'strong': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontWeight: FontWeight.bold,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                      'body': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                    },
                                                                  ),
                                                                ],
                                                              ),
                                                              onCloseTap: () {
                                                                ref.isCanSayManuallyPaused.value = true;
                                                                ref.headerInfoText.value = null;
                                                                ref.infoAudioUrl.value = ref
                                                                    .mindCanSayData[ref.selectedMindTrapValue.value]
                                                                    ?.audio;
                                                              },
                                                              visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                              margin: EdgeInsets.symmetric(
                                                                vertical: AppSize.h8,
                                                              ),
                                                              bodyText: headerPlanInfoTextV,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                      VideoPlayerScreen(
                                                        imageList: [AsLocaleKeys.lsNtMindTrapsVideoPoster.tr()],
                                                        //  onTap: onPlayTap,
                                                        navigationFunction: () {},
                                                        videoList: [AsLocaleKeys.lsNtMindTrapsVideoSrc.tr()],
                                                        onTap: () {
                                                          ref.isManuallyPaused.value = true;
                                                          //ref.infoAudioUrl.value = null;
                                                        },
                                                        onVideoEnded: () {
                                                          AppNavigation.previousScreen(context);
                                                          //ref.infoAudioUrl.value = AsLocaleKeys.lsNtMindTrapsAudio.tr();
                                                        },
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsNtMindTrapsTitle.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      ValueListenableBuilder(
                                                        valueListenable: ref.selectMindTrapValue,
                                                        builder: (context, value, child) {
                                                          return ValueListenableBuilder(
                                                            valueListenable: ref.isButtonClicked,
                                                            builder: (context, value2, child) {
                                                              return ValueListenableBuilder(
                                                                valueListenable: ref.isButtonClicked,
                                                                builder: (context, value2, child) {
                                                                  return CustomRadioListWidget(
                                                                    isError: ref.isButtonClicked.value && value == '',
                                                                    isMultiOption: true,
                                                                    height: AppSize.h48,
                                                                    fontSize: AppSize.sp12,
                                                                    padding: EdgeInsets.zero,
                                                                    //isButtonClicked: assessmentCubit.isEmotionButtonClicked.value,
                                                                    options: (DynamicAssetLoader.getNestedValue(
                                                                      AsLocaleKeys.lsNtMindTrapsOptionsText,
                                                                      context,
                                                                    ) as List<dynamic>)
                                                                        .map(
                                                                          (e) => e['text'].toString(),
                                                                        )
                                                                        .toList(),
                                                                    selectedValue: value, // Safely access first item
                                                                    onChanged: (newValue) {
                                                                      if (newValue != null) {
                                                                        ref.selectMindTrapValue.value = newValue;
                                                                        ref.getMindTrapValue(newValue);
                                                                      }
                                                                    },
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  CustomButton(
                                                    padding: EdgeInsets.zero,
                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                    onTap: () async {
                                                      if (ref.selectMindTrapValue.value != '') {
                                                        ref.selectedMindTrapValue.logD;
                                                        ref.isManuallyPaused.value = false;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = null;
                                                        // ref.isAudioPanelVisible.value = false;
                                                        ref.isButtonClicked.value = false;

                                                        await AppNavigation.nextScreen(
                                                          context,
                                                          BlocProvider.value(
                                                            value: ref,
                                                            child: const MyThoughtsUnderstandPage(),
                                                          ),
                                                        );
                                                      } else {
                                                        ref.isButtonClicked.value = true;

                                                        // CustomSnackbar.showErrorSnackBar(
                                                        //   message: AsLocaleKeys.lsNtErrorsRequired.tr(),
                                                        // );
                                                      }
                                                    },
                                                    isBottom: true,
                                                    color: context.themeColors.blueColor,
                                                  ),
                                                  //  SpaceV(AppSize.h10),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
