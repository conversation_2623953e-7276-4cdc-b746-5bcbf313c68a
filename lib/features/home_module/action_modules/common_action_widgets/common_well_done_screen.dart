import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_confetti_animation.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/parse_tagged_text.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CommonWellDoneScreen extends StatelessWidget {
  const CommonWellDoneScreen({
    required this.wellDoneTitleText,
    required this.wellDoneDetailText,
    required this.controller,
    super.key,
    this.scaffoldKey,
    this.infoAudioUrl,
    this.isAudioPannelVisible,
    this.drawer,
    this.appBar,
    this.title,
    this.subtitle,
    this.icon,
    this.onInfoTap,
    this.onLearnTap,
    this.infoWidget,
    this.onButtonTap,
    this.custominfoWidget,
    this.onBackArrowTap,
    this.isManuallyPaused,
  });
  final Key? scaffoldKey;
  final ValueNotifier<String?>? infoAudioUrl;
  final ValueNotifier<bool>? isAudioPannelVisible;
  final ValueNotifier<bool>? isManuallyPaused;
  final Widget? drawer;
  final Widget? custominfoWidget;
  final PreferredSizeWidget? appBar;
  final String? title;
  final String? subtitle;
  final String? icon;
  final VoidCallback? onInfoTap;
  final VoidCallback? onLearnTap;
  final Widget? infoWidget;
  final String wellDoneTitleText;
  final String wellDoneDetailText;
  final VoidCallback? onButtonTap;
  final void Function()? onBackArrowTap;
  final ConfettiController controller;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      resizeToAvoidBottomInset: false,
      scaffoldKey: scaffoldKey,
      isManuallyPaused: isManuallyPaused,
      isAudioPanelVisible: isAudioPannelVisible,
      infoAudioUrl: infoAudioUrl,
      drawer: drawer,
      appBar: appBar,
      body: ColoredBox(
        color: context.themeColors.whiteColor,
        child: Column(
          children: [
            CustomConfettiAnimation(controller: controller),
            Expanded(
              child: Padding(
                padding:  EdgeInsets.only(right: AppSize.w4),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return CustomRawScrollbar(
                      child: SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: constraints.maxHeight,
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: AppSize.w22,
                              right: AppSize.w22,
                              top: AppSize.h20,
                              bottom: AppSize.h20,
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  children: [
                                    custominfoWidget ??
                                        InformationPageHeadingWidget(
                                          title:
                                              title ?? CoreLocaleKeys.titlesInformationStrategiesDifficultSituations.tr(),
                                          subtitle: subtitle ?? IsLocaleKeys.dsTitle.tr(),
                                          icon: icon ?? Assets.icons.infoPage.difficultSituation,
                                          onInfoTap: onInfoTap,
                                          onLearnTap: onLearnTap,
                                          infoWidget: infoWidget,
                                          onBackArrowTap: onBackArrowTap,
                                        ),
                                    AppTextWidget(
                                      textAlign: TextAlign.center,
                                      wellDoneTitleText,
                                      style: context.textTheme.titleSmall?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    SpaceV(AppSize.h14),
                                    RichText(
  text: parseTaggedText(
    text: wellDoneDetailText,
    baseStyle: context.textTheme.titleSmall,
  ),
)

                                  ],
                                ),
                                SpaceV(AppSize.h12),
                                CustomButton(
                                  padding: EdgeInsets.zero,
                                  title: CoreLocaleKeys.buttonsNext.tr(),
                                  onTap: onButtonTap ?? () {},
                                  isBottom: true,
                                  color: context.themeColors.blueColor,
                                ),
                                //SpaceV(AppSize.h10),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
