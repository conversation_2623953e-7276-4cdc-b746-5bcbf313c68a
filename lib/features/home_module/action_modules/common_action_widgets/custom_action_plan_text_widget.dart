import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/assets_path.dart';
import 'package:flutter/material.dart';

class CustomActionPlanTextWidget extends StatelessWidget {
  const CustomActionPlanTextWidget({
    required this.question,
    super.key, this.padding,
  });

  final String question;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    '>?>?>? quation = $question'.logV;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding:padding ??  EdgeInsets.only(top: AppSize.h10),
          child: const AppCachedNetworkImage(imageUrl: AssetsPath.greenBulletPoint),
        ),
        SpaceH(AppSize.w10),
        Expanded(
          child: AppTextWidget(
            question,
            style: context.textTheme.titleSmall?.copyWith(
              fontSize: AppSize.sp13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
