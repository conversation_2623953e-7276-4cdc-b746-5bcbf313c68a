// To parse this JSON data, do
//
//     final strategiesModel = strategiesModelFromJson(jsonString);

class StrategiesModel {
  StrategiesModel({
    this.success,
    this.strategies,
  });

  factory StrategiesModel.fromJson(Map<String, dynamic> json) => StrategiesModel(
        success: json['success'] as bool,
        strategies: json['strategies'] == null ? null : Strategies.fromJson(json['strategies'] as Map<String, dynamic>),
      );
  bool? success;
  Strategies? strategies;

  Map<String, dynamic> toJson() => {
        'success': success,
        'strategies': strategies?.toJson(),
      };
}

class Strategies {
  Strategies({
    this.ntAs,
    this.eiAs,
    this.lsAs,
    this.dsAs,
    this.ubAs,
    this.psAs,
    this.eiIs,
    this.dsIs,
    this.lsIs,
    this.ntIs,
    this.ubIs,
    this.psIs,
  });

  factory Strategies.fromJson(Map<String, dynamic> json) => Strategies(
        ntAs: json['ntAS'] == null
            ? []
            : List<NtA>.from((json['ntAS'] as List<dynamic>).map((x) => NtA.fromJson(x as Map<String, dynamic>))),
        eiAs: json['eiAS'] == null
            ? []
            : List<EiA>.from((json['eiAS'] as List<dynamic>).map((x) => EiA.fromJson(x as Map<String, dynamic>))),
        lsAs: json['lsAS'] == null
            ? []
            : List<LsA>.from((json['lsAS'] as List<dynamic>).map((x) => LsA.fromJson(x as Map<String, dynamic>))),
        dsAs: json['dsAS'] == null
            ? []
            : List<DsA>.from((json['dsAS'] as List<dynamic>).map((x) => DsA.fromJson(x as Map<String, dynamic>))),
        ubAs: json['ubAS'] == null
            ? []
            : List<UbA>.from((json['ubAS'] as List<dynamic>).map((x) => UbA.fromJson(x as Map<String, dynamic>))),
        psAs: json['psAS'] == null
            ? []
            : List<PsA>.from((json['psAS'] as List<dynamic>).map((x) => PsA.fromJson(x as Map<String, dynamic>))),
        eiIs: json['eiIS'] == null
            ? []
            : List<DsI>.from((json['eiIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
        dsIs: json['dsIS'] == null
            ? []
            : List<DsI>.from((json['dsIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
        lsIs: json['lsIS'] == null
            ? []
            : List<DsI>.from((json['lsIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
        ntIs: json['ntIS'] == null
            ? []
            : List<DsI>.from((json['ntIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
        ubIs: json['ubIS'] == null
            ? []
            : List<DsI>.from((json['ubIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
        psIs: json['psIS'] == null
            ? []
            : List<DsI>.from((json['psIS'] as List<dynamic>).map((x) => DsI.fromJson(x as Map<String, dynamic>))),
      );
  List<NtA>? ntAs;
  List<EiA>? eiAs;
  List<LsA>? lsAs;
  List<DsA>? dsAs;
  List<UbA>? ubAs;
  List<PsA>? psAs;
  List<DsI>? eiIs;
  List<DsI>? dsIs;
  List<DsI>? lsIs;
  List<DsI>? ntIs;
  List<DsI>? ubIs;
  List<DsI>? psIs;

  Map<String, dynamic> toJson() => {
        'ntAS': ntAs == null ? <dynamic>[] : List<dynamic>.from(ntAs!.map((x) => x.toJson())),
        'eiAS': eiAs == null ? <dynamic>[] : List<dynamic>.from(eiAs!.map((x) => x.toJson())),
        'lsAS': lsAs == null ? <dynamic>[] : List<dynamic>.from(lsAs!.map((x) => x.toJson())),
        'dsAS': dsAs == null ? <dynamic>[] : List<dynamic>.from(dsAs!.map((x) => x.toJson())),
        'ubAS': ubAs == null ? <dynamic>[] : List<dynamic>.from(ubAs!.map((x) => x.toJson())),
        'psAS': psAs == null ? <dynamic>[] : List<dynamic>.from(psAs!.map((x) => x.toJson())),
        'eiIS': eiIs == null ? <dynamic>[] : List<dynamic>.from(eiIs!.map((x) => x.toJson())),
        'dsIS': dsIs == null ? <dynamic>[] : List<dynamic>.from(dsIs!.map((x) => x.toJson())),
        'lsIS': lsIs == null ? <dynamic>[] : List<dynamic>.from(lsIs!.map((x) => x.toJson())),
        'ntIS': ntIs == null ? <dynamic>[] : List<dynamic>.from(ntIs!.map((x) => x.toJson())),
        'ubIS': ubIs == null ? <dynamic>[] : List<dynamic>.from(ubIs!.map((x) => x.toJson())),
        'psIS': psIs == null ? <dynamic>[] : List<dynamic>.from(psIs!.map((x) => x.toJson())),
      };
}

class DsI {
  DsI({
    this.time,
  });

  factory DsI.fromJson(Map<String, dynamic> json) => DsI(
        time: json['time'] as int,
      );
  int? time;

  Map<String, dynamic> toJson() => {
        'time': time,
      };
}

class DsA {
  DsA({
    this.time,
    this.data,
  });

  factory DsA.fromJson(Map<String, dynamic> json) => DsA(
        time: json['time'] as int,
        data: json['data'] == null
            ? []
            : List<Datum>.from((json['data'] as List<dynamic>).map((x) => Datum.fromJson(x as Map<String, dynamic>))),
      );
  int? time;
  List<Datum>? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  Datum({
    this.what,
    this.why,
    this.how,
    this.lat,
    this.lng,
    this.customHow,
    this.customWhy,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        what: json['what'] == null ? null : json['what'] as String?,
        why: json['why'] as int?,
        how: json['how'] as int?,
        lat: json['lat'] == null ? null : json['lat'].toDouble() as double?,
        lng: json['lng'] == null ? null : json['lng']?.toDouble() as double?,
        customHow: json['customHow'] == null ? null : json['customHow'] as String?,
        customWhy: json['customWhy'] == null ? null : json['customWhy'] as String?,
      );
  String? what;
  String? customWhy;
  String? customHow;
  int? why;
  int? how;
  double? lat;
  double? lng;

  Datum copyWith({
    String? what,
    int? why,
    int? how,
    double? lat,
    double? lng,
    String? customHow,
    String? customWhy,
  }) {
    return Datum(
      what: what ?? this.what, // Use the provided value or keep the current value
      why: why ?? this.why,
      how: how ?? this.how,
      lat: lat ?? this.lat,
      lng: lng ?? this.lng,
      customHow: customHow ?? customHow,
      customWhy: customWhy ?? customWhy,
    );
  }

  Map<String, dynamic> toJson() => {
        'what': what,
        'why': why,
        'how': how,
        'lat': lat,
        'lng': lng,
        'customWhy': customWhy,
        'customHow': customHow,
      };
}

class EiA {
  EiA({
    this.time,
    this.data,
  });

  factory EiA.fromJson(Map<String, dynamic> json) => EiA(
        time: json['time'] as int,
        data: json['data'] == null ? null : EiAData.fromJson(json['data'] as Map<String, dynamic>),
      );
  int? time;
  EiAData? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data?.toJson(),
      };
}

class EiAData {
  EiAData({
    this.rateBefore,
    this.rateAfter,
    // this.videoName,
  });

  factory EiAData.fromJson(Map<String, dynamic> json) => EiAData(
        rateBefore: json['rateBefore'] as int? ?? 0,
        rateAfter: json['rateAfter'] as int? ?? 0,
        //videoName: json['videoName'] as String,
      );
  int? rateBefore;
  int? rateAfter;
  //String? videoName;

  Map<String, dynamic> toJson() => {
        'rateBefore': rateBefore,
        'rateAfter': rateAfter,
        // 'videoName': videoName,
      };
}

class LsA {
  LsA({
    this.time,
    this.data,
  });

  factory LsA.fromJson(Map<String, dynamic> json) => LsA(
        time: json['time'] as int,
        data: json['data'] == null ? null : LsAData.fromJson(json['data'] as Map<String, dynamic>),
      );
  int? time;
  LsAData? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data?.toJson(),
      };
}

class LsAData {
  LsAData({
    this.mountain,
    this.stepOne,
    this.stepTwo,
    this.stepThree,
    this.stepFour,
  });

  factory LsAData.fromJson(Map<String, dynamic> json) => LsAData(
        mountain: json['mountain'] == null ? null : Mountain.fromJson(json['mountain'] as Map<String, dynamic>),
        stepOne: json['stepOne'] == null ? null : StepOne.fromJson(json['stepOne'] as Map<String, dynamic>),
        stepTwo: json['stepTwo'] == null ? null : StepTwo.fromJson(json['stepTwo'] as Map<String, dynamic>),
        stepThree: json['stepThree'] == null ? null : StepThree.fromJson(json['stepThree'] as Map<String, dynamic>),
        stepFour: json['stepFour'] == null ? null : StepFour.fromJson(json['stepFour'] as Map<String, dynamic>),
      );
  Mountain? mountain;
  StepOne? stepOne;
  StepTwo? stepTwo;
  StepThree? stepThree;
  StepFour? stepFour;

  Map<String, dynamic> toJson() => {
        'mountain': mountain?.toJson(),
        'stepOne': stepOne?.toJson(),
        'stepTwo': stepTwo?.toJson(),
        'stepThree': stepThree?.toJson(),
        'stepFour': stepFour?.toJson(),
      };
}

// class Mountain {
//   Mountain({
//     this.name,
//   });

//   factory Mountain.fromJson(Map<String, dynamic> json) => Mountain(
//         name: mountainNameValues.map[json['name']],
//       );
//   MountainName? name;

//   Map<String, dynamic> toJson() => {
//         'name': mountainNameValues.reverse[name],
//       };
// }

class Mountain {
  Mountain({
    this.name,
  });

  factory Mountain.fromJson(Map<String, dynamic> json) => Mountain(
        name: json['name'] != null ? json['name'] as String : null,
      );
  String? name;

  Map<String, dynamic> toJson() => {
        'name': name,
      };
}

class StepFour {
  StepFour({
    this.benefits,
    this.otherBenefit,
  });

  factory StepFour.fromJson(Map<String, dynamic> json) => StepFour(
        benefits: json['benefits'] == null ? [] : List<int>.from((json['benefits'] as List<dynamic>).map((x) => x)),
        otherBenefit: json['otherBenefit'] != null ? json['otherBenefit'] as String : null,
      );
  List<int>? benefits;
  String? otherBenefit;

  Map<String, dynamic> toJson() => {
        'benefits': benefits == null ? <dynamic>[] : List<dynamic>.from(benefits!.map((x) => x)),
        'otherBenefit': otherBenefit,
      };
}

class StepOne {
  StepOne({
    this.lifeGoal,
    this.nextStep,
  });

  factory StepOne.fromJson(Map<String, dynamic> json) => StepOne(
        lifeGoal: json['lifeGoal'] as String,
        nextStep: json['nextStep'] as String,
      );
  String? lifeGoal;
  String? nextStep;

  Map<String, dynamic> toJson() => {
        'lifeGoal': lifeGoal,
        'nextStep': nextStep,
      };
}

class StepThree {
  StepThree({
    this.date,
  });

  factory StepThree.fromJson(Map<String, dynamic> json) => StepThree(
        date: json['date'] as String,
      );
  String? date;

  Map<String, dynamic> toJson() => {
        'date': date,
      };
}

class StepTwo {
  StepTwo({
    this.barrier,
  });

  factory StepTwo.fromJson(Map<String, dynamic> json) => StepTwo(
        barrier: json['barrier'] as int,
      );
  int? barrier;

  Map<String, dynamic> toJson() => {
        'barrier': barrier,
      };
}

class NtA {
  NtA({
    this.time,
    this.data,
  });

  factory NtA.fromJson(Map<String, dynamic> json) => NtA(
        time: json['time'] as int,
        data: json['data'] == null ? null : NtAData.fromJson(json['data'] as Map<String, dynamic>),
      );
  int? time;
  NtAData? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data?.toJson(),
      };
}

class NtAData {
  NtAData({
    this.mindTrap,
    this.lookAtYourThoughts,
    this.canDo,
  });

  factory NtAData.fromJson(Map<String, dynamic> json) => NtAData(
        mindTrap: json['mindTrap'] as String,
        lookAtYourThoughts: json['lookAtYourThoughts'] == null
            ? null
            : LookAtYourThoughts.fromJson(json['lookAtYourThoughts'] as Map<String, dynamic>),
        canDo: json['canDo'] != null ? json['canDo'] as String : null,
      );
  String? mindTrap;
  LookAtYourThoughts? lookAtYourThoughts;
  String? canDo;

  Map<String, dynamic> toJson() => {
        'mindTrap': mindTrap,
        'lookAtYourThoughts': lookAtYourThoughts?.toJson(),
        'canDo': canDo,
      };
}

class LookAtYourThoughts {
  LookAtYourThoughts({
    this.q1,
    this.q2,
    this.q3,
    this.q4,
    this.other,
  });

  factory LookAtYourThoughts.fromJson(Map<String, dynamic> json) => LookAtYourThoughts(
        q1: json['q1'] != null ? json['q1'] as int : null,
        q2: json['q2'] != null ? json['q2'] as int : null,
        q3: json['q3'] != null ? json['q3'] as int : null,
        q4: json['q4'] != null ? json['q4'] as int : null,
        other: json['other'] != null ? json['other'] as String : null,
      );
  int? q1;
  int? q2;
  int? q3;
  int? q4;
  String? other;

  Map<String, dynamic> toJson() => {
        'q1': q1,
        'q2': q2,
        'q3': q3,
        'q4': q4,
        'other': other,
      };
}

class PsA {
  PsA({
    this.time,
    this.data,
  });

  factory PsA.fromJson(Map<String, dynamic> json) => PsA(
        time: json['time'] as int? ?? 0,
        data: json['data'] == null ? null : PsAData.fromJson(json['data'] as Map<String, dynamic>),
      );
  int? time;
  PsAData? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data?.toJson(),
      };
}

class PsAData {
  PsAData({
    this.rateBefore,
    this.rateAfter,
  });

  factory PsAData.fromJson(Map<String, dynamic> json) => PsAData(
        rateBefore: json['rateBefore'] as int? ?? 0,
        rateAfter: json['rateAfter'] as int? ?? 0,
      );
  int? rateBefore;
  int? rateAfter;

  Map<String, dynamic> toJson() => {
        'rateBefore': rateBefore,
        'rateAfter': rateAfter,
      };
}

class UbA {
  UbA({
    this.time,
    this.data,
  });

  factory UbA.fromJson(Map<String, dynamic> json) => UbA(
        time: json['time'] as int,
        data: json['data'] == null ? null : UbAData.fromJson(json['data'] as Map<String, dynamic>),
      );
  int? time;
  UbAData? data;

  Map<String, dynamic> toJson() => {
        'time': time,
        'data': data?.toJson(),
      };
}

class UbAData {
  UbAData({
    this.thur,
    this.fri,
    this.sat,
    this.sun,
    this.mon,
    this.wed,
    this.tue,
  });

  factory UbAData.fromJson(Map<String, dynamic> json) => UbAData(
        thur: json['THUR'] == null
            ? []
            : List<Fri>.from((json['THUR'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        fri: json['FRI'] == null
            ? []
            : List<Fri>.from((json['FRI'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        sat: json['SAT'] == null
            ? []
            : List<Fri>.from((json['SAT'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        sun: json['SUN'] == null
            ? []
            : List<Fri>.from((json['SUN'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        mon: json['MON'] == null
            ? []
            : List<Fri>.from((json['MON'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        tue: json['TUE'] == null
            ? []
            : List<Fri>.from((json['TUE'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
        wed: json['WED'] == null
            ? []
            : List<Fri>.from((json['WED'] as List<dynamic>).map((x) => Fri.fromJson(x as Map<String, dynamic>))),
      );
  List<Fri>? thur;
  List<Fri>? fri;
  List<Fri>? sat;
  List<Fri>? sun;
  List<Fri>? mon;
  List<Fri>? tue;
  List<Fri>? wed;

  Map<String, dynamic> toJson() => {
        'THUR': thur == null ? <dynamic>[] : List<dynamic>.from(thur!.map((x) => x.toJson())),
        'FRI': fri == null ? <dynamic>[] : List<dynamic>.from(fri!.map((x) => x.toJson())),
        'SAT': sat == null ? <dynamic>[] : List<dynamic>.from(sat!.map((x) => x.toJson())),
        'SUN': sun == null ? <dynamic>[] : List<dynamic>.from(sun!.map((x) => x.toJson())),
        'MON': mon == null ? <dynamic>[] : List<dynamic>.from(mon!.map((x) => x.toJson())),
        'TUE': tue == null ? <dynamic>[] : List<dynamic>.from(tue!.map((x) => x.toJson())),
        'WED': wed == null ? <dynamic>[] : List<dynamic>.from(wed!.map((x) => x.toJson())),
      };
}

class Fri {
  Fri({
    this.name,
    this.time,
    this.type,
    this.day,
    this.color,
  });

  factory Fri.fromJson(Map<String, dynamic> json) => Fri(
        color: json['color'] == null ? null : Color1.fromJson(json['color'] as Map<String, dynamic>),
        name: json['name'] as String?,
        time: json['time'] as String?,
        type: json['type'] as String?,
        day: json['day'] as String?,
      );

  String? name;
  String? time;
  String? type;
  String? day;
  Color1? color;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{
      'name': name,
      'time': time,
      'type': type,
      'day': day,
    };

    // Add color only if it's not null
    if (color != null) {
      data['color'] = color?.toJson();
    }

    return data;
  }
}

class Color1 {
  Color1({
    this.colorDefault,
  });

  factory Color1.fromJson(Map<String, dynamic> json) => Color1(
        colorDefault: json['default'] as String?,
      );

  String? colorDefault;

  Map<String, dynamic> toJson() => {
        'default': colorDefault,
      };
}
