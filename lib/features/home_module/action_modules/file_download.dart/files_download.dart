import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:breakingfree_v2/res/logger.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:media_store_plus/media_store_plus.dart';
import 'package:open_file/open_file.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class FilesDownload {
  static Future<void> downloadAndOpenPdf(String encodedStr, String fileName) async {
    //  String csvContent;

    String? directoryPath;

    try {
      final csvContent = base64.decode(encodedStr);
      final downloadsDirectory = await getApplicationDocumentsDirectory();
      directoryPath = downloadsDirectory.path;
      'android directory path : $directoryPath'.logD;
      final filePath = path.join(directoryPath, fileName);
      final file = File(filePath);
      await file.writeAsBytes(csvContent);
      if (Platform.isAndroid) {
        final mediaStorePlugin = MediaStore();
        // final downloadsDirectory = Directory('/storage/emulated/0/Download/');
        final res = await mediaStorePlugin.saveFile(
          tempFilePath: file.path,
          dirType: DirType.download,
          dirName: DirType.download.defaults,
        );
        res?.isSuccessful.logD;
        res?.uri.toString().logD;
        res.logD;
      }
      // CustomSnackbar.showSucessSnackBar(
      //   message: 'pdf downloaded successfully',
      // );
      await file.writeAsBytes(csvContent);
      await OpenFile.open(filePath);
      'File downloaded successfully'.logD;
    } catch (e) {
      'Error saving CSV file: $e'.logD;
    }
  }

  static Future<void> downloadAndOpenCsv(String encodedStr, String fileName) async {
    //  String csvContent;

    String? directoryPath;

    try {
      final downloadsDirectory = await getApplicationDocumentsDirectory();
      directoryPath = downloadsDirectory.path;
      'android directory path : $directoryPath'.logD;
      final filePath = path.join(directoryPath, fileName);
      final file = File(filePath);
      await file.writeAsString(encodedStr);
      if (Platform.isAndroid) {
        final mediaStorePlugin = MediaStore();
        // final downloadsDirectory = Directory('/storage/emulated/0/Download/');
        final res = await mediaStorePlugin.saveFile(
          tempFilePath: file.path,
          dirType: DirType.download,
          dirName: DirType.download.defaults,
        );
        res?.isSuccessful.logD;
        res?.uri.toString().logD;
        res.logD;
      }
      // CustomSnackbar.showSucessSnackBar(
      //   message: 'pdf downloaded successfully',
      // );
      await file.writeAsString(encodedStr);
      await OpenFile.open(filePath);
      'File downloaded successfully'.logD;
    } catch (e) {
      'Error saving CSV file: $e'.logD;
    }
  }

  // static Future<void> downloadAndOpenPdf(String encodedStr, String fileName) async {
  //   try {
  //     String? dir;

  //     // Decode the base64 string
  //     final bytes = base64.decode(encodedStr);

  //     // Get the correct path based on platform
  //     if (Platform.isAndroid) {
  //       dir = await _getDownloadDirectory();
  //     } else if (Platform.isIOS) {
  //       dir = (await getApplicationDocumentsDirectory()).path;
  //     }

  //     if (dir == null || dir.isEmpty) {
  //       log('Failed to get a valid directory path.');
  //       return;
  //     }

  //     final filePath = '$dir/$fileName';

  //     // Save the file locally
  //     final file = File(filePath);
  //     await file.writeAsBytes(bytes);

  //     // Log the file path for debugging
  //     log('File saved at: $filePath');

  //     // Open the file
  //     await OpenFile.open(filePath);
  //   } catch (e) {
  //     log('Error during download and open process: $e');
  //   }
  // }

  Future<String?> getDownloadDirectory() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 30) {
        // Android 11+ requires manageExternalStorage permission
        final status = await Permission.manageExternalStorage.request();

        if (!status.isGranted) {
          log('Permission denied for manageExternalStorage.');
          return null;
        }
        // Use the public "Download" directory
        return '/storage/emulated/0/Download';
      } else {
        // Use app-specific external storage for Android 10 and below
        final directory = await getExternalStorageDirectory();
        return directory?.path;
      }
    } else {
      return null; // Not Android
    }
  }

  Future<void> handlePermissions() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 30) {
        // Android 11+ (API 30+) requires manageExternalStorage permission
        final status = await Permission.manageExternalStorage.request();

        if (status.isGranted) {
          log('Permission granted. Proceeding with file operations.');
        } else if (status.isDenied) {
          log('Permission denied. Cannot proceed.');
        } else if (status.isPermanentlyDenied) {
          log('Permission permanently denied. Redirecting to app settings.');
          await openAppSettings();
        }
      } else {
        // For Android 10 and below, request storage permissions
        final status = await Permission.storage.request();

        if (status.isGranted) {
          log('Storage permission granted.');
        } else if (status.isDenied) {
          log('Storage permission denied.');
        } else if (status.isPermanentlyDenied) {
          log('Storage permission permanently denied. Redirecting to app settings.');
          await openAppSettings();
        }
      }
    } else {
      log('This is not an Android device.');
    }
  }
}
