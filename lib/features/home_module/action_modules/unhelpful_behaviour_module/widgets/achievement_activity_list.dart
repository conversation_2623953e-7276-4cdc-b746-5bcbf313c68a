import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';

class AchievementActivictyList {
  static void addAchievementActivity(
    List<String> imagesList,
    List<String> imagesNameList,
    List<String> imageDetailList,
  ) {
    // Add activities to the list

    imagesNameList.clear(); // Clear the existing list to avoid duplication
    imagesList.clear(); // Clear the existing list to avoid duplication
    imageDetailList.clear(); // Clear the existing list to avoid duplication

    imagesNameList.addAll([
      AsLocaleKeys.lsUbAchievementsDoBreakingFreeName.tr(),
      AsLocaleKeys.lsUbAchievementsDoExerciseName.tr(),
      AsLocaleKeys.lsUbAchievementsCleanMyHomeName.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeIroningName.tr(),
      AsLocaleKeys.lsUbAchievementsShopForFoodName.tr(),
      AsLocaleKeys.lsUbAchievementsCookAMealName.tr(),
      AsLocaleKeys.lsUbAchievementsAlterMyHomeName.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeDIYName.tr(),
      AsLocaleKeys.lsUbAchievementsAttendWorkName.tr(),
      AsLocaleKeys.lsUbAchievementsAttendCollegeName.tr(),
      AsLocaleKeys.lsUbAchievementsHelpSomeoneName.tr(),
      AsLocaleKeys.lsUbAchievementsDoVolunteeringName.tr(),
      AsLocaleKeys.lsUbAchievementsWriteAListName.tr(),
      AsLocaleKeys.lsUbAchievementsWashUpName.tr(),
      AsLocaleKeys.lsUbAchievementsTidyUpName.tr(),
      AsLocaleKeys.lsUbAchievementsWashSomethingName.tr(),
      AsLocaleKeys.lsUbAchievementsWriteABlogName.tr(),
      AsLocaleKeys.lsUbAchievementsGo1DaySoberName.tr(),
      AsLocaleKeys.lsUbAchievementsGo7DaysSoberName.tr(),
      AsLocaleKeys.lsUbAchievementsGo30DaysSoberName.tr(),
      AsLocaleKeys.lsUbAchievementsBookAMeetingName.tr(),
      AsLocaleKeys.lsUbAchievementsPlanMyFinancesName.tr(),
      AsLocaleKeys.lsUbAchievementsCreateACVName.tr(),
      AsLocaleKeys.lsUbAchievementsApplyForAJobName.tr(),
      AsLocaleKeys.lsUbAchievementsDoAnInterviewName.tr(),
      AsLocaleKeys.lsUbAchievementsSeeADoctorName.tr(),
      AsLocaleKeys.lsUbAchievementsSeeAPharmacistName.tr(), // See a pharmacist
      AsLocaleKeys.lsUbAchievementsSeeADentistName.tr(), // See a dentist
      AsLocaleKeys.lsUbAchievementsSeeAnOpticianName.tr(), // See an optician
      AsLocaleKeys.lsUbAchievementsSeeACounsellorName.tr(), // See a counsellor
      AsLocaleKeys.lsUbAchievementsAttendAGroupName.tr(), // Attend a group
      AsLocaleKeys.lsUbAchievementsHaveAcupunctureName.tr(), // Have acupuncture
      AsLocaleKeys.lsUbAchievementsSeeALawyerName.tr(), // See a lawyer
      AsLocaleKeys.lsUbAchievementsGoToABankName.tr(), // Go to a bank
      AsLocaleKeys.lsUbAchievementsFindANewHomeName.tr(), // Find a new home
      AsLocaleKeys.lsUbAchievementsGoToALibraryName.tr(), // Go to a library
      AsLocaleKeys.lsUbAchievementsAttendACourseName.tr(), // Attend a course
      AsLocaleKeys.lsUbAchievementsGetAHaircutName.tr(), // Get a haircut
      AsLocaleKeys.lsUbAchievementsHaveAShaveName.tr(), // Have a shave
      AsLocaleKeys.lsUbAchievementsGrowPlantsName.tr(),
      AsLocaleKeys.lsUbAchievementsGrowVegetablesName.tr(),
      AsLocaleKeys.lsUbAchievementsAidMyCommunityName.tr(),
    ]);

    imagesList.addAll([
      AsLocaleKeys.lsUbAchievementsDoBreakingFreeImg.tr(),
      AsLocaleKeys.lsUbAchievementsDoExerciseImg.tr(),
      AsLocaleKeys.lsUbAchievementsCleanMyHomeImg.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeIroningImg.tr(),
      AsLocaleKeys.lsUbAchievementsShopForFoodImg.tr(),
      AsLocaleKeys.lsUbAchievementsCookAMealImg.tr(),
      AsLocaleKeys.lsUbAchievementsAlterMyHomeImg.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeDIYImg.tr(),
      AsLocaleKeys.lsUbAchievementsAttendWorkImg.tr(),
      AsLocaleKeys.lsUbAchievementsAttendCollegeImg.tr(),
      AsLocaleKeys.lsUbAchievementsHelpSomeoneImg.tr(),
      AsLocaleKeys.lsUbAchievementsDoVolunteeringImg.tr(),
      AsLocaleKeys.lsUbAchievementsWriteAListImg.tr(),
      AsLocaleKeys.lsUbAchievementsWashUpImg.tr(),
      AsLocaleKeys.lsUbAchievementsTidyUpImg.tr(),
      AsLocaleKeys.lsUbAchievementsWashSomethingImg.tr(),
      AsLocaleKeys.lsUbAchievementsWriteABlogImg.tr(),
      AsLocaleKeys.lsUbAchievementsGo1DaySoberImg.tr(),
      AsLocaleKeys.lsUbAchievementsGo7DaysSoberImg.tr(),
      AsLocaleKeys.lsUbAchievementsGo30DaysSoberImg.tr(),
      AsLocaleKeys.lsUbAchievementsBookAMeetingImg.tr(),
      AsLocaleKeys.lsUbAchievementsPlanMyFinancesImg.tr(),
      AsLocaleKeys.lsUbAchievementsCreateACVImg.tr(),
      AsLocaleKeys.lsUbAchievementsApplyForAJobImg.tr(),
      AsLocaleKeys.lsUbAchievementsDoAnInterviewImg.tr(),
      AsLocaleKeys.lsUbAchievementsSeeADoctorImg.tr(),
      AsLocaleKeys.lsUbAchievementsSeeAPharmacistImg.tr(), // See a pharmacist
      AsLocaleKeys.lsUbAchievementsSeeADentistImg.tr(), // See a dentist
      AsLocaleKeys.lsUbAchievementsSeeAnOpticianImg.tr(), // See an optician
      AsLocaleKeys.lsUbAchievementsSeeACounsellorImg.tr(), // See a counsellor
      AsLocaleKeys.lsUbAchievementsAttendAGroupImg.tr(), // Attend a group
      AsLocaleKeys.lsUbAchievementsHaveAcupunctureImg.tr(), // Have acupuncture
      AsLocaleKeys.lsUbAchievementsSeeALawyerImg.tr(), // See a lawyer
      AsLocaleKeys.lsUbAchievementsGoToABankImg.tr(), // Go to a bank
      AsLocaleKeys.lsUbAchievementsFindANewHomeImg.tr(), // Find a new home
      AsLocaleKeys.lsUbAchievementsGoToALibraryImg.tr(), // Go to a library
      AsLocaleKeys.lsUbAchievementsAttendACourseImg.tr(), // Attend a course
      AsLocaleKeys.lsUbAchievementsGetAHaircutImg.tr(), // Get a haircut
      AsLocaleKeys.lsUbAchievementsHaveAShaveImg.tr(), // Have a shave
      AsLocaleKeys.lsUbAchievementsGrowPlantsImg.tr(),
      AsLocaleKeys.lsUbAchievementsGrowVegetablesImg.tr(),
      AsLocaleKeys.lsUbAchievementsAidMyCommunityImg.tr(),
    ]);

    imageDetailList.addAll([
      AsLocaleKeys.lsUbAchievementsDoBreakingFreeImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsDoExerciseImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsCleanMyHomeImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeIroningImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsShopForFoodImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsCookAMealImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsAlterMyHomeImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsDoSomeDIYImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsAttendWorkImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsAttendCollegeImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsHelpSomeoneImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsDoVolunteeringImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsWriteAListImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsWashUpImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsTidyUpImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsWashSomethingImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsWriteABlogImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsGo1DaySoberImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsGo7DaysSoberImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsGo30DaysSoberImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsBookAMeetingImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsPlanMyFinancesImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsCreateACVImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsApplyForAJobImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsDoAnInterviewImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsSeeADoctorImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsSeeAPharmacistImgFull.tr(), // See a pharmacist
      AsLocaleKeys.lsUbAchievementsSeeADentistImgFull.tr(), // See a dentist
      AsLocaleKeys.lsUbAchievementsSeeAnOpticianImgFull.tr(), // See an optician
      AsLocaleKeys.lsUbAchievementsSeeACounsellorImgFull.tr(), // See a counsellor
      AsLocaleKeys.lsUbAchievementsAttendAGroupImgFull.tr(), // Attend a group
      AsLocaleKeys.lsUbAchievementsHaveAcupunctureImgFull.tr(), // Have acupuncture
      AsLocaleKeys.lsUbAchievementsSeeALawyerImgFull.tr(), // See a lawyer
      AsLocaleKeys.lsUbAchievementsGoToABankImgFull.tr(), // Go to a bank
      AsLocaleKeys.lsUbAchievementsFindANewHomeImgFull.tr(), // Find a new home
      AsLocaleKeys.lsUbAchievementsGoToALibraryImgFull.tr(), // Go to a library
      AsLocaleKeys.lsUbAchievementsAttendACourseImgFull.tr(), // Attend a course
      AsLocaleKeys.lsUbAchievementsGetAHaircutImgFull.tr(), // Get a haircut
      AsLocaleKeys.lsUbAchievementsHaveAShaveImgFull.tr(), // Have a shave
      AsLocaleKeys.lsUbAchievementsGrowPlantsImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsGrowVegetablesImgFull.tr(),
      AsLocaleKeys.lsUbAchievementsAidMyCommunityImgFull.tr(),
    ]);
  }

  static Map<String, String> activityImages = {
    'Do Breaking Free': AsLocaleKeys.lsUbAchievementsDoBreakingFreeImg.tr(),
    'Do exercise': AsLocaleKeys.lsUbAchievementsDoExerciseImg.tr(),
    'Clean my home': AsLocaleKeys.lsUbAchievementsCleanMyHomeImg.tr(),
    'Do some ironing': AsLocaleKeys.lsUbAchievementsDoSomeIroningImg.tr(),
    'Shop for food': AsLocaleKeys.lsUbAchievementsShopForFoodImg.tr(),
    'Cook a meal': AsLocaleKeys.lsUbAchievementsCookAMealImg.tr(),
    'Alter my home': AsLocaleKeys.lsUbAchievementsAlterMyHomeImg.tr(),
    'Do some DIY': AsLocaleKeys.lsUbAchievementsDoSomeDIYImg.tr(),
    'Attend work': AsLocaleKeys.lsUbAchievementsAttendWorkImg.tr(),
    'Attend college': AsLocaleKeys.lsUbAchievementsAttendCollegeImg.tr(),
    'Help someone': AsLocaleKeys.lsUbAchievementsHelpSomeoneImg.tr(),
    'Do volunteering': AsLocaleKeys.lsUbAchievementsDoVolunteeringImg.tr(),
    'Write a list': AsLocaleKeys.lsUbAchievementsWriteAListImg.tr(),
    'Wash up': AsLocaleKeys.lsUbAchievementsWashUpImg.tr(),
    'Tidy up': AsLocaleKeys.lsUbAchievementsTidyUpImg.tr(),
    'Wash something': AsLocaleKeys.lsUbAchievementsWashSomethingImg.tr(),
    'Write a blog': AsLocaleKeys.lsUbAchievementsWriteABlogImg.tr(),
    'Go 1 day sober': AsLocaleKeys.lsUbAchievementsGo1DaySoberImg.tr(),
    'Go 7 days sober': AsLocaleKeys.lsUbAchievementsGo7DaysSoberImg.tr(),
    'Go 30 days sober': AsLocaleKeys.lsUbAchievementsGo30DaysSoberImg.tr(),
    'Book a meeting': AsLocaleKeys.lsUbAchievementsBookAMeetingImg.tr(),
    'Plan my finances': AsLocaleKeys.lsUbAchievementsPlanMyFinancesImg.tr(),
    'Create a CV': AsLocaleKeys.lsUbAchievementsCreateACVImg.tr(),
    'Apply for a job': AsLocaleKeys.lsUbAchievementsApplyForAJobImg.tr(),
    'Do an interview': AsLocaleKeys.lsUbAchievementsDoAnInterviewImg.tr(),
    'See a doctor': AsLocaleKeys.lsUbAchievementsSeeADoctorImg.tr(),
    'See a pharmacist': AsLocaleKeys.lsUbAchievementsSeeAPharmacistImg.tr(),
    'See a dentist': AsLocaleKeys.lsUbAchievementsSeeADentistImg.tr(),
    'See an optician': AsLocaleKeys.lsUbAchievementsSeeAnOpticianImg.tr(),
    'See a counsellor': AsLocaleKeys.lsUbAchievementsSeeACounsellorImg.tr(),
    'Attend a group': AsLocaleKeys.lsUbAchievementsAttendAGroupImg.tr(),
    'Have acupuncture': AsLocaleKeys.lsUbAchievementsHaveAcupunctureImg.tr(),
    'See a lawyer': AsLocaleKeys.lsUbAchievementsSeeALawyerImg.tr(),
    'Go to a bank': AsLocaleKeys.lsUbAchievementsGoToABankImg.tr(),
    'Find a new home': AsLocaleKeys.lsUbAchievementsFindANewHomeImg.tr(),
    'Go to a library': AsLocaleKeys.lsUbAchievementsGoToALibraryImg.tr(),
    'Attend a course': AsLocaleKeys.lsUbAchievementsAttendACourseImg.tr(),
    'Get a haircut': AsLocaleKeys.lsUbAchievementsGetAHaircutImg.tr(),
    'Have a shave': AsLocaleKeys.lsUbAchievementsHaveAShaveImg.tr(),
    'Grow plants': AsLocaleKeys.lsUbAchievementsGrowPlantsImg.tr(),
    'Grow vegetables': AsLocaleKeys.lsUbAchievementsGrowVegetablesImg.tr(),
    'Aid my community': AsLocaleKeys.lsUbAchievementsAidMyCommunityImg.tr(),
  };
}
