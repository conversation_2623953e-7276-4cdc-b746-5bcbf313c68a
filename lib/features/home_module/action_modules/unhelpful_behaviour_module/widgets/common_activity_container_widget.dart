import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_activity_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/achievement_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/enjoyment_activity_list.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommonActivityContanerWidget extends StatelessWidget {
  const CommonActivityContanerWidget({
    required this.enjoymentActivitiesForIndex,
    required this.ref, // required this.currentEnjoymentActivities,
    required this.index,
    required this.dayNumber,
    required this.weekday,
    required this.selectedDate,
    required this.title,
    super.key,
    this.onTap,
    this.onAddAnotherTap,
    this.onDeleteTap,
    this.onTap1,
  });

  final List<Fri> enjoymentActivitiesForIndex;
  final void Function(Fri activity)? onDeleteTap;
  final void Function(Fri activity)? onTap1;
  final UnhelpfulBehaviourCubit ref;
  // final List<Fri> currentEnjoymentActivities;
  final int index;
  final String dayNumber;
  final DateTime selectedDate;
  final void Function()? onTap;
  final void Function()? onAddAnotherTap;
  final String weekday;
  final String title;

  @override
  Widget build(BuildContext context) {
    'enjoymentActivitiesForIndex ${enjoymentActivitiesForIndex.length}'.logV;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(bottom: AppSize.h10),
        child: Column(
          children: [
            Builder(
              builder: (context) {
                return enjoymentActivitiesForIndex.isEmpty
                    ? Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h8),
                            child: GestureDetector(
                              onTap: onTap,
                              child: DottedBorder(
                                borderType: BorderType.RRect,
                                color: context.themeColors.greyColor,
                                strokeWidth: 1.5,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: AppSize.h18),
                                  child: Center(
                                    child: AppTextWidget(
                                      AsLocaleKeys.lsUbAddNew.tr(),
                                      style: context.textTheme.titleSmall?.copyWith(
                                        color: context.themeColors.greenColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...enjoymentActivitiesForIndex.map((activity) {
                            'activity.color ${activity.color?.colorDefault}'.logD;
                            'activity.name ${activity.name}'.logD;
                            //  final data = activity.data.fri;
                            return GestureDetector(
                              onTap: onTap1 != null
                                  ? () => onTap1!(activity)
                                  : () {
                                      // Fallback logic if onDeleteTap is null
                                    },
                              child: Container(
                                height: AppSize.h60,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2), // Soft shadow
                                      offset: const Offset(
                                        4,
                                        4,
                                      ),
                                      blurRadius: 6,
                                    ),
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      offset: const Offset(
                                        -4,
                                        -4,
                                      ), // Shadow towards top-left
                                      blurRadius: 6,
                                    ),
                                  ],
                                ),
                                width: MediaQuery.of(context).size.width,
                                margin: EdgeInsets.only(
                                  top: AppSize.h6,
                                  bottom: AppSize.h6,
                                ),
                                child: Padding(
                                  padding: EdgeInsets.all(AppSize.sp6),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Center(
                                            child: ColoredBox(
                                              color: activity.color != null
                                                  ? Color(
                                                      int.parse(
                                                        '0xFF${activity.color?.colorDefault?.replaceAll('#', '')}',
                                                      ),
                                                    )
                                                  : Colors.transparent, // Default transparent color
                                              child: (() {
                                                final doBreakingFreeLocalized = AsLocaleKeys.lsUbAchievementsDoBreakingFreeImg.tr();  //for "Do Breaking Free" where image is not show
                                                if (activity.name == doBreakingFreeLocalized) {  //for "Do Breaking Free" where image is not show
                                                  // Show default image for 'Do Breaking Free'  //for "Do Breaking Free" where image is not show
                                                  return AppCachedNetworkImage(  //for "Do Breaking Free" where image is not show
                                                    imageUrl: AsLocaleKeys.lsUbCustomLogo.tr(),  //for "Do Breaking Free" where image is not show
                                                    width: AppSize.w44,  //for "Do Breaking Free" where image is not show
                                                    height: AppSize.w44,  //for "Do Breaking Free" where image is not show
                                                    fit: BoxFit.cover,  //for "Do Breaking Free" where image is not show
                                                  );  //for "Do Breaking Free" where image is not show
                                                } else {
                                                  final locToEn = localizedToEnglishName();
                                                  final englishName = locToEn[activity.name] ?? activity.name;
                                                  final imageUrl = activity.type == AsLocaleKeys.lsUbEnjoyment.tr()
                                                      ? EnjoymentActivictyList.activityImages[englishName]
                                                      : AchievementActivictyList.activityImages[englishName];
                                                  print('//////////////// Image for \'${activity.name}\' (\'${englishName}\'): $imageUrl');
                                                  return AppCachedNetworkImage(
                                                    imageUrl: imageUrl ?? AsLocaleKeys.lsUbCustomLogo.tr(),
                                                    width: AppSize.w44,
                                                    height: AppSize.w44,
                                                    fit: BoxFit.cover,
                                                  );
                                                }
                                              })(),
                                            ),
                                          ),

                                          SpaceH(AppSize.w10),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  activity.name?.trim() ?? '',
                                                  maxLines: 2,
                                                  overflow: TextOverflow.ellipsis,
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontSize: AppSize.sp12,
                                                  ),
                                                ),
                                                Text(
                                                  activity.time ?? '',
                                                  maxLines: 1,
                                                  overflow: TextOverflow.ellipsis,
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontSize: AppSize.sp12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          // if (ref.selectedForActivityIndex.value == activity)
                                          Visibility(
                                            visible: ref.shouldShowDeleteIcon(
                                              activity,
                                              (ref.state.activitiesMap[weekday] ?? [])
                                                  .where((activity) => activity.type == AsLocaleKeys.lsUbAchievement.tr())
                                                  .toList(),
                                              (ref.state.activitiesMap[weekday] ?? [])
                                                  .where((activity) => activity.type == AsLocaleKeys.lsUbEnjoyment.tr())
                                                  .toList(),
                                            ),
                                            child: InkWell(
                                              onTap: onDeleteTap != null
                                                  ? () => onDeleteTap!(activity)
                                                  : () {
                                                      // Fallback logic if onDeleteTap is null
                                                    },
                                              child: const Icon(
                                                Icons.delete,
                                                color: AppColors.activityTextColor,
                                              ),
                                            ),
                                          ),
                                          // else
                                          //   const SizedBox(),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }),

                          if (enjoymentActivitiesForIndex.length < 3)
                            GestureDetector(
                              onTap: onAddAnotherTap ??
                                  () {
                                    ref.headerInfoText.value = null;
                                    ref.infoAudioUrl.value = null;
                                    AppNavigation.nextScreen(
                                      context,
                                      BlocProvider.value(
                                        value: ref,
                                        child: UnhelpfulActivityPage(
                                          title: title,
                                          index: index,
                                          dayNumber: weekday,
                                          selectedDate: selectedDate,
                                        ),
                                      ),
                                    );
                                  },
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: AppTextWidget(
                                  AsLocaleKeys.lsUbAddAnother.tr(),
                                  style: context.textTheme.titleSmall?.copyWith(
                                    fontSize: AppSize.sp12,
                                  ),
                                ),
                              ),
                            ),
                          // // Button to add another achievement activity
                        ],
                      );
              },
            ),
          ],
        ),
      ),
    );
  }
}
