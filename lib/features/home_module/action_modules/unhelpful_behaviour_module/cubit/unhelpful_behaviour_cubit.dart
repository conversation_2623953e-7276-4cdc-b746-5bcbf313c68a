import 'dart:convert';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_well_done_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/unhelpful_behaviour_repository/unhelpful_behaviour_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/achievement_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/enjoyment_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'unhelpful_behaviour_cubit.freezed.dart';
part 'unhelpful_behaviour_state.dart';

class UnhelpfulBehaviourCubit extends Cubit<UnhelpfulBehaviourState> {
  UnhelpfulBehaviourCubit({required bool isFromSelectTime}) : super(UnhelpfulBehaviourState()) {
    Future.delayed(Duration.zero, () async{
      'UnhelpfulBehaviourCubit init'.logE;
      'UnhelpfulBehaviourCubit'.logE;
      if (isFromSelectTime) {
        await userData(navigatorKey.currentContext!);
    } else {
        await  userInitalData(navigatorKey.currentContext!);
    }

    });
  }

  final UnhelpfulBehaviourRepository repository = UnhelpfulBehaviourRepository();
  AuthRepository authRepository = AuthRepository();

  //list for enjoyment
  List<String> imagesList = [];
  List<String> imagesNameList = [];
  List<String> imageDetailList = [];

  //list for achievements
  List<String> achievementImagesList = [];
  List<String> achievementNameList = [];
  List<String> achievementDetailList = [];
  List<Fri> enjoymentActivities = <Fri>[];
  List<Fri> achievementActivities = <Fri>[];

  Fri selectedActivity = Fri();
  ValueNotifier<int> selectedIndex = ValueNotifier(-1);

  final TextEditingController selectedHourController = TextEditingController();
  final TextEditingController selectedMinuteController = TextEditingController();
  final TextEditingController addCustomActivityController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  ValueNotifier<bool?> isText = ValueNotifier(false);

  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<String?> infoActivityAudioUrl = ValueNotifier(null);
  ValueNotifier<String?> infoSelectTimeAudioUrl = ValueNotifier(null);
  ValueNotifier<String?> infoCustomActivityAudioUrl = ValueNotifier(null);

  ValueNotifier<String?> infoWellDoneAudioUrl = ValueNotifier(AsLocaleKeys.lsUbSummaryAudioApp.tr());
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> infoWellDonePaused = ValueNotifier(false);
  ValueNotifier<bool> infoActionPlanPaused = ValueNotifier(false);
  ValueNotifier<bool> isError = ValueNotifier(false);

  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<String?> infoActionAudioUrl = ValueNotifier(AsLocaleKeys.lsUbActionPlanAudio.tr());
  // ValueNotifier<bool> isAudioActionPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> headerVideoUrl = ValueNotifier(false);

  ValueNotifier<String> activityName = ValueNotifier('');
  ValueNotifier<String> activityImage = ValueNotifier('');
  ValueNotifier<String> activityColor = ValueNotifier('');
  ValueNotifier<bool> isButtonClick = ValueNotifier(false);
  String imageUrl = '';
  String activityId = '';
  int selectedHour = 12;
  int selectedMinute = 0;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final scaffoldActivity = GlobalKey<ScaffoldState>();
  final scaffoldcustomActivity = GlobalKey<ScaffoldState>();
  final scaffoldTime = GlobalKey<ScaffoldState>();
  final scaffoldWellDone = GlobalKey<ScaffoldState>();
  final scaffoldActionPlanDone = GlobalKey<ScaffoldState>();

  DateTime today = DateTime.now();
  DateFormat dateFormat = DateFormat('EEE, MMM d'); // Customize the format

  // Use this map to convert names to colors
  final Map<String, Color> activityColorList = {
    'darkBlue': AppColors.darkBlueColor,
    'lightBlue': AppColors.lightBlueColor,
    'skyBlue': AppColors.skyBlueColor,
    'orange': AppColors.orangeColor,
    'red': AppColors.redColor,
    'green': AppColors.greenColor,
  };

  String formatDate(DateTime date, {bool isDay = false}) {
    final mon = AsLocaleKeys.lsUbDaysMon.tr();
    final tue = AsLocaleKeys.lsUbDaysTue.tr();
    final wed = AsLocaleKeys.lsUbDaysWed.tr();
    final thu = AsLocaleKeys.lsUbDaysThur.tr();
    final fri = AsLocaleKeys.lsUbDaysFri.tr();
    final sat = AsLocaleKeys.lsUbDaysSat.tr();
    final sun = AsLocaleKeys.lsUbDaysSun.tr();

    final days = [sun, mon, tue, wed, thu, fri, sat];

    final dayIndex = date.weekday % 7;

    final translatedDay = days[dayIndex];
    'translatedDay : $translatedDay'.logD;
    if (isDay) {
      return translatedDay.toUpperCase();
    } else {
      return '$translatedDay ${date.day}';
    }
  }

  void incrementHour() {
    selectedHour = (selectedHour + 1) % 24;
    selectedHourController.text = selectedHour.toString();
  }

  void decrementHour() {
    selectedHour = (selectedHour - 1 + 24) % 24;
    selectedHourController.text = selectedHour.toString();
  }

  void incrementMinute() {
    //TODO: after notification done
    selectedMinute = (selectedMinute + 5) % 60;
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
  }

  void decrementMinute() {
    selectedMinute = (selectedMinute - 5 + 60) % 60;
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
  }

  String getDayFromTimestamp(DateTime timestamp) {
    return DateFormat('EEE').format(timestamp).toUpperCase(); // Get day in abbreviated form (e.g., MON, TUE)
  }

  String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2, 8)}';
  }

  bool shouldShowDeleteIcon(Fri activity, List<Fri> achievementActivities, List<Fri> enjoymentActivities) {
    if (activity == selectedActivity) {
      return true; // Show delete icon only for the selected activity
    }
    return false;
  }

  // Add a flag to track if activities have been cleared
  bool _activitiesCleared = false;

  Future<void> deleteActivity(Fri activity, String? weekDay) async {
    final locToEn = localizedToEnglishName();
    final englishName = locToEn[activity.name] ?? activity.name;
    final englishType = locToEn[activity.type] ?? activity.type;
    // Remove the activity from achievement or enjoyment activities
    if (englishType == AsLocaleKeys.lsUbAchievement.tr()) {
      achievementActivities.removeWhere(
        (existingActivity) =>
            existingActivity.day == activity.day &&
            (localizedToEnglishName()[existingActivity.name] ?? existingActivity.name) == englishName &&
            (localizedToEnglishName()[existingActivity.type] ?? existingActivity.type) == englishType,
      );
    } else {
      enjoymentActivities.removeWhere(
        (existingActivity) =>
            existingActivity.day == activity.day &&
            (localizedToEnglishName()[existingActivity.name] ?? existingActivity.name) == englishName &&
            (localizedToEnglishName()[existingActivity.type] ?? existingActivity.type) == englishType,
      );
    }

    // Remove the activity from the activitiesMap
    if (state.activitiesMap.containsKey(activity.day)) {
      final dayList = state.activitiesMap[activity.day];
      if (dayList != null) {
        final index = dayList.indexWhere((e) => (localizedToEnglishName()[e.name] ?? e.name) == englishName);
        if (index != -1) {
          dayList.removeAt(index);
        }
        // Update the activitiesMap with the modified list
        emit(
          state.copyWith(
            activitiesMap: {
              ...state.activitiesMap,
              activity.day ?? 'Unknown Day': dayList, // Update the list for the specific day
            },
          ),
        );
      }
    }

    enjoymentActivities =
        (state.activitiesMap[activity.day] ?? []).where((activity) => activity.type == AsLocaleKeys.lsUbEnjoyment.tr()).toList();
    achievementActivities =
        (state.activitiesMap[activity.day] ?? []).where((activity) => activity.type == AsLocaleKeys.lsUbAchievement.tr()).toList();

    //Now, update the local storage (SharedPreferences) to reflect the changes
    final storedActivityJson = prefs.getString('newActivity');
    if (storedActivityJson != null && storedActivityJson.isNotEmpty) {
      final storedActivitiesList = jsonDecode(storedActivityJson) as List<dynamic>;
      final storedActivities = storedActivitiesList.map((e) => Fri.fromJson(e as Map<String, dynamic>)).toList()

        // Remove the activity from the stored activities list
        ..removeWhere(
          (existingActivity) =>
              existingActivity.day == activity.day &&
              existingActivity.name == activity.name &&
              existingActivity.type == activity.type,
        );

      // Save the updated list back to SharedPreferences
      final updatedActivitiesJson = jsonEncode(storedActivities);
      await prefs.setString('newActivity', updatedActivitiesJson);
    } else {
      await prefs.remove('newActivity');
    }
    print('Activity removed from both in-memory and local storage.');
  }

  Future<void> clearMyAllActivities() async {
    const activityKey = 'newActivity';

    // Clear the activities from memory
    enjoymentActivities.clear();
    achievementActivities.clear();
    
    // Clear the activitiesMap explicitly in the state
    // state.activitiesMap.clear();
    // emit(state.copyWith(activitiesMap: {}));

    // Save an empty list to SharedPreferences to confirm cleared state
    await prefs.setString(activityKey, jsonEncode([]));
    'All activities cleared from SharedPreferences by setting to empty list.'.logD;
    
    // Set the cleared flag
    _activitiesCleared = true;
    
    // Emit empty state to clear from UI
    emit(state.copyWith(activitiesMap: {}));

    // Clear the API data by updating the user model (optional, depending on API behavior)
    if (Injector.instance<AppDB>().userModel?.user.strategies?.ubAs != null) {
      Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.clear();
    }
  }

  Future<void> updateUbAData({
    required UbAData data,
    required String name,
    required String image,
    required String day,
    required String title,
    String? hexColor,
  }) async {
    '>>>>>>>>> data = ${data.toJson()}'.logD;
    '>>>>>>>>> name = $name'.logD;
    '>>>>>>>>> image = $image'.logD;
    '>>>>>>>>> day = $day'.logD;
    '>>>>>>>>> title = $title'.logD;

    final locToEn = localizedToEnglishName();
    final englishName = locToEn[name] ?? name;
    final englishTitle = locToEn[title] ?? title;

    final fullDay = day == 'THU' ? 'THUR' : day;

    final formattedTime = '${selectedHour.toString().padLeft(2, '0')}:${selectedMinute.toString().padLeft(2, '0')}';

    final newActivity = Fri(
      name: englishName, // Store English name
      time: formattedTime,
      type: englishTitle, // Store English type
      day: fullDay,
      color: ((hexColor?.isNotEmpty ?? false) && hexColor != null) ? Color1(colorDefault: hexColor) : null,
    );
'currrrrrrrrrr :: $state'.logE;
    newActivity.toJson().logD;
    state.activitiesMap.logD;

    // Map to localized for display
    final localizedActivity = _mapActivityToLocalized(newActivity); // last change

    // Get current activities from the state (will be empty if cleared)
    final currentActivities = List<Fri>.from(state.activitiesMap[fullDay] ?? [])
    ..add(localizedActivity); // last change

    // Update the state directly
    final updatedMap = Map<String, List<Fri>>.from(state.activitiesMap);
    updatedMap[fullDay] = currentActivities;
    '///// updateMap = ${updatedMap}'.logD;
    'currrrrrrrrrr2222 :: $state'.logE;
    state.activitiesMap.logD;
updatedMap.logD;
    emit(state.copyWith(activitiesMap: {...updatedMap}));
      'currrrrrrrrrr33 :: $state'.logE;
    state.activitiesMap.logD;

    // Get all activities from the updated state's map to save to SharedPreferences
    // This ensures only activities currently in the state are saved
    final allActivitiesToSave = updatedMap.values.expand((list) => list).toList();
    await prefs.setString('newActivity', jsonEncode(allActivitiesToSave.map((e) => e.toJson()).toList()));
    'allActivitiesToSave : $allActivitiesToSave'.logD;

    // Reset cleared flag after adding a new activity
    _activitiesCleared = false;
  }

  Future<void> userData(BuildContext context) async {
    // If activities were cleared, load only from SharedPreferences
    if (_activitiesCleared) {
       final storedActivityJson = prefs.getString('newActivity');
       if (storedActivityJson != null && storedActivityJson.isNotEmpty) {
         try {
           final storedActivitiesList = jsonDecode(storedActivityJson) as List<dynamic>;
           final storedActivities = storedActivitiesList.map((e) => _mapActivityToLocalized(Fri.fromJson(e as Map<String, dynamic>))).toList();

           'storedActivities : $storedActivities'.logD;

           emit(
             state.copyWith(
               activitiesMap: storedActivities.fold<Map<String, List<Fri>>>({}, (map, activity) {
                 final day = activity.day ?? 'Unknown';
                 if (!map.containsKey(day)) {
                   map[day] = [];
                 }
                 map[day]?.add(activity);
                 return map;
               }),
             ),
           );
         } catch (e) {
            'Error decoding JSON in userData when cleared: $e'.logD;
         }
       } else {
           // If cleared and SharedPreferences is empty, ensure state is empty
           emit(state.copyWith(activitiesMap: {}));
       }
       return;
    }

    // Normal loading logic if activities were not cleared
    final entries = <MapEntry<DateTime, UbAData>>[];
    'if=====> ${Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.isEmpty}'.logD;
    'if=====> ${Injector.instance<AppDB>().userModel?.user.strategies?.ubAs}'.logD;

    // Check if ubAs is null or empty
    if (Injector.instance<AppDB>().userModel?.user.strategies?.ubAs == null ||
        Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.isEmpty == true) {
      'if=====> 1'.logD;

      // Handle the case when ubAs is empty
      'No data available in ubAs.'.logV;

      // Check if there is data in local storage (SharedPreferences)
      final storedActivityJson = prefs.getString('newActivity');
      'storedActivityJson ===> get user $storedActivityJson'.logD;

      if (storedActivityJson != null && storedActivityJson.isNotEmpty) {
        // If data is available in local storage, decode and use it
        final storedActivitiesList = jsonDecode(storedActivityJson) as List<dynamic>;
        final storedActivities = storedActivitiesList.map((e) => _mapActivityToLocalized(Fri.fromJson(e as Map<String, dynamic>))).toList();

        // Update activitiesMap with stored activities
        emit(
          state.copyWith(
            activitiesMap: storedActivities.fold<Map<String, List<Fri>>>({}, (map, activity) {
              final day = activity.day ?? 'Unknown';
              if (!map.containsKey(day)) {
                map[day] = [];
              }
              map[day]?.add(activity);
              return map;
            }),
          ),
        );
      } else {
        'else storedActivityJson ===> get user $storedActivityJson'.logD;
        emit(
          state.copyWith(
            activitiesMap: {}, // Set activitiesMap to empty if no data is available
          ),
        );
      }
      return; // Exit early if no data is available
    } else {
      // If ubAs is not empty, proceed with processing the data
      Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.forEach((element) {
        final time = element.time;
        final data = element.data;

        if (time != null && data != null) {
          final dateTime = DateTime.fromMillisecondsSinceEpoch(time);
          entries.add(MapEntry(dateTime, data));
        }
      });

      entries.sort((a, b) => b.key.compareTo(a.key));
      final mostRecentEntry = entries.isNotEmpty ? entries.first : null;

      if (mostRecentEntry != null) {
        final mostRecentData = mostRecentEntry.value;
        final mostRecentDataJson = Map<String, dynamic>.from(mostRecentData.toJson());
        'API Data Retrieved: $mostRecentDataJson'.logD;

        // Update the activitiesMap directly from the API data
        emit(
          state.copyWith(
            activitiesMap: mostRecentDataJson.map<String, List<Fri>>((key, value) {
            print('storedActivities userData:: $key : $value');

              return MapEntry(
                key,
                (value as List).map((e) => _mapActivityToLocalized(Fri.fromJson(e as Map<String, dynamic>))).toList(),
              );
            }),
          ),
        );
      } else {
        // Handle the case where mostRecentEntry is null
        print('No recent entry found.');
        emit(
          state.copyWith(
            activitiesMap: {}, // Set activitiesMap to empty if no recent entry
          ),
        );
      }
    }
  }

  Future<void> userInitalData(BuildContext context) async {
    final storedActivityJson = prefs.getString('newActivity');

     // If activities were cleared, load only from SharedPreferences
    if (_activitiesCleared) {
       if (storedActivityJson != null && storedActivityJson.isNotEmpty) {
         try {
           final storedActivitiesList = jsonDecode(storedActivityJson) as List<dynamic>;
           final storedActivities = storedActivitiesList.map((e) => _mapActivityToLocalized(Fri.fromJson(e as Map<String, dynamic>))).toList();
           'storedActivities : $storedActivities'.logD;
           emit(
             state.copyWith(
               activitiesMap: storedActivities.fold<Map<String, List<Fri>>>({}, (map, activity) {
                 final day = activity.day ?? 'Unknown';
                 if (!map.containsKey(day)) {
                   map[day] = [];
                 }
                 map[day]?.add(activity);
                 return map;
               }),
             ),
           );
         } catch (e) {
            'Error decoding JSON in userInitalData when cleared: $e'.logD;
         }
       } else {
           // If cleared and SharedPreferences is empty, ensure state is empty
           emit(state.copyWith(activitiesMap: {}));
       }
      return;
    }

    final entries = <MapEntry<DateTime, UbAData>>[];
    'if=====> ${Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.isEmpty}'.logD;
    'if=====> ${Injector.instance<AppDB>().userModel?.user.strategies?.ubAs}'.logD;

    // Check if ubAs is null or empty
    if (Injector.instance<AppDB>().userModel?.user.strategies?.ubAs == null ||
        Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.isEmpty == true) {
      'No data available in ubAs.'.logV;
      emit(state.copyWith(activitiesMap: {}));
      return;
    }

    // If ubAs is not empty, proceed with processing the data
    Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.forEach((element) {
      final time = element.time;
      final data = element.data;

      if (time != null && data != null) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(time);
        entries.add(MapEntry(dateTime, data));
      }
    });

    entries.sort((a, b) => b.key.compareTo(a.key));
    final mostRecentEntry = entries.isNotEmpty ? entries.first : null;

    if (mostRecentEntry != null) {
      final mostRecentData = mostRecentEntry.value;
      final mostRecentDataJson = Map<String, dynamic>.from(mostRecentData.toJson());
      'API Data Retrieved: $mostRecentDataJson'.logD;

      // Update the activitiesMap directly from the API data
      emit(
        state.copyWith(
          activitiesMap: mostRecentDataJson.map<String, List<Fri>>((key, value) {
            print('storedActivities init:: $key : $value');
            return MapEntry(
              key,
              (value as List).map((e) => _mapActivityToLocalized(Fri.fromJson(e as Map<String, dynamic>))).toList(),
            );
          }),
        ),
      );
    } else {
      'No recent entry found from API.'.logD;
      emit(state.copyWith(activitiesMap: {}));
    }
  }

  void updateSelectedDayAndDate(String day, DateTime date) {
    print('========<<><><><><> ${day} : ${date}');
    day.logD;
    date.logD;
    emit(
      state.copyWith(
        selectedDay: day,
        selectedDate: date,
      ),
    );
  }

  void updateEnjoymentActivity(
    UbAData currentUbA,
    String day,
    String activityName,
    String activityTime,
    String activityType,
  ) {
    // Create a new activity
    final activity = Fri(
      name: activityName,
      time: activityTime,
      type: activityType,
      day: day,
    );

    // Update the data for the specific day
    switch (day) {
      case 'THUR':
        currentUbA.thur?.add(activity);
      case 'FRI':
        currentUbA.fri?.add(activity);
      case 'SAT':
        currentUbA.sat?.add(activity);
      case 'SUN':
        currentUbA.sun?.add(activity);
      case 'MON':
        currentUbA.mon?.add(activity);
      case 'WED':
        currentUbA.wed?.add(activity);
    }
  }

  Future<void> unhelpfulStrategforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
  }) async {
    isEmail ? emit(state.copyWith(isEmailPdfAPILoading: true)) : emit(state.copyWith(isDownloadPdfAPILoading: true));
    try {
      final response = await repository.ubActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Planning your time positively.pdf',
            );
          }
        } else {
          // CustomSnackbar.showSucessSnackBar(
          //   message: 'Email sent successfully',
          // );
        }
      }
      isEmail
          ? emit(state.copyWith(isEmailPdfAPILoading: false))
          : emit(state.copyWith(isDownloadPdfAPILoading: false));
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail
          ? emit(state.copyWith(isEmailPdfAPILoading: false))
          : emit(state.copyWith(isDownloadPdfAPILoading: false));
    }
  }

  Future<void> unhelpfulStrategyAPI({
    required UnhelpfulBehaviourCubit ref,
    required BuildContext context,
    required Map<String, dynamic> data,
  }) async {
    emit(state.copyWith(isAPILoading: true));
    '????? data = ${data.toString()}'.logD;
    // emit(LoadingEmotionalState());
    try {
      final response = await repository.unhelpfuleBehaviourStrategy(
        context: context,
        data: data,
      );
      '????? response = ${response?.toJson()}'.logD;
      // response.logD;
      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && response.success == true) {
        final prefs = await SharedPreferences.getInstance();

        await prefs.remove(
          'newActivity',
        );
        ref.headerInfoText.value = null;
        ref.infoAudioUrl.value = null;
        if (response.strategies != null) {
          Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;
          'User data ===> ${Injector.instance<AppDB>().userModel?.user.strategies?.ubAs}'.logD;
          await authRepository.getUserData(context: context);
          'actvites ===>${Injector.instance<AppDB>().userModel?.user.app?.alerts?.activities}'.logD;
          // if ((Injector.instance<AppDB>().userModel?.user.app?.alerts?.activities ?? false) == true) {
            '|||||||||||| yes'.logV;
            await BlocProvider.of<MyAlertCubit>(context).setPlanningScheduleNotification();
          // }
        }
        ref.infoWellDonePaused.value = false;
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(
            value: ref,
            child: UnhelpfulWellDonePage(),
          ),
        );
      } else {
        // if ((Injector.instance<AppDB>().userModel?.user.app?.alerts?.activities ?? false) == true) {
          await BlocProvider.of<MyAlertCubit>(context).setPlanningScheduleNotification();
        // }
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(
            value: ref,
            child: UnhelpfulWellDonePage(),
          ),
        );
      }
      emit(state.copyWith(isAPILoading: false));
    } catch (e) {
      emit(state.copyWith(isAPILoading: false));
    }
  }

  void initializeActivityImages() {
    EnjoymentActivictyList.addEnjoymentActivity(imagesList, imagesNameList, imageDetailList);
    AchievementActivictyList.addAchievementActivity(achievementImagesList, achievementNameList, achievementDetailList);
    if (imagesNameList.isNotEmpty) activityName.value = imagesNameList[0];
    selectedHourController.text = selectedHour.toString();
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
    print('/////////// ENJOYMENT: \\${imagesNameList.length} \\${imagesNameList}');
    print('/////////// ACHIEVEMENT: \\${achievementNameList.length} \\${achievementNameList}');
  }

  @override
  Future<void> close() {
    'UnhelpfulBehaviourCubit closed'.logE;
    // TODO: implement close
    selectedHourController.dispose();
    selectedMinuteController.dispose();
    achievementActivities.clear();
    infoAudioUrl.dispose();
    achievementDetailList.clear();
    activityId = '';
    activityColor.dispose();
    enjoymentActivities.clear();
    achievementActivities.clear();
    addCustomActivityController.dispose();
    headerVideoUrl.dispose();
    activityName.dispose();
    selectedHour = 12;
    selectedMinute = 0;
    isAudioPanelVisible.dispose();
    imagesList.clear();
    selectedIndex.dispose();
    imagesNameList.clear();
    activityImage.dispose();
    return super.close();
  }
}

Map<String, String> englishToLocalizedName() {
  // Enjoyment
  final enjoymentLocalizedNames = [
    AsLocaleKeys.lsUbActivitiesEnjoymentListenToMusicName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentTakeAWalkName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentEatAGoodMealName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentPlaySomeSportName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentCallSomeoneName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentMeetAFriendName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentSeeMyFamilyName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentWatchTVName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentReadAMagazineName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentReadABookName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoToACafeName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoToAParkName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoShoppingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentEatATakeawayName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentSurfOnlineName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentConnectOnlineName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentEmailSomeoneName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentDoSomeGamingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentPamperMyselfName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentTakeAHotBathName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentPlayAGameName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoSwimmingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentSeeAMovieName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentDoGardeningName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoBowlingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoDancingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentWatchSportName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentRideABikeName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoFishingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentWalkADogName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentVisitATownName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGetOutdoorsName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentVisitABuildingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoToABeachName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoCampingName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentSeeAComedianName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentPlaySomeMusicName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoToAConcertName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentDrawOrPaintName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentDoPhotographyName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentWriteALetterName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentPraiseMyFaithName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentMeditateName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentBeMindfulName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentDoArtsOrCraftsName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentGoToAMallName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentSeeAPlayName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentVisitAMuseumName.tr(),
    AsLocaleKeys.lsUbActivitiesEnjoymentVisitAGalleryName.tr(),
  ];
  final enjoymentEnglishNames = [
    'Listen to music',
    'Take a walk',
    'Eat a good meal',
    'Play some sport',
    'Call someone',
    'Meet a friend',
    'See my family',
    'Watch TV',
    'Read a magazine',
    'Read a book',
    'Go to a cafe',
    'Go to a park',
    'Go shopping',
    'Eat a takeaway',
    'Surf online',
    'Connect online',
    'Email someone',
    'Do some gaming',
    'Pamper myself',
    'Take a hot bath',
    'Play a game',
    'Go swimming',
    'See a movie',
    'Do gardening',
    'Go bowling',
    'Go dancing',
    'Watch sport',
    'Ride a bike',
    'Go fishing',
    'Walk a dog',
    'Visit a town',
    'Get outdoors',
    'Visit a building',
    'Go to a beach',
    'Go camping',
    'See a comedian',
    'Play some music',
    'Go to a concert',
    'Draw or paint',
    'Do photography',
    'Write a letter',
    'Praise my faith',
    'Meditate',
    'Be mindful',
    'Do arts or crafts',
    'Go to a mall',
    'See a play',
    'Visit a museum',
    'Visit a gallery',
  ];
  final achievementLocalizedNames = [
    AsLocaleKeys.lsUbAchievementsDoBreakingFreeName.tr(),
    AsLocaleKeys.lsUbAchievementsDoExerciseName.tr(),
    AsLocaleKeys.lsUbAchievementsCleanMyHomeName.tr(),
    AsLocaleKeys.lsUbAchievementsDoSomeIroningName.tr(),
    AsLocaleKeys.lsUbAchievementsShopForFoodName.tr(),
    AsLocaleKeys.lsUbAchievementsCookAMealName.tr(),
    AsLocaleKeys.lsUbAchievementsAlterMyHomeName.tr(),
    AsLocaleKeys.lsUbAchievementsDoSomeDIYName.tr(),
    AsLocaleKeys.lsUbAchievementsAttendWorkName.tr(),
    AsLocaleKeys.lsUbAchievementsAttendCollegeName.tr(),
    AsLocaleKeys.lsUbAchievementsHelpSomeoneName.tr(),
    AsLocaleKeys.lsUbAchievementsDoVolunteeringName.tr(),
    AsLocaleKeys.lsUbAchievementsWriteAListName.tr(),
    AsLocaleKeys.lsUbAchievementsWashUpName.tr(),
    AsLocaleKeys.lsUbAchievementsTidyUpName.tr(),
    AsLocaleKeys.lsUbAchievementsWashSomethingName.tr(),
    AsLocaleKeys.lsUbAchievementsWriteABlogName.tr(),
    AsLocaleKeys.lsUbAchievementsGo1DaySoberName.tr(),
    AsLocaleKeys.lsUbAchievementsGo7DaysSoberName.tr(),
    AsLocaleKeys.lsUbAchievementsGo30DaysSoberName.tr(),
    AsLocaleKeys.lsUbAchievementsBookAMeetingName.tr(),
    AsLocaleKeys.lsUbAchievementsPlanMyFinancesName.tr(),
    AsLocaleKeys.lsUbAchievementsCreateACVName.tr(),
    AsLocaleKeys.lsUbAchievementsApplyForAJobName.tr(),
    AsLocaleKeys.lsUbAchievementsDoAnInterviewName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeADoctorName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeAPharmacistName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeADentistName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeAnOpticianName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeACounsellorName.tr(),
    AsLocaleKeys.lsUbAchievementsAttendAGroupName.tr(),
    AsLocaleKeys.lsUbAchievementsHaveAcupunctureName.tr(),
    AsLocaleKeys.lsUbAchievementsSeeALawyerName.tr(),
    AsLocaleKeys.lsUbAchievementsGoToABankName.tr(),
    AsLocaleKeys.lsUbAchievementsFindANewHomeName.tr(),
    AsLocaleKeys.lsUbAchievementsGoToALibraryName.tr(),
    AsLocaleKeys.lsUbAchievementsAttendACourseName.tr(),
    AsLocaleKeys.lsUbAchievementsGetAHaircutName.tr(),
    AsLocaleKeys.lsUbAchievementsHaveAShaveName.tr(),
    AsLocaleKeys.lsUbAchievementsGrowPlantsName.tr(),
    AsLocaleKeys.lsUbAchievementsGrowVegetablesName.tr(),
    AsLocaleKeys.lsUbAchievementsAidMyCommunityName.tr(),
  ];
  final achievementEnglishNames = [
    'Do Breaking Free',
    'Do exercise',
    'Clean my home',
    'Do some ironing',
    'Shop for food',
    'Cook a meal',
    'Alter my home',
    'Do some DIY',
    'Attend work',
    'Attend college',
    'Help someone',
    'Do volunteering',
    'Write a list',
    'Wash up',
    'Tidy up',
    'Wash something',
    'Write a blog',
    'Go 1 day sober',
    'Go 7 days sober',
    'Go 30 days sober',
    'Book a meeting',
    'Plan my finances',
    'Create a CV',
    'Apply for a job',
    'Do an interview',
    'See a doctor',
    'See a pharmacist',
    'See a dentist',
    'See an optician',
    'See a counsellor',
    'Attend a group',
    'Have acupuncture',
    'See a lawyer',
    'Go to a bank',
    'Find a new home',
    'Go to a library',
    'Attend a course',
    'Get a haircut',
    'Have a shave',
    'Grow plants',
    'Grow vegetables',
    'Aid my community',
  ];
  final map = <String, String>{};
  for (int i = 0; i < enjoymentEnglishNames.length; i++) {
    map[enjoymentEnglishNames[i]] = enjoymentLocalizedNames[i];
  }
  for (int i = 0; i < achievementEnglishNames.length; i++) {
    map[achievementEnglishNames[i]] = achievementLocalizedNames[i];
  }
  return map;
}

Map<String, String> localizedToEnglishName() {
  final map = <String, String>{};
  final englishToLocalized = englishToLocalizedName();
  englishToLocalized.forEach((en, loc) {
    map[loc] = en;
  });
  return map;
}

Fri _mapActivityToLocalized(Fri activity) {
  final nameMap = englishToLocalizedName();
  String localizedName = nameMap[activity.name ?? ''] ?? (activity.name ?? '');
  if (localizedName.isEmpty) {
    localizedName = activity.name ?? '';
  }
  String localizedType = activity.type ?? '';
  if (activity.type == 'Enjoyment') {
    final tr = AsLocaleKeys.lsUbEnjoyment.tr();
    localizedType = (tr != null && tr.isNotEmpty) ? tr : 'Enjoyment';
  } else if (activity.type == 'Achievement') {
    final tr = AsLocaleKeys.lsUbAchievement.tr();
    localizedType = (tr != null && tr.isNotEmpty) ? tr : 'Achievement';
  }
  if (localizedType.isEmpty) {
    localizedType = activity.type ?? '';
  }
  return Fri(
    name: localizedName,
    time: activity.time,
    type: localizedType,
    day: activity.day,
    color: activity.color,
  );
}
