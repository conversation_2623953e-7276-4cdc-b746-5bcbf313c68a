part of 'unhelpful_behaviour_cubit.dart';

@freezed
class UnhelpfulBehaviourState with _$UnhelpfulBehaviourState {
  factory UnhelpfulBehaviourState({
    @Default([]) List<Fri> enjoymentActivityList,
    @Default([]) List<Fri> achievementActivityList,
    @Default(false) bool isAPILoading,
    @Default(false) bool isDownloadPdfAPILoading,
    @Default(false) bool isEmailPdfAPILoading,
    @Default('') String selectedDay,
    @Default({}) Map<String, List<Fri>> activitiesMap,
    DateTime? selectedDate,
  }) = _UnhelpfulBehaviourState;
}
