import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/hour_picker_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/minute_picker_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_behaviour_calendar_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class UnhelpfulSelectTimePage extends StatelessWidget {
  const UnhelpfulSelectTimePage({required this.isCommingFromCustom,super.key, this.title, this.index, this.dayNumber, this.selectedDate, });
  final String? title;
  final int? index;
  final String? dayNumber;
  final DateTime? selectedDate;
  final bool isCommingFromCustom;

  @override
  Widget build(BuildContext context) {
    title.logD;
    '>?>?>? title = ${title}'.logD;
    '>?>?>? index = ${index}'.logD;
    '>?>?>? dayNumber = ${dayNumber}'.logD;
    '>?>?>? selectedDate = ${selectedDate}'.logD;

    return BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
      builder: (ctx, state) {
        final ref = ctx.read<UnhelpfulBehaviourCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoSelectTimeAudioUrl,
          builder: (context, value, child) {
            '>?>?>? activity name  = ${ref.activityName.value}'.logV;
    '>?>?>? activity image = ${ref.activityImage.value}'.logV;
            return AppScaffold(
              resizeToAvoidBottomInset: false,
              scaffoldKey: ref.scaffoldTime,
              isAudioPanelVisible: ref.isAudioPanelVisible,
              infoAudioUrl: ref.infoSelectTimeAudioUrl,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldTime),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldTime.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoSelectTimeAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                  }
                },
              ),
              body: ColoredBox(
                color: context.themeColors.whiteColor,
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: EdgeInsets.only(
                            left: AppSize.w24,
                            right: AppSize.w24,
                            bottom: AppSize.h20,
                            top: AppSize.h24,
                          ),
                          child: ValueListenableBuilder(
                            valueListenable: ref.isButtonClick,
                            builder: (context, value, child) {
                              return Column(
                                children: [
                                  InformationPageHeadingWidget(
                                    onBackArrowTap: () {
                                      ref.headerInfoText.value = null;
                                      ref.infoActivityAudioUrl.value = null;
                                      ref.isButtonClick.value = false;

                                      Navigator.pop(context);
                                    },
                                    title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                    subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                    icon: Assets.icons.actionStrategiesPlanningYourTimePositively, //Assets.icons.infoPage.difficultSituation,
                                    onInfoTap: () {
                                      ref.infoSelectTimeAudioUrl.value =
                                          AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                      final info = (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsUbInfoPanelsInformationText,
                                        context,
                                      ) as List)
                                          .join('<br/><br/>');
                                      ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                              ref.headerInfoText.value == info)
                                          ? null
                                          : info;
                                      ref.headerVideoUrl = ValueNotifier(true);
                                    },
                                    onLearnTap: () {
                                      ref.infoSelectTimeAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();

                                      final info = (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsUbInfoPanelsLearnText,
                                        context,
                                      ) as List)
                                          .join('<br/><br/>');
                                      ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                              ref.headerInfoText.value == info)
                                          ? null
                                          : info;
                                      ref.headerVideoUrl = ValueNotifier(false);
                                    },
                                    infoWidget: ValueListenableBuilder(
                                      valueListenable: ref.headerInfoText,
                                      builder: (context, headerInfoTextV, _) {
                                        return CustomInfoWidget(
                                          customWidget: Column(
                                            children: [
                                              Html(
                                                data: ref.headerInfoText.value ?? '',
                                                style: {
                                                  'strong': Style(
                                                    fontSize: FontSize(AppSize.sp13),
                                                    color: context.themeColors.darkOrangeColor,
                                                    fontWeight: FontWeight.bold,
                                                    fontFamily: 'Poppins',
                                                  ),
                                                  'body': Style(
                                                    fontSize: FontSize(AppSize.sp13),
                                                    color: context.themeColors.darkOrangeColor,
                                                    fontFamily: 'Poppins',
                                                  ),
                                                },
                                              ),
                                              if (ref.headerVideoUrl.value != false) ...[
                                                SpaceV(AppSize.h16),
                                                VideoPlayerScreen(
                                                  imageList: [
                                                    AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster.tr(),
                                                  ],
                                                  //  onTap: onPlayTap,
                                                  navigationFunction: () {},
                                                  videoList: [AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc.tr()],
                                                ),
                                              ],
                                            ],
                                          ),
                                          onCloseTap: () {
                                            ref.headerInfoText.value = null;
                                            ref.infoSelectTimeAudioUrl.value = null;
                                          },
                                          visible: headerInfoTextV.isNotEmptyAndNotNull,
                                          margin: EdgeInsets.symmetric(
                                            vertical: AppSize.h8,
                                          ),
                                          bodyText: headerInfoTextV,
                                        );
                                      },
                                    ),
                                  ),
                                  SpaceV(AppSize.h10),
                                  AppTextWidget(
                                    title ?? '',
                                    style: context.textTheme.titleSmall?.copyWith(
                                      fontSize: AppSize.sp14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  SpaceV(AppSize.h20),
                                  AppTextWidget(
                                    AsLocaleKeys.lsUbSelectWhatTime.tr(),
                                    style: context.textTheme.titleSmall,
                                  ),
                                  SpaceV(AppSize.h20),
                                  Row(
                                    children: [
                                      SpaceH(AppSize.w40),
                                      Expanded(
                                        child: HourPickerWidget(
                                          isLifestyleCubit: false,
                                          unhelpfulBehaviourCubit: ref,
                                          isError: DateTime(
                                                selectedDate?.year ?? DateTime.now().year,
                                                selectedDate?.month ?? DateTime.now().month,
                                                selectedDate?.day ?? DateTime.now().day,
                                                ref.selectedHour,
                                                ref.selectedMinute,
                                              ).isBefore(DateTime.now()) &&
                                              ref.isButtonClick.value,
                                        ),
                                      ),
                                      SpaceH(AppSize.w20),
                                      Expanded(
                                        child: MinutePickerWidget(
                                          isLifestyleCubit: false,
                                          unhelpfulBehaviourCubit: ref,
                                          isError: DateTime(
                                                selectedDate?.year ?? DateTime.now().year,
                                                selectedDate?.month ?? DateTime.now().month,
                                                selectedDate?.day ?? DateTime.now().day,
                                                ref.selectedHour,
                                                ref.selectedMinute,
                                              ).isBefore(DateTime.now()) &&
                                              ref.isButtonClick.value,
                                        ),
                                      ),
                                      SpaceH(AppSize.w40),
                                    ],
                                  ),
                                  SpaceV(AppSize.h5),
                                  if (DateTime(
                                        selectedDate?.year ?? DateTime.now().year,
                                        selectedDate?.month ?? DateTime.now().month,
                                        selectedDate?.day ?? DateTime.now().day,
                                        ref.selectedHour,
                                        ref.selectedMinute,
                                      ).isBefore(DateTime.now()) &&
                                      ref.isButtonClick.value)
                                    Center(child: CustomErrorWidget(errorMessgaeText: AsLocaleKeys.lsErrorsFuture.tr()))
                                  else
                                    const SizedBox(),
                                  SpaceV(AppSize.h20),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    CustomButton(
                      title: CoreLocaleKeys.buttonsNext.tr(),
                      onTap: () async {
                        if (DateTime(
                          selectedDate?.year ?? DateTime.now().year,
                          selectedDate?.month ?? DateTime.now().month, // Current month
                          selectedDate?.day ?? DateTime.now().day, // provide fallback
                          ref.selectedHour, // Selected hour
                          ref.selectedMinute, // Selected minute
                        ).isBefore(DateTime.now())) {
                          ref.isButtonClick.value = true;
                          'storedActivityJson 1${selectedDate?.day}'.logD;
                        } else {
                          ref.isButtonClick.value = false;

                          final ubAData = UbAData(); // Create an empty UbAData object
                          ref.activityImage.value.logD;
                          ref.activityName.value.logD;
                          await ref.updateUbAData(
                            data: ubAData,
                            image: ref.activityImage.value,
                            name: ref.addCustomActivityController.text.isNotEmpty ? ref.addCustomActivityController.text : ref.activityName.value,
                            day: dayNumber ?? '',
                            title: title == AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                ? AsLocaleKeys.lsUbEnjoyment.tr()
                                :  AsLocaleKeys.lsUbAchievement.tr(),
                            hexColor: ref.activityColor.value.isNotEmpty && isCommingFromCustom 
                                ? ref.colorToHex(ref.activityColorList[ref.activityColor.value] ?? Colors.transparent)
                                : null, // Send color only if not empty
                          );
                          ref.infoSelectTimeAudioUrl.value = null;

                          if (isCommingFromCustom ?? false) {
  AppNavigation.previousScreen(context);
  AppNavigation.previousScreen(context);
  AppNavigation.previousScreen(context);
  ref.addCustomActivityController.text = '';
  ref.selectedIndex.value = -1;
  ref.selectedHour = 12;
  ref.selectedMinute = 0;
  ref.selectedHourController.text = '12';
  ref.selectedMinuteController.text = '00';
} else {
  AppNavigation.previousScreen(context);
  AppNavigation.previousScreen(context);
  ref.selectedHour = 12;
  ref.selectedMinute = 0;
  ref.selectedHourController.text = '12';
  ref.selectedMinuteController.text = '00';
}



                          // await AppNavigation.pushAndRemoveAllScreen(
                          //   context,
                          //   const UnhelpfulBehaviourCalendarPage(
                          //     isFromSelectTime: true,
                          //   ),
                          // );
                        }

                        log('activity?.name == ${state.enjoymentActivityList}');
                      },
                      isBottom: true,
                      color: context.themeColors.blueColor,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
