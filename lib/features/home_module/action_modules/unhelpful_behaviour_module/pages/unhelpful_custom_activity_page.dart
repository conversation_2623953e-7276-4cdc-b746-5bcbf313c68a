import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_select_time_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class UnhelpfulCustomActivityPage extends StatelessWidget {
  const UnhelpfulCustomActivityPage({super.key, this.title, this.index, this.dayNumber, this.selectedDate});
  final String? title;
  final String? dayNumber;
  final int? index;
  final DateTime? selectedDate;
  @override
  Widget build(BuildContext context) {
    
    return BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
      builder: (ctx, state) {
        final ref = ctx.read<UnhelpfulBehaviourCubit>();
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            ref.isText.value = false;
            ref.addCustomActivityController.text = '';
            ref.activityColor.value = '';
  ref.selectedIndex.value = -1;
  ref.selectedHour = 12;
  ref.selectedMinute = 0;
  ref.selectedHourController.text = '12';
  ref.selectedMinuteController.text = '00';
          },
          child: ValueListenableBuilder(
            valueListenable: ref.infoCustomActivityAudioUrl,
            builder: (context, value, child) {
              '>?>?>? color = ${ref.activityColor.value}'.logV;
              return AppScaffold(
                resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldcustomActivity,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoCustomActivityAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldcustomActivity),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldcustomActivity.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoCustomActivityAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: Form(
                  key: ref.formKey,
                  child: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.only(
                                left: AppSize.w24,
                                right: AppSize.w24,
                                bottom: AppSize.h20,
                                top: AppSize.h24,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InformationPageHeadingWidget(
                                    onBackArrowTap: () {
                                      ref.headerInfoText.value = null;
                                      ref.infoActivityAudioUrl.value = null;
                                      Navigator.pop(context);
                                      
            ref.selectedIndex.value = -1;
            ref.selectedHour = 12;
            ref.selectedMinute = 0;
            ref.selectedHourController.text = '12';
            ref.selectedMinuteController.text = '00';
            ref.isText.value = false;
                                      ref.addCustomActivityController.text = '';
                                      ref.activityColor.value = '';
                                    },
                                    title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                    subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                    icon: Assets.icons.actionStrategiesPlanningYourTimePositively, //Assets.icons.infoPage.difficultSituation,
                                    onInfoTap: () {
                                      ref.infoCustomActivityAudioUrl.value =
                                          AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                      final info = (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsUbInfoPanelsInformationText,
                                        context,
                                      ) as List)
                                          .join('<br/><br/>');
                                      ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                              ref.headerInfoText.value == info)
                                          ? null
                                          : info;
                                      ref.headerVideoUrl = ValueNotifier(true);
          
                                      // final audio = AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                      // log('audio $audio');
                                      // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                    },
                                    onLearnTap: () {
                                      ref.infoCustomActivityAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
          
                                      final info = (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsUbInfoPanelsLearnText,
                                        context,
                                      ) as List)
                                          .join('<br/><br/>');
                                      ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                              ref.headerInfoText.value == info)
                                          ? null
                                          : info;
                                      // ref.headerVideoUrl = ValueNotifier(false);
                                      // final audio = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                      // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                    },
                                    infoWidget: ValueListenableBuilder(
                                      valueListenable: ref.headerInfoText,
                                      builder: (context, headerInfoTextV, _) {
                                        return CustomInfoWidget(
                                          customWidget: Column(
                                            children: [
                                              Html(
                                                data: ref.headerInfoText.value ?? '',
                                                style: {
                                                  'strong': Style(
                                                    fontSize: FontSize(AppSize.sp13),
                                                    color: context.themeColors.darkOrangeColor,
                                                    fontWeight: FontWeight.bold,
                                                    fontFamily: 'Poppins',
                                                  ),
                                                  'body': Style(
                                                    fontSize: FontSize(AppSize.sp13),
                                                    color: context.themeColors.darkOrangeColor,
                                                    fontFamily: 'Poppins',
                                                  ),
                                                },
                                              ),
                                              if (ref.headerVideoUrl.value != false) ...[
                                                SpaceV(AppSize.h16),
                                                VideoPlayerScreen(
                                                  imageList: [
                                                    AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster.tr(),
                                                  ],
                                                  //  onTap: onPlayTap,
                                                  navigationFunction: () {},
                                                  videoList: [AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc.tr()],
                                                ),
                                              ],
                                            ],
                                          ),
                                          onCloseTap: () {
                                            ref.headerInfoText.value = null;
                                            ref.infoCustomActivityAudioUrl.value = null;
                                          },
                                          visible: headerInfoTextV.isNotEmptyAndNotNull,
                                          margin: EdgeInsets.symmetric(
                                            vertical: AppSize.h8,
                                          ),
                                          bodyText: headerInfoTextV,
                                        );
                                      },
                                    ),
                                  ),
                                  SpaceV(AppSize.h10),
                                  Center(
                                    child: AppTextWidget(
                                      title ?? '',
                                      style: context.textTheme.titleSmall?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  SpaceV(AppSize.h20),
                                  AppTextWidget(
                                    AsLocaleKeys.lsUbAddYourOwn.tr(),
                                    style: context.textTheme.titleSmall,
                                  ),
                                  SpaceV(AppSize.h20),
                                  CustomOutlinedTextfield(
                                    onChanged: (p0) {
                                      ref.addCustomActivityController.text.trim().isNotEmptyAndNotNull ? ref.isText.value = true : ref.isText.value = false;
                                    },
                                    controller: ref.addCustomActivityController,
                                    hintText: AsLocaleKeys.lsUbActivityName.tr(),
                                    validator: activityValidator().call,
                                    autovalidateMode: AutovalidateMode.onUserInteraction,
                                  ),
                                  SpaceV(AppSize.h20),
                                  AppTextWidget(
                                    AsLocaleKeys.lsUbChooseColour.tr(),
                                    style: context.textTheme.titleSmall,
                                  ),
                                  SpaceV(AppSize.h10),
                                  SizedBox(
                                    height: AppSize.w40, // Define a fixed height for the horizontal ListView
                                    child: ListView.builder(
                                      shrinkWrap: true,
                                      itemCount: ref.activityColorList.length, // Use the map length for itemCount
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        final colorName = ref.activityColorList.keys
                                            .elementAt(index); // Get color name from the map keys
                                        final color = ref.activityColorList[
                                            colorName]; // Get the corresponding color from the map values
          
                                        return ValueListenableBuilder(
                                          valueListenable: ref.selectedIndex,
                                          builder: (context, value, child) {
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 4,
                                              ), // Add padding between circles
                                              child: GestureDetector(
                                                onTap: () {
                                                  ref.selectedIndex.value = index;
                                                  ref.activityColor.value = colorName; // Store color name
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    color: ref.selectedIndex.value == index
                                                        ? Colors.white
                                                        : color, // Use the color directly
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                      color: ref.selectedIndex.value == index
                                                          ? color ?? Colors.transparent
                                                          : Colors.transparent,
                                                      width: 4,
                                                    ),
                                                  ),
                                                  height: AppSize.w30, // Circle height
                                                  width: AppSize.w30, // Circle width
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  SpaceV(AppSize.h20),
                                ],
                              ),
                            ),
                          ),
                        ),
                        ValueListenableBuilder(
                          valueListenable: ref.isText,
                          builder: (context, value, child) {
                            return ValueListenableBuilder(
                              valueListenable: ref.activityColor,
                              builder: (context, value, child) {
                                return CustomButton(
                                  title: CoreLocaleKeys.buttonsNext.tr(),
                                  onTap: () async {
                                    //ref.activityName.value = ref.addCustomActivityController.text;
                                    ref.activityImage.value = AsLocaleKeys.lsUbCustomLogo.tr();
                                      
                                    //  ref.activityColor.value = ref.activityColorList[ref.selectedIndex.value].toString();
                                    log('ref.activityColor.value${ref.activityColor.value}');
                                    if (ref.formKey.currentState!.validate() &&
                                        ref.addCustomActivityController.text.trim() != '' &&
                                        ref.activityColor.value != '') {
                                      ref.infoCustomActivityAudioUrl.value = null;
                                      await AppNavigation.nextScreen(
                                        context,
                                        BlocProvider.value(
                                          value: ref,
                                          child: UnhelpfulSelectTimePage(
                                            title: title ?? '',
                                            index: index,
                                            dayNumber: dayNumber,
                                            selectedDate: selectedDate,
                                            isCommingFromCustom: true,
                                          ),
                                        ),
                                      );
                                    }
                                  },
                                  isBottom: true,
                                  color: ((ref.isText.value ?? false) && ref.addCustomActivityController.text.isNotEmptyAndNotNull && ref.activityColor.value.isNotEmptyAndNotNull)
                                      ? context.themeColors.blueColor
                                      : context.themeColors.blueColor.withOpacity(0.7),
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
