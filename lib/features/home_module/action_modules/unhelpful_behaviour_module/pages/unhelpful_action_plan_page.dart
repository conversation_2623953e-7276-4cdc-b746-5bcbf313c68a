import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/achievement_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/enjoyment_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class UnhelpfulActionPlanPage extends StatelessWidget {
  const UnhelpfulActionPlanPage({super.key});

  @override
  Widget build(BuildContext context) {
    bool isNavigating = false;
    return BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
      builder: (ctx, state) {
        final ref = ctx.read<UnhelpfulBehaviourCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoActionAudioUrl,
          builder: (context, value, child) {
            'ref.infoActionAudioUrl ${ref.infoActionAudioUrl}'.logV;

            return AppScaffold(
              resizeToAvoidBottomInset: false,
              scaffoldKey: ref.scaffoldActionPlanDone,
              isAudioPanelVisible: ref.isAudioPanelVisible,
              isManuallyPaused: ref.infoActionPlanPaused,
              infoAudioUrl: ref.infoActionAudioUrl,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldActionPlanDone),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldActionPlanDone.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoActionAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                  }
                },
              ),
              body: ColoredBox(
                color: context.themeColors.whiteColor,
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      left: AppSize.w24,
                                      right: AppSize.w24,
                                      bottom: AppSize.h20,
                                      top: AppSize.h24,
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            InformationPageHeadingWidget(
                                              onBackArrowTap: () {
                                                ref.infoActionAudioUrl.value = null;
                                                ref.infoWellDonePaused.value = false;
                                                ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                ref.headerInfoText.value = null;
                                                Navigator.pop(context);
                                              },
                                              title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                              subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                              icon: Assets.icons.actionStrategiesPlanningYourTimePositively, //Assets.icons.infoPage.difficultSituation,
                                              onInfoTap: () {
                                                // ref.infoActionAudioUrl.value =
                                                //     AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                                final info = (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsUbInfoPanelsInformationText,
                                                  context,
                                                ) as List)
                                                    .join('<br/><br/>');
                                                // ref.headerInfoText.value =
                                                //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                //             ref.headerInfoText.value == info)
                                                //         ? null
                                                //         : info;
                                                ref.headerVideoUrl = ValueNotifier(true);

                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.infoActionPlanPaused.value = true;
                                                  ref.headerInfoText.value = null;
                                                  ref.infoActionAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                } else {
                                                  ref.infoActionPlanPaused.value = false;
                                                  ref.headerVideoUrl.value = true;
                                                  ref.infoActionAudioUrl.value =
                                                      AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              onLearnTap: () {
                                                // ref.infoActionAudioUrl.value =
                                                //     AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                final info = (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsUbInfoPanelsLearnText,
                                                  context,
                                                ) as List)
                                                    .join('<br/><br/>');
                                                // ref.headerInfoText.value =
                                                //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                //             ref.headerInfoText.value == info)
                                                //         ? null
                                                //         : info;
                                                ref.headerVideoUrl = ValueNotifier(false);
                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.infoActionPlanPaused.value = true;
                                                  ref.headerInfoText.value = null;
                                                  ref.infoActionAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                } else {
                                                  ref.infoActionPlanPaused.value = false;
                                                  ref.headerVideoUrl.value = true;
                                                  ref.infoActionAudioUrl.value =
                                                      AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              infoWidget: ValueListenableBuilder(
                                                valueListenable: ref.headerInfoText,
                                                builder: (context, headerInfoTextV, _) {
                                                  return CustomInfoWidget(
                                                    customWidget: Column(
                                                      children: [
                                                        Html(
                                                          data: ref.headerInfoText.value ?? '',
                                                          style: {
                                                            'strong': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontWeight: FontWeight.bold,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                            'body': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                          },
                                                        ),
                                                        if (ref.headerVideoUrl.value != false) ...[
                                                          SpaceV(AppSize.h16),
                                                          VideoPlayerScreen(
                                                            onTap: () {
                                                              ref.infoActionPlanPaused.value = true;
                                                                      //ref.infoActionAudioUrl.value = null;
                                                                    },
                                                                    onVideoEnded: () async {
  if (isNavigating) return; // 👈 Prevent multiple calls
  isNavigating = true;

  await Future.delayed(const Duration(milliseconds: 300));

  if (Navigator.of(context).canPop()) {
    AppNavigation.previousScreen(context);
  }

  isNavigating = false; // Reset for next time
},
                                                            imageList: [
                                                              AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster.tr(),
                                                            ],
                                                            //  onTap: onPlayTap,
                                                            navigationFunction: () {},
                                                            videoList: [
                                                              AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc.tr(),
                                                            ],
                                                          ),
                                                        ],
                                                      ],
                                                    ),
                                                    onCloseTap: () {
                                                      ref.infoActionPlanPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      ref.infoActionAudioUrl.value =
                                                          AsLocaleKeys.lsUbActionPlanAudio.tr();
                                                    },
                                                    visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                    margin: EdgeInsets.symmetric(
                                                      vertical: AppSize.h8,
                                                    ),
                                                    bodyText: headerInfoTextV,
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h10),
                                            AppTextWidget(
                                              AsLocaleKeys.lsUbActionPlanTitle.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                fontSize: AppSize.sp14,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            AppTextWidget(
                                              (DynamicAssetLoader.getNestedValue(
                                                AsLocaleKeys.lsUbActionPlanText0,
                                                context,
                                              ) as List)
                                                  .join('\n\n'),
                                              style: context.textTheme.titleSmall,
                                            ),
                                            SpaceV(AppSize.h20),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                                              children: [
                                                AppTextWidget(
                                                  AsLocaleKeys.lsUbEnjoyment.tr(),
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontSize: AppSize.sp14,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                                AppTextWidget(
                                                  AsLocaleKeys.lsUbAchievement.tr(),
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontSize: AppSize.sp14,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h10),
                                           Column(
  children: [
    ...List.generate(7, (index) {
      final date = ref.today.add(Duration(days: index));
      final weekday = DateFormat('EEE').format(date).toUpperCase();
      final formattedWeekday = weekday == 'THU' ? 'THUR' : weekday;

      ref
        ..enjoymentActivities = (state.activitiesMap[formattedWeekday] ?? [])
            .where((activity) => activity.type == AsLocaleKeys.lsUbEnjoyment.tr())
            .toList()
        ..achievementActivities = (state.activitiesMap[formattedWeekday] ?? [])
            .where((activity) => activity.type == AsLocaleKeys.lsUbAchievement.tr())
            .toList();

      if (ref.enjoymentActivities.isEmpty && ref.achievementActivities.isEmpty) {
        return const SizedBox();
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppTextWidget(
            ref.formatDate(date),
            style: context.textTheme.titleSmall?.copyWith(
              fontSize: AppSize.sp12,
              fontWeight: FontWeight.w600,
              color: AppColors.activityTextColor,
            ),
          ),
          SizedBox(height: AppSize.h6),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enjoyment Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: ref.enjoymentActivities.map((activity) {
                    return _buildActivityCard(context, activity, ref);
                  }).toList(),
                ),
              ),
              SizedBox(width: AppSize.w10),
              // Achievement Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: ref.achievementActivities.map((activity) {
                    return _buildActivityCard(context, activity, ref);
                  }).toList(),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSize.h12),
        ],
      );
    }),
  ],
)

                                          ],
                                        ),
                                        SpaceV(AppSize.h12),
                                        CustomYesNoButton(
                                          exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                          agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                          isDownLoad: true,
                                          inNoProgress: ValueNotifier(
                                            state.isDownloadPdfAPILoading || state.isEmailPdfAPILoading,
                                          ),
                                          onDownloadTap: () {
                                            CustomDownloadPopup.buildPopupMenu(
                                              context: context,
                                              onDownLoadPdf: () async {
                                                await ref.unhelpfulStrategforDownloadPdfApi(
                                                  context: context,
                                                  isEmail: false,
                                                );
                                              },
                                              onEmailDownload: () async {
                                                await ref.unhelpfulStrategforDownloadPdfApi(
                                                  context: context,
                                                  isEmail: true,
                                                );
                                              },
                                            );
                                          },
                                          padding: EdgeInsets.zero,
                                          onTapYes: () async {
                                            ref.infoActionAudioUrl.value = null;
                                            '>?>?>? 14'.logV;
                                            await AppNavigation.pushAndRemoveAllScreen(
                                              context,
                                              const MyDiagramPage(),
                                            );
                                          },
                                          onTapNo: () async {
                                            // Request permissions
                                          },
                                          noButtonColor: context.themeColors.orangeColor,
                                          isYesNoButton: true,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    // SafeArea(
                    //   child: CustomButton(
                    //     title: AsLocaleKeys.lsUbButtonsFinish.tr(),
                    //     onTap: () async {
                    //       ref.infoActionAudioUrl.value = null;
                    //       await AppNavigation.nextScreen(
                    //         context,
                    //         const MyDiagramPage(),
                    //       );
                    //     },
                    //     isBottom: true,
                    //     color: context.themeColors.blueColor,
                    //   ),
                    // ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}


Widget _buildActivityCard(BuildContext context, Fri activity, UnhelpfulBehaviourCubit ref) {
  '/// image achievment = ${AchievementActivictyList.activityImages[activity.name]}'.logV;
  '/// image enjoyment = ${EnjoymentActivictyList.activityImages[activity.name]}'.logV;
  return Column(
    children: [
      
      Container(
        height: AppSize.h60,
        // margin: EdgeInsets.only(
        //                               top: AppSize.h6,
        //                               bottom: AppSize.h6,
        //                             ),
        padding: EdgeInsets.symmetric(vertical: AppSize.h6, horizontal: AppSize.w6),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              offset: Offset(4, 4),
              blurRadius: 6,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              offset: Offset(-4, -4),
              blurRadius: 6,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              color: activity.color != null
                  ? Color(int.parse('0xFF${activity.color?.colorDefault?.replaceAll('#', '')}'))
                  : Colors.grey,
              child: AppCachedNetworkImage(
                imageUrl: (() {
                  final locToEn = localizedToEnglishName();
                  final englishName = locToEn[activity.name] ?? activity.name;
                  if (activity.type == AsLocaleKeys.lsUbEnjoyment.tr()) {
                    return EnjoymentActivictyList.activityImages[englishName] ?? AsLocaleKeys.lsUbCustomLogo.tr();
                  } else {
                    return AchievementActivictyList.activityImages[englishName] ?? AsLocaleKeys.lsUbCustomLogo.tr();
                  }
                })(),
                width: AppSize.w44,
                height: AppSize.w44,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: AppSize.w10),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppTextWidget(
                    activity.name ?? '',
                    style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
                  ),
                  AppTextWidget(
                    activity.time ?? '',
                    style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      SpaceV(AppSize.h12)
    ],
  );
}
