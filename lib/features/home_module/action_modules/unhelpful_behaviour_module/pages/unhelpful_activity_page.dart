// ignore_for_file: public_member_api_docs, sort_constructors_first, unnecessary_statements
import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_custom_activity_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_select_time_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class UnhelpfulActivityPage extends StatefulWidget {
  const UnhelpfulActivityPage({
    required this.selectedDate,
    super.key,
    this.title,
    this.index,
    this.dayNumber,
  });
  final String? title;
  final String? dayNumber;
  final DateTime selectedDate;
  final int? index;

  @override
  State<UnhelpfulActivityPage> createState() => _UnhelpfulActivityPageState();
}

class _UnhelpfulActivityPageState extends State<UnhelpfulActivityPage> {
  CarouselSliderController controller = CarouselSliderController();

  @override
  void initState() {
    super.initState();
    print('/////////// UnhelpfulActivityPage initState');
    final ref = context.read<UnhelpfulBehaviourCubit>();
    ref.initializeActivityImages();
    '//// widget.title = ${widget.title}'.logD;
    '//// AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr() = ${AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()}'.logD;
    if (widget.title == AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()) {
      ref.activityName.value = ref.imagesNameList.isNotEmpty ? ref.imagesNameList.first : '';
      ref.activityImage.value = ref.imagesList.isNotEmpty ? ref.imagesList.first : '';
    } else {
      ref.activityName.value = ref.achievementNameList.isNotEmpty ? ref.achievementNameList.first : '';
      ref.activityImage.value = ref.achievementImagesList.isNotEmpty ? ref.achievementImagesList.first : '';
    }
    ref.activityName.value.logD;
    ref.activityImage.value.logD;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
      builder: (ctx, state) {
        'onTapp'.logV;
        final ref = ctx.read<UnhelpfulBehaviourCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoActivityAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              resizeToAvoidBottomInset: false,
              scaffoldKey: ref.scaffoldActivity,
              isAudioPanelVisible: ref.isAudioPanelVisible,
              infoAudioUrl: ref.infoActivityAudioUrl,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldActivity),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldActivity.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoActivityAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                  }
                },
              ),
              body: ColoredBox(
                color: context.themeColors.whiteColor,
                child: Column(
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context,constrains) {
                          return SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: constrains.maxHeight),
                              child: Padding(
                                padding: EdgeInsets.only(bottom: AppSize.h20, top: AppSize.h24),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 24),
                                      child: InformationPageHeadingWidget(
                                        onBackArrowTap: () {
                                          ref.headerInfoText.value = null;
                                          ref.infoAudioUrl.value = null;
                                          Navigator.pop(context);
                                        },
                                        title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                        subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                        icon: Assets.icons.actionStrategiesPlanningYourTimePositively, //Assets.icons.infoPage.difficultSituation,
                                        onInfoTap: () {
                                          ref.infoActivityAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                          final info = (DynamicAssetLoader.getNestedValue(
                                            AsLocaleKeys.lsUbInfoPanelsInformationText,
                                            context,
                                          ) as List)
                                              .join('<br/><br/>');
                                          ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info)
                                              ? null
                                              : info;
                                          ref.headerVideoUrl = ValueNotifier(true);
                                        },
                                        onLearnTap: () {
                                          ref.infoActivityAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                        
                                          final info = (DynamicAssetLoader.getNestedValue(
                                            AsLocaleKeys.lsUbInfoPanelsLearnText,
                                            context,
                                          ) as List)
                                              .join('<br/><br/>');
                                          ref.headerInfoText.value = (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info)
                                              ? null
                                              : info;
                                          ref.headerVideoUrl = ValueNotifier(false);
                                        },
                                        infoWidget: ValueListenableBuilder(
                                          valueListenable: ref.headerInfoText,
                                          builder: (context, headerInfoTextV, _) {
                                            return CustomInfoWidget(
                                              customWidget: Column(
                                                children: [
                                                  Html(
                                                    data: ref.headerInfoText.value ?? '',
                                                    style: {
                                                      'strong': Style(
                                                        fontSize: FontSize(AppSize.sp13),
                                                        color: context.themeColors.darkOrangeColor,
                                                        fontWeight: FontWeight.bold,
                                                        fontFamily: 'Poppins',
                                                      ),
                                                      'body': Style(
                                                        fontSize: FontSize(AppSize.sp13),
                                                        color: context.themeColors.darkOrangeColor,
                                                        fontFamily: 'Poppins',
                                                      ),
                                                    },
                                                  ),
                                                  if (ref.headerVideoUrl.value != false) ...[
                                                    SpaceV(AppSize.h16),
                                                    InkWell(
                                                      onTap: () {
                                                        'onTap+++'.logV;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoActivityAudioUrl.value = null;
                                                      },
                                                      child: VideoPlayerScreen(
                                                        imageList: [
                                                          AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster.tr(),
                                                        ],
                                                        //  onTap: onPlayTap,
                                                        navigationFunction: () {},
                                                        onTap: () {
                                                          ref.infoActivityAudioUrl.value = null;
                                                        },
                                                        videoList: [AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc.tr()],
                                                      ),
                                                    ),
                                                  ],
                                                ],
                                              ),
                                              onCloseTap: () {
                                                ref.headerInfoText.value = null;
                                                ref.infoActivityAudioUrl.value = null;
                                              },
                                              visible: headerInfoTextV.isNotEmptyAndNotNull,
                                              margin: EdgeInsets.symmetric(
                                                vertical: AppSize.h8,
                                              ),
                                              bodyText: headerInfoTextV,
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                    SpaceV(AppSize.h10),
                                    Center(
                                      child: AppTextWidget(
                                        widget.title ?? '',
                                        style: context.textTheme.titleSmall?.copyWith(
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    SpaceV(AppSize.h20),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                      child: AppTextWidget(
                                        AsLocaleKeys.lsUbSelectOrAddOwn.tr(),
                                        style: context.textTheme.titleSmall,
                                      ),
                                    ),
                                    //SpaceV(AppSize.h10),
                                    ValueListenableBuilder(
                                      valueListenable: ref.activityName,
                                      builder: (context, value, child) {
                                        return Padding(
                                          padding: EdgeInsets.symmetric(vertical: AppSize.h40, horizontal: AppSize.w10),
                                          child: Row(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  controller.previousPage();
                                                },
                                                child: Container(
                                                  height: AppSize.h30,
                                                  width: AppSize.w30,
                                                  decoration: BoxDecoration(
                                                    color: context.themeColors.blackColor.withOpacity(0.5),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.chevron_left,
                                                    size: 25,
                                                    color: context.themeColors.whiteColor,
                                                  ),
                                                ),
                                              ),
                                              SpaceH(AppSize.w10),
                                              Expanded(
                                                child: ref.imagesNameList.isEmpty && widget.title == AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                                    ? Center(child: Text('No activities available'))
                                                    : ref.achievementNameList.isEmpty && widget.title != AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                                        ? Center(child: Text('No activities available'))
                                                        : CarouselSlider.builder(
                                                            itemCount:
                                                                widget.title == AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                                                    ? ref.imageDetailList.length
                                                                    : ref.achievementImagesList.length,
                                                            itemBuilder: (context, index, _) {
                                                              return Padding(
                                                                padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
                                                                child: Column(
                                                                  children: [
                                                                    Expanded(
                                                                      child: AppCachedNetworkImage(
                                                                        imageUrl: widget.title ==
                                                                                AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                                                            ? ref.imageDetailList[index]
                                                                            : ref.achievementDetailList[index],
                                                                      ),
                                                                    ),
                                                                    Padding(
                                                                      padding: EdgeInsets.only(top: AppSize.h5),
                                                                      child: AppTextWidget(
                                                                        textAlign: TextAlign.center,
                                                                        widget.title ==
                                                                                AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr()
                                                                            ? ref.imagesNameList[index]
                                                                            : ref.achievementNameList[index],
                                                                        style: context.textTheme.titleSmall?.copyWith(
                                                                          fontWeight: (widget.title ==
                                                                                      AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment
                                                                                          .tr()
                                                                                  ? ref.imagesNameList[index] == value
                                                                                  : ref.achievementNameList[index] == value)
                                                                              ? FontWeight.w600
                                                                              : FontWeight.w400,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              );
                                                            },
                                                            carouselController: controller,
                                                            options: CarouselOptions(
                                                              enlargeStrategy: CenterPageEnlargeStrategy.height,
                                                              height: AppSize.h200,
                                                              enlargeCenterPage: true,
                                                              viewportFraction: 0.58,
                                                              onPageChanged: (index, reason) {
                                                                ref.activityName.value = (widget.title ==
                                                                        AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr())
                                                                    ? ref.imagesNameList[index]
                                                                    : ref.achievementNameList[index];
                                                                // ref.activityImage.value = ref.imageDetailList[index];
                                                                ref.activityImage.value = (widget.title ==
                                                                        AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment.tr())
                                                                    ? ref.imagesList[index]
                                                                    : ref.achievementImagesList[index];
                                                                ref.activityName.value.logD;
                                                                ref.activityImage.value.logD;
                                                              },
                                                            ),
                                                          ),
                                              ),
                                              SpaceH(AppSize.w10),
                                              InkWell(
                                                onTap: () {
                                                  controller.nextPage();
                                                },
                                                child: Container(
                                                  height: AppSize.h30,
                                                  width: AppSize.w30,
                                                  decoration: BoxDecoration(
                                                    color: context.themeColors.blackColor.withOpacity(0.5),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.chevron_right,
                                                    size: 25,
                                                    color: context.themeColors.whiteColor,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                    //SpaceV(AppSize.h20),
                                    CustomButton(
                      title: AsLocaleKeys.lsUbButtonsSelectActivity.tr(),
                      isSelectActivity: true,
                      onTap: () async {
                        ref.headerInfoText.value = null;
                        ref.infoActivityAudioUrl.value = null;
                        await AppNavigation.nextScreen(
                          context,
                          BlocProvider.value(
                            value: ref,
                            child: UnhelpfulSelectTimePage(
                              isCommingFromCustom: false,
                              title: widget.title ?? '',
                              dayNumber: widget.dayNumber,
                              index: widget.index,
                              selectedDate: widget.selectedDate,
                            ),
                          ),
                        );
                      },
                      isBottom: true,
                      color: context.themeColors.blueColor,
                    ),
                    CustomButton(
                      title: AsLocaleKeys.lsUbButtonsAddMyOwn.tr(),
                      onTap: () async {
                        ref.headerInfoText.value = null;
                        ref.infoAudioUrl.value = null;
                        await AppNavigation.nextScreen(
                          context,
                          BlocProvider.value(
                            value: ref,
                            child: UnhelpfulCustomActivityPage(
                              title: widget.title ?? '',
                              index: widget.index,
                              dayNumber: widget.dayNumber,
                              selectedDate: widget.selectedDate,
                            ),
                          ),
                        );
                      },
                      isBottom: true,
                      color: context.themeColors.orangeColor,
                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
