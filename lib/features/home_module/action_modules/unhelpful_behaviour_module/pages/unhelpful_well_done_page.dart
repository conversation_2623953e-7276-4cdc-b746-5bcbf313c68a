import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_confetti_animation.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/parse_tagged_text.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class UnhelpfulWellDonePage extends StatelessWidget {
  UnhelpfulWellDonePage({super.key});
  final controller = ConfettiController();

  @override
  Widget build(BuildContext context) {
    bool isNavigating = false;
    return BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
      builder: (ctx, state) {
        final ref = ctx.read<UnhelpfulBehaviourCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoWellDoneAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: ref.infoWellDonePaused,
              resizeToAvoidBottomInset: false,
              scaffoldKey: ref.scaffoldWellDone,
              isAudioPanelVisible: ref.isAudioPanelVisible,
              infoAudioUrl: ref.infoWellDoneAudioUrl,//ValueNotifier(AsLocaleKeys.lsUbSummaryAudioApp.tr()),//ref.infoWellDoneAudioUrl,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldWellDone),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldWellDone.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoWellDoneAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                  }
                },
              ),
              body: ColoredBox(
                color: context.themeColors.whiteColor,
                child: Column(
                  children: [
                    Center(child: CustomConfettiAnimation(controller: controller)),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      left: AppSize.w24,
                                      right: AppSize.w24,
                                      bottom: AppSize.h20,
                                      top: AppSize.h24,
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            InformationPageHeadingWidget(
                                              onBackArrowTap: () {
                                                ref.infoAudioUrl.value = null;
                                                ref.headerInfoText.value = null;

                                                Navigator.pop(context);
                                              },
                                              title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                              subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                              icon: Assets.icons.actionStrategiesPlanningYourTimePositively, //Assets.icons.infoPage.difficultSituation,
                                              onInfoTap: () {
                                          
                                                ref.headerVideoUrl = ValueNotifier(true);

                                                final info = (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsUbInfoPanelsInformationText,
                                                  context,
                                                ) as List<dynamic>? ?? [])
                                                    .join('<br/><br/>');
                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.infoWellDonePaused.value = true;
                                                  ref.headerInfoText.value = null;
                                                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                } else {
                                                  ref.infoWellDonePaused.value = false;
                                                  ref.headerVideoUrl.value = true;
                                                  ref.infoWellDoneAudioUrl.value =
                                                      AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              onLearnTap: () {
                                                // ref.infoWellDoneAudioUrl.value =
                                                //     AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                final info = (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsUbInfoPanelsLearnText,
                                                  context,
                                                ) as List<dynamic>? ?? [])
                                                    .join('<br/><br/>');
                                                // ref.headerInfoText.value =
                                                //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                //             ref.headerInfoText.value == info)
                                                //         ? null
                                                //         : info;
                                                ref.headerVideoUrl = ValueNotifier(false);
                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.infoWellDonePaused.value = true;
                                                  ref.headerInfoText.value = null;
                                                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                } else {
                                                  ref.infoWellDonePaused.value = false;
                                                  ref.headerVideoUrl.value = true;
                                                  ref.infoWellDoneAudioUrl.value =
                                                      AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              infoWidget: ValueListenableBuilder(
                                                valueListenable: ref.headerInfoText,
                                                builder: (context, headerInfoTextV, _) {
                                                  return CustomInfoWidget(
                                                    customWidget: Column(
                                                      children: [
                                                        Html(
                                                          data: ref.headerInfoText.value ?? '',
                                                          style: {
                                                            'strong': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontWeight: FontWeight.bold,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                            'body': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                          },
                                                        ),
                                                        if (ref.headerVideoUrl.value != false) ...[
                                                          SpaceV(AppSize.h16),
                                                          VideoPlayerScreen(
                                                            onTap: () {
                                                              ref.infoWellDonePaused.value = true;
                                                                      //ref.infoWellDoneAudioUrl.value = null;
                                                                    },
                                                                    onVideoEnded: () async {
  if (isNavigating) return; // 👈 Prevent multiple calls
  isNavigating = true;

  await Future.delayed(const Duration(milliseconds: 300));

  if (Navigator.of(context).canPop()) {
    AppNavigation.previousScreen(context);
  }

  isNavigating = false; // Reset for next time
},
                                                            imageList: [
                                                              AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster.tr(),
                                                            ],
                                                            //  onTap: onPlayTap,
                                                            navigationFunction: () {},
                                                            videoList: [
                                                              AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc.tr(),
                                                            ],
                                                          ),
                                                        ],
                                                      ],
                                                    ),
                                                    onCloseTap: () {
                                                      ref.infoWellDonePaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      ref.infoWellDoneAudioUrl.value =
                                                          AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                                    },
                                                    visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                    margin: EdgeInsets.symmetric(
                                                      vertical: AppSize.h8,
                                                    ),
                                                    bodyText: headerInfoTextV,
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h10),
                                            AppTextWidget(
                                              AsLocaleKeys.lsUbSummaryTitle.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                fontSize: AppSize.sp14,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            RichText(
  text: parseTaggedText(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsUbSummaryTextApp,
      context,
    ) as List<dynamic>? ?? []).join('\n\n'),
    baseStyle: context.textTheme.titleSmall,
  ),
)

                                          ],
                                        ),
                                        SpaceV(AppSize.h12),
                                        CustomButton(
                                          padding: EdgeInsets.zero,
                                          title: CoreLocaleKeys.buttonsNext.tr(),
                                          onTap: () async {
                                            //   ref.isAudioPanelVisible.value = false;
                                            //    ref.infoAudioUrl.value = null;
                                            ref.headerInfoText.value = null;
                                            ref.infoWellDoneAudioUrl.value = null;
                                            ref.infoActionPlanPaused.value = false;
                                            ref.infoActionAudioUrl.value = AsLocaleKeys.lsUbActionPlanAudio.tr();
                                            await AppNavigation.nextScreen(
                                              context,
                                              BlocProvider.value(
                                                value: ref,
                                                child: const UnhelpfulActionPlanPage(),
                                              ),
                                            );
                                          },
                                          isBottom: true,
                                          color: context.themeColors.blueColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
