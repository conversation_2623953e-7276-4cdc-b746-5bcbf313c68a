import 'dart:convert';
import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_activity_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/common_activity_container_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/enjoyment_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/achievement_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:breakingfree_v2/custom_widgets/common_functions.dart/common_functions.dart';

class UnhelpfulBehaviourCalendarPage extends StatefulWidget {
  const UnhelpfulBehaviourCalendarPage({
    super.key,
    this.isFromSelectTime = false,
  });
  final bool isFromSelectTime;
  @override
  State<UnhelpfulBehaviourCalendarPage> createState() => _UnhelpfulBehaviourCalendarPageState();
}

class _UnhelpfulBehaviourCalendarPageState extends State<UnhelpfulBehaviourCalendarPage> {
  int mostRecentTimestamp = 0;
  UnhelpfulBehaviourCubit? _cubit;
  Locale? _lastLocale;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<UnhelpfulBehaviourCubit>();
      cubit.initializeActivityImages();
      _cubit = cubit;
      _lastLocale = Localizations.localeOf(context);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final currentLocale = Localizations.localeOf(context);
    if (_cubit != null && _lastLocale != null && _lastLocale != currentLocale) {
      _cubit?.initializeActivityImages();
      _lastLocale = currentLocale;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isNavigating = false;
    return BlocProvider(
      create: (context) => UnhelpfulBehaviourCubit(isFromSelectTime: widget.isFromSelectTime),
      child: BlocBuilder<UnhelpfulBehaviourCubit, UnhelpfulBehaviourState>(
        builder: (context, state) {
          final ref = context.read<UnhelpfulBehaviourCubit>();
          return AbsorbPointer(
            absorbing: state.isAPILoading,
            child: ValueListenableBuilder(
              valueListenable: ref.infoAudioUrl,
              builder: (context, value, child) {
                return AppScaffold(
                    isManuallyPaused: ref.isManuallyPaused,
                    resizeToAvoidBottomInset: false,
                    scaffoldKey: ref.scaffoldKey,
                    isAudioPanelVisible: ref.isAudioPanelVisible,
                    infoAudioUrl: ref.infoAudioUrl,
                    drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                    appBar: CommonAppBar(
                      onPrefixTap: () {
                        ref.scaffoldKey.currentState?.openDrawer();
                      },
                      onSuffixTap: () {
                        if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                          ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                        }
                      },
                    ),
                    body: ColoredBox(
                      color: context.themeColors.whiteColor,
                      child: Column(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(right: AppSize.w4),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return CustomRawScrollbar(
                                    child: SingleChildScrollView(
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: AppSize.w24,
                                            right: AppSize.w24,
                                            bottom: AppSize.h20,
                                            top: AppSize.h24,
                                          ),
                                          child: ValueListenableBuilder(
                                            valueListenable: ref.isError,
                                            builder: (context, value, child) {
                                              return Column(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    children: [
                                                      InformationPageHeadingWidget(
                                                        onBackArrowTap: () {
                                                          '>?>?>? 15'.logV;
                                                          AppNavigation.pushAndRemoveAllScreen(
                                                            context,
                                                            const MyDiagramPage(),
                                                          );
                                                          // ref.isAudioPanelVisible.dispose();
                                                          // ref.headerInfoText.dispose();
                                                          // ref.isAudioPanelVisible.dispose();
                                                          ref.infoActionAudioUrl.value = null;
                                                          //  ref.infoActionAudioUrl.value = null;
                                                          // ref.isAudioActionPanelVisible.dispose();
                                                        },
                                                        title: CoreLocaleKeys
                                                            .titlesInformationStrategiesUnhelpfulBehaviours
                                                            .tr(),
                                                        subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                                        icon: Assets.icons.actionStrategiesPlanningYourTimePositively,//Assets.icons.infoPage.difficultSituation,
                                                        onInfoTap: () {
                                                          ref.infoAudioUrl.value =
                                                              AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsUbInfoPanelsInformationText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          ref.headerInfoText.value =
                                                              (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                      ref.headerInfoText.value == info)
                                                                  ? null
                                                                  : info;
                                                          ref.headerVideoUrl = ValueNotifier(true);

                                                          //  final audio = AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();
                                                          //    log('audio $audio');
                                                          //  ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                                        },
                                                        onLearnTap: () {
                                                          ref.infoAudioUrl.value =
                                                              AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();

                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsUbInfoPanelsLearnText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          ref.headerInfoText.value =
                                                              (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                      ref.headerInfoText.value == info)
                                                                  ? null
                                                                  : info;
                                                          // ref.headerVideoUrl = ValueNotifier(false);
                                                          // final audio = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                                          // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                                        },
                                                        infoWidget: ValueListenableBuilder(
                                                          valueListenable: ref.headerInfoText,
                                                          builder: (context, headerInfoTextV, _) {
                                                            return CustomInfoWidget(
                                                              customWidget: Column(
                                                                children: [
                                                                  Html(
                                                                    data: ref.headerInfoText.value ?? '',
                                                                    style: {
                                                                      'strong': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontWeight: FontWeight.bold,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                      'body': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                    },
                                                                  ),
                                                                  if (ref.headerVideoUrl.value != false) ...[
                                                                    SpaceV(AppSize.h16),
                                                                    VideoPlayerScreen(
                                                                      onTap: () {
                                                                        ref.isManuallyPaused.value = true;
                                                                        //ref.infoAudioUrl.value = null;
                                                                      },
                                                                      onVideoEnded: () async {
    if (isNavigating) return; // 👈 Prevent multiple calls
    isNavigating = true;

    await Future.delayed(const Duration(milliseconds: 300));

    if (Navigator.of(context).canPop()) {
      AppNavigation.previousScreen(context);
    }

    isNavigating = false; // Reset for next time
},
                                                                      imageList: [
                                                                        AsLocaleKeys.lsUbInfoPanelsInformationVideoPoster
                                                                            .tr(),
                                                                      ],
                                                                      //  onTap: onPlayTap,
                                                                      navigationFunction: () {},
                                                                      videoList: [
                                                                        AsLocaleKeys.lsUbInfoPanelsInformationVideoSrc
                                                                            .tr(),
                                                                      ],
                                                                    ),
                                                                  ],
                                                                ],
                                                              ),
                                                              onCloseTap: () {
                                                                ref.headerInfoText.value = null;
                                                                ref.infoAudioUrl.value = null;
                                                                ref.isAudioPanelVisible.value = false;
                                                              },
                                                              visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                              margin: EdgeInsets.symmetric(
                                                                vertical: AppSize.h8,
                                                              ),
                                                              bodyText: headerInfoTextV,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsUbSubtitlesCalendar.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontSize: AppSize.sp14,
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                        children: [
                                                          AppTextWidget(
                                                            AsLocaleKeys.lsUbEnjoyment.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontSize: AppSize.sp14,
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                          AppTextWidget(
                                                            AsLocaleKeys.lsUbAchievement.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontSize: AppSize.sp14,
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                      Column(
                                                        children: List.generate(7, (index) {
                                                          final date = ref.today.add(Duration(days: index));

                                                          final dayNumber =
                                                              DateFormat('d').format(date); // Day number (1, 2, 3, etc.)
                                                          final weekday = DateFormat('EEE')
                                                              .format(date)
                                                              .toUpperCase(); // Day name (e.g., MON, TUE, etc.)
                                                          'aas ActivitiesMap without changes: ${state.activitiesMap}'
                                                              .logD;



                                                          final formattedWeekday = weekday == 'THU' ? 'THUR' : weekday;
                                                          'formattedWeekday $formattedWeekday'.logD;

                                                          ref
                                                            ..enjoymentActivities =
                                                                (state.activitiesMap[formattedWeekday] ?? [])
                                                                    .where((activity) {
                                                                      '?|?|?| activity.type = ${activity.type}'.logV;
                                                                      '?|?|?| AsLocaleKeys.lsUbEnjoyment.tr() = ${AsLocaleKeys.lsUbEnjoyment.tr()}'.logV;
                                                                     return  ((Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-US') ?? false ) || 
                                                                     (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-CA') ?? false ) || 
                                                                      (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-AU') ?? false ) || 
                                                                      (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-GB') ?? false ) ) ? activity.type == 'Enjoyment' : activity.type == AsLocaleKeys.lsUbEnjoyment.tr();
                                                                  })
                                                                    .toList()
                                                            ..achievementActivities =
                                                                (state.activitiesMap[formattedWeekday] ?? [])
                                                                    .where((activity) {
                                                                      '?|?|?| activity.type = ${activity.type}'.logV;
                                                                      '?|?|?| AsLocaleKeys.lsUbEnjoyment.tr() = ${AsLocaleKeys.lsUbAchievement.tr()}'.logV;
                                                                      return ((Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-US') ?? false ) || 
                                                                      (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-CA') ?? false ) || 
                                                                      (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-AU') ?? false ) || 
                                                                      (Injector.instance<AppDB>().langugaeModel?.languages?.contains('en-GB') ?? false ) ) ? activity.type == 'Achievement' : activity.type == AsLocaleKeys.lsUbAchievement.tr();
                                                                  })
                                                                    .toList();
                                                          // Debugging: Print activities
                                                          'Enjoyment Activities: ${ref.enjoymentActivities.toList()}'.logD;
                                                          'Achievement Activities: ${ref.achievementActivities.length}'.logD;

                                                          // Return the widget for each day
                                                          return Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              AppTextWidget(
                                                                ref.formatDate(date),
                                                                // '$formattedWeekday $dayNumber',
                                                                style: context.textTheme.titleSmall?.copyWith(
                                                                  fontSize: AppSize.sp12,
                                                                  fontWeight: FontWeight.w600,
                                                                  color: const Color.fromARGB(255, 74, 64, 64),
                                                                ),
                                                              ),
                                                              SizedBox(height: AppSize.h4),
                                                              Row(
                                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  // Container for Enjoyment Activities
                                                                 CommonActivityContanerWidget(
                                                                    title: AsLocaleKeys.lsUbSubtitlesActivityForEnjoyment
                                                                        .tr(),
                                                                    weekday: formattedWeekday,
                                                                    onTap1: (activity) {
                                                                      
                                                                      '?|?|?| activity = ${activity.toJson()}'.logV;
                                                                      log('is tabbed${activity.day}');
                                                                      log('is tabbed${activity.name}');
                                                                      log('is tabbed${activity.type}');
                                                                      log('is tabbed${ref.achievementActivities.toList()}');
                                                                      setState(() {
                                                                        ref.selectedActivity = activity;
                                                                      }); 
                                                                      //'isTrue$isTrue'.logD;
                                                                    },
                                                                    dayNumber: dayNumber,
                                                                    index: index,
                                                                    enjoymentActivitiesForIndex: ref.enjoymentActivities,
                                                                    ref: ref,
                                                                    onDeleteTap: (activity) async {
                                                                      await ref.deleteActivity(activity,formattedWeekday);
                                                                      setState(() {});
                                                                    },
                                                                    onTap: () {
                                                                      '?|?|?| onTap'.logV;
                                                                      ref.updateSelectedDayAndDate(dayNumber, date);
                                                                      ref.headerInfoText.value = null;
                                                                      ref.infoAudioUrl.value = null;
                                                                      '//// submitted activity = ${AsLocaleKeys
                                                                                .lsUbSubtitlesActivityForEnjoyment.tr()}'.logV;
                                                                      AppNavigation.nextScreen(
                                                                        context,
                                                                        BlocProvider.value(
                                                                          value: ref,
                                                                          child: UnhelpfulActivityPage(
                                                                            title: AsLocaleKeys
                                                                                .lsUbSubtitlesActivityForEnjoyment
                                                                                .tr(),
                                                                            index: index,
                                                                            dayNumber: formattedWeekday,
                                                                            selectedDate: date,
                                                                          ),
                                                                        ),
                                                                      );
                                                                    },
                                                                    selectedDate: date,
                                                                    // currentEnjoymentActivities: const [],
                                                                  ),
                                                                  SizedBox(width: AppSize.w10),
                                                                  // Container for Achievement Activities
                                                                  CommonActivityContanerWidget(
                                                                    onTap1: (activity) {
                                                                      setState(() {
                                                                        ref.selectedActivity = activity;
                                                                      });
                                                                      //'isTrue$isTrue'.logD;
                                                                    },
                                                                    weekday: formattedWeekday,
                                                                    dayNumber: dayNumber,
                                                                    index: index,
                                                                    enjoymentActivitiesForIndex:
                                                                        ref.achievementActivities,
                                                                    ref: ref,
                                                                    title: AsLocaleKeys
                                                                        .lsUbSubtitlesActivityForAchievement
                                                                        .tr(),

                                                                    onDeleteTap: (activity) async {
                                                                      await ref.deleteActivity(activity,formattedWeekday);
                                                                      setState(() {});
                                                                    },
                                                                    onTap: () {
                                                                      ref.updateSelectedDayAndDate(dayNumber, date);
                                                                      ref.headerInfoText.value = null;
                                                                      ref.infoAudioUrl.value = null;
                                                                      AppNavigation.nextScreen(
                                                                        context,
                                                                        BlocProvider.value(
                                                                          value: ref,
                                                                          child: UnhelpfulActivityPage(
                                                                            title: AsLocaleKeys
                                                                                .lsUbSubtitlesActivityForAchievement
                                                                                .tr(),
                                                                            index: index,
                                                                            dayNumber: formattedWeekday,
                                                                            selectedDate: date,
                                                                          ),
                                                                        ),
                                                                      );
                                                                    },
                                                                    selectedDate: date,
                                                                    // currentEnjoymentActivities: const [],
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          );
                                                        }),
                                                      ),
                                                      if (ref.isError.value && (ref.enjoymentActivities.isEmpty || ref.achievementActivities.isEmpty))
                                                        CustomErrorWidget(
                                                          errorMessgaeText: AsLocaleKeys.lsUbErrorsMinimumOne.tr(),
                                                        )
                                                      else
                                                        const SizedBox(),
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        flex: 40,
                                                        child: GestureDetector(
                                                          onTap: ref.clearMyAllActivities,
                                                          child: Container(
                                                            height: AppSize.h34,
                                                            decoration: BoxDecoration(
                                                              color: context.themeColors.redColor,
                                                              borderRadius: BorderRadius.circular(AppSize.r32),
                                                            ),
                                                            child: Padding(
                                                              padding: const EdgeInsets.all(8),
                                                              child: Center(
                                                                child: Text(
                                                                  overflow: TextOverflow.ellipsis,
                                                                  AsLocaleKeys.lsUbButtonsClear.tr(),
                                                                  style: context.textTheme.titleMedium?.copyWith(
                                                                    fontSize: AppSize.sp12,
                                                                    color: context.themeColors.scaffoldColor,
                                                                    fontWeight: FontWeight.w500,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.h6),
                                                      Expanded(
                                                        flex: 60,
                                                        child: CustomButton(
                                                          padding: EdgeInsets.zero,
                                                          title: CoreLocaleKeys.buttonsNext.tr(),
                                                          inProgress: state.isAPILoading,
                                                          onTap: () async {
                                                            'state.activitiesMap${state.activitiesMap}'.logD;
                                                            'state.activitiesMap${state.activitiesMap.entries}'.logD;
                                                            final mutableMap =
                                                                Map<String, List<Fri>>.from(state.activitiesMap);
                                                            'state.activitiesMap${mutableMap.values.any((value) => value.isEmpty)}'
                                                                .logD;
                                                            'state.activitiesMap${mutableMap.values.any((value) => value.isNotEmpty)}'
                                                                .logD;
                                                            // TEST: Schedule a test notification for Tuesday at 18:50
                                                            //await CommonFunction.testWeeklyNotificationForSpecificTime();
                                                            if (!mutableMap.values.every((value) => value.isEmpty)) {
                                                              ref.isError.value = false;

                                                              // Show error if all lists are empty
                                                              '==== Error: All activities are empty!'.logD;
                                                            } else {
                                                              // Do something else if at least one list is available
                                                              '==== At least one activity is available!'.logD;
                                                            }

                                                            if (!mutableMap.values.every((value) => value.isEmpty)) {
                                                              mutableMap
                                                                ..forEach((key, value) {
                                                                  for (final fri in value) {
                                                                    if (fri.day == 'Thu') {
                                                                      fri.day = 'THUR'; // Update the day
                                                                    }
                                                                  }
                                                                })
                                                                // Remove entries with empty lists
                                                                ..removeWhere((key, value) => value.isEmpty);

                                                              final data = mutableMap.map((key, value) {
                                                                final locToEn = localizedToEnglishName();
                                                                return MapEntry(
                                                                  key,
                                                                  value.map((fri) {
                                                                    final englishName = locToEn[fri.name] ?? fri.name;
                                                                    // Map type to English
                                                                    String englishType = fri.type ?? '';
                                                                    if (fri.type == AsLocaleKeys.lsUbEnjoyment.tr()) {
                                                                      englishType = 'Enjoyment';
                                                                    } else if (fri.type == AsLocaleKeys.lsUbAchievement.tr()) {
                                                                      englishType = 'Achievement';
                                                                    }
                                                                    final json = fri.toJson();
                                                                    json['name'] = englishName;
                                                                    json['type'] = englishType;
                                                                    return json;
                                                                  }).toList(),
                                                                );
                                                              });

                                                              '????? My ? data = $data'.logD;

                                                              'ActivitiesMap without changes: ${state.activitiesMap}'
                                                                  .logD;

                                                              'data asaActivitiesMap without changes: $data'.logV;

                                                              log('ref.currentAchievementActivities${ref.achievementActivities}');

                                                              await ref.unhelpfulStrategyAPI(
                                                                ref: ref,
                                                                context: context,
                                                                data: data,
                                                              );
                                                            } else {
                                                              ref.isError.value = true;

                                                              // CustomSnackbar.showErrorSnackBar(
                                                              //   message: AsLocaleKeys.lsUbErrorsMinimumOne.tr(),
                                                              // );
                                                            }
                                                          },
                                                          isBottom: true,
                                                          color: context.themeColors.blueColor,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          },
        )
    );
  }
}
