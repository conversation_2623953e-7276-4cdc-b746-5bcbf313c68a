import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class UnhelpfulBehaviourRepository {
  Future<StrategiesModel?> unhelpfuleBehaviourStrategy({
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      '???????????? data = ${data.toString()}'.logV;
      final response = await APIFunction.postAPICall(
        {'type': 'ubAS', 'data': data},
        apiName: EndPoints.strategy,
        context: context,
      );
      '???????????? response = ${response.toString()}'.logV;
      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          '???????????? response = ${response.toString()}'.logV;
          return StrategiesModel.fromJson(response.data!);
        } else {
          // CustomSnackbar.showErrorSnackBar(
          //   message: data?['message'] as String,
          // );
          return null;
        }
      } else {
        if (response is DioException) {
          // CustomSnackbar.showErrorSnackBar(
          //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          // );
        }
        return null;
      }
    } catch (e) {
      '?|?|?| catach = ${e.toString()}'.logV;
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> ubActionStrategyforDownload({
    required BuildContext context,
    required bool isEmail,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isEmail ? '${EndPoints.ubActionStrategy}?email=true' : EndPoints.ubActionStrategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
