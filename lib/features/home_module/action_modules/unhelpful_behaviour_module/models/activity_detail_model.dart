// class Activity {
//   Activity({
//     required this.name,
//     required this.img,
//     required this.time,
//     required this.index,
//     this.selectedColor,
//   });

//   // Converts JSON to Activity object
//   factory Activity.fromJson(Map<String, dynamic> json) {
//     return Activity(
//       name: json['name'] as String,
//       img: json['img'] as String,
//       time: json['time'] as String,
//       index: json['index'] as int,
//       selectedColor: json['selectedColor'] as String?,
//     );
//   }

//   String name;
//   String img;
//   String time;
//   int index;
//   String? selectedColor;

//   // Converts Activity object to JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'name': name,
//       'img': img,
//       'time': time,
//       'index': index,
//       'selectedColor': selectedColor,
//     };
//   }

//   // Override == to compare Activity objects based on the fields that matter (e.g., name, index, etc.)
//   @override
//   bool operator ==(Object other) {
//     if (identical(this, other)) return true;
//     return other is Activity &&
//         other.name == name &&
//         other.img == img &&
//         other.time == time &&
//         other.index == index &&
//         other.selectedColor == selectedColor;
//   }

//   // Override hashCode to maintain consistency with == operator
//   @override
//   int get hashCode => name.hashCode ^ img.hashCode ^ time.hashCode ^ index.hashCode ^ selectedColor.hashCode;

//   // Override toString for better logging and debugging
//   @override
//   String toString() {
//     return 'Activity(name: $name, img: $img, time: $time, index: $index, selectedColor: $selectedColor)';
//   }
// }
