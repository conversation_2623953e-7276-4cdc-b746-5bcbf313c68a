import 'dart:ui' as ui;
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:flutter/material.dart';

class FlagPainter extends CustomPainter {
  FlagPainter({
    required this.lightGreenFlag,
    required this.darkGreenFlag,
    required this.flagOffsets,
    required this.lineOffsets,
    required this.animation,
  });
  final ui.Image lightGreenFlag;
  final ui.Image darkGreenFlag;
  final List<Offset> flagOffsets;
  final List<List<Offset>> lineOffsets;
  final double animation;

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final paint = Paint()
      ..color = AppColors.mountainLightGreenColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    for (var i = 0; i < lineOffsets.length - 1; i++) {
      canvas.drawLine(
        Offset(
          width * lineOffsets[i].first.dx + (lightGreenFlag.width / 4),
          height * lineOffsets[i].first.dy + (lightGreenFlag.height / 1.3),
        ),
        Offset(
          (width * lineOffsets[i].last.dx) + (lightGreenFlag.width / 4),
          (height * lineOffsets[i].last.dy) + (lightGreenFlag.height / 1.3),
        ),
        paint,
      );
    }
    canvas.drawLine(
      Offset(
        (width * (lineOffsets.last.last.dx + ((lineOffsets.last.first.dx - lineOffsets.last.last.dx) * animation))) +
            (lightGreenFlag.width / 4),
        (height * (lineOffsets.last.last.dy + ((lineOffsets.last.first.dy - lineOffsets.last.last.dy) * animation))) +
            (lightGreenFlag.height / 1.3),
      ),
      Offset(
        (width * lineOffsets.last.last.dx) + (lightGreenFlag.width / 4),
        (height * lineOffsets.last.last.dy) + (lightGreenFlag.height / 1.3),
      ),
      paint,
    );

    for (var i = 0; i < flagOffsets.length; i++) {
      canvas.drawImage(
        i == 0 ? lightGreenFlag : darkGreenFlag,
        Offset(
          (width * flagOffsets[i].dx) - (lightGreenFlag.width / 4),
          (height * flagOffsets[i].dy) - (lightGreenFlag.height / 1.3),
        ),
        Paint(),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
