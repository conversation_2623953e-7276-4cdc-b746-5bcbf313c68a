import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class IdentifyButtonWidget extends StatelessWidget {
  const IdentifyButtonWidget({
    required this.ref,
    required this.currentState,
    required this.questionText,
    required this.errorText,
    super.key,
  });

  final LifestyleCubit ref;
  final ValueNotifier<ButtonState> currentState;
  final String questionText;
  final String errorText;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        QuestionRowWidget(questionText: questionText),
        Padding(
          padding: EdgeInsets.only(
            left: AppSize.w34,
            right: AppSize.w28,
            top: AppSize.h12,
            bottom: AppSize.h20,
          ),
          child: Column(
            children: [
              CustomAssessmentButton(
                currentState: currentState,
                buttonFirstText: DataJsonKeys.buttonYes.tr(),
                buttonSecondText: DataJsonKeys.buttonNo.tr(),
                onNoTap: () {
                  currentState.value = ButtonState.yesEnabled;
                },
                onYesTap: () {
                  currentState.value = ButtonState.noEnabled;
                },
                selectedValue: ref.isButtonClicked.value,
              ),
              ValueListenableBuilder(
                valueListenable: currentState,
                builder: (context, value, child) {
                  return Visibility(
                    visible: currentState.value == ButtonState.yesEnabled,
                    child: Padding(
                      padding: EdgeInsets.only(top: AppSize.h10),
                      child: AppTextWidget(
                        errorText,
                        style: context.textTheme.titleSmall?.copyWith(
                          fontSize: AppSize.sp12,
                          fontWeight: FontWeight.w500,
                          color: context.themeColors.errorRedColor,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
