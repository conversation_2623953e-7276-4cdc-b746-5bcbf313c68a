import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:flutter/material.dart';

class DatePickerWidget extends StatelessWidget {
  const DatePickerWidget({required this.lifestyleCubit, super.key});

  final LifestyleCubit lifestyleCubit;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          splashColor: context.themeColors.scaffoldColor,
          highlightColor: context.themeColors.scaffoldColor,
          onTap: lifestyleCubit.incrementDate, // Increment date on tap
          child: Container(
            height: AppSize.h40,
            width: AppSize.w40,
            decoration: BoxDecoration(
              color: context.themeColors.greenColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.add,
                color: context.themeColors.whiteColor,
              ),
            ),
          ),
        ),
        SpaceV(AppSize.h8),
        CustomOutlinedTextfield(
          textAlign: TextAlign.center,
          readOnly: true,
          isDense: true, controller: lifestyleCubit.selectedDateController, // Link controller
        ),
        SpaceV(AppSize.h8),
        InkWell(
          splashColor: context.themeColors.scaffoldColor,
          highlightColor: context.themeColors.scaffoldColor,
          onTap: lifestyleCubit.decrementDate, // Decrement date on tap
          child: Container(
            height: AppSize.h40,
            width: AppSize.w40,
            decoration: BoxDecoration(
              color: context.themeColors.greenColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.remove,
                color: context.themeColors.whiteColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
