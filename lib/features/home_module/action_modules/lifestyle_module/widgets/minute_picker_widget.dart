import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/cubit/unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class MinutePickerWidget extends StatelessWidget {
  const MinutePickerWidget({
    this.lifestyleCubit,this.isError,
    super.key,
    this.isLifestyleCubit = true,
    this.unhelpfulBehaviourCubit,
  });
  final bool? isError;

  final LifestyleCubit? lifestyleCubit;
  final bool isLifestyleCubit;
  final UnhelpfulBehaviourCubit? unhelpfulBehaviourCubit;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          splashColor: context.themeColors.scaffoldColor,
          highlightColor: context.themeColors.scaffoldColor,
          onTap: isLifestyleCubit ? lifestyleCubit?.incrementMinute : unhelpfulBehaviourCubit?.incrementMinute,
          child: Container(
            height: AppSize.h40,
            width: AppSize.w40,
            decoration: BoxDecoration(
              color: context.themeColors.greenColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.add,
                color: context.themeColors.whiteColor,
              ),
            ),
          ),
        ),
        SpaceV(AppSize.h8),
        CustomOutlinedTextfield(
          textAlign: TextAlign.center,
          isDense: true, keyboardType: TextInputType.number,
          readOnly: true,
          isError: isError,
          controller: isLifestyleCubit
              ? lifestyleCubit?.selectedMinuteController
              : unhelpfulBehaviourCubit?.selectedMinuteController, // Link controller
        ),
        SpaceV(AppSize.h8),
        InkWell(
          splashColor: context.themeColors.scaffoldColor,
          highlightColor: context.themeColors.scaffoldColor,
          onTap: isLifestyleCubit ? lifestyleCubit?.decrementMinute : unhelpfulBehaviourCubit?.decrementMinute,
          child: Container(
            height: AppSize.h40,
            width: AppSize.w40,
            decoration: BoxDecoration(
              color: context.themeColors.greenColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.remove,
                color: context.themeColors.whiteColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
