import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:flutter/material.dart';

class MountainOptionWidget extends StatelessWidget {
  const MountainOptionWidget({
    required this.index,
    required this.imageUrl,
    required this.label,
    required this.ref,
    super.key,
    this.backgroundColor,
  });
  final int index;
  final String imageUrl;
  final String label;
  final LifestyleCubit ref;
  final Color? backgroundColor; // New parameter for background color

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ref.selectedMountainIndex,
      builder: (context, value, child) {
        return GestureDetector(
          onTap: () => ref.setSelectedMountain(index),
          child: Container(
            // padding: EdgeInsets.only(left: AppSize.w20),
            color: ref.selectedMountainIndex.value == -1
                ? Colors.transparent
                : ref.selectedMountainIndex.value == index
                    ? AppColors.mountainDarkGreenColor
                    : AppColors.mountainLightGreenColor,
            padding: EdgeInsets.symmetric(
              vertical: AppSize.h7,
              horizontal: AppSize.w7,
            ),
            //color: backgroundColor ?? Colors.red,
            child: Stack(
              children: [
                AppCachedNetworkImage(imageUrl: imageUrl),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: AppSize.h30,
                    color: const Color.fromRGBO(120, 144, 156, 0.65),
                    child: Center(
                      child: AppTextWidget(
                        label,
                        style: context.textTheme.titleSmall?.copyWith(
                          color: context.themeColors.whiteColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
