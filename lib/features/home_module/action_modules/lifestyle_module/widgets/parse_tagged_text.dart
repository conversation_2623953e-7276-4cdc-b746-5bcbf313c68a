import 'package:flutter/material.dart';

TextSpan parseTaggedText({
  required String text,
  required TextStyle? baseStyle,
}) {
  final regex = RegExp(r'<strong>(.*?)<\/strong>', caseSensitive: false);
  final spans = <TextSpan>[];
  int currentIndex = 0;

  for (final match in regex.allMatches(text)) {
    if (match.start > currentIndex) {
      spans.add(TextSpan(
        text: text.substring(currentIndex, match.start),
        style: baseStyle,
      ));
    }

    spans.add(TextSpan(
      text: match.group(1),
      style: baseStyle?.copyWith(fontWeight: FontWeight.bold),
    ));

    currentIndex = match.end;
  }

  if (currentIndex < text.length) {
    spans.add(TextSpan(
      text: text.substring(currentIndex),
      style: baseStyle,
    ));
  }

  return TextSpan(children: spans);
}
