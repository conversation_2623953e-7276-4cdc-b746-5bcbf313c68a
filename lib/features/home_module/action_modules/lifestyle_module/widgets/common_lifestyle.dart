import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/utils/assets_path.dart';
import 'package:easy_localization/easy_localization.dart';

class MyLifeStyle {
  static String getMountainImage(int index) {
    switch (index) {
      case 0:
        return AssetsPath.everest;
      case 1:
        return AssetsPath.kilimanjaro;
      case 2:
        return AssetsPath.machuPichu;
      case 3:
        return AssetsPath.matheran;
      default:
        return '';
    }
  }

  static String getRelaxingSceneImage(int index) {
    switch (index) {
      case 0:
        return AsLocaleKeys.eiVideoParkPreview.tr();
      case 1:
        return AsLocaleKeys.eiVideoBeachPreview.tr();
      default:
        return '';
    }
  }
}
