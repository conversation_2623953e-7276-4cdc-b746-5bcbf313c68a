import 'dart:ui';

class FlagOffset {
  static List<Offset> getEverestFlagsByIndex(int index, int selectedMountainIndex)  {
    return switch (selectedMountainIndex) {
      0 => [
          if (index >= 0) ...[
            const Offset(0.40, 0.99),
            const Offset(0.55, 0.74),
          ],
          if (index >= 1) ...[
            const Offset(0.40, 0.495),
          ],
          if (index >= 2) ...[
            const Offset(0.59, 0.36),
          ],
          if (index >= 3) ...[
            const Offset(0.45, 0.25),
          ],
        ],
      1 => [
          if (index >= 0) ...[
            const Offset(0.45, 0.99),
            const Offset(0.33, 0.72),
          ],
          if (index >= 1) ...[
            const Offset(0.50, 0.555),
          ],
          if (index >= 2) ...[
            const Offset(0.65, 0.40),
          ],
          if (index >= 3) ...[
            const Offset(0.50, 0.25),
          ],
        ],
      2 => [
          if (index >= 0) ...[
            const Offset(0.17, 0.74),
            const Offset(0.25, 0.65),
          ],
          if (index >= 1) ...[
            const Offset(0.40, 0.51),
          ],
          if (index >= 2) ...[
            const Offset(0.36, 0.35),
          ],
          if (index >= 3) ...[
            const Offset(0.42, 0.22),
          ],
        ],
      3 => [
          if (index >= 0) ...[
            const Offset(0.30, 0.99),
            const Offset(0.50, 0.80),
          ],
          if (index >= 1) ...[
            const Offset(0.42, 0.53),
          ],
          if (index >= 2) ...[
            const Offset(0.60, 0.43),
          ],
          if (index >= 3) ...[
            const Offset(0.50, 0.28),
          ],
        ],
      _ => [],
    };
  }

  static  List<List<Offset>> getEverestLineByIndex(int index,int selectedMountainIndex) {
      return switch (selectedMountainIndex) {
        0 => [
            if (index >= 0) ...[
              [
                const Offset(0.52, 0.61),
                const Offset(0.378, 0.843),
              ],
            ],
            if (index >= 1) ...[
              [
                const Offset(0.378, 0.348),
                const Offset(0.54, 0.61),
              ],
            ],
            if (index >= 2) ...[
              [
                const Offset(0.56, 0.22),
                const Offset(0.378, 0.348),
              ],
            ],
            if (index >= 3) ...[
              [
                const Offset(0.42, 0.09),
                const Offset(0.56, 0.22),
              ]
            ],
          ],
        1 => [
            if (index >= 0) ...[
              [
                const Offset(0.31, 0.58),
                const Offset(0.42, 0.83),
              ],
            ],
            if (index >= 1) ...[
              [
                const Offset(0.48, 0.41),
                const Offset(0.30, 0.57),
              ],
            ],
            if (index >= 2) ...[
              [
                const Offset(0.62, 0.26),
                const Offset(0.48, 0.40),
              ],
            ],
            if (index >= 3) ...[
              [
                const Offset(0.46, 0.09),
                const Offset(0.63, 0.25),
              ]
            ],
          ],
        2 => [
            if (index >= 0) ...[
              [
                const Offset(0.22, 0.51),
                const Offset(0.15, 0.60),
              ],
            ],
            if (index >= 1) ...[
              [
                const Offset(0.378, 0.368),
                const Offset(0.23, 0.50),
              ],
            ],
            if (index >= 2) ...[
              [
                const Offset(0.34, 0.21),
                const Offset(0.378, 0.368),
              ],
            ],
            if (index >= 3) ...[
              [
                const Offset(0.40, 0.08),
                const Offset(0.33, 0.22),
              ]
            ],
          ],
        3 => [
            if (index >= 0) ...[
              [
                const Offset(0.48, 0.65),
                const Offset(0.29, 0.84),
              ],
            ],
            if (index >= 1) ...[
              [
                const Offset(0.395, 0.37),
                const Offset(0.47, 0.63),
              ],
            ],
            if (index >= 2) ...[
              [
                const Offset(0.58, 0.27),
                const Offset(0.39, 0.38),
              ],
            ],
            if (index >= 3) ...[
              [
                const Offset(0.47, 0.13),
                const Offset(0.57, 0.275),
              ]
            ],
          ],
        _ => [],
      };
    }
  
}
