import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class LifestyleRepository {
  Future<StrategiesModel?> lifestyleStrategy({
    required String mountainName,
    required String stepOnelifeGoal,
    required String stepOnenextStep,
    required String stepTwoBarrier,
    required String stepThreeDate,
    required String typedBarrier,
    required String whyText,
    required String whatText,
    required String whoText,
    required String otherBenefitText,
    required List<int> benefit,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'type': 'lsAS',
          'data': {
            'mountain': {'name': mountainName},
            'stepOne': {'lifeGoal': stepOnelifeGoal, 'nextStep': stepOnenextStep},
            'stepTwo': {'barrier': stepTwoBarrier, if (typedBarrier.isNotEmpty) 'typedBarrier': typedBarrier},
            'stepThree': {
              'date': stepThreeDate,
              if (whyText.isNotEmpty && whatText.isNotEmpty && whoText.isNotEmpty)
                'otherBarrier': {'why': whyText, 'what': whatText, 'who': whoText},
            },
            'stepFour': {
              'benefits': benefit,
              if (otherBenefitText.isNotEmpty) 'otherBenefit': otherBenefitText,
            },
          },
        },
        apiName: EndPoints.strategy,
        context: context,
      );
      '///// response $response'.logV;
      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          'response.data ${response.data}'.logD;
          return StrategiesModel.fromJson(response.data!);
        } else {
          '///// message ${data?['message']}'.logV;
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          '///// message ${response.response?.data?['message']}'.logV;
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> lsActionStrategyforDownload({
    required BuildContext context,
    required bool isEmail,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isEmail ? '${EndPoints.lsActionStrategy}?email=true' : EndPoints.lsActionStrategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
