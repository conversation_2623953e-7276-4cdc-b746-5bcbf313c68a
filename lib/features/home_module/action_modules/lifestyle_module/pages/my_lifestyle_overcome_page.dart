import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestye_benefits_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/date_picker_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/hour_picker_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/minute_picker_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestyleOvercomePage extends StatefulWidget {
  const MyLifestyleOvercomePage({required this.imageUrl,super.key});
  final String imageUrl;

  @override
  State<MyLifestyleOvercomePage> createState() => _MyLifestyleOvercomePageState();
}

class _MyLifestyleOvercomePageState extends State<MyLifestyleOvercomePage> with TickerProviderStateMixin {
  late AnimationController animationController;
  late AnimationController _controller;

  @override
  void initState() {
    animationController = AnimationController(duration: const Duration(seconds: 1), vsync: this);
    _controller = AnimationController(duration: const Duration(minutes: 8), vsync: this, value: .90)..repeat();
    animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();

        return ValueListenableBuilder(
          valueListenable: ref.infoOvercomeAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.headerInfoText.value = null;
                  ref.infoBarrierAudioUrl.value = AsLocaleKeys.lsIdentifyBarrierAudio.tr();
                  if (ref.whyQuestionController.text.isNotEmpty ||
                      ref.whatQuestionController.text.isNotEmpty ||
                      ref.whoQuestionController.text.isNotEmpty) {
                    ref.whyQuestionController.text = '';
                    ref.whatQuestionController.text = '';
                    ref.whoQuestionController.text = '';
                  }
                }
              },
              child: AppScaffold(
                // resizeToAvoidBottomInset: false,
                isManuallyPaused: ref.isOverComeAudioPaused,
                scaffoldKey: ref.scaffoldOverComeKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ValueNotifier(ref.infoOvercomeAudioUrl.value),
                drawer: AppDrawer(scaffoldKey: ref.scaffoldOverComeKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldOverComeKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoOvercomeAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20,
                                        top: AppSize.h20,
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  InformationPageHeadingWidget(
                                                    onBackArrowTap: () {
                                                      ref.headerInfoText.value = null;

                                                      ref.infoBarrierAudioUrl.value =
                                                          AsLocaleKeys.lsIdentifyBarrierAudio.tr();
                                                      if (ref.whyQuestionController.text.isNotEmpty ||
                                                          ref.whatQuestionController.text.isNotEmpty ||
                                                          ref.whoQuestionController.text.isNotEmpty) {
                                                        ref.whyQuestionController.text = '';
                                                        ref.whatQuestionController.text = '';
                                                        ref.whoQuestionController.text = '';
                                                      }
                                                      Navigator.pop(context);
                                                    },
                                                    title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                    subtitle: AsLocaleKeys.lsTitle.tr(),
                                                    icon: Assets.icons.actionIcons.lifestyle,
                                                    onInfoTap: () {
                                                      // if (ref.infoOvercomeAudioUrl.value ==
                                                      //     AsLocaleKeys.lsInfoPanelsInformationAudio.tr()) {
                                                      //   ref.infoOvercomeAudioUrl.value = ref.selectBarrierValue.value ==
                                                      //           ref.barrierList.last
                                                      //       ? 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3'
                                                      //       : ref.getBarrierData(ref.selectBarrierValue.value).audio;
                                                      // } else {
                                                      //   ref.infoOvercomeAudioUrl.value =
                                                      //       AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                      // }

                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.lsInfoPanelsInformationText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');
                                                      // ref.headerInfoText.value =
                                                      //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      //             ref.headerInfoText.value == info)
                                                      //         ? null
                                                      //         : info;
                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isOverComeAudioPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoOvercomeAudioUrl.value = ref.selectBarrierValue.value ==
                                                                ref.barrierList.last
                                                            ? 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3'
                                                            : ref.getBarrierData(ref.selectBarrierValue.value).audio;
                                                      } else {
                                                        ref.isOverComeAudioPaused.value = false;
                                                        ref.infoOvercomeAudioUrl.value =
                                                            AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                        ref.headerInfoText.value = info;
                                                      }
                                                    },
                                                    onLearnTap: () {
                                                      // if (ref.infoOvercomeAudioUrl.value ==
                                                      //     AsLocaleKeys.lsInfoPanelsLearnAudio.tr()) {
                                                      //   ref.infoOvercomeAudioUrl.value = ref.selectBarrierValue.value ==
                                                      //           ref.barrierList.last
                                                      //       ? 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3'
                                                      //       : ref.getBarrierData(ref.selectBarrierValue.value).audio;
                                                      // } else {
                                                      //   ref.infoOvercomeAudioUrl.value =
                                                      //       AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                      // }

                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.lsInfoPanelsLearnText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');
                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isOverComeAudioPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoOvercomeAudioUrl.value = ref.selectBarrierValue.value ==
                                                                ref.barrierList.last
                                                            ? 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3'
                                                            : ref.getBarrierData(ref.selectBarrierValue.value).audio;
                                                      } else {
                                                        ref.isOverComeAudioPaused.value = false;
                                                        ref.infoOvercomeAudioUrl.value =
                                                            AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                        ref.headerInfoText.value = info;
                                                      }
                                                      // final audio = AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                      // ref.infoOvercomeAudioUrl.value =
                                                      //     ref.infoOvercomeAudioUrl.value == audio ? null : audio;
                                                    },
                                                    infoWidget: ValueListenableBuilder(
                                                      valueListenable: ref.headerInfoText,
                                                      builder: (context, headerPlanInfoTextV, _) {
                                                        return CustomInfoWidget(
                                                          customWidget: Column(
                                                            children: [
                                                              Html(
                                                                data: ref.headerInfoText.value ?? '',
                                                                style: {
                                                                  'strong': Style(
                                                                    fontSize: FontSize(AppSize.sp13),
                                                                    color: context.themeColors.darkOrangeColor,
                                                                    fontWeight: FontWeight.bold,
                                                                    fontFamily: 'Poppins',
                                                                  ),
                                                                  'body': Style(
                                                                    fontSize: FontSize(AppSize.sp13),
                                                                    color: context.themeColors.darkOrangeColor,
                                                                    fontFamily: 'Poppins',
                                                                  ),
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                                          onCloseTap: () {
                                                            ref.isOverComeAudioPaused.value = true;
                                                            ref.headerInfoText.value = null;
                                                            ref.infoOvercomeAudioUrl.value = ref
                                                                        .selectBarrierValue.value ==
                                                                    ref.barrierList.last
                                                                ? 'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3'
                                                                : ref
                                                                    .getBarrierData(ref.selectBarrierValue.value)
                                                                    .audio;
                                                          },
                                                          visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                          margin: EdgeInsets.symmetric(
                                                            vertical: AppSize.h8,
                                                          ),
                                                          bodyText: headerPlanInfoTextV,
                                                        );
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h6),
                                                  Container(
                                                    clipBehavior: Clip.hardEdge,
                                                    decoration: BoxDecoration(
                                                      color: const Color.fromRGBO(200, 208, 232, 1),
                                                      borderRadius: BorderRadius.circular(AppSize.r14),
                                                    ),
                                                    child: Stack(
                                                      children: [
                                                        Positioned(
                                                          top: -10,
                                                          left: 0,
                                                          right: 0,
                                                          bottom: -AppSize.h120,
                                                          child: AnimatedBuilder(
                                                            animation: _controller,
                                                            builder: (context, child) {
                                                              return RotationTransition(
                                                                turns: _controller,
                                                                child: Align(
                                                                  alignment: Alignment.topCenter,
                                                                  child: child,
                                                                ),
                                                              );
                                                            },
                                                            child: Assets.icons.animAsset.sun.image(
                                                              height: AppSize.h40,
                                                              width: AppSize.h40,
                                                            ),
                                                          ),
                                                        ),
                                                        Positioned(
                                                          top: -10,
                                                          left: 0,
                                                          right: 0,
                                                          bottom: -AppSize.h120,
                                                          child: AnimatedBuilder(
                                                            animation: _controller,
                                                            builder: (context, child) {
                                                              return RotationTransition(
                                                                turns: AlwaysStoppedAnimation(
                                                                  _controller.value - 0.5,
                                                                ),
                                                                child: Align(
                                                                  alignment: Alignment.topCenter,
                                                                  child: child,
                                                                ),
                                                              );
                                                            },
                                                            child: Assets.icons.animAsset.moon.image(
                                                              height: AppSize.h40,
                                                              width: AppSize.h40,
                                                            ),
                                                          ),
                                                        ),
                                                        Image.network(widget.imageUrl),
                                                        // AppCachedNetworkImage(
                                                        //   imageUrl: MyLifeStyle.getMountainImage(
                                                        //     ref.selectedMountainIndex.value,
                                                        //   ),
                                                        // ),
                                                        FutureBuilder(
                                                          future: Future.wait(
                                                            [ref.lightGreenFlag, ref.darkGreenFlag],
                                                          ),
                                                          builder: (context, snapshot) {
                                                            if (snapshot.hasData) {
                                                              return SizedBox(
                                                                height: AppSize.h130,
                                                                width: double.maxFinite,
                                                                child: AnimatedBuilder(
                                                                  animation: animationController,
                                                                  builder: (context, child) {
                                                                    return CustomPaint(
                                                                      painter: FlagPainter(
                                                                        darkGreenFlag: snapshot.data!.last,
                                                                        lightGreenFlag: snapshot.data!.first,
                                                                        flagOffsets: FlagOffset.getEverestFlagsByIndex(
                                                                          2,
                                                                          ref.selectedMountainIndex.value,
                                                                        ),
                                                                        lineOffsets: FlagOffset.getEverestLineByIndex(
                                                                          2,
                                                                          ref.selectedMountainIndex.value,
                                                                        ),
                                                                        animation: animationController.value,
                                                                      ),
                                                                    );
                                                                  },
                                                                ),
                                                              );
                                                            } else {
                                                              return SizedBox(
                                                                height: AppSize.h100,
                                                              );
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h16),
                                                  Center(
                                                    child: AppTextWidget(
                                                      textAlign: TextAlign.center,
                                                      AsLocaleKeys.lsOvercomeBarrierTitle.tr(),
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      if (ref.selectBarrierValue.value != ref.barrierList.last) ...[
                                                        AppTextWidget(
                                                          AsLocaleKeys.lsOvercomeBarrierCanSay.tr(),
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        AppTextWidget(
                                                          ref
                                                              .getBarrierData(ref.selectBarrierValue.value)
                                                              .canSay
                                                              .join('\n\n'),
                                                          style: context.textTheme.titleSmall,
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        AppTextWidget(
                                                          AsLocaleKeys.lsOvercomeBarrierCanDo.tr(),
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontSize: AppSize.sp13,
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        AppTextWidget(
                                                          ref
                                                              .getBarrierData(ref.selectBarrierValue.value)
                                                              .canDo
                                                              .join('\n\n'),
                                                          style: context.textTheme.titleSmall,
                                                        ),
                                                      ] else ...[
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.isButtonClicked,
                                                          builder: (context, isButtonClicked, child) {
                                                            return CommonoverbarrierQuestionWidget(
                                                              key: ref.whyKey,
                                                              titleText: AsLocaleKeys.lsQuestionsWhyTitle.tr(),
                                                              titleDetailText: AsLocaleKeys.lsQuestionsWhyText.tr(),
                                                              controller: ref.whyQuestionController,
                                                              isError: ref.isButtonClicked.value &&
                                                                  ref.whyQuestionController.text.trim().isEmpty,
                                                              onChanged: (_) {
                                                                ref.isButtonClicked.notifyListeners();
                                                              },
                                                            );
                                                          },
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.isButtonClicked,
                                                          builder: (context, isButtonClicked, child) {
                                                            return CommonoverbarrierQuestionWidget(
                                                              key: ref.whatKey,
                                                              titleText: AsLocaleKeys.lsQuestionsWhatTitle.tr(),
                                                              titleDetailText: AsLocaleKeys.lsQuestionsWhatText.tr(),
                                                              controller: ref.whatQuestionController,
                                                              isError: ref.isButtonClicked.value &&
                                                                  ref.whatQuestionController.text.trim().isEmpty,
                                                              onChanged: (_) {
                                                                ref.isButtonClicked.notifyListeners();
                                                              },
                                                            );
                                                          },
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.isButtonClicked,
                                                          builder: (context, isButtonClicked, child) {
                                                            return CommonoverbarrierQuestionWidget(
                                                              key: ref.whoKey,
                                                              titleText: AsLocaleKeys.lsQuestionsWhoTitle.tr(),
                                                              titleDetailText: AsLocaleKeys.lsQuestionsWhoText.tr(),
                                                              controller: ref.whoQuestionController,
                                                              isError: ref.isButtonClicked.value &&
                                                                  ref.whoQuestionController.text.trim().isEmpty,
                                                              onChanged: (_) {
                                                                ref.isButtonClicked.notifyListeners();
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ],
                                                      SpaceV(AppSize.h14),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsOvercomeBarrierCommit.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h14),
                                                      AppTextWidget(
                                                        (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsOvercomeBarrierCommitText,
                                                          context,
                                                        ) as List)
                                                            .join('\n\n'),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h24),
                                                      ValueListenableBuilder(
                                                        valueListenable: ref.isButtonClicked,
                                                        builder: (context, value, child) {
                                                          return ValueListenableBuilder(
                                                            valueListenable: ref.isError,
                                                            builder: (context, value, child) {
                                                              return Column(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  AppTextWidget(
                                                                    AsLocaleKeys.selectDateAndTime.tr(),
                                                                    style: context.textTheme.titleSmall?.copyWith(
                                                                      fontWeight: FontWeight.w600,
                                                                    ),
                                                                  ),
                                                                  Row(
                                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                                    children: [
                                                                      Expanded(
                                                                        child: Column(
                                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                                          children: [
                                                                            SpaceV(AppSize.h10),
                                                                            Padding(
                                                                              padding: EdgeInsets.only(
                                                                                right: AppSize.w40,
                                                                              ),
                                                                              child: DatePickerWidget(
                                                                                lifestyleCubit: ref,
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                      Expanded(
                                                                        child: Column(
                                                                          children: [
                                                                            SpaceV(AppSize.h10),
                                                                            Row(
                                                                              children: [
                                                                                Expanded(
                                                                                  child: HourPickerWidget(
                                                                                    lifestyleCubit: ref,
                                                                                  ),
                                                                                ),
                                                                                SpaceH(AppSize.w4),
                                                                                AppTextWidget(
                                                                                  ':',
                                                                                  style: context.textTheme.titleSmall
                                                                                      ?.copyWith(
                                                                                    fontWeight: FontWeight.w600,
                                                                                  ),
                                                                                ),
                                                                                SpaceH(AppSize.w4),
                                                                                Expanded(
                                                                                  child: MinutePickerWidget(
                                                                                    lifestyleCubit: ref,
                                                                                  ),
                                                                                ),
                                                                                SpaceH(AppSize.w20),
                                                                              ],
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  if (!DateTime(
                                                                        ref.selectedDate.year,
                                                                        ref.selectedDate.month,
                                                                        ref.selectedDate.day,
                                                                        ref.selectedHour,
                                                                        ref.selectedMinute,
                                                                      ).isAfter(
                                                                        DateTime.now(),
                                                                      ) &&
                                                                      ref.isButtonClicked.value)
                                                                    CustomErrorWidget(
                                                                      errorMessgaeText:
                                                                          AsLocaleKeys.lsErrorsFuture.tr(),
                                                                    )
                                                                  else
                                                                    const SizedBox(),
                                                                ],
                                                              );
                                                            },
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              SpaceV(AppSize.h12),
                                              CustomButton(
                                                padding: EdgeInsets.zero,
                                                title: CoreLocaleKeys.buttonsNext.tr(),
                                                onTap: () async {
                                                  if (ref.selectBarrierValue.value == ref.barrierList.last) {
                                                    if (ref.whyQuestionController.text.isEmpty ||
                                                        ref.whatQuestionController.text.isEmpty ||
                                                        ref.whoQuestionController.text.isEmpty) {
                                                      ref.isButtonClicked.value = true;
                                                      if (ref.whyQuestionController.text.trim().isEmpty) {
                                                        await AppCommonFunctions.scrollToKey(ref.whyKey);
                                                        return;
                                                      }
                                                      if (ref.whatQuestionController.text.trim().isEmpty) {
                                                        await AppCommonFunctions.scrollToKey(ref.whatKey);
                                                        return;
                                                      }
                                                      if (ref.whoQuestionController.text.trim().isEmpty) {
                                                        await AppCommonFunctions.scrollToKey(ref.whoKey);
                                                        return;
                                                      }
                                                      // CustomSnackbar.showErrorSnackBar(
                                                      //   message: AsLocaleKeys.lsErrorsRequired.tr(),
                                                      // );
                                                    }
                                                  }
                                                  if (DateTime(
                                                    ref.selectedDate.year,
                                                    ref.selectedDate.month,
                                                    ref.selectedDate.day,
                                                    ref.selectedHour,
                                                    ref.selectedMinute,
                                                  ).isAfter(DateTime.now())) {
                                                    ref.isButtonClicked.value = false;
                                                    ref.isError.value = false;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoOvercomeAudioUrl.value = null;
                                                    ref.isBenefitAudioPaused.value = false;
                                                    ref.infoBenefitAudioUrl.value = AsLocaleKeys.lsMotivateAudio.tr();

                                                    await AppNavigation.nextScreen(
                                                      context,
                                                      BlocProvider.value(
                                                        value: ref,
                                                        child: MyLifestyeBenefitsPage(imageUrl: widget.imageUrl,),
                                                      ),
                                                    );
                                                  } else {
                                                    ref.isButtonClicked.value = true;
                                                    ref.isError.value = true;

                                                    //  CustomSnackbar.showErrorSnackBar(message: AsLocaleKeys.lsErrorsFuture.tr());
                                                  }
                                                },
                                                isBottom: true,
                                                color: context.themeColors.blueColor,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class CommonoverbarrierQuestionWidget extends StatelessWidget {
  const CommonoverbarrierQuestionWidget({
    required this.titleText,
    required this.titleDetailText,
    required this.controller,
    required this.isError,
    super.key,
    this.onChanged,
  });
  final String titleText;
  final String titleDetailText;
  final TextEditingController controller;
  final bool isError;
  final void Function(String?)? onChanged;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTextWidget(
          titleText,
          style: context.textTheme.titleSmall?.copyWith(
            fontSize: AppSize.sp13,
            fontWeight: FontWeight.w600,
          ),
        ),
        SpaceV(AppSize.h10),
        AppTextWidget(
          titleDetailText,
          style: context.textTheme.titleSmall?.copyWith(
            fontSize: AppSize.sp13,
          ),
        ),
        SpaceV(AppSize.h12),
        CustomOutlinedTextfield(
          hintText: CoreLocaleKeys.labelsTextPlaceholder.tr(),
          controller: controller,
          isError: isError,
          onChanged: onChanged,
        ),
        Visibility(
          visible: isError,
          child: CustomErrorWidget(errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr()),
        ),
      ],
    );
  }
}
