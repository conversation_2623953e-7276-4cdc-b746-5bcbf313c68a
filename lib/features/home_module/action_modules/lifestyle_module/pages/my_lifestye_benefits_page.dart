import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestyeBenefitsPage extends StatefulWidget {
  const MyLifestyeBenefitsPage({required this.imageUrl,super.key});
  final String imageUrl; 

  @override
  State<MyLifestyeBenefitsPage> createState() => _MyLifestyeBenefitsPageState();
}

class _MyLifestyeBenefitsPageState extends State<MyLifestyeBenefitsPage> with TickerProviderStateMixin {
  late AnimationController animationController;
  late AnimationController _controller;

  @override
  void initState() {
    animationController = AnimationController(duration: const Duration(seconds: 1), vsync: this);
    _controller = AnimationController(duration: const Duration(minutes: 8), vsync: this, value: .90)..repeat();
    animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoBenefitAudioUrl,
          builder: (context, value, child) {
            return AbsorbPointer(
              absorbing: state.maybeWhen(orElse: () => false, loading: () => true),
              child: PopScope(
                onPopInvokedWithResult: (didPop, result) {
                  if (didPop) {
                    if (ref.selectBarrierValue.value == ref.barrierList.last) {
                      ref.infoOvercomeAudioUrl.value =
                          'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3';
                    } else {
                      ref.infoOvercomeAudioUrl.value = ref.getBarrierData(ref.selectBarrierValue.value).audio;
                    }
                    ref.headerInfoText.value = null;
                    ref.selectedBenefitValue.value = List.generate(ref.beenfitList.length, (index) => false);
                    ref.isBarrierAudioUrlPaused.value = false;
                  }
                },
                child: AppScaffold(
                  // resizeToAvoidBottomInset: false,
                  scaffoldKey: ref.scaffoldBenefitKey,
                  isAudioPanelVisible: ref.isAudioPanelVisible,
                  infoAudioUrl: ref.infoBenefitAudioUrl,
                  isManuallyPaused: ref.isBenefitAudioPaused,
                  drawer: AppDrawer(scaffoldKey: ref.scaffoldBenefitKey),
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      ref.scaffoldBenefitKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoBenefitAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                      }
                    },
                  ),
                  body: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(right: AppSize.w4),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return CustomRawScrollbar(
                                  child: SingleChildScrollView(
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w24,
                                          right: AppSize.w24,
                                          bottom: AppSize.h20,
                                          top: AppSize.h20,
                                        ),
                                        child: ValueListenableBuilder(
                                          valueListenable: ref.isButtonClicked,
                                          builder: (context, value, child) {
                                            return ValueListenableBuilder(
                                              valueListenable: ref.selectedBenefitValue,
                                              builder: (context, value, child) {
                                                return Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        InformationPageHeadingWidget(
                                                          onBackArrowTap: () {
                                                            ref.headerInfoText.value = null;
                                                            ref.isOverComeAudioPaused.value = false;

                                                            if (ref.selectBarrierValue.value == ref.barrierList.last) {
                                                              ref.infoOvercomeAudioUrl.value =
                                                                  'https://d24v3ngjgcwbka.cloudfront.net/audio/Li.as.4.other.bfo.uk.f.en.mp3';
                                                            } else {
                                                              ref.infoOvercomeAudioUrl.value = ref
                                                                  .getBarrierData(ref.selectBarrierValue.value)
                                                                  .audio;
                                                            }
                                                            ref.headerInfoText.value = null;
                                                            ref.selectedBenefitValue.value =
                                                                List.generate(ref.beenfitList.length, (index) => false);
                                                            Navigator.pop(context);
                                                          },
                                                          title:
                                                              CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                          subtitle: AsLocaleKeys.lsTitle.tr(),
                                                          icon: Assets.icons.actionIcons.lifestyle,
                                                          onInfoTap: () {
                                                            // if (ref.infoBenefitAudioUrl.value ==
                                                            //     AsLocaleKeys.lsInfoPanelsInformationAudio.tr()) {
                                                            //   ref.infoBenefitAudioUrl.value =
                                                            //       AsLocaleKeys.lsMotivateAudio.tr();
                                                            // } else {
                                                            //   ref.infoBenefitAudioUrl.value =
                                                            //       AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                            // }

                                                            final info = (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.lsInfoPanelsInformationText,
                                                              context,
                                                            ) as List)
                                                                .join('<br/><br/>');

                                                            if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                ref.headerInfoText.value == info) {
                                                              ref.isBenefitAudioPaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoBenefitAudioUrl.value =
                                                                  AsLocaleKeys.lsMotivateAudio.tr();
                                                            } else {
                                                              ref.isBenefitAudioPaused.value = false;
                                                              ref.infoBenefitAudioUrl.value =
                                                                  AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                              ref.headerInfoText.value = info;
                                                            }
                                                          },
                                                          onLearnTap: () {
                                                            'on Learn Tap'.logV;
                                                            final info = (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.lsInfoPanelsLearnText,
                                                              context,
                                                            ) as List)
                                                                .join('<br/><br/>');
                                                            if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                ref.headerInfoText.value == info) {
                                                              ref.isBenefitAudioPaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoBenefitAudioUrl.value =
                                                                  AsLocaleKeys.lsMotivateAudio.tr();
                                                            } else {
                                                              ref.isBenefitAudioPaused.value = false;
                                                              ref.infoBenefitAudioUrl.value =
                                                                  AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                              ref.headerInfoText.value = info;
                                                            }
                                                          },
                                                          infoWidget: ValueListenableBuilder(
                                                            valueListenable: ref.headerInfoText,
                                                            builder: (context, headerPlanInfoTextV, _) {
                                                              return CustomInfoWidget(
                                                                customWidget: Column(
                                                                  children: [
                                                                    Html(
                                                                      data: ref.headerInfoText.value ?? '',
                                                                      style: {
                                                                        'strong': Style(
                                                                          fontSize: FontSize(AppSize.sp13),
                                                                          color: context.themeColors.darkOrangeColor,
                                                                          fontWeight: FontWeight.bold,
                                                                          fontFamily: 'Poppins',
                                                                        ),
                                                                        'body': Style(
                                                                          fontSize: FontSize(AppSize.sp13),
                                                                          color: context.themeColors.darkOrangeColor,
                                                                          fontFamily: 'Poppins',
                                                                        ),
                                                                      },
                                                                    ),
                                                                  ],
                                                                ),
                                                                onCloseTap: () {
                                                                  ref.isBenefitAudioPaused.value = true;
                                                                  ref.headerInfoText.value = null;
                                                                  ref.infoBenefitAudioUrl.value =
                                                                      AsLocaleKeys.lsMotivateAudio.tr();
                                                                  //  ref.infoBenefitAudioUrl.value = null;
                                                                },
                                                                visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                                margin: EdgeInsets.symmetric(
                                                                  vertical: AppSize.h8,
                                                                ),
                                                                bodyText: headerPlanInfoTextV,
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h6),
                                                        Container(
                                                          clipBehavior: Clip.hardEdge,
                                                          decoration: BoxDecoration(
                                                            color: const Color.fromRGBO(200, 208, 232, 1),
                                                            borderRadius: BorderRadius.circular(AppSize.r14),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Positioned(
                                                                top: -10,
                                                                left: 0,
                                                                right: 0,
                                                                bottom: -AppSize.h120,
                                                                child: AnimatedBuilder(
                                                                  animation: _controller,
                                                                  builder: (context, child) {
                                                                    return RotationTransition(
                                                                      turns: _controller,
                                                                      child: Align(
                                                                        alignment: Alignment.topCenter,
                                                                        child: child,
                                                                      ),
                                                                    );
                                                                  },
                                                                  child: Assets.icons.animAsset.sun.image(
                                                                    height: AppSize.h40,
                                                                    width: AppSize.h40,
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                top: -10,
                                                                left: 0,
                                                                right: 0,
                                                                bottom: -AppSize.h120,
                                                                child: AnimatedBuilder(
                                                                  animation: _controller,
                                                                  builder: (context, child) {
                                                                    return RotationTransition(
                                                                      turns: AlwaysStoppedAnimation(
                                                                        _controller.value - 0.5,
                                                                      ),
                                                                      child: Align(
                                                                        alignment: Alignment.topCenter,
                                                                        child: child,
                                                                      ),
                                                                    );
                                                                  },
                                                                  child: Assets.icons.animAsset.moon.image(
                                                                    height: AppSize.h40,
                                                                    width: AppSize.h40,
                                                                  ),
                                                                ),
                                                              ),
                                                              Image.network(widget.imageUrl),
                                                              // AppCachedNetworkImage(
                                                              //   imageUrl: MyLifeStyle.getMountainImage(
                                                              //     ref.selectedMountainIndex.value,
                                                              //   ),
                                                              // ),
                                                              FutureBuilder(
                                                                future: Future.wait(
                                                                  [ref.lightGreenFlag, ref.darkGreenFlag],
                                                                ),
                                                                builder: (context, snapshot) {
                                                                  if (snapshot.hasData) {
                                                                    return SizedBox(
                                                                      height: AppSize.h130,
                                                                      width: double.maxFinite,
                                                                      child: AnimatedBuilder(
                                                                        animation: animationController,
                                                                        builder: (context, child) {
                                                                          return CustomPaint(
                                                                            painter: FlagPainter(
                                                                              darkGreenFlag: snapshot.data!.last,
                                                                              lightGreenFlag: snapshot.data!.first,
                                                                              flagOffsets:
                                                                                  FlagOffset.getEverestFlagsByIndex(
                                                                                3,
                                                                                ref.selectedMountainIndex.value,
                                                                              ),
                                                                              lineOffsets:
                                                                                  FlagOffset.getEverestLineByIndex(
                                                                                3,
                                                                                ref.selectedMountainIndex.value,
                                                                              ),
                                                                              animation: animationController.value,
                                                                            ),
                                                                          );
                                                                        },
                                                                      ),
                                                                    );
                                                                  } else {
                                                                    return SizedBox(
                                                                      height: AppSize.h100,
                                                                    );
                                                                  }
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h16),
                                                        Center(
                                                          child: AppTextWidget(
                                                            textAlign: TextAlign.center,
                                                            AsLocaleKeys.lsMotivateTitle.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        AppTextWidget(
                                                          (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsMotivateText,
                                                            context,
                                                          ) as List)
                                                              .join('\n\n'),
                                                          style: context.textTheme.titleSmall,
                                                        ),
                                                        SpaceV(AppSize.h12),
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.selectedBenefitValue,
                                                          builder: (context, value, child) {
                                                            return ListView.builder(
                                                              shrinkWrap: true,
                                                              physics: const NeverScrollableScrollPhysics(),
                                                              itemCount: ref.beenfitList.length,
                                                              itemBuilder: (context, index) {
                                                                final isChecked = ref.selectedBenefitValue.value[index];
                                                                return InkWell(
                                                                  onTap: () {
                                                                    ref.selectedBenefitValue.value =
                                                                        List.from(ref.selectedBenefitValue.value)
                                                                          ..[index] = !isChecked;
                                                                  },
                                                                  child: Row(
                                                                    children: [
                                                                      Checkbox(
                                                                        materialTapTargetSize: MaterialTapTargetSize
                                                                            .padded, // This removes extra space
                                                                        visualDensity: VisualDensity.compact,
                                                                        side: BorderSide(
                                                                          color: ref.isButtonClicked.value &&
                                                                                  (ref.selectedBenefitValue.value.every(
                                                                                    (element) => !element,
                                                                                  ))
                                                                              ? context.themeColors.errorRedColor
                                                                              : context.themeColors.greenColor,
                                                                          width: 2,
                                                                        ),
                                                                        activeColor: context.themeColors.greenColor,
                                                                        value: isChecked,
                                                                        onChanged: (bool? newValue) {
                                                                          ref.selectedBenefitValue.value =
                                                                              List.from(ref.selectedBenefitValue.value)
                                                                                ..[index] = newValue ?? false;
                                                                          'ref.selectedBenefitValue.value ${ref.selectedBenefitValue.value}'
                                                                              .logV;
                                                                          // ref.isButtonClicked.value = true;
                                                                        },
                                                                      ),
                                                                      Expanded(
                                                                        child: AppTextWidget(
                                                                          ref.beenfitList[index],
                                                                          style: context.textTheme.titleSmall,
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                );
                                                              },
                                                            );
                                                          },
                                                        ),
                                                        if (ref.isButtonClicked.value &&
                                                            (ref.selectedBenefitValue.value.every(
                                                              (element) => !element,
                                                            )))
                                                          CustomErrorWidget(
                                                            errorMessgaeText: AsLocaleKeys.lsErrorsMinimumOne.tr(),
                                                          )
                                                        else
                                                          const SizedBox(),
                                                        SpaceV(AppSize.h8),
                                                        CustomOutlinedTextfield(
                                                          controller: ref.otherBenefitController,
                                                          hintText: AsLocaleKeys.lsMotivateOther.tr(),
                                                        ),
                                                      ],
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      inProgress: state.maybeWhen(
                                                        loading: () => true,
                                                        orElse: () => false,
                                                      ),
                                                      onTap: () async {
                                                        'ref.selectedBenefitValue.value ${ref.selectedBenefitValue.value}'
                                                            .logV;
                                                        'ref.selectedBenefitValue.value ${ref.selectedBenefitValue.value}'
                                                            .logV;
                                                        if ((ref.selectedBenefitValue.value.every(
                                                              (element) => !element,
                                                            )) &&
                                                            ref.otherBenefitController.text.isEmpty) {
                                                          ref.isButtonClicked.value = true;

                                                          // CustomSnackbar.showErrorSnackBar(
                                                          //   message: AsLocaleKeys.lsErrorsMinimumOne.tr(),
                                                          // );
                                                        } else {
                                                          log('indexs ${ref.selectedBenefitValue.value.asMap().entries.where((entry) => entry.value) // Check if the value is true
                                                              .map((entry) => entry.key) // Get the index
                                                              .toList()}');
                                                          ref.isButtonClicked.value = false;
                                                          await ref.lifestyleStrategyAPI(
                                                            ref: ref,
                                                            context: context,
                                                            imageUrl: widget.imageUrl
                                                          );
                                                        }
                                                      },
                                                      isBottom: true,
                                                      color: context.themeColors.blueColor,
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
