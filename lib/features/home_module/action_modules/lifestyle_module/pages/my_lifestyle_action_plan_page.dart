import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/custom_action_plan_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/assets_path.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestyleActionPlanPage extends StatelessWidget {
  const MyLifestyleActionPlanPage({required this.imageUrl,super.key});
  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();
        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              ref.headerInfoText.value = null;
              ref.infoWelldoneAudioUrl.value = AsLocaleKeys.lsSummaryAudioApp.tr();
            }
          },
          child: ValueListenableBuilder(
            valueListenable: ref.infoActionAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                // resizeToAvoidBottomInset: false,
                isManuallyPaused: ref.isActionPlanPaused,
                scaffoldKey: ref.scaffoldActionPlanKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoActionAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldActionPlanKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldActionPlanKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoActionAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return LayoutBuilder(
                                builder: (context, constraints) {
                                  return CustomRawScrollbar(
                                    child: SingleChildScrollView(
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: AppSize.w24,
                                            right: AppSize.w24,
                                            bottom: AppSize.h20,
                                            top: AppSize.h20,
                                          ),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      InformationPageHeadingWidget(
                                                        onBackArrowTap: () {
                                                          ref.isWelldonePaused.value = false;

                                                          ref.headerInfoText.value = null;

                                                          ref.infoWelldoneAudioUrl.value =
                                                              AsLocaleKeys.lsSummaryAudioApp.tr();
                                                          //  ref.infoActionAudioUrl.value = AsLocaleKeys.lsActionPlanAudio.tr();
                                                          Navigator.pop(context);
                                                        },
                                                        title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                        subtitle: AsLocaleKeys.lsTitle.tr(),
                                                        icon: Assets.icons.actionIcons.lifestyle,
                                                        onInfoTap: () {
                                                          // if (ref.infoActionAudioUrl.value ==
                                                          //     AsLocaleKeys.lsInfoPanelsInformationAudio.tr()) {
                                                          //   ref.infoActionAudioUrl.value =
                                                          //       AsLocaleKeys.lsActionPlanAudio.tr();
                                                          // } else {
                                                          //   ref.infoActionAudioUrl.value =
                                                          //       AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                          // }

                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsInfoPanelsInformationText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');
                                                          // ref.headerInfoText.value =
                                                          //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          //             ref.headerInfoText.value == info)
                                                          //         ? null
                                                          //         : info;
                                                          if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                              ref.headerInfoText.value == info) {
                                                            ref.isActionPlanPaused.value = true;
                                                            ref.headerInfoText.value = null;
                                                            ref.infoActionAudioUrl.value =
                                                                AsLocaleKeys.lsActionPlanAudio.tr();
                                                          } else {
                                                            ref.isActionPlanPaused.value = false;
                                                            ref.infoActionAudioUrl.value =
                                                                AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                            ref.headerInfoText.value = info;
                                                          }
                                                        },
                                                        onLearnTap: () {
                                                          // if (ref.infoActionAudioUrl.value ==
                                                          //     AsLocaleKeys.lsInfoPanelsLearnAudio.tr()) {
                                                          //   ref.infoActionAudioUrl.value =
                                                          //       AsLocaleKeys.lsActionPlanAudio.tr();
                                                          // } else {
                                                          //   ref.infoActionAudioUrl.value =
                                                          //       AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                          // }
                                                          final info = (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.lsInfoPanelsLearnText,
                                                            context,
                                                          ) as List)
                                                              .join('<br/><br/>');

                                                          if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                              ref.headerInfoText.value == info) {
                                                            ref.isActionPlanPaused.value = true;
                                                            ref.headerInfoText.value = null;
                                                            ref.infoActionAudioUrl.value =
                                                                AsLocaleKeys.lsActionPlanAudio.tr();
                                                          } else {
                                                            ref.isActionPlanPaused.value = false;
                                                            ref.infoActionAudioUrl.value =
                                                                AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                            ref.headerInfoText.value = info;
                                                          }
                                                        },
                                                        infoWidget: ValueListenableBuilder(
                                                          valueListenable: ref.headerInfoText,
                                                          builder: (context, headerPlanInfoTextV, _) {
                                                            return CustomInfoWidget(
                                                              customWidget: Column(
                                                                children: [
                                                                  Html(
                                                                    data: ref.headerInfoText.value ?? '',
                                                                    style: {
                                                                      'strong': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontWeight: FontWeight.bold,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                      'body': Style(
                                                                        fontSize: FontSize(AppSize.sp13),
                                                                        color: context.themeColors.darkOrangeColor,
                                                                        fontFamily: 'Poppins',
                                                                      ),
                                                                    },
                                                                  ),
                                                                ],
                                                              ),
                                                              onCloseTap: () {
                                                                ref.isActionPlanPaused.value = true;

                                                                ref.headerInfoText.value = null;
                                                                ref.infoActionAudioUrl.value =
                                                                    AsLocaleKeys.lsActionPlanAudio.tr();
                                                              },
                                                              visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                              margin: EdgeInsets.symmetric(
                                                                vertical: AppSize.h8,
                                                              ),
                                                              bodyText: headerPlanInfoTextV,
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h6),
                                                      Center(
                                                        child: AppTextWidget(
                                                          AsLocaleKeys.lsActionPlanTitle.tr(),
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h14),
                                                      AppTextWidget(
                                                        (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsActionPlanText,
                                                          context,
                                                        ) as List)
                                                            .join('\n\n'),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h16),
                                                      Container(
                                                        clipBehavior: Clip.hardEdge,
                                                        decoration: BoxDecoration(
                                                          color: const Color.fromRGBO(200, 208, 232, 1),
                                                          borderRadius: BorderRadius.circular(AppSize.r14),
                                                        ),
                                                        child: Stack(
                                                          children: [
                                                            Image.network(imageUrl),
                                                            // AppCachedNetworkImage(
                                                            //   imageUrl: MyLifeStyle.getMountainImage(
                                                            //     ref.selectedMountainIndex.value,
                                                            //   ),
                                                            // ),
                                                            FutureBuilder(
                                                              future:
                                                                  Future.wait([ref.lightGreenFlag, ref.darkGreenFlag]),
                                                              builder: (context, snapshot) {
                                                                if (snapshot.hasData) {
                                                                  return SizedBox(
                                                                    height: AppSize.h130,
                                                                    width: double.maxFinite,
                                                                    child: CustomPaint(
                                                                      painter: FlagPainter(
                                                                        darkGreenFlag: snapshot.data!.last,
                                                                        lightGreenFlag: snapshot.data!.first,
                                                                        flagOffsets: FlagOffset.getEverestFlagsByIndex(
                                                                          3,
                                                                          ref.selectedMountainIndex.value,
                                                                        ),
                                                                        lineOffsets: FlagOffset.getEverestLineByIndex(
                                                                          3,
                                                                          ref.selectedMountainIndex.value,
                                                                        ),
                                                                        animation: 1,
                                                                      ),
                                                                    ),
                                                                  );
                                                                } else {
                                                                  return SizedBox(
                                                                    height: AppSize.h100,
                                                                  );
                                                                }
                                                              },
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanLifeGoal.tr(),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      CustomActionPlanTextWidget(
                                                        question: ref.idetifyQue1Controller.text,
                                                        padding: EdgeInsets.only(top: AppSize.h8),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanNextStep.tr(),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      CustomActionPlanTextWidget(
                                                        question: ref.idetifyQue2Controller.text,
                                                        padding: EdgeInsets.only(top: AppSize.h8),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanBarrier.tr(),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      CustomActionPlanTextWidget(
                                                        question: ref.selectBarrierValue.value == 'Other barrier:'
                                                            ? ref.otherBarrierController.text
                                                            : ref.selectBarrierValue.value,
                                                        padding: EdgeInsets.only(top: AppSize.h8),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanPlanned.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          fontSize: AppSize.sp13,
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanCommit.tr(),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      CustomActionPlanTextWidget(
                                                        question:
                                                            '${ref.formatDate(ref.selectedDate, isDay: true)} ${ref.selectedHour.toString().padLeft(2, '0')}:${ref.selectedMinute.toString().padRight(2, '0')}',
                                                        padding: EdgeInsets.only(top: AppSize.h8),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      AppTextWidget(
                                                        AsLocaleKeys.lsActionPlanBenefits.tr(),
                                                        style: context.textTheme.titleSmall,
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: ref.selectedBenefitValue.value
                                                            .asMap()
                                                            .entries
                                                            .where(
                                                              (entry) => entry.value == true,
                                                            ) // Filter the true values
                                                            .map((line) {
                                                          final index = line.key;
                                                          final line1 = ref.beenfitList[index];
                                                          return Column(
                                                            children: [
                                                              Row(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  Padding(
                                                                    padding: EdgeInsets.only(top: AppSize.h6),
                                                                    child: const AppCachedNetworkImage(
                                                                      imageUrl: AssetsPath.greenBulletPoint,
                                                                    ),
                                                                  ),
                                                                  SpaceH(AppSize.w10),
                                                                  Expanded(
                                                                    child: AppTextWidget(
                                                                      line1,
                                                                      style: context.textTheme.titleSmall?.copyWith(
                                                                        fontWeight: FontWeight.w600,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              SpaceV(AppSize.h8),
                                                            ],
                                                          );
                                                        }).toList(), // Fallback to an empty list if null
                                                      ),
                                                      if (ref.otherBenefitController.text.isNotEmpty) ...{
                                                        //SpaceV(AppSize.h8),
                                                        CustomActionPlanTextWidget(
                                                          question: ref.otherBenefitController
                                                              .text, // Join the filtered values with new line
                                                          padding: EdgeInsets.only(top: AppSize.h8),
                                                        ),
                                                      },
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  ColoredBox(
                                                    color: context.themeColors.whiteColor,
                                                    child: CustomYesNoButton(
                                                      padding: EdgeInsets.zero,
                                                      isDownLoad: true,
                                                      isYesNoButton: true,
                                                      exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                                      agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                                      inNoProgress: ValueNotifier(
                                                        state.maybeWhen(
                                                              downloadPdfLoading: () => true,
                                                              orElse: () => false,
                                                            ) ||
                                                            state.maybeWhen(
                                                              emailPdfLoading: () => true,
                                                              orElse: () => false,
                                                            ),
                                                      ),
                                                      onDownloadTap: () {
                                                        CustomDownloadPopup.buildPopupMenu(
                                                          context: context,
                                                          onDownLoadPdf: () async {
                                                            await ref.lsActionStrategyforDownloadPdfApi(
                                                              context: context,
                                                              isEmail: false,
                                                            );
                                                          },
                                                          onEmailDownload: () async {
                                                            await ref.lsActionStrategyforDownloadPdfApi(
                                                              context: context,
                                                              isEmail: true,
                                                            );
                                                          },
                                                        );
                                                      },
                                                      onTapYes: () async {
                                                        log('ref.selectedBenefitValue.value ${ref.selectedDateController.text}');
                                                        ref.infoActionAudioUrl.value = null;
                                                        '>?>?>? 11'.logV;
                                                        await AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                                      },
                                                      onTapNo: () async {},
                                                      noButtonColor: context.themeColors.orangeColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
