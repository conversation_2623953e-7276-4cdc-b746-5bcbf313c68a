import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_identify_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/mountain_option_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestylePage extends StatelessWidget {
  const MyLifestylePage({super.key});

  @override
  Widget build(BuildContext context) {
    const imageUrk = '';
    return BlocProvider(
      create: (context) => LifestyleCubit(),
      child: BlocBuilder<LifestyleCubit, LifestyleState>(
        builder: (ctx, state) {
          final ref = ctx.read<LifestyleCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return ValueListenableBuilder(
                valueListenable: ref.isClicked,
                builder: (context, value, child) {
                  return ValueListenableBuilder(
                    valueListenable: ref.isButtonClicked,
                    builder: (context, value, child) {
                      return AppScaffold(
                        isManuallyPaused: ref.isLifeStyleManuallyPaused,
                        scaffoldKey: ref.scaffoldKey,
                        drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                        appBar: CommonAppBar(
                          onPrefixTap: () {
                            ref.scaffoldKey.currentState?.openDrawer();
                          },
                          onSuffixTap: () {
                            if (ref.infoIdentifyAudioUrl.value.isNotEmptyAndNotNull) {
                              ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                            }
                          },
                        ),
                        isAudioPanelVisible: ref.isAudioPanelVisible,
                        infoAudioUrl: ref.infoAudioUrl,
                        resizeToAvoidBottomInset: false,
                        body: ColoredBox(
                          color: context.themeColors.whiteColor,
                          child: Column(
                            children: [
                              SpaceV(AppSize.h4),
                              Expanded(
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    return SingleChildScrollView(
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: AppSize.w24,
                                            right: AppSize.w24,
                                            bottom: AppSize.h20,
                                            top: AppSize.h20,
                                          ),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  InformationPageHeadingWidget(
                                                    onBackArrowTap: () {
                                                      // ref.infoAudioUrl.value = AsLocaleKeys.lsDsSummaryAudioApp.tr();
                                                      Navigator.pop(context);
                                                    },
                                                    title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                    subtitle: AsLocaleKeys.lsTitle.tr(),
                                                    icon: Assets.icons.actionIcons.lifestyle,
                                                    onInfoTap: () {
                                                      // if (ref.infoAudioUrl.value ==
                                                      //     AsLocaleKeys.lsInfoPanelsInformationAudio.tr()) {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                      // } else {
                                                      //   ref.infoAudioUrl.value =
                                                      //       AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                      // }

                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.lsInfoPanelsInformationText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');

                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isLifeStyleManuallyPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                      } else {
                                                        ref.isLifeStyleManuallyPaused.value = false;

                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                        ref.headerInfoText.value = info;
                                                      }
                                                    },
                                                    onLearnTap: () {
                                                      // if (ref.infoAudioUrl.value ==
                                                      //     AsLocaleKeys.lsInfoPanelsLearnAudio.tr()) {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                      // } else {
                                                      //   ref.infoAudioUrl.value =
                                                      //       AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                      // }

                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.lsInfoPanelsLearnText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');

                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isLifeStyleManuallyPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                      } else {
                                                        ref.isLifeStyleManuallyPaused.value = false;

                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                        ref.headerInfoText.value = info;
                                                      }
                                                    },
                                                    infoWidget: ValueListenableBuilder(
                                                      valueListenable: ref.headerInfoText,
                                                      builder: (context, headerPlanInfoTextV, _) {
                                                        return CustomInfoWidget(
                                                          customWidget: Column(
                                                            children: [
                                                              Html(
                                                                data: ref.headerInfoText.value ?? '',
                                                                style: {
                                                                  'strong': Style(
                                                                    fontSize: FontSize(AppSize.sp13),
                                                                    color: context.themeColors.darkOrangeColor,
                                                                    fontWeight: FontWeight.bold,
                                                                    fontFamily: 'Poppins',
                                                                  ),
                                                                  'body': Style(
                                                                    fontSize: FontSize(AppSize.sp13),
                                                                    color: context.themeColors.darkOrangeColor,
                                                                    fontFamily: 'Poppins',
                                                                  ),
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                                          onCloseTap: () {
                                                            ref.isLifeStyleManuallyPaused.value = true;
                                                            ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                            //  assessmentLifeStyleCubit.islifeFirstInfoVisible.value = false;
                                                            ref.headerInfoText.value = null;
                                                            //  ref.infoAudioUrl.value = null;
                                                          },
                                                          visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                          margin: EdgeInsets.symmetric(
                                                            vertical: AppSize.h8,
                                                          ),
                                                          bodyText: headerPlanInfoTextV,
                                                        );
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h10),
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w14,
                                                      right: AppSize.w14,
                                                    ),
                                                    child: Column(
                                                      children: [
                                                        AppTextWidget(
                                                          AsLocaleKeys.lsMountainsTitle.tr(),
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            fontSize: AppSize.sp14,
                                                            fontWeight: FontWeight.w600,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        AppTextWidget(
                                                          AsLocaleKeys.lsMountainsText.tr(),
                                                          style: context.textTheme.titleSmall,
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                      ],
                                                    ),
                                                  ),
                                                  ValueListenableBuilder(
                                                    valueListenable: ref.selectedMountainIndex,
                                                    builder: (context, value, child) {
                                                      return Padding(
                                                        padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
                                                        child: Column(
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                              children: [
                                                                Expanded(
                                                                  child: MountainOptionWidget(
                                                                    index: 0,
                                                                    imageUrl:
                                                                        AsLocaleKeys.lsMountainsOptionsEverestImg.tr(),
                                                                    label: AsLocaleKeys.lsMountainsOptionsEverestLabel
                                                                        .tr(),
                                                                    ref: ref,
                                                                  ),
                                                                ),
                                                                //  SpaceH(AppSize.w20),
                                                                Expanded(
                                                                  child: MountainOptionWidget(
                                                                    index: 1,
                                                                    imageUrl: AsLocaleKeys
                                                                        .lsMountainsOptionsKilimanjaroImg
                                                                        .tr(),
                                                                    label: AsLocaleKeys
                                                                        .lsMountainsOptionsKilimanjaroLabel
                                                                        .tr(),
                                                                    ref: ref,
                                                                    backgroundColor:
                                                                        ref.selectedMountainIndex.value == 1
                                                                            ? Colors.green
                                                                            : Colors.lightGreen,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            Row(
                                                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                              children: [
                                                                Expanded(
                                                                  child: MountainOptionWidget(
                                                                    index: 2,
                                                                    imageUrl:
                                                                        AsLocaleKeys.lsMountainsOptionsMachuImg.tr(),
                                                                    label:
                                                                        AsLocaleKeys.lsMountainsOptionsMachuLabel.tr(),
                                                                    ref: ref,
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  child: MountainOptionWidget(
                                                                    index: 3,
                                                                    imageUrl: AsLocaleKeys
                                                                        .lsMountainsOptionsMatterhornImg
                                                                        .tr(),
                                                                    label: AsLocaleKeys.lsMountainsOptionsMatterhornLabel.tr(),
                                                                    ref: ref,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            if (ref.isClicked.value &&
                                                                ref.selectedMountainIndex.value == -1)
                                                              CustomErrorWidget(
                                                                errorMessgaeText:
                                                                    AsLocaleKeys.lsErrorsSelectMountain.tr(),
                                                              ),
                                                            Visibility(
                                                              visible: ref.isButtonClicked.value &&
                                                                  ref.selectedMountainIndex.value == -1,
                                                              child: const CustomErrorWidget(
                                                                errorMessgaeText: AsLocaleKeys.lsErrorsSelectMountain,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                              SpaceV(AppSize.h10),
                                              CustomButton(
                                                padding: EdgeInsets.zero,
                                                title: CoreLocaleKeys.buttonsNext.tr(),
                                                onTap: () async {
                                                  //  ref.isAudioPanelVisible.value = true;
                                                  // player.stop();
                                                  if (ref.selectedMountainIndex.value == -1) {
                                                    ref.isClicked.value = true;
                                                    //     ref.isButtonClicked.value = true;
                                                    // CustomSnackbar.showErrorSnackBar(
                                                    //     //   message: AsLocaleKeys.lsErrorsSelectMountain.tr(),
                                                    //     // );
                                                  } else {
                                                    ref.isButtonClicked.value = false;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoAudioUrl.value = null;

                                                    ref.isClicked.value = false;
                                                    ref.isIdenitfyPaused.value = false;

                                                    await precacheImage(NetworkImage(ref.imageUrl), context);

                                                    await AppNavigation.nextScreen(
                                                      context,
                                                      BlocProvider.value(
                                                        value: ref,
                                                        child: MyLifestyleIdentifyPage(
                                                          imageUrl: ref.imageUrl,
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                },
                                                isBottom: true,
                                                color: context.themeColors.blueColor,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
