import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_confetti_animation.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/parse_tagged_text.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestyleWellDonePage extends StatelessWidget {
  MyLifestyleWellDonePage({required this.imageUrl,super.key});
  final String imageUrl;
  final controller = ConfettiController();
  @override
  Widget build(BuildContext context) {
    '>?>?>? ${AsLocaleKeys.lsSummaryAudioApp.tr()}'.logD;
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoWelldoneAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.headerInfoText.value = null;
                  ref.infoBenefitAudioUrl.value = AsLocaleKeys.lsMotivateAudio.tr();
                }
              },
              child: AppScaffold(
                // resizeToAvoidBottomInset: false,
                isManuallyPaused: ref.isWelldonePaused,
                scaffoldKey: ref.scaffoldWellDoneKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoWelldoneAudioUrl,//ValueNotifier(AsLocaleKeys.lsSummaryAudioApp.tr()),//ref.infoWelldoneAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldWellDoneKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldWellDoneKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoWelldoneAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      CustomConfettiAnimation(controller: controller),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return LayoutBuilder(
                                builder: (context, constraints) {
                                  return CustomRawScrollbar(
                                    child: SingleChildScrollView(
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                          child: Padding(
                                            padding: EdgeInsets.only(
                                              left: AppSize.w24,
                                              right: AppSize.w24,
                                              bottom: AppSize.h20,
                                              top: AppSize.h20,
                                            ),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        InformationPageHeadingWidget(
                                                          onBackArrowTap: () {
                                                            ref.headerInfoText.value = null;
                                                            ref.infoBenefitAudioUrl.value =
                                                                AsLocaleKeys.lsMotivateAudio.tr();
                                                            ref.isBenefitAudioPaused.value = false;

                                                            Navigator.pop(context);
                                                          },
                                                          title:
                                                              CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                          subtitle: AsLocaleKeys.lsTitle.tr(),
                                                          icon: Assets.icons.actionIcons.lifestyle,
                                                          onInfoTap: () {
                                                          

                                                            final info = (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.lsInfoPanelsInformationText,
                                                              context,
                                                            ) as List<dynamic>? ?? [])
                                                                .join('<br/><br/>');
                                                        

                                                            if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                ref.headerInfoText.value == info) {
                                                              ref.isWelldonePaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoWelldoneAudioUrl.value =
                                                                  AsLocaleKeys.lsSummaryAudioApp.tr();
                                                            } else {
                                                              ref.isWelldonePaused.value = false;
                                                              ref.infoWelldoneAudioUrl.value =
                                                                  AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                              ref.headerInfoText.value = info;
                                                            }
                                                          },
                                                          onLearnTap: () {
                                                        
                                                            final info = (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.lsInfoPanelsLearnText,
                                                              context,
                                                            ) as List<dynamic>? ?? [])
                                                                .join('<br/><br/>');
                                                          
                                                           if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                ref.headerInfoText.value == info) {
                                                              ref.isWelldonePaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              ref.infoWelldoneAudioUrl.value =
                                                                  AsLocaleKeys.lsSummaryAudioApp.tr();
                                                            } else {
                                                              ref.isWelldonePaused.value = false;
                                                              ref.infoWelldoneAudioUrl.value =
                                                                  AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                              ref.headerInfoText.value = info;
                                                            }
                                                          },
                                                          infoWidget: ValueListenableBuilder(
                                                            valueListenable: ref.headerInfoText,
                                                            builder: (context, headerPlanInfoTextV, _) {
                                                              return CustomInfoWidget(
                                                                customWidget: Column(
                                                                  children: [
                                                                    Html(
                                                                      data: ref.headerInfoText.value ?? '',
                                                                      style: {
                                                                        'strong': Style(
                                                                          fontSize: FontSize(AppSize.sp13),
                                                                          color: context.themeColors.darkOrangeColor,
                                                                          fontWeight: FontWeight.bold,
                                                                          fontFamily: 'Poppins',
                                                                        ),
                                                                        'body': Style(
                                                                          fontSize: FontSize(AppSize.sp13),
                                                                          color: context.themeColors.darkOrangeColor,
                                                                          fontFamily: 'Poppins',
                                                                        ),
                                                                      },
                                                                    ),
                                                                  ],
                                                                ),
                                                                onCloseTap: () {
                                                                  ref.isWelldonePaused.value = true;
                                                                  ref.headerInfoText.value = null;
                                                                  ref.infoWelldoneAudioUrl.value =
                                                                      AsLocaleKeys.lsSummaryAudioApp.tr();
                                                                },
                                                                visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                                margin: EdgeInsets.symmetric(
                                                                  vertical: AppSize.h8,
                                                                ),
                                                                bodyText: headerPlanInfoTextV,
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h6),
                                                        Container(
                                                          clipBehavior: Clip.hardEdge,
                                                          decoration: BoxDecoration(
                                                            color: const Color.fromRGBO(200, 208, 232, 1),
                                                            borderRadius: BorderRadius.circular(AppSize.r14),
                                                          ),
                                                          child: Stack(
                                                            children: [
                                                              Image.network(imageUrl),
                                                              // AppCachedNetworkImage(
                                                              //   imageUrl: MyLifeStyle.getMountainImage(
                                                              //     ref.selectedMountainIndex.value,
                                                              //   ),
                                                              // ),
                                                              FutureBuilder(
                                                                future: Future.wait(
                                                                  [ref.lightGreenFlag, ref.darkGreenFlag],
                                                                ),
                                                                builder: (context, snapshot) {
                                                                  if (snapshot.hasData) {
                                                                    return SizedBox(
                                                                      height: AppSize.h130,
                                                                      width: double.maxFinite,
                                                                      child: CustomPaint(
                                                                        painter: FlagPainter(
                                                                          darkGreenFlag: snapshot.data!.last,
                                                                          lightGreenFlag: snapshot.data!.first,
                                                                          flagOffsets:
                                                                              FlagOffset.getEverestFlagsByIndex(
                                                                            3,
                                                                            ref.selectedMountainIndex.value,
                                                                          ),
                                                                          lineOffsets: FlagOffset.getEverestLineByIndex(
                                                                            3,
                                                                            ref.selectedMountainIndex.value,
                                                                          ),
                                                                          animation: 1,
                                                                        ),
                                                                      ),
                                                                    );
                                                                  } else {
                                                                    return SizedBox(
                                                                      height: AppSize.h100,
                                                                    );
                                                                  }
                                                                },
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h16),
                                                        Center(
                                                          child: AppTextWidget(
                                                            textAlign: TextAlign.center,
                                                            AsLocaleKeys.lsSummaryTitle.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h14),
                                                        RichText(
  text: parseTaggedText(
    text: (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsSummaryTextApp,
      context,
    ) as List<dynamic>? ?? []).join('\n\n'),
    baseStyle: context.textTheme.titleSmall,
  ),
)

                                                      ],
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      onTap: () async {
                                                        ref.headerInfoText.value = null;
                                                        ref.infoWelldoneAudioUrl.value = null;

                                                        log('ref.selectedBenefitValue.value ${ref.selectedBenefitValue.value}');
                                                        await AppNavigation.nextScreen(
                                                          context,
                                                          BlocProvider.value(
                                                            value: ref,
                                                            child: MyLifestyleActionPlanPage(imageUrl: imageUrl,),
                                                          ),
                                                        );
                                                      },
                                                      isBottom: true,
                                                      color: context.themeColors.blueColor,
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
