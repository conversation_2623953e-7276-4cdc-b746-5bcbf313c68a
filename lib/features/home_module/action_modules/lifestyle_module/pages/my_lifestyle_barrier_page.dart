import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_overcome_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyLifestyleBarrierPage extends StatefulWidget {
  const MyLifestyleBarrierPage({required this.imageUrl,super.key});
  final String imageUrl;

  @override
  State<MyLifestyleBarrierPage> createState() => _MyLifestyleBarrierPageState();
}

class _MyLifestyleBarrierPageState extends State<MyLifestyleBarrierPage> with TickerProviderStateMixin {
  late AnimationController animationController;
  late AnimationController _controller;

  @override
  void initState() {
    animationController = AnimationController(duration: const Duration(seconds: 1), vsync: this);
    _controller = AnimationController(duration: const Duration(minutes: 8), vsync: this, value: .90)..repeat();
    animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoBarrierAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.headerInfoText.value = null;
                  ref.infoIdentifyAudioUrl.value = AsLocaleKeys.lsIdentifyGoalAudio.tr();
                }
              },
              child: AppScaffold(
                isManuallyPaused: ref.isBarrierAudioUrlPaused,
                // resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldBarrierKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoBarrierAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldBarrierKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldBarrierKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoBarrierAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: Form(
                  key: ref.globalKey3,
                  child: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(right: AppSize.w4),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return CustomRawScrollbar(
                                  child: SingleChildScrollView(
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w24,
                                          right: AppSize.w24,
                                          bottom: AppSize.h20,
                                          top: AppSize.h20,
                                        ),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              children: [
                                                InformationPageHeadingWidget(
                                                  onBackArrowTap: () {
                                                    ref.headerInfoText.value = null;
                                                    ref.q1InfoVisible.value = false;
                                                    ref.q2InfoVisible.value = false;
                                                    ref.infoIdentifyAudioUrl.value =
                                                        AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                    ref.isIdenitfyPaused.value = false;
                                                    Navigator.pop(context);
                                                  },
                                                  title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                  subtitle: AsLocaleKeys.lsTitle.tr(),
                                                  icon: Assets.icons.actionIcons.lifestyle,
                                                  onInfoTap: () {
                                                    final info = (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.lsInfoPanelsInformationText,
                                                      context,
                                                    ) as List)
                                                        .join('<br/><br/>');

                                                    if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                        ref.headerInfoText.value == info) {
                                                      ref.isBarrierAudioUrlPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      ref.infoBarrierAudioUrl.value =
                                                          AsLocaleKeys.lsIdentifyBarrierAudio.tr();
                                                    } else {
                                                      ref.isBarrierAudioUrlPaused.value = false;
                                                      ref.infoBarrierAudioUrl.value =
                                                          AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                      ref.headerInfoText.value = info;
                                                    }
                                                  },
                                                  onLearnTap: () {
                                                    final info = (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.lsInfoPanelsLearnText,
                                                      context,
                                                    ) as List)
                                                        .join('<br/><br/>');

                                                    if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                        ref.headerInfoText.value == info) {
                                                      ref.isBarrierAudioUrlPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      ref.infoBarrierAudioUrl.value =
                                                          AsLocaleKeys.lsIdentifyBarrierAudio.tr();
                                                    } else {
                                                      ref.isBarrierAudioUrlPaused.value = false;
                                                      ref.infoBarrierAudioUrl.value =
                                                          AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                      ref.headerInfoText.value = info;
                                                    }
                                                  },
                                                  infoWidget: ValueListenableBuilder(
                                                    valueListenable: ref.headerInfoText,
                                                    builder: (context, headerPlanInfoTextV, _) {
                                                      return CustomInfoWidget(
                                                        customWidget: Column(
                                                          children: [
                                                            Html(
                                                              data: ref.headerInfoText.value ?? '',
                                                              style: {
                                                                'strong': Style(
                                                                  fontSize: FontSize(AppSize.sp13),
                                                                  color: context.themeColors.darkOrangeColor,
                                                                  fontWeight: FontWeight.bold,
                                                                  fontFamily: 'Poppins',
                                                                ),
                                                                'body': Style(
                                                                  fontSize: FontSize(AppSize.sp13),
                                                                  color: context.themeColors.darkOrangeColor,
                                                                  fontFamily: 'Poppins',
                                                                ),
                                                              },
                                                            ),
                                                          ],
                                                        ),
                                                        onCloseTap: () {
                                                          ref.isBarrierAudioUrlPaused.value = true;

                                                          ref.headerInfoText.value = null;
                                                          ref.infoBarrierAudioUrl.value = ref.infoBarrierAudioUrl
                                                              .value = AsLocaleKeys.lsIdentifyBarrierAudio.tr();
                                                        },
                                                        visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                        margin: EdgeInsets.symmetric(
                                                          vertical: AppSize.h8,
                                                        ),
                                                        bodyText: headerPlanInfoTextV,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h6),
                                                Container(
                                                  clipBehavior: Clip.hardEdge,
                                                  decoration: BoxDecoration(
                                                    color: const Color.fromRGBO(200, 208, 232, 1),
                                                    borderRadius: BorderRadius.circular(AppSize.r14),
                                                  ),
                                                  child: Stack(
                                                    children: [
                                                      Positioned(
                                                        top: -AppSize.h10,
                                                        left: 0,
                                                        right: 0,
                                                        bottom: -AppSize.h130,
                                                        child: AnimatedBuilder(
                                                          animation: _controller,
                                                          builder: (context, child) {
                                                            return RotationTransition(
                                                              turns: _controller,
                                                              child: Align(
                                                                alignment: Alignment.topCenter,
                                                                child: child,
                                                              ),
                                                            );
                                                          },
                                                          child: Assets.icons.animAsset.sun.image(
                                                            height: AppSize.h40,
                                                            width: AppSize.h40,
                                                          ),
                                                        ),
                                                      ),
                                                      Positioned(
                                                        top: -10,
                                                        left: 0,
                                                        right: 0,
                                                        bottom: -AppSize.h120,
                                                        child: AnimatedBuilder(
                                                          animation: _controller,
                                                          builder: (context, child) {
                                                            return RotationTransition(
                                                              turns: AlwaysStoppedAnimation(_controller.value - 0.5),
                                                              child: Align(
                                                                alignment: Alignment.topCenter,
                                                                child: child,
                                                              ),
                                                            );
                                                          },
                                                          child: Assets.icons.animAsset.moon.image(
                                                            height: AppSize.h40,
                                                            width: AppSize.h40,
                                                          ),
                                                        ),
                                                      ),
                                                      Image.network(widget.imageUrl),
                                                      // AppCachedNetworkImage(
                                                      //   imageUrl: ref.imageUrl,
                                                      //   // imageUrl: MyLifeStyle.getMountainImage(
                                                      //   //   ref.selectedMountainIndex.value,
                                                      //   // ),
                                                      // ),
                                                      FutureBuilder(
                                                        future: Future.wait([ref.lightGreenFlag, ref.darkGreenFlag]),
                                                        builder: (context, snapshot) {
                                                          if (snapshot.hasData) {
                                                            return SizedBox(
                                                              height: AppSize.h130,
                                                              width: double.maxFinite,
                                                              child: AnimatedBuilder(
                                                                animation: animationController,
                                                                builder: (context, child) {
                                                                  return CustomPaint(
                                                                    painter: FlagPainter(
                                                                      darkGreenFlag: snapshot.data!.last,
                                                                      lightGreenFlag: snapshot.data!.first,
                                                                      flagOffsets: FlagOffset.getEverestFlagsByIndex(
                                                                        1,
                                                                        ref.selectedMountainIndex.value,
                                                                      ),
                                                                      lineOffsets: FlagOffset.getEverestLineByIndex(
                                                                        1,
                                                                        ref.selectedMountainIndex.value,
                                                                      ),
                                                                      animation: animationController.value,
                                                                    ),
                                                                  );
                                                                },
                                                              ),
                                                            );
                                                          } else {
                                                            return SizedBox(
                                                              height: AppSize.h100,
                                                            );
                                                          }
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                SpaceV(AppSize.h16),
                                                AppTextWidget(
                                                  textAlign: TextAlign.center,
                                                  AsLocaleKeys.lsIdentifyBarrierTitle.tr(),
                                                  style: context.textTheme.titleSmall?.copyWith(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                                SpaceV(AppSize.h20),
                                                AppTextWidget(
                                                  AsLocaleKeys.lsIdentifyBarrierText.tr(),
                                                  style: context.textTheme.titleSmall,
                                                ),
                                                ValueListenableBuilder(
                                                  valueListenable: ref.selectBarrierValue,
                                                  builder: (context, value, child) {
                                                    return Column(
                                                      children: [
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.isButtonClicked,
                                                          builder: (context, value2, child) {
                                                            return CustomRadioListWidget(
                                                              fontSize: AppSize.sp12,
                                                              padding: EdgeInsets.zero,
                                                              isMultiOption: true,
                                                              isError: ref.isButtonClicked.value &&
                                                                  ref.selectBarrierValue.value.isEmpty,
                                                              //isButtonClicked: assessmentCubit.isEmotionButtonClicked.value,
                                                              options: ref.barrierList,
                                                              selectedValue: value, // Safely access first item
                                                              onChanged: (newValue) {
                                                                log('newValue $newValue');
                                                                log(' ref.selectBarrierValue.value ${ref.selectBarrierValue.value}');
                                                                if (newValue != null) {
                                                                  ref.selectBarrierValue.value = newValue;
                                                                  if (newValue != ref.barrierList.last) {
                                                                    ref.otherBarrierController.text = '';
                                                                  }
                                                                }
                                                              },
                                                            );
                                                          },
                                                        ),
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.isButtonClicked,
                                                          builder: (context, isButtonClicked, child) {
                                                            return Padding(
                                                              padding: EdgeInsets.symmetric(
                                                                horizontal: AppSize.w14,
                                                                vertical: AppSize.h16,
                                                              ),
                                                              child: Column(
                                                                children: [
                                                                  Visibility(
                                                                    visible: ref.selectBarrierValue.value ==
                                                                        ref.barrierList.last,
                                                                    child: CustomOutlinedTextfield(
                                                                      hintText:
                                                                          CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                                      controller: ref.otherBarrierController,
                                                                      isError: ref.isButtonClicked.value &&
                                                                          ref.otherBarrierController.text
                                                                              .trim()
                                                                              .isEmpty,
                                                                      // validator: (p0) => otherBarrier().call(p0),
                                                                      onChanged: (p0) {
                                                                        ref.isButtonClicked.notifyListeners();
                                                                      },
                                                                    ),
                                                                  ),
                                                                  Visibility(
                                                                    visible: ref.isButtonClicked.value &&
                                                                        ref.otherBarrierController.text
                                                                            .trim()
                                                                            .isEmpty &&
                                                                        ref.selectBarrierValue.value ==
                                                                            ref.barrierList.last,
                                                                    child: CustomErrorWidget(
                                                                      errorMessgaeText: AssessmentLocaleKeys
                                                                          .errorsRequiredMessage
                                                                          .tr(),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            );
                                                          },
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                            CustomButton(
                                              padding: EdgeInsets.zero,
                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                              onTap: () async {
                                                print('?|??|" onTap triggered');
                                                ref.globalKey3.currentState?.validate();

                                                ref.isButtonClicked.value = true;
                                                FocusManager.instance.primaryFocus?.unfocus();

                                                // Check if no barrier is selected
                                                if (ref.selectBarrierValue.value.isEmpty) {
                                                  print('?|??|" Early return: No barrier selected');
                                                  return;
                                                }

                                                // Handle 'Other barrier:' case
                                                if (ref.selectBarrierValue.value == ref.barrierList.last) {
                                                  if (ref.otherBarrierController.text.trim().isEmpty) {
                                                    print('?|??|" Early return: Other barrier text empty');
                                                    return;
                                                  }
                                                }
                                                try {
                                                  final audioList = DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsOvercomeBarrierAudio,
                                                    context,
                                                  ) as List?;
                                                  String selectedAudio = "";
                                                  if (ref.selectBarrierValue.value == ref.barrierList.last) {
                                                    // If 'Other barrier' is selected, use the last audio
                                                    selectedAudio = (audioList != null && audioList.isNotEmpty) ? audioList.last.toString() : "";
                                                  } else {
                                                    // Otherwise, use the audio corresponding to the selected barrier's index
                                                    final index = ref.barrierList.indexOf(ref.selectBarrierValue.value);
                                                    selectedAudio = (audioList != null && index >= 0 && index < audioList.length)
                                                      ? audioList[index].toString()
                                                      : "";
                                                  }
                                                  print('?|??|" selectedAudio: $selectedAudio (type: ${selectedAudio.runtimeType})');
                                                  ref.infoOvercomeAudioUrl.value = selectedAudio;
                                                } catch (e, stack) {
                                                  print('?|??|" EXCEPTION in audio selection: $e');
                                                  print('?|??|" STACK: $stack');
                                                }
                                                '?|??| this is running 22....'.logV;
                                                print('?|??|" infoOvercomeAudioUrl.value: ${ref.infoOvercomeAudioUrl.value} (type: ${ref.infoOvercomeAudioUrl.value.runtimeType})');
                                                ref.isButtonClicked.value = false; 

                                                ref.infoBarrierAudioUrl.value = null;
                                                ref.headerInfoText.value = null;
                                                ref.isOverComeAudioPaused.value = false;
                                                await AppNavigation.nextScreen(
                                                  context,
                                                  BlocProvider.value(
                                                    value: ref,
                                                    child: MyLifestyleOvercomePage(imageUrl: widget.imageUrl,),
                                                  ),
                                                );
                                              },
                                              isBottom: true,
                                              color: context.themeColors.blueColor,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class BarrierData {
  BarrierData({
    required this.audio,
    required this.canSay,
    required this.canDo,
  });
  final List<String> canSay;
  final List<String> canDo;
  final String audio;
}
