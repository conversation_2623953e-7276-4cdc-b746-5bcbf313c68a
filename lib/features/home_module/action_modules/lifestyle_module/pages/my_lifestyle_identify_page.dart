// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/cubit/lifestyle_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_barrier_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/identfy_button_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class MyLifestyleIdentifyPage extends StatefulWidget {
  const MyLifestyleIdentifyPage({required this.imageUrl,super.key});
  final String imageUrl;

  @override
  State<MyLifestyleIdentifyPage> createState() => _MyLifestyleIdentifyPageState();
}

class _MyLifestyleIdentifyPageState extends State<MyLifestyleIdentifyPage> with TickerProviderStateMixin {
  late AnimationController animationController;
  late AnimationController _controller;

  @override
  void initState() {
    animationController = AnimationController(duration: const Duration(seconds: 1), vsync: this);
    _controller = AnimationController(duration: const Duration(minutes: 8), vsync: this, value: .90)..repeat();
    animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LifestyleCubit, LifestyleState>(
      builder: (ctx, state) {
        final ref = ctx.read<LifestyleCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoIdentifyAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.headerInfoText.value = null;
                  ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                  ref.isLifeStyleManuallyPaused.value = false;
                }
              },
              child: AppScaffold(
                // resizeToAvoidBottomInset: false,
                isManuallyPaused: ref.isIdenitfyPaused,
                scaffoldKey: ref.scaffoldIdentifyKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoIdentifyAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldIdentifyKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldIdentifyKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoIdentifyAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: Form(
                  key: ref.globalKey,
                  child: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20,
                                        top: AppSize.h20,
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              InformationPageHeadingWidget(
                                                onBackArrowTap: () {
                                                  ref.isLifeStyleManuallyPaused.value = false;
                                                  ref.headerInfoText.value = null;
                                                  ref.q1InfoVisible.value = false;
                                                  ref.q2InfoVisible.value = false;
                                                  ref.infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
                                                  Navigator.pop(context);
                                                },
                                                title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                                subtitle: AsLocaleKeys.lsTitle.tr(),
                                                icon: Assets.icons.actionIcons.lifestyle,
                                                onInfoTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsInfoPanelsInformationText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                    
                                                  if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerInfoText.value == info) {
                                                    ref.isIdenitfyPaused.value = true;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoIdentifyAudioUrl.value =
                                                        AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                  } else {
                                                    ref.isIdenitfyPaused.value = false;
                                                    
                                                    ref.infoIdentifyAudioUrl.value =
                                                        AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                                    ref.headerInfoText.value = info;
                                                  }
                                                },
                                                onLearnTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsInfoPanelsLearnText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerInfoText.value == info) {
                                                    ref.isIdenitfyPaused.value = true;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoIdentifyAudioUrl.value =
                                                        AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                  } else {
                                                    ref.isIdenitfyPaused.value = false;
                                                    
                                                    ref.infoIdentifyAudioUrl.value =
                                                        AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                                    ref.headerInfoText.value = info;
                                                  }
                                                },
                                                infoWidget: ValueListenableBuilder(
                                                  valueListenable: ref.headerInfoText,
                                                  builder: (context, headerPlanInfoTextV, _) {
                                                    return CustomInfoWidget(
                                                      customWidget: Column(
                                                        children: [
                                                          Html(
                                                            data: ref.headerInfoText.value ?? '',
                                                            style: {
                                                              'strong': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontWeight: FontWeight.bold,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                              'body': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      onCloseTap: () {
                                                        ref.isIdenitfyPaused.value = true;
                                                        ref.infoIdentifyAudioUrl.value =
                                                            AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                    
                                                        ref.headerInfoText.value = null;
                                                        //  ref.infoIdentifyAudioUrl.value = null;
                                                      },
                                                      visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerPlanInfoTextV,
                                                    );
                                                  },
                                                ),
                                              ),
                                              SpaceV(AppSize.h6),
                                              Container(
                                                clipBehavior: Clip.hardEdge,
                                                decoration: BoxDecoration(
                                                  color: const Color.fromRGBO(200, 208, 232, 1),
                                                  borderRadius: BorderRadius.circular(AppSize.r14),
                                                ),
                                                child: Stack(
                                                  children: [
                                                    Positioned(
                                                      top: -AppSize.h10,
                                                      left: 0,
                                                      right: 0,
                                                      bottom: -AppSize.h130,
                                                      child: AnimatedBuilder(
                                                        animation: _controller,
                                                        builder: (context, child) {
                                                          return RotationTransition(
                                                            turns: _controller,
                                                            child: Align(
                                                              alignment: Alignment.topCenter,
                                                              child: child,
                                                            ),
                                                          );
                                                        },
                                                        child: Assets.icons.animAsset.sun.image(
                                                          height: AppSize.h40,
                                                          width: AppSize.h40,
                                                        ),
                                                      ),
                                                    ),
                                                    Positioned(
                                                      top: -10,
                                                      left: 0,
                                                      right: 0,
                                                      bottom: -AppSize.h120,
                                                      child: AnimatedBuilder(
                                                        animation: _controller,
                                                        builder: (context, child) {
                                                          return RotationTransition(
                                                            turns: AlwaysStoppedAnimation(_controller.value - 0.5),
                                                            child: Align(
                                                              alignment: Alignment.topCenter,
                                                              child: child,
                                                            ),
                                                          );
                                                        },
                                                        child: Assets.icons.animAsset.moon.image(
                                                          height: AppSize.h40,
                                                          width: AppSize.h40,
                                                        ),
                                                      ),
                                                    ),
                                                    Image.network(widget.imageUrl),
                                                    //ref.image,
                                                    //CachedNetworkImage(imageUrl: ref.imageUrl0,),
                                                    FutureBuilder(
                                                      future: Future.wait([ref.lightGreenFlag, ref.darkGreenFlag]),
                                                      builder: (context, snapshot) {
                                                        if (snapshot.hasData) {
                                                          return SizedBox(
                                                            height: AppSize.h130,
                                                            width: double.maxFinite,
                                                            child: AnimatedBuilder(
                                                              animation: animationController,
                                                              builder: (context, child) {
                                                                return CustomPaint(
                                                                  painter: FlagPainter(
                                                                    darkGreenFlag: snapshot.data!.last,
                                                                    lightGreenFlag: snapshot.data!.first,
                                                                    flagOffsets: FlagOffset.getEverestFlagsByIndex(
                                                                      0,
                                                                      ref.selectedMountainIndex.value,
                                                                    ),
                                                                    lineOffsets: FlagOffset.getEverestLineByIndex(
                                                                      0,
                                                                      ref.selectedMountainIndex.value,
                                                                    ),
                                                                    animation: animationController.value,
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                          );
                                                        } else {
                                                          return SizedBox(
                                                            height: AppSize.h100,
                                                          );
                                                        }
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SpaceV(AppSize.h16),
                                              AppTextWidget(
                                                textAlign: TextAlign.center,
                                                AsLocaleKeys.lsIdentifyGoalTitle.tr(),
                                                style: context.textTheme.titleSmall?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              SpaceV(AppSize.h20),
                                              SizedBox(
                                                key: ref.identifyQue1ControllerKey,
                                                child: ValueListenableBuilder(
                                                  valueListenable: ref.q1InfoVisible,
                                                  builder: (context, q1InfoVisible, child) {
                                                    return QuestionRowWidget(
                                                      oninfoTap: () {},
                                                      custominfoWidget: GestureDetector(
                                                        onTap: () {
                                                          ref.q1InfoVisible.value = !ref.q1InfoVisible.value;
                                                          if (ref.q1InfoVisible.value) {
                                                            ref.isIdenitfyPaused.value = false;
                                                    
                                                            ref.infoIdentifyAudioUrl.value =
                                                                AsLocaleKeys.lsQuestionsLifeGoalInfoPanelAudio.tr();
                                                          } else {
                                                            ref.isIdenitfyPaused.value = true;
                                                    
                                                            ref.infoIdentifyAudioUrl.value =
                                                                AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                          }
                                                        },
                                                        child: AppSvgAsset(
                                                          svgAsset: Assets.icons.actionIcons.lifestyleInoIcon,
                                                          size: AppSize.sp30,
                                                          // color: color ?? context.themeColors.orangeColor,
                                                        ),
                                                      ),
                                                      infoWidget: CustomInfoWidget(
                                                        padding: EdgeInsets.only(
                                                          left: AppSize.w12,
                                                          right: AppSize.w12,
                                                          top: AppSize.h10,
                                                        ),
                                                        visible: q1InfoVisible,
                                                        onCloseTap: () {
                                                          'ref.isIdenitfyPaused.value ${ref.isIdenitfyPaused.value}'
                                                              .logV;
                                                          ref.isIdenitfyPaused.value = true;
                                                    
                                                          ref.q1InfoVisible.value = false;
                                                          ref.infoIdentifyAudioUrl.value =
                                                              AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                        },
                                                        //  ref.headerInfoText.value = null;                                                  },
                                                        bodyText: // Flatten the nested list and join elements with double line breaks
                                                            (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsQuestionsLifeGoalInfoPanelHtml,
                                                          context,
                                                        ) as List<dynamic>)
                                                                .expand(
                                                                  (item) => item is List
                                                                      ? item.map((e) => '• $e').toList()
                                                                      : [item],
                                                                ) // Flatten the nested lists
                                                                .join('\n\n'),
                                                      ),
                                                      questionText: AsLocaleKeys.lsQuestionsLifeGoalLabel.tr(),
                                                    );
                                                  },
                                                ),
                                              ),
                                              SpaceV(AppSize.h14),
                                              ValueListenableBuilder(
                                                valueListenable: ref.isButtonClicked,
                                                builder: (context, isButtonClicked, child) {
                                                  return Column(
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                        child: CustomOutlinedTextfield(
                                                          hintText: CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                          controller: ref.idetifyQue1Controller,
                                                          isError: ref.isButtonClicked.value &&
                                                              ref.idetifyQue1Controller.text.trim().isEmpty,
                                                          // validator: (p0) => riskyplaceValidator().call(p0),
                                                          onChanged: (p0) {
                                                            ref.isButtonClicked.notifyListeners();
                                                            // ref.globalKey.currentState?.validate();
                                                          },
                                                        ),
                                                      ),
                                                      Visibility(
                                                        visible: ref.isButtonClicked.value &&
                                                            ref.idetifyQue1Controller.text.trim().isEmpty,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomErrorWidget(
                                                            spacing: AppSize.h10,
                                                            errorMessgaeText:
                                                                AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h20),
                                              SizedBox(
                                                key: ref.identifyQue2ControllerKey,
                                                child: ValueListenableBuilder(
                                                  valueListenable: ref.q2InfoVisible,
                                                  builder: (context, q2InfoVisible, child) {
                                                    return QuestionRowWidget(
                                                      oninfoTap: () {},
                                                      custominfoWidget: GestureDetector(
                                                        onTap: () {
                                                          ref.q2InfoVisible.value = !ref.q2InfoVisible.value;
                                                          // ref.infoIdentifyAudioUrl.value =
                                                          //     AsLocaleKeys.lsQuestionsNextStepInfoPanelAudio.tr();
                                                    
                                                          // if (ref.infoIdentifyAudioUrl.value ==
                                                          //     AsLocaleKeys.lsQuestionsNextStepInfoPanelAudio.tr()) {
                                                          //   ref.infoIdentifyAudioUrl.value =
                                                          //       AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                          // } else {
                                                          //   ref.infoIdentifyAudioUrl.value =
                                                          //       AsLocaleKeys.lsQuestionsNextStepInfoPanelAudio.tr();
                                                          // }
                                                    
                                                          if (ref.q2InfoVisible.value) {
                                                            ref.isIdenitfyPaused.value = false;
                                                    
                                                            ref.infoIdentifyAudioUrl.value =
                                                                AsLocaleKeys.lsQuestionsNextStepInfoPanelAudio.tr();
                                                          } else {
                                                            ref.isIdenitfyPaused.value = true;
                                                    
                                                            ref.infoIdentifyAudioUrl.value =
                                                                AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                          }
                                                        },
                                                        child: AppSvgAsset(
                                                          svgAsset: Assets.icons.actionIcons.lifestyleInoIcon,
                                                          size: AppSize.sp30,
                                                          // color: color ?? context.themeColors.orangeColor,
                                                        ),
                                                      ),
                                                      infoWidget: CustomInfoWidget(
                                                        padding: EdgeInsets.only(
                                                          left: AppSize.w12,
                                                          right: AppSize.w12,
                                                          top: AppSize.h10,
                                                        ),
                                                        visible: q2InfoVisible,
                                                        onCloseTap: () {
                                                          ref.isIdenitfyPaused.value = true;
                                                    
                                                          ref.q2InfoVisible.value = false;
                                                          // ref.infoIdentifyAudioUrl.value = null;
                                                    
                                                          ref.infoIdentifyAudioUrl.value =
                                                              AsLocaleKeys.lsIdentifyGoalAudio.tr();
                                                        },
                                                        bodyText: (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.lsQuestionsNextStepInfoPanelHtml,
                                                          context,
                                                        ) as List<dynamic>)
                                                            .expand(
                                                              (item) => item is List
                                                                  ? item.map((e) => '• $e').toList()
                                                                  : [item],
                                                            ) // Flatten the nested lists
                                                            .join('\n\n'),
                                                      ),
                                                      questionText: AsLocaleKeys.lsQuestionsNextStepLabel.tr(),
                                                    );
                                                  },
                                                ),
                                              ),
                                              SpaceV(AppSize.h14),
                                              ValueListenableBuilder(
                                                valueListenable: ref.isButtonClicked,
                                                builder: (context, isButtonClicked, child) {
                                                  return Column(
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                        child: CustomOutlinedTextfield(
                                                          hintText: CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                          controller: ref.idetifyQue2Controller,
                                                          isError: ref.isButtonClicked.value &&
                                                              ref.idetifyQue2Controller.text.trim().isEmpty,
                                                          // validator: (p0) => riskyplaceValidator().call(p0),
                                                          onChanged: (p0) {
                                                            ref.isButtonClicked.notifyListeners();
                                                          },
                                                        ),
                                                      ),
                                                      Visibility(
                                                        visible: ref.isButtonClicked.value &&
                                                            ref.idetifyQue2Controller.text.trim().isEmpty,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomErrorWidget(
                                                            spacing: AppSize.h10,
                                                            errorMessgaeText:
                                                                AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                              SpaceV(AppSize.h20),
                                              AppTextWidget(
                                                AsLocaleKeys.lsIdentifyGoalCheck.tr(),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h20),
                                              ValueListenableBuilder(
                                                valueListenable: ref.isButtonClicked,
                                                builder: (context, value, child) {
                                                  return Column(
                                                    children: [
                                                      IdentifyButtonWidget(
                                                        questionText: AsLocaleKeys.lsQuestionsQ1Label.tr(),
                                                        errorText: AsLocaleKeys.lsQuestionsQ1Err.tr(),
                                                        ref: ref,
                                                        currentState: ref.identifyQue1State,
                                                      ),
                                                      IdentifyButtonWidget(
                                                        questionText: AsLocaleKeys.lsQuestionsQ2Label.tr(),
                                                        errorText: AsLocaleKeys.lsQuestionsQ2Err.tr(),
                                                        ref: ref,
                                                        currentState: ref.identifyQue2State,
                                                      ),
                                                      IdentifyButtonWidget(
                                                        questionText: AsLocaleKeys.lsQuestionsQ3Label.tr(),
                                                        errorText: AsLocaleKeys.lsQuestionsQ3Err.tr(),
                                                        ref: ref,
                                                        currentState: ref.identifyQue3State,
                                                      ),
                                                      IdentifyButtonWidget(
                                                        questionText: AsLocaleKeys.lsQuestionsQ4Label.tr(),
                                                        errorText: AsLocaleKeys.lsQuestionsQ4Err.tr(),
                                                        ref: ref,
                                                        currentState: ref.identifyQue4State,
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                          MultiValueListenableBuilder(
                                            valueListenables: [
                                              ref.identifyQue1State,
                                              ref.identifyQue2State,
                                              ref.identifyQue3State,
                                              ref.identifyQue4State,
                                            ],
                                            builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                              final identifyQue1State = values[0]; // ButtonState
                                              final identifyQue2State = values[1]; // ButtonState
                                              final identifyQue3State = values[2]; // ButtonState
                                              final identifyQue4State = values[3]; // ButtonState
                                                    
                                              final isButtonEnabled = ref.idetifyQue1Controller.text.isNotEmpty &&
                                                  ref.idetifyQue2Controller.text.isNotEmpty &&
                                                  identifyQue1State != ButtonState.bothDisabled &&
                                                  identifyQue2State != ButtonState.bothDisabled &&
                                                  identifyQue3State != ButtonState.bothDisabled &&
                                                  identifyQue4State != ButtonState.bothDisabled &&
                                                  identifyQue1State == ButtonState.noEnabled &&
                                                  identifyQue2State == ButtonState.noEnabled &&
                                                  identifyQue3State == ButtonState.noEnabled &&
                                                  identifyQue4State == ButtonState.noEnabled;
                                              return CustomButton(
                                                padding: EdgeInsets.zero,
                                                title: CoreLocaleKeys.buttonsNext.tr(),
                                                onTap: () async {
                                                  if (!isButtonEnabled) {
                                                    ref.globalKey.currentState?.validate();
                                                    ref.isButtonClicked.value = true;
                                                    
                                                    if (ref.idetifyQue1Controller.text.isEmpty) {
                                                      await AppCommonFunctions.scrollToKey(
                                                        ref.identifyQue1ControllerKey,
                                                      );
                                                      return;
                                                    }
                                                    if (ref.idetifyQue2Controller.text.isEmpty) {
                                                      await AppCommonFunctions.scrollToKey(
                                                        ref.identifyQue2ControllerKey,
                                                      );
                                                      return;
                                                    }
                                                    if (ref.identifyQue1State.value != ButtonState.bothDisabled) {
                                                      await AppCommonFunctions.scrollToKey(ref.identifyQue1Key);
                                                      return;
                                                    }
                                                    if (ref.identifyQue2State.value != ButtonState.bothDisabled) {
                                                      await AppCommonFunctions.scrollToKey(ref.identifyQue2Key);
                                                      return;
                                                    }
                                                    if (ref.identifyQue3State.value != ButtonState.bothDisabled) {
                                                      await AppCommonFunctions.scrollToKey(ref.identifyQue3Key);
                                                      return;
                                                    }
                                                    if (ref.identifyQue4State.value != ButtonState.bothDisabled) {
                                                      await AppCommonFunctions.scrollToKey(ref.identifyQue4Key);
                                                      return;
                                                    }
                                                    // CustomSnackbar.showErrorSnackBar(
                                                    //   message: AsLocaleKeys.lsErrorsRequired.tr(),
                                                    // );
                                                  } else {
                                                    ref.isButtonClicked.value = false;
                                                    
                                                    // if (ref.identifyQue1State.value != ButtonState.noEnabled &&
                                                    //     ref.identifyQue2State.value != ButtonState.noEnabled &&
                                                    //     ref.identifyQue3State.value != ButtonState.noEnabled &&
                                                    //     ref.identifyQue4State.value != ButtonState.noEnabled) {
                                                    // } else {
                                                    // ref.infoIdentifyAudioUrl.value = null;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoIdentifyAudioUrl.value = null;
                                                    ref.isBarrierAudioUrlPaused.value = false;
                                                    await precacheImage(NetworkImage(ref.imageUrl), context);
                                                    await AppNavigation.nextScreen(
                                                      context,
                                                      BlocProvider.value(
                                                        value: ref,
                                                        child: MyLifestyleBarrierPage(imageUrl: ref.imageUrl,),
                                                      ),
                                                    );
                                                    // }
                                                  }
                                                },
                                                isBottom: true,
                                                color: context.themeColors.blueColor,
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
