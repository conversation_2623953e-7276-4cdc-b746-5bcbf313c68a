import 'dart:async';
import 'dart:ui' as ui;

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/lifestyle_repository/lifestyle_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_barrier_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_well_done_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'lifestyle_cubit.freezed.dart';
part 'lifestyle_state.dart';

class LifestyleCubit extends Cubit<LifestyleState> {
  LifestyleCubit() : super(const LifestyleState.initial()) {
    initializeBarrierData();

    // infoAudioUrl.value = AsLocaleKeys.lsMountainsAudio.tr();
    selectedDateController.text = formatDate(selectedDate);
    selectedHourController.text = selectedHour.toString();
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
    selectedBenefitValue.value = List.generate(beenfitList.length, (index) => false);
    lightGreenFlag = _loadImage('assets/icons/anim_asset/flag_light_green.png');
    darkGreenFlag = _loadImage('assets/icons/anim_asset/flag_dark_green.png');
  }

  late Future<ui.Image> lightGreenFlag;
  late Future<ui.Image> darkGreenFlag;
  //audio variable for situation screen
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AsLocaleKeys.lsMountainsAudio.tr());
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isLifeStyleManuallyPaused = ValueNotifier(false);
  // ValueNotifier<bool> isManuallyPaused2 = ValueNotifier(false);

  ValueNotifier<String?> infoIdentifyAudioUrl = ValueNotifier(AsLocaleKeys.lsIdentifyGoalAudio.tr());
  ValueNotifier<bool> isIdenitfyPaused = ValueNotifier(false);

  ValueNotifier<String?> infoBarrierAudioUrl = ValueNotifier(AsLocaleKeys.lsIdentifyBarrierAudio.tr());
  ValueNotifier<bool> isBarrierAudioUrlPaused = ValueNotifier(false);

  ValueNotifier<String?> infoOvercomeAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isOverComeAudioPaused = ValueNotifier(false);

  ValueNotifier<String?> infoBenefitAudioUrl = ValueNotifier(AsLocaleKeys.lsMotivateAudio.tr());
  ValueNotifier<bool> isBenefitAudioPaused = ValueNotifier(false);

  ValueNotifier<String?> infoWelldoneAudioUrl = ValueNotifier(AsLocaleKeys.lsSummaryAudioApp.tr());
  ValueNotifier<bool> isWelldonePaused = ValueNotifier(false);

  ValueNotifier<String?> infoActionAudioUrl = ValueNotifier(AsLocaleKeys.lsActionPlanAudio.tr());
  ValueNotifier<bool> isActionPlanPaused = ValueNotifier(false);

  ValueNotifier<int> selectedMountainIndex = ValueNotifier(-1);

  String imageUrl ='';
  CachedNetworkImage image = CachedNetworkImage(imageUrl: 'https://',);

  ValueNotifier<bool> q1InfoVisible = ValueNotifier(false);
  ValueNotifier<bool> q2InfoVisible = ValueNotifier(false);
  ValueNotifier<bool> isClicked = ValueNotifier(false);

  GlobalKey<FormState> globalKey = GlobalKey<FormState>();
  GlobalKey<FormState> globalKey3 = GlobalKey<FormState>();

  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isError = ValueNotifier(false);

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final scaffoldIdentifyKey = GlobalKey<ScaffoldState>();
  final scaffoldBarrierKey = GlobalKey<ScaffoldState>();
  final scaffoldOverComeKey = GlobalKey<ScaffoldState>();
  final scaffoldBenefitKey = GlobalKey<ScaffoldState>();
  final scaffoldWellDoneKey = GlobalKey<ScaffoldState>();
  final scaffoldActionPlanKey = GlobalKey<ScaffoldState>();

  DateTime selectedDate = DateTime.now();
  final DateTime startDate = DateTime.now();
  int selectedHour = 12;
  int selectedMinute = 0;

  final TextEditingController idetifyQue1Controller = TextEditingController();
  final TextEditingController idetifyQue2Controller = TextEditingController();
  final TextEditingController otherBarrierController = TextEditingController();
  final TextEditingController selectedDateController = TextEditingController();
  final TextEditingController selectedHourController = TextEditingController();
  final TextEditingController selectedMinuteController = TextEditingController();
  final TextEditingController whyQuestionController = TextEditingController();
  final TextEditingController whatQuestionController = TextEditingController();
  final TextEditingController whoQuestionController = TextEditingController();
  final TextEditingController otherBenefitController = TextEditingController();

  ValueNotifier<ButtonState> identifyQue1State = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> identifyQue2State = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> identifyQue3State = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> identifyQue4State = ValueNotifier(ButtonState.bothDisabled);

  ValueNotifier<String> selectBarrierValue = ValueNotifier('');
  ValueNotifier<List<bool>> selectedBenefitValue = ValueNotifier([]);

  LifestyleRepository repository = LifestyleRepository();
  AuthRepository authRepository = AuthRepository();

  final identifyQue1ControllerKey = GlobalKey();
  final identifyQue2ControllerKey = GlobalKey();
  final identifyQue1Key = GlobalKey();
  final identifyQue2Key = GlobalKey();
  final identifyQue3Key = GlobalKey();
  final identifyQue4Key = GlobalKey();
  final whyKey = GlobalKey();
  final whatKey = GlobalKey();
  final whoKey = GlobalKey();

  final List<Map<String, dynamic>> barrierDataList = [];

  List<String> barrierList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsIdentifyBarrierOptions,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  final List<String> beenfitList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsMotivateBenefits,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  Future<ui.Image> _loadImage(String imagePath) async {
    final bd = await rootBundle.load(imagePath);
    final bytes = Uint8List.view(bd.buffer);
    final codec = await ui.instantiateImageCodec(bytes, targetHeight: 30, targetWidth: 25, allowUpscaling: false);
    final image = (await codec.getNextFrame()).image;
    return image;
  }

  String getLastAudioFromJson(String jsonKey) {
    final dynamic audioList = jsonKey.tr();
    print('getLastAudioFromJson: value=$audioList, type=${audioList.runtimeType}');
    if (audioList is List && audioList.isNotEmpty) {
      return audioList.last.toString();
    }
    return "";
  }


  // String formatDate(DateTime date) {
  //   return DateFormat('EEE d').format(date).toUpperCase();
  // }
  String formatDate(DateTime date, {bool isDay = false}) {
    final localizedDays = (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsDays,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();

    final dayIndex = date.weekday % 7;

    final translatedDay = localizedDays[dayIndex];
    if (isDay) {
      return translatedDay;
    } else {
      return '$translatedDay ${date.day}';
    }
  }

  void incrementDate() {
    // Calculate days difference from the start date
    final daysDifference = selectedDate.difference(startDate).inDays;
    if (daysDifference < 8) {
      selectedDate = selectedDate.add(const Duration(days: 1));
    } else {
      // Reset to the start date if 8 days have passed
      selectedDate = startDate;
    }
    selectedDateController.text = formatDate(selectedDate); // Update the text field
  }

  void decrementDate() {
    final daysDifference = selectedDate.difference(startDate).inDays;
    if (daysDifference > 0) {
      selectedDate = selectedDate.subtract(const Duration(days: 1));
    } else {
      selectedDate = startDate.add(const Duration(days: 8));
    }
    selectedDateController.text = formatDate(selectedDate); // Update the text field
  }

  void incrementHour() {
    selectedHour = (selectedHour + 1) % 24;
    selectedHourController.text = selectedHour.toString();
  }

  // Decrement hour in 24-hour cycle
  void decrementHour() {
    selectedHour = (selectedHour - 1 + 24) % 24;
    selectedHourController.text = selectedHour.toString();
  }

  void incrementMinute() {
    selectedMinute = (selectedMinute + 5) % 60;
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
  }

  // Decrement minute by 5-minute intervals, cycling back at 0
  void decrementMinute() {
    selectedMinute = (selectedMinute - 5 + 60) % 60;
    selectedMinuteController.text = selectedMinute.toString().padLeft(2, '0');
  }
 
  void setSelectedMountain(int index) async{
    '///select'.logV;
    '///index = ${index}'.logV;

    selectedMountainIndex.value = index;
    imageUrl = await MyLifeStyle.getMountainImage(index);
    image = await CachedNetworkImage(imageUrl: imageUrl,);
    emit(const LifestyleState.initial());
  }

  String getMountainName(int index) {
    switch (index) {
      case 0:
        return AsLocaleKeys.lsMountainsOptionsEverestLabel.tr();
      case 1:
        return AsLocaleKeys.lsMountainsOptionsKilimanjaroLabel.tr();
      case 2:
        return AsLocaleKeys.lsMountainsOptionsMachuLabel.tr();
      case 3:
        '/////3'.logV;
        '/////${AsLocaleKeys.lsMountainsOptionsMatterhornLabel.tr()}'.logV;
        return AsLocaleKeys.lsMountainsOptionsMatterhornLabel.tr() == 'Cervino' || AsLocaleKeys.lsMountainsOptionsMatterhornLabel.tr() == 'Mont Cervin' 
            ? 'Matterhorn'
            : AsLocaleKeys.lsMountainsOptionsMatterhornLabel.tr();
      default:
        '/////4'.logV;
        return '';
    }
  }

  void initializeBarrierData() {
    final barrierAudioList = (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsOvercomeBarrierAudio,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();

    final barrierList1 = (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsIdentifyBarrierOptions,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();

    final barrierList = barrierList1.sublist(0, barrierList1.length - 1);

    // Add canSay and canDo data
    final barriers = DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsOvercomeBarrierBarriers,
      navigatorKey.currentContext!,
    ) as List;

    final canSayList = barriers.map((e) => (e['canSay'] as List).cast<String>()).toList();

    final canDoList = barriers.map((e) => (e['canDo'] as List).cast<String>()).toList();

    // Ensure data lengths match
    for (var i = 0; i < barrierList.length; i++) {
      barrierDataList.add({
        'barrier': barrierList[i],
        'audio': barrierAudioList[i],
        'canSay': canSayList[i],
        'canDo': canDoList[i],
      });
    }
  }

// Function to get BarrierData dynamically
  BarrierData getBarrierData(String selectedBarrier) {
    // Find the barrier in the list
    final barrierData = barrierDataList.firstWhere(
      (barrier) => barrier['barrier'] == selectedBarrier,
    );

    return BarrierData(
      audio: barrierData['audio'] as String,
      canSay: List<String>.from(barrierData['canSay'] as List<String>),
      canDo: List<String>.from(barrierData['canDo'] as List<String>),
    );
  }

  Future<void> lsActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
  }) async {
    isEmail ? emit(const LifestyleState.emailPdfLoading()) : emit(const LifestyleState.downloadPdfLoading());
    try {
      final response = await repository.lsActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Achieving your life goals.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      emit(const LifestyleState.initial());
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(const LifestyleState.initial());
    }
  }

  Future<void> lifestyleStrategyAPI({
    required LifestyleCubit ref,
    required BuildContext context,
    required String imageUrl,
  }) async {
    emit(const LifestyleState.loading());
    try {
      final localDateTime = DateTime(
        ref.selectedDate.year,
        ref.selectedDate.month,
        ref.selectedDate.day,
        ref.selectedHour,
        ref.selectedMinute,
      ).toLocal().toIso8601String();

      'formattedDate == ++ $localDateTime'.logD;

      final response = await repository.lifestyleStrategy(
        benefit: ref.selectedBenefitValue.value
            .asMap()
            .entries
            .where((entry) => entry.value) // Check if the value is true
            .map((entry) => entry.key) // Get the index
            .toList(),
        otherBenefitText: ref.otherBenefitController.text,
        context: context,
        mountainName: ref.getMountainName(ref.selectedMountainIndex.value),
        stepOnelifeGoal: ref.idetifyQue1Controller.text,
        stepOnenextStep: ref.idetifyQue2Controller.text,
        stepTwoBarrier: ref.barrierList.indexOf(ref.selectBarrierValue.value).toString(),
        stepThreeDate: localDateTime,
        typedBarrier: ref.otherBarrierController.text,
        whatText: ref.whatQuestionController.text,
        whoText: ref.whoQuestionController.text,
        whyText: ref.whyQuestionController.text,
      );

      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && (response.success ?? false) == true) {
        if (response.strategies != null) {
          Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;
          await authRepository.getUserData(context: context);
          if ((Injector.instance<AppDB>().userModel?.user.app?.alerts?.commitments ?? false) == true) {
            await BlocProvider.of<MyAlertCubit>(context).scheduleActivityNotifications();
          }
          ref.headerInfoText.value = null;
          ref.infoBenefitAudioUrl.value = null;
          ref.isWelldonePaused.value = false;

          await AppNavigation.nextScreen(
            context,
            BlocProvider.value(value: ref, child: MyLifestyleWellDonePage(imageUrl: imageUrl,)),
          );
        }
      } else {
        await authRepository.getUserData(context: context);
        await BlocProvider.of<MyAlertCubit>(context).scheduleActivityNotifications();
        ref.headerInfoText.value = null;
        ref.infoBenefitAudioUrl.value = null;
        ref.isWelldonePaused.value = false;

        //  ref.infoWelldoneAudioUrl.value = AsLocaleKeys.lsSummaryAudioApp.tr();
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(value: ref, child: MyLifestyleWellDonePage(imageUrl: imageUrl,)),
        );
      }
      emit(const LifestyleState.initial());
    } catch (e) {
      emit(const LifestyleState.initial());
    }
  }

  @override
  Future<void> close() {
    idetifyQue1Controller.dispose();
    idetifyQue2Controller.dispose();
    otherBarrierController.dispose();
    otherBenefitController.dispose();
    identifyQue1State.dispose();
    identifyQue2State.dispose();
    identifyQue3State.dispose();
    identifyQue4State.dispose();
    barrierList.clear();
    beenfitList.clear();
    q1InfoVisible.dispose();
    q2InfoVisible.dispose();
    infoAudioUrl.dispose();
    whatQuestionController.dispose();
    whyQuestionController.dispose();
    whoQuestionController.dispose();
    headerInfoText.dispose();
    isAudioPanelVisible.dispose();
    barrierDataList.clear();
    selectedDateController.dispose();
    selectedHourController.dispose();
    selectedMinuteController.dispose();
    selectedMountainIndex.dispose();
    barrierList.clear();

    return super.close();
  }
}
