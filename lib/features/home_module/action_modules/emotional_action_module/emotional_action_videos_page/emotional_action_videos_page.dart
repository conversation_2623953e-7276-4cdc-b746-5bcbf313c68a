import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_rate_after_page/emotional_action_rate_after_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_selected_videos_page/emotional_action_selected_video_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_videos_page/cubit/emotional_action_videos_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_videos_page/widgets/relaxing_scene_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/mountain_option_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmotionalActionVideosPage extends StatelessWidget {
  EmotionalActionVideosPage({
    required this.beforeRate,
    required this.infoAudioUrl1,
    super.key,
    this.isPhysicalSensation = false,
    this.audioUrl,
    this.isAudioPanelVisible,
  });
  final int beforeRate;
  final bool isPhysicalSensation;
  final String? audioUrl;
  final ValueNotifier<bool>? isAudioPanelVisible;
  final ValueNotifier<String?> infoAudioUrl1;
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
     bool isNavigating = false;
    return BlocProvider(
      create: (context) => EmotionalActionVideosPageCubit(isPhyscation: isPhysicalSensation,isSelectedVideoPage: false),
      child: BlocBuilder<EmotionalActionVideosPageCubit, EmotionalActionVideosPageState>(
        builder: (context, state) {
          final ref = context.read<EmotionalActionVideosPageCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.isClicked,
            builder: (context,value,child) {
              return ValueListenableBuilder(
                valueListenable: ref.infoAudioUrl,
                builder: (context, value, child) {
                  return AppScaffold(
                    scaffoldKey: _scaffoldKey,
                    isAudioPanelVisible: ref.isAudioPannelVisible,
                    infoAudioUrl: ref.infoAudioUrl,
                    isManuallyPaused: ref.isAudioPaused,
                    appBar: CommonAppBar(
                      onPrefixTap: () {
                        _scaffoldKey.currentState?.openDrawer();
                      },
                      // suffixIcon: Icon(
                      //   Icons.volume_up,
                      //   size: AppSize.h24,
                      //   color: context.themeColors.greyColor,
                      // ),
                      onSuffixTap: () {
                        if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                          ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                          isAudioPanelVisible?.value = !(isAudioPanelVisible?.value ?? false);
                        }
                      },
                    ),
                    drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                    body: ColoredBox(
                      color: context.themeColors.whiteColor,
                      child: Column(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(right: 0),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return CustomRawScrollbar(
                                    child: SingleChildScrollView(
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  InformationPageHeadingWidget(
                                                    onBackArrowTap: () {
                                                      Navigator.pop(context);
                                                      infoAudioUrl1.value = isPhysicalSensation
                                                          ? AsLocaleKeys.psRateBeforeAudio.tr()
                                                          : AsLocaleKeys.eiRateBeforeAudio.tr();
                                                    },
                                                    title: isPhysicalSensation
                                                        ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                                                        : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                                                    subtitle: isPhysicalSensation
                                                        ? AsLocaleKeys.psTitle.tr()
                                                        : AsLocaleKeys.eiTitle.tr(),
                                                    icon: isPhysicalSensation
                                                        ? Assets.icons.actionIcons.physicalSansations
                                                        : Assets.icons.actionIcons.emotionalImpact,
                                                    onInfoTap: () {
                                                      // if (isPhysicalSensation) {
                                                      //   // if (ref.infoAudioUrl.value ==
                                                      //   //     AsLocaleKeys.psInfoPanelsInformationAudio.tr()) {
                                                      //   //   ref.infoAudioUrl.value = null;
                                                      //   // } else {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                      //   // }
                                                      // } else {
                                                      //   // if (ref.infoAudioUrl.value ==
                                                      //   //     AsLocaleKeys.eiInfoPanelsInformationAudio.tr()) {
                                                      //   //   ref.infoAudioUrl.value = null;
                                                      //   // } else {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                      //   // }
                                                      // }
                                                      final info = isPhysicalSensation
                                                          ? (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.psInfoPanelsInformationText,
                                                              context,
                                                            ) as List)
                                                              .join('\n\n')
                                                          : (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.eiInfoPanelsInformationText,
                                                              context,
                                                            ) as List)
                                                              .join('\n\n');
                                                      // ref.headerInfoText.value =
                                                      //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      //             ref.headerInfoText.value == info)
                                                      //         ? null
                                                      //         : info;
                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isAudioPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = isPhysicalSensation
                                                            ? null
                                                            : AsLocaleKeys.eiSelectVideoAudio.tr();
                                                      } else {
                                                        ref.isAudioPaused.value = false;
                                                        if (isPhysicalSensation) {
                                                          ref.infoAudioUrl.value =
                                                              AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                        } else {
                                                          ref.infoAudioUrl.value =
                                                              AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                        }

                                                        ref.headerInfoText.value = info;
                                                      }
                                                    },
                                                    onLearnTap: () {
                                                      // if (isPhysicalSensation) {
                                                      //   // if (ref.infoAudioUrl.value ==
                                                      //   //     AsLocaleKeys.psInfoPanelsLearnAudio.tr()) {
                                                      //   //   ref.infoAudioUrl.value = null;
                                                      //   // } else {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                      //   // }
                                                      // } else {
                                                      //   // if (ref.infoAudioUrl.value ==
                                                      //   //     AsLocaleKeys.eiInfoPanelsLearnAudio.tr()) {
                                                      //   //   ref.infoAudioUrl.value = null;
                                                      //   // } else {
                                                      //   ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                      //   //  }
                                                      // }
                                                      final info = isPhysicalSensation
                                                          ? (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.psInfoPanelsLearnText,
                                                              context,
                                                            ) as List)
                                                              .join('\n\n')
                                                          : (DynamicAssetLoader.getNestedValue(
                                                              AsLocaleKeys.eiInfoPanelsLearnText,
                                                              context,
                                                            ) as List)
                                                              .join('\n\n');
                                                      // ref.headerInfoText.value =
                                                      //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      //             ref.headerInfoText.value == info)
                                                      //         ? null
                                                      //         : info;
                                                      if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                          ref.headerInfoText.value == info) {
                                                        ref.isAudioPaused.value = true;
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = isPhysicalSensation
                                                            ? null
                                                            : AsLocaleKeys.eiSelectVideoAudio.tr();
                                                      } else {
                                                        ref.isAudioPaused.value = false;
                                                        if (isPhysicalSensation) {
                                                          ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                        } else {
                                                          ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                        }

                                                        ref.headerInfoText.value = info;
                                                      }
                                                    },
                                                    infoWidget: ValueListenableBuilder(
                                                      valueListenable: ref.headerInfoText,
                                                      builder: (context, headerInfoTextV, _) {
                                                        return CustomInfoWidget(
                                                          onCloseTap: () {
                                                            ref.isAudioPaused.value = true;

                                                            ref.headerInfoText.value = null;
                                                            ref.infoAudioUrl.value = isPhysicalSensation
                                                                ? null
                                                                : AsLocaleKeys.eiSelectVideoAudio.tr();
                                                          },
                                                          visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                          margin: EdgeInsets.symmetric(
                                                            vertical: AppSize.h8,
                                                          ),
                                                          bodyText: headerInfoTextV,
                                                        );
                                                      },
                                                    ),
                                                  ),
                                                  if (!isPhysicalSensation)
                                                    Column(
                                                      children: [
                                                        AppTextWidget(
                                                          AsLocaleKeys.eiSelectVideoDescription.tr(),
                                                          style: context.textTheme.labelSmall?.copyWith(
                                                            fontSize: AppSize.sp13,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h16),
                                                        ValueListenableBuilder(
                                                          valueListenable: ref.selectedRiskySceneIndex,
                                                          builder: (context,selectedRiskySceneIndex,child) {
                                                            return Column(
                                                              children: [
                                                                Row(
                                                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                                  children: [
                                                                    Expanded(
                                                                      child: RelaxingSceneWidget(
                                                                        index: 0,
                                                                        imageUrl: AsLocaleKeys.eiVideoParkPreview.tr(),
                                                                        label:  AsLocaleKeys.eiSelectVideoLabelsPark.tr(),
                                                                        ref: ref,
                                                                      ),
                                                                    ),
                                                                    //  SpaceH(AppSize.w20),
                                                                    Expanded(
                                                                      child: RelaxingSceneWidget(
                                                                        index: 1,
                                                                        imageUrl: AsLocaleKeys.eiVideoBeachPreview.tr(),
                                                                        label: AsLocaleKeys.eiSelectVideoLabelsBeach.tr(),
                                                                        ref: ref,
                                                                        backgroundColor:
                                                                        ref.selectedRiskySceneIndex.value == 1
                                                                            ? Colors.green
                                                                            : Colors.lightGreen,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                if (ref.isClicked.value && ref.selectedRiskySceneIndex.value == -1)
                                                                    Padding(
                                                                      padding: EdgeInsets.only(left: AppSize.w4),
                                                                      child: CustomErrorWidget(
                                                                        errorMessgaeText: AsLocaleKeys.eiErrorsVideo.tr(),
                                                                      ),
                                                                    ),
                                                              ],
                                                            );
                                                          },
                                                        ),
                                                  /*      Align(
                                                          alignment: Alignment.centerLeft,
                                                          child: AppTextWidget(
                                                            AsLocaleKeys.eiSelectVideoLabelsPark.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontWeight: FontWeight.w500,
                                                            ),
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h8),
                                                        VideoPlayerScreen(
                                                          videoList: [AsLocaleKeys.eiVideoParkSrc.tr()],
                                                          onVideoEnded: () {
                                                            if (isPhysicalSensation) {
                                                              ref.infoAudioUrl.value = null;
                                                            } else {
                                                              ref.infoAudioUrl.value = AsLocaleKeys.eiSelectVideoAudio.tr();
                                                            }
                                                            AppNavigation.previousScreen(context);
                                                          },
                                                          imageList: [
                                                            AsLocaleKeys.eiVideoParkPoster.tr(),
                                                          ],
                                                          navigationFunction: () {},
                                                          onTap: () {
                                                            ref.infoAudioUrl.value = null;
                                                          },
                                                        ),
                                                        SpaceV(AppSize.h16),
                                                        Align(
                                                          alignment: Alignment.centerLeft,
                                                          child: AppTextWidget(
                                                            AsLocaleKeys.eiSelectVideoLabelsBeach.tr(),
                                                            style: context.textTheme.titleSmall?.copyWith(
                                                              fontWeight: FontWeight.w500,
                                                            ),
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h8),
                                                        VideoPlayerScreen(
                                                          videoList: [AsLocaleKeys.eiVideoBeachSrc.tr()],
                                                          onVideoEnded: () {
                                                            if (isPhysicalSensation) {
                                                              ref.infoAudioUrl.value = null;
                                                            } else {
                                                              ref.infoAudioUrl.value = AsLocaleKeys.eiSelectVideoAudio.tr();
                                                            }
                                                            AppNavigation.previousScreen(context);
                                                          },
                                                          imageList: [
                                                            AsLocaleKeys.eiVideoBeachPoster.tr(),
                                                          ],
                                                          navigationFunction: () {},
                                                          onTap: () {
                                                            ref.infoAudioUrl.value = null;
                                                          },
                                                        ),*/
                                                      ],
                                                    )
                                                  else
                                                    ListView.builder(
                                                      shrinkWrap: true,
                                                      physics: const BouncingScrollPhysics(),
                                                      itemCount: 3,
                                                      itemBuilder: (context, index) {
                                                        final list = DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psVideoTitles,
                                                          context,
                                                        ) as List;
                                                        final videosSrc = DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psVideosSrc,
                                                          context,
                                                        ) as List;
                                                        final videosList = videosSrc.map((e) {
                                                          return e;
                                                        }).toList();

                                                        return Column(
                                                          children: [
                                                            Align(
                                                              alignment: Alignment.centerLeft,
                                                              child: AppTextWidget(
                                                                list[index] as String,
                                                                style: context.textTheme.titleSmall?.copyWith(
                                                                  fontWeight: FontWeight.w500,
                                                                ),
                                                              ),
                                                            ),
                                                            SpaceV(AppSize.h8),
                                                            VideoPlayerScreen(
                                                              videoList: [videosList[index]['src'] as String],
                                                              imageList: [videosList[index]['poster'] as String],
                                                              navigationFunction: () {},
                                                              onTap: () {
                                                                ref.isAudioPaused.value = true;
                                                                //ref.infoAudioUrl.value = null;
                                                              },
                                                              onVideoEnded: () async {
  if (isNavigating) return; // 👈 Prevent multiple calls
  isNavigating = true;

  await Future.delayed(const Duration(milliseconds: 300));

  if (Navigator.of(context).canPop()) {
    AppNavigation.previousScreen(context);
  }

  isNavigating = false; // Reset for next time
},
isContain: true,
                                                            ),
                                                            SpaceV(AppSize.h16),
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                ],
                                              ),
                                              SpaceV(AppSize.h20),
                                              CustomButton(
                                                padding: EdgeInsets.zero,
                                                isBottom: true,
                                                title: CoreLocaleKeys.buttonsNext.tr(),
                                                color: context.themeColors.blueColor,
                                                onTap: () {
                                                  if(!isPhysicalSensation){
                                                    if (ref.selectedRiskySceneIndex.value == -1) {
                                                      ref.isClicked.value = true;
                                                    }else{
                                                      //ref.infoAudioUrl.value = null;
                                                      ref.isClicked.value = false;
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        BlocProvider.value(
                                                            value: ref,
                                                            child: EmotionalActionSelectedVideoPage(
                                                              isAudioPanelVisible: isAudioPanelVisible,
                                                              isPhysicalSensation: isPhysicalSensation,
                                                              beforeRate: beforeRate,
                                                            ),
                                                        )
                                                      );
                                                    }
                                                  } else{
                                                    //ref.infoAudioUrl.value = null;
                                                    AppNavigation.nextScreen(
                                                      context,
                                                      EmotionalActionRateAfterPage(
                                                        infoAudioUrl: ref.infoAudioUrl,
                                                        isSelectedVideo: false,
                                                        beforeRate: beforeRate,
                                                        isAudioPanelVisible: isAudioPanelVisible,
                                                        isPhysicalSensation: isPhysicalSensation,
                                                      ),
                                                    );
                                                  }

                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
