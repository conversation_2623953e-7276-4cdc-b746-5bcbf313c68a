import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'emotional_action_videos_page_cubit.freezed.dart';
part 'emotional_action_videos_page_state.dart';

class EmotionalActionVideosPageCubit extends Cubit<EmotionalActionVideosPageState> {
  EmotionalActionVideosPageCubit({required this.isPhyscation,this.isSelectedVideoPage = true} ) : super(const EmotionalActionVideosPageState.initial()) {
   isSelectedVideoPage?infoAudioUrl.value = '':infoAudioUrl.value = isPhyscation?'':AsLocaleKeys.eiSelectVideoAudio.tr();
  }
  final bool isPhyscation;
  final bool isSelectedVideoPage;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isAudioPaused = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<int> selectedRiskySceneIndex = ValueNotifier(-1);
  ValueNotifier<bool> isClicked = ValueNotifier(false);

  String imageUrl ='';
  CachedNetworkImage image = CachedNetworkImage(imageUrl: 'https://',);

  void setSelectedRelaxingScene(int index) async{
    '///select'.logV;
    '///index = ${index}'.logV;

    selectedRiskySceneIndex.value = index;
    imageUrl = await MyLifeStyle.getRelaxingSceneImage(index);
    image = await CachedNetworkImage(imageUrl: imageUrl,);
    emit(const EmotionalActionVideosPageState.initial());
  }


  @override
  Future<void> close() {
    infoAudioUrl.dispose();
  //  isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
