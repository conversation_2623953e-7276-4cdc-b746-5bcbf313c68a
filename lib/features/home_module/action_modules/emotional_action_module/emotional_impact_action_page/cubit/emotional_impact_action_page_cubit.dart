import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'emotional_impact_action_page_cubit.freezed.dart';
part 'emotional_impact_action_page_state.dart';

class EmotionalImpactActionPageCubit extends Cubit<EmotionalImpactActionPageState> {
  EmotionalImpactActionPageCubit({required this.isPhyscation}) : super(const EmotionalImpactActionPageState.initial()) {
    ' infoAudioUr'.logD;
    infoAudioUrl.value = isPhyscation ? AsLocaleKeys.psRateBeforeAudio.tr() : AsLocaleKeys.eiRateBeforeAudio.tr();
    isAudioPaused.value = false;
  }
  final bool isPhyscation;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isAudioPaused = ValueNotifier(false);
  ValueNotifier<bool> isEmotionalImpactButtonClick = ValueNotifier(false);
  ValueNotifier<bool> isPhysicalSensationButtonClick = ValueNotifier(false);
  ValueNotifier<int> emotionBeforeValue = ValueNotifier(-1);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    emotionBeforeValue.dispose();
    return super.close();
  }
}
