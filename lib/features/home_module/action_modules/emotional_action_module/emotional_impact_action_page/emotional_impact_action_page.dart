import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_videos_page/emotional_action_videos_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_impact_action_page/cubit/emotional_impact_action_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmotionalImpactActionPage extends StatelessWidget {
  EmotionalImpactActionPage({super.key, this.isPhysicalSensation = false});
  final bool isPhysicalSensation;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EmotionalImpactActionPageCubit(isPhyscation: isPhysicalSensation),
      child: BlocBuilder<EmotionalImpactActionPageCubit, EmotionalImpactActionPageState>(
        builder: (context, state) {
          final ref = context.read<EmotionalImpactActionPageCubit>();
          return PopScope(
            onPopInvokedWithResult: (didPop, result) {
              if (didPop) {
                ref.headerInfoText.value = null;
                ref.infoAudioUrl.value = AsLocaleKeys.lsSummaryAudioApp.tr();
              }
            },
            child: ValueListenableBuilder(
              valueListenable: ref.infoAudioUrl,
              builder: (context, value, child) {
                return AppScaffold(
                  isManuallyPaused: ref.isAudioPaused,
                  scaffoldKey: _scaffoldKey,
                  isAudioPanelVisible: ref.isAudioPannelVisible,
                  infoAudioUrl: ref.infoAudioUrl,
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      _scaffoldKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                      }
                    },
                  ),
                  drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                  body: ValueListenableBuilder(
                    valueListenable: ref.isEmotionalImpactButtonClick,
                    builder: (context, value, child) {
                      return ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Column(
                          children: [
                            Expanded(
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return SingleChildScrollView(
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              children: [
                                                InformationPageHeadingWidget(
                                                  title: isPhysicalSensation
                                                      ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                                                      : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                                                  subtitle: isPhysicalSensation
                                                      ? AsLocaleKeys.psTitle.tr()
                                                      : AsLocaleKeys.eiTitle.tr(),
                                                  icon: isPhysicalSensation
                                                      ? Assets.icons.actionIcons.physicalSansations
                                                      : Assets.icons.actionIcons.emotionalImpact,
                                                  onInfoTap: () {
                                                    final info = isPhysicalSensation
                                                        ? (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.psInfoPanelsInformationText,
                                                            context,
                                                          ) as List)
                                                            .join('\n\n')
                                                        : (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.eiInfoPanelsInformationText,
                                                            context,
                                                          ) as List)
                                                            .join('\n\n');

                                                    if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                        ref.headerInfoText.value == info) {
                                                      ref.isAudioPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      if (isPhysicalSensation) {
                                                        ref.infoAudioUrl.value = AsLocaleKeys.psRateBeforeAudio.tr();
                                                      } else {
                                                        ref.infoAudioUrl.value = AsLocaleKeys.eiRateBeforeAudio.tr();
                                                      }
                                                    } else {
                                                      ref.isAudioPaused.value = false;
                                                      if (isPhysicalSensation) {
                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                      } else {
                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                      }

                                                      ref.headerInfoText.value = info;
                                                    }
                                                  },
                                                  onLearnTap: () {
                                                    // if (isPhysicalSensation) {
                                                    //   if (ref.infoAudioUrl.value ==
                                                    //       AsLocaleKeys.psInfoPanelsLearnAudio.tr()) {
                                                    //     ref.infoAudioUrl.value = AsLocaleKeys.psRateBeforeAudio.tr();
                                                    //   } else {
                                                    //     ref.infoAudioUrl.value =
                                                    //         AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                    //   }
                                                    // } else {
                                                    //   if (ref.infoAudioUrl.value ==
                                                    //       AsLocaleKeys.eiInfoPanelsLearnAudio.tr()) {
                                                    //     ref.infoAudioUrl.value = AsLocaleKeys.eiRateBeforeAudio.tr();
                                                    //   } else {
                                                    //     ref.infoAudioUrl.value =
                                                    //         AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                    //   }
                                                    // }

                                                    final info = isPhysicalSensation
                                                        ? (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.psInfoPanelsLearnText,
                                                            context,
                                                          ) as List)
                                                            .join('\n\n')
                                                        : (DynamicAssetLoader.getNestedValue(
                                                            AsLocaleKeys.eiInfoPanelsLearnText,
                                                            context,
                                                          ) as List)
                                                            .join('\n\n');
                                                    if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                        ref.headerInfoText.value == info) {
                                                      ref.isAudioPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      if (isPhysicalSensation) {
                                                        ref.infoAudioUrl.value = AsLocaleKeys.psRateBeforeAudio.tr();
                                                      } else {
                                                        ref.infoAudioUrl.value = AsLocaleKeys.eiRateBeforeAudio.tr();
                                                      }
                                                    } else {
                                                      ref.isAudioPaused.value = false;
                                                      if (isPhysicalSensation) {
                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                      } else {
                                                        ref.infoAudioUrl.value =
                                                            AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                      }

                                                      ref.headerInfoText.value = info;
                                                    }
                                                  },
                                                  infoWidget: ValueListenableBuilder(
                                                    valueListenable: ref.headerInfoText,
                                                    builder: (context, headerInfoTextV, _) {
                                                      return CustomInfoWidget(
                                                        onCloseTap: () {
                                                          ref.isAudioPaused.value = true;

                                                          ref.infoAudioUrl.value = isPhysicalSensation
                                                              ? AsLocaleKeys.psRateBeforeAudio.tr()
                                                              : AsLocaleKeys.eiRateBeforeAudio.tr();

                                                          ref.headerInfoText.value = null;
                                                        },
                                                        visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                        margin: EdgeInsets.symmetric(
                                                          vertical: AppSize.h8,
                                                        ),
                                                        bodyText: headerInfoTextV,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                AppTextWidget(
                                                  !isPhysicalSensation
                                                      ? (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.eiRateBeforeDescription0,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n')
                                                      : (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psRateBeforeDescription0,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n'),
                                                  style: context.textTheme.labelSmall?.copyWith(
                                                    fontSize: AppSize.sp13,
                                                  ),
                                                ),
                                                SpaceV(AppSize.h8),
                                                SliderScreen(
                                                  selectedValue: ref.emotionBeforeValue,
                                                  firstText: !isPhysicalSensation
                                                      ? (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.eiRateBeforeLabels0,
                                                          context,
                                                        ) as List)
                                                          .first
                                                          .toString()
                                                      : (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psRateBeforeLabels0,
                                                          context,
                                                        ) as List)
                                                          .first
                                                          .toString(),
                                                  secondText: !isPhysicalSensation
                                                      ? (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.eiRateBeforeLabels0,
                                                          context,
                                                        ) as List)
                                                          .last
                                                          .toString()
                                                      : (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psRateBeforeLabels0,
                                                          context,
                                                        ) as List)
                                                          .last
                                                          .toString(),
                                                  changeTextColor: true,
                                                  reverseGradient: true,
                                                  onSelect: (value) {
                                                    ref.emotionBeforeValue.value = value;
                                                  },
                                                  isClick: ref.isEmotionalImpactButtonClick.value,
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h10),
                                            CustomButton(
                                              padding: EdgeInsets.zero,
                                              isBottom: true,
                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                              color: context.themeColors.blueColor,
                                              onTap: () {
                                                if (ref.emotionBeforeValue.value == -1) {
                                                  ref.isEmotionalImpactButtonClick.value = true;
                                                } else {
                                                  ref.infoAudioUrl.value = null;
                                                  if (!isPhysicalSensation) {
                                                    AppNavigation.nextScreen(
                                                      context,
                                                      EmotionalActionVideosPage(
                                                        isAudioPanelVisible: ref.isAudioPannelVisible,
                                                        beforeRate: ref.emotionBeforeValue.value,
                                                        audioUrl: IsLocaleKeys.summaryAudio.tr(),
                                                        infoAudioUrl1: ref.infoAudioUrl,
                                                      ),
                                                    );
                                                  } else {
                                                    AppNavigation.nextScreen(
                                                      context,
                                                      EmotionalActionVideosPage(
                                                        isAudioPanelVisible: ref.isAudioPannelVisible,
                                                        beforeRate: ref.emotionBeforeValue.value,
                                                        isPhysicalSensation: isPhysicalSensation,
                                                        audioUrl: IsLocaleKeys.summaryAudio.tr(),
                                                        infoAudioUrl1: ref.infoAudioUrl,
                                                      ),
                                                    );
                                                  }
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
