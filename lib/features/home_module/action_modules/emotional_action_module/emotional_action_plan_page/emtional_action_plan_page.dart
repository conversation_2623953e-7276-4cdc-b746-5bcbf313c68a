import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/custom_action_plan_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_plan_page/cubit/emtional_action_plan_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmtionalActionPlanPage extends StatelessWidget {
  const EmtionalActionPlanPage({super.key, this.isPhysicalSensation = false, this.isAudioPanelVisible});
  final bool isPhysicalSensation;
  final ValueNotifier<bool>? isAudioPanelVisible;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EmtionalActionPlanCubit(isPhyscation: isPhysicalSensation),
      child: BlocBuilder<EmtionalActionPlanCubit, EmtionalActionPlanState>(
        builder: (ctx, state) {
          final ref = ctx.read<EmtionalActionPlanCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                scaffoldKey: ref.scaffoldKey,
                isManuallyPaused: ref.isAudioPaused,
                isAudioPanelVisible: isAudioPanelVisible,
                infoAudioUrl: ref.infoAudioUrl,
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                      isAudioPanelVisible?.value = !(isAudioPanelVisible?.value ?? false);
                    }
                  },
                ),
                drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              InformationPageHeadingWidget(
                                                title: isPhysicalSensation
                                                    ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                                                    : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                                                subtitle: isPhysicalSensation
                                                    ? AsLocaleKeys.psTitle.tr()
                                                    : AsLocaleKeys.eiTitle.tr(),
                                                icon: isPhysicalSensation
                                                    ? Assets.icons.actionIcons.physicalSansations
                                                    : Assets.icons.actionIcons.emotionalImpact,
                                                onInfoTap: () {
                                                  // if (isPhysicalSensation) {
                                                  //   if (ref.infoAudioUrl.value ==
                                                  //       AsLocaleKeys.psInfoPanelsInformationAudio.tr()) {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.psActionPlanAudio.tr();
                                                  //   } else {
                                                  //     ref.infoAudioUrl.value =
                                                  //         AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                  //   }
                                                  // } else {
                                                  //   if (ref.infoAudioUrl.value ==
                                                  //       AsLocaleKeys.eiInfoPanelsInformationAudio.tr()) {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.eiActionPlanAudio.tr();
                                                  //   } else {
                                                  //     ref.infoAudioUrl.value =
                                                  //         AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                  //   }
                                                  // }
                                                  final info = isPhysicalSensation
                                                      ? (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psInfoPanelsInformationText,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n')
                                                      : (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.eiInfoPanelsInformationText,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n');
                                                  if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerInfoText.value == info) {
                                                    ref.isAudioPaused.value = true;
                                                    ref.headerInfoText.value = null;
                                                    if (isPhysicalSensation) {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.eiActionPlanAudio.tr();
                                                    } else {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.psActionPlanAudio.tr();
                                                    }
                                                  } else {
                                                    ref.isAudioPaused.value = false;
                                                    if (isPhysicalSensation) {
                                                      ref.infoAudioUrl.value =
                                                          AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                    } else {
                                                      ref.infoAudioUrl.value =
                                                          AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                    }

                                                    ref.headerInfoText.value = info;
                                                  }
                                                },
                                                onLearnTap: () {
                                                  // if (isPhysicalSensation) {
                                                  //   if (ref.infoAudioUrl.value ==
                                                  //       AsLocaleKeys.psInfoPanelsLearnAudio.tr()) {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.psActionPlanAudio.tr();
                                                  //   } else {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                  //   }
                                                  // } else {
                                                  //   if (ref.infoAudioUrl.value ==
                                                  //       AsLocaleKeys.eiInfoPanelsLearnAudio.tr()) {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.eiActionPlanAudio.tr();
                                                  //   } else {
                                                  //     ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                  //   }
                                                  // }

                                                  final info = isPhysicalSensation
                                                      ? (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.psInfoPanelsLearnText,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n')
                                                      : (DynamicAssetLoader.getNestedValue(
                                                          AsLocaleKeys.eiInfoPanelsLearnText,
                                                          context,
                                                        ) as List)
                                                          .join('\n\n');
                                                  if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerInfoText.value == info) {
                                                    ref.isAudioPaused.value = true;
                                                    ref.headerInfoText.value = null;
                                                    if (isPhysicalSensation) {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.eiActionPlanAudio.tr();
                                                    } else {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.psActionPlanAudio.tr();
                                                    }
                                                  } else {
                                                    ref.isAudioPaused.value = false;
                                                    if (isPhysicalSensation) {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                    } else {
                                                      ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                    }

                                                    ref.headerInfoText.value = info;
                                                  }
                                                },
                                                infoWidget: ValueListenableBuilder(
                                                  valueListenable: ref.headerInfoText,
                                                  builder: (context, headerInfoTextV, _) {
                                                    return CustomInfoWidget(
                                                      onCloseTap: () {
                                                        ref.headerInfoText.value = null;
                                                        ref.isAudioPaused.value = true;
                                                        if (isPhysicalSensation) {
                                                          ref.infoAudioUrl.value = AsLocaleKeys.psActionPlanAudio.tr();
                                                        } else {
                                                          ref.infoAudioUrl.value = AsLocaleKeys.eiActionPlanAudio.tr();
                                                        }
                                                      },
                                                      visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerInfoTextV,
                                                    );
                                                  },
                                                ),
                                              ),
                                              AppTextWidget(
                                                AsLocaleKeys.eiActionPlanTitle.tr(),
                                                style: context.textTheme.labelSmall?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              SpaceV(AppSize.h14),
                                              AppTextWidget(
                                                isPhysicalSensation
                                                    ? (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.psActionPlanText,
                                                        context,
                                                      ) as List)
                                                        .join('\n\n')
                                                    : (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.eiActionPlanText,
                                                        context,
                                                      ) as List)
                                                        .join('\n\n'),
                                                style: context.textTheme.labelSmall?.copyWith(
                                                  fontSize: AppSize.sp13,
                                                ),
                                              ),
                                              SpaceV(AppSize.h14),
                                              ListView.builder(
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                itemCount: isPhysicalSensation
                                                    ? ref.bulletPhysicalSensationList.length
                                                    : ref.bulletList.length,
                                                itemBuilder: (context, index) {
                                                  return Column(
                                                    children: [
                                                      CustomActionPlanTextWidget(
                                                        question: isPhysicalSensation
                                                            ? ref.bulletPhysicalSensationList[index]
                                                            : ref.bulletList[index],
                                                      ),
                                                      SpaceV(AppSize.h10),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                          SpaceV(AppSize.h10),
                                          CustomYesNoButton(
                                            padding: EdgeInsets.zero,
                                            isDownLoad: true,
                                            exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                            agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                            inNoProgress: ValueNotifier(
                                              state is LoadingDownloadPdfState || state is LoadingDownloadEmailState,
                                            ),
                                            onTapYes: () {
                                              '>?>?>? 10'.logV;
                                              ref.infoAudioUrl.value = null;
                                              AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                            },
                                            onDownloadTap: () {
                                              CustomDownloadPopup.buildPopupMenu(
                                                context: context,
                                                onDownLoadPdf: () async {
                                                  await ref.psAndeiActionStrategyforDownloadPdfApi(
                                                    context: context,
                                                    isPhysicalSensation: isPhysicalSensation,
                                                  );
                                                },
                                                onEmailDownload: () async {
                                                  await ref.psAndeiActionStrategyforEmailPdfApi(
                                                    context: context,
                                                    isPhysicalSensation: isPhysicalSensation,
                                                  );
                                                  if (!context.mounted) return;

                                                },
                                              );
                                            },
                                            onTapNo: () async {
                                              // Request permissions
                                            },
                                            noButtonColor: context.themeColors.orangeColor,
                                            isYesNoButton: true,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
