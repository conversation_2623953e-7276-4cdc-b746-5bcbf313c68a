import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/widet/accessbility_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_rate_after_page/emotional_action_repository/emotional_action_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'emtional_action_plan_state.dart';

class EmtionalActionPlanCubit extends Cubit<EmtionalActionPlanState> {
  EmtionalActionPlanCubit({required this.isPhyscation}) : super(EmtionalActionPlanInitial()) {
        infoAudioUrl.value = isPhyscation ? AsLocaleKeys.psActionPlanAudio.tr() : AsLocaleKeys.eiActionPlanAudio.tr();

  }
  final bool isPhyscation;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isAudioPaused = ValueNotifier(false);
  ValueNotifier<int> emotionValue = ValueNotifier(-1);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final EmotionalActionRepository repository = EmotionalActionRepository();

  final bulletList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.eiActionPlanBullets,
    navigatorKey.currentContext!,
  ) as List<dynamic>)
      .cast<String>();

  final bulletPhysicalSensationList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.psActionPlanBullets,
    navigatorKey.currentContext!,
  ) as List<dynamic>)
      .cast<String>();

  Future<void> psAndeiActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isPhysicalSensation,
  }) async {
    emit(LoadingDownloadPdfState());
    try {
      final response = await repository.psActionStrategy(
        context: context,
        isPhysicalSensation: isPhysicalSensation,
      );
      if (response != null && response.data!['success'] == true) {
        if (response.data!['pdf'] != null) {
          final encodedStr = response.data?['pdf'];
          'downnnnnnnnnnnnnnnload'.logD;
          await FilesDownload.downloadAndOpenPdf(
            encodedStr as String,
            isPhysicalSensation ? 'Surfing_your_cravings_and_urges.pdf' : 'Shifting your focus.pdf',
          );
        }
      }
      emit(EmtionalActionPlanInitial());
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(EmtionalActionPlanInitial());
    }
  }

  Future<void> psAndeiActionStrategyforEmailPdfApi({
    required BuildContext context,
    required bool isPhysicalSensation,
  }) async {
    emit(LoadingDownloadEmailState());
    try {
      final response = await repository.psAndeiActionStrategyforEmail(
        context: context,
        isPhysicalSensation: isPhysicalSensation,
      );
      if (response != null && response.data!['success'] == true) {
        // CustomSnackbar.showSucessSnackBar(
        //   message: 'Email sent successfully',
        // );
      }
      emit(EmtionalActionPlanInitial());
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(EmtionalActionPlanInitial());
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    emotionValue.dispose();
    headerInfoText.dispose();
    emotionValue.dispose();
    return super.close();
  }
}
