import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'emotional_well_done_state.dart';

class EmotionalWellDoneCubit extends Cubit<EmotionalWellDoneState> {
  EmotionalWellDoneCubit({required this.isPhyscation}) : super(EmotionalWellDoneInitial()) {
    // infoWellDoneAudioUrl.value = AsLocaleKeys.eiSummaryAudio.tr();
    infoWellDoneAudioUrl.value = isPhyscation ? AsLocaleKeys.psSummaryAudio.tr() : AsLocaleKeys.eiSummaryAudio.tr();
  }
  final bool isPhyscation;

  final scaffoldWellDoneKey = GlobalKey<ScaffoldState>();
  ValueNotifier<String?> infoWellDoneAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioWellDonePanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isAudioManullyPaused = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  @override
  Future<void> close() {
    infoWellDoneAudioUrl.dispose();
    // isAudioWellDonePanelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
