import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/common_well_done_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_plan_page/emtional_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_well_done_page/cubit/emotional_well_done_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmotionalWellDonePage extends StatelessWidget {
  EmotionalWellDonePage({
    required this.infoAudioUrl,
    required this.rateAfter,
    required this.rateBefore,
    super.key,
    this.isPhysicalSensation = false,
    this.isAudioPanelVisible,
  });
  final bool isPhysicalSensation;
  final controller = ConfettiController();
  final ValueNotifier<bool>? isAudioPanelVisible;
  final ValueNotifier<String?> infoAudioUrl;
  final int rateAfter;
  final int rateBefore;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EmotionalWellDoneCubit(isPhyscation: isPhysicalSensation),
      child: BlocBuilder<EmotionalWellDoneCubit, EmotionalWellDoneState>(
        builder: (ctx, state) {
          final ref = ctx.read<EmotionalWellDoneCubit>();

          return ValueListenableBuilder(
            valueListenable: ref.infoWellDoneAudioUrl,
            builder: (context, value, child) {
              return CommonWellDoneScreen(
                controller: controller,
                title: isPhysicalSensation
                    ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                    : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                subtitle: isPhysicalSensation ? AsLocaleKeys.psTitle.tr() : AsLocaleKeys.eiTitle.tr(),
                icon: isPhysicalSensation
                    ? Assets.icons.actionIcons.physicalSansations
                    : Assets.icons.actionIcons.emotionalImpact,
                isAudioPannelVisible: isAudioPanelVisible,
                infoAudioUrl: ref.infoWellDoneAudioUrl,
                isManuallyPaused: ref.isAudioManullyPaused,
                scaffoldKey: ref.scaffoldWellDoneKey,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldWellDoneKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldWellDoneKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoWellDoneAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioWellDonePanelVisible.value = !ref.isAudioWellDonePanelVisible.value;
                      isAudioPanelVisible?.value = !(isAudioPanelVisible?.value ?? false);
                    }
                  },
                ),
                wellDoneTitleText:
                    !isPhysicalSensation ? AsLocaleKeys.eiSummaryTitle.tr() : AsLocaleKeys.psSummaryTitle.tr(),
                onInfoTap: () {
                  // if (isPhysicalSensation) {
                  //   if (ref.infoWellDoneAudioUrl.value == AsLocaleKeys.psInfoPanelsInformationAudio.tr()) {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psSummaryAudio.tr();
                  //   } else {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                  //   }
                  // } else {
                  //   if (ref.infoWellDoneAudioUrl.value == AsLocaleKeys.eiInfoPanelsInformationAudio.tr()) {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiSummaryAudio.tr();
                  //   } else {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                  //   }
                  // }
                  final info = isPhysicalSensation
                      ? (DynamicAssetLoader.getNestedValue(
                          AsLocaleKeys.psInfoPanelsInformationText,
                          context,
                        ) as List)
                          .join('\n\n')
                      : (DynamicAssetLoader.getNestedValue(
                          AsLocaleKeys.eiInfoPanelsInformationText,
                          context,
                        ) as List)
                          .join('\n\n');
                  // ref.headerInfoText.value =
                  //     (ref.headerInfoText.value.isNotEmptyAndNotNull && ref.headerInfoText.value == info) ? null : info;
                  if (ref.headerInfoText.value.isNotEmptyAndNotNull && ref.headerInfoText.value == info) {
                    ref.isAudioManullyPaused.value = true;
                    ref.headerInfoText.value = null;
                    if (isPhysicalSensation) {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiSummaryAudio.tr();
                    } else {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psSummaryAudio.tr();
                    }
                  } else {
                    ref.isAudioManullyPaused.value = false;
                    if (isPhysicalSensation) {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                    } else {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                    }

                    ref.headerInfoText.value = info;
                  }
                },
                onBackArrowTap: () {
                  infoAudioUrl.value = rateAfter < rateBefore
                      ? isPhysicalSensation
                          ? AsLocaleKeys.psCompareAudioImproved.tr()
                          : AsLocaleKeys.eiCompareAudioImproved.tr()
                      : isPhysicalSensation
                          ? AsLocaleKeys.psCompareAudioNotImproved.tr()
                          : AsLocaleKeys.eiCompareAudioNotImproved.tr();
                  Navigator.pop(context);
                },
                onLearnTap: () {
                  // if (isPhysicalSensation) {
                  //   if (ref.infoWellDoneAudioUrl.value == AsLocaleKeys.psInfoPanelsLearnAudio.tr()) {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psSummaryAudio.tr();
                  //   } else {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                  //   }
                  // } else {
                  //   if (ref.infoWellDoneAudioUrl.value == AsLocaleKeys.eiInfoPanelsLearnAudio.tr()) {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiSummaryAudio.tr();
                  //   } else {
                  //     ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                  //   }
                  // }
                  final info = isPhysicalSensation
                      ? (DynamicAssetLoader.getNestedValue(
                          AsLocaleKeys.psInfoPanelsLearnText,
                          context,
                        ) as List)
                          .join('\n\n')
                      : (DynamicAssetLoader.getNestedValue(
                          AsLocaleKeys.eiInfoPanelsLearnText,
                          context,
                        ) as List)
                          .join('\n\n');
                  if (ref.headerInfoText.value.isNotEmptyAndNotNull && ref.headerInfoText.value == info) {
                    ref.isAudioManullyPaused.value = true;
                    ref.headerInfoText.value = null;
                    if (isPhysicalSensation) {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiSummaryAudio.tr();
                    } else {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psSummaryAudio.tr();
                    }
                  } else {
                    ref.isAudioManullyPaused.value = false;
                    if (isPhysicalSensation) {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                    } else {
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                    }

                    ref.headerInfoText.value = info;
                  }
                },
                onButtonTap: () {
                  ref.infoWellDoneAudioUrl.value = null;
                  AppNavigation.nextScreen(
                    context,
                    EmtionalActionPlanPage(
                      isPhysicalSensation: isPhysicalSensation,
                      isAudioPanelVisible: isAudioPanelVisible,
                    ),
                  );
                },
                infoWidget: ValueListenableBuilder(
                  valueListenable: ref.headerInfoText,
                  builder: (context, headerInfoTextV, _) {
                    return CustomInfoWidget(
                      onCloseTap: () {
                        ref.isAudioManullyPaused.value = true;

                        ref.headerInfoText.value = null;
                        // ref.infoWellDoneAudioUrl.value = null;

                        ref.infoWellDoneAudioUrl.value =
                            isPhysicalSensation ? AsLocaleKeys.psSummaryAudio.tr() : AsLocaleKeys.eiSummaryAudio.tr();
                      },
                      visible: headerInfoTextV.isNotEmptyAndNotNull,
                      margin: EdgeInsets.symmetric(
                        vertical: AppSize.h8,
                      ),
                      bodyText: headerInfoTextV,
                    );
                  },
                ),
                wellDoneDetailText: !isPhysicalSensation
                    ? (DynamicAssetLoader.getNestedValue(
                        AsLocaleKeys.eiSummaryText,
                        context,
                      ) as List)
                        .join('\n\n')
                    : (DynamicAssetLoader.getNestedValue(
                        AsLocaleKeys.psSummaryText,
                        context,
                      ) as List)
                        .join('\n\n'),
              );
            },
          );
        },
      ),
    );
  }
}
