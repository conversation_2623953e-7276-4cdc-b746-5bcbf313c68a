import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_rate_after_page/emotional_action_repository/emotional_action_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_well_done_page/emotional_well_done_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'emotional_action_compare_state.dart';

class EmotionalActionCompareCubit extends Cubit<EmotionalActionCompareState> {
  EmotionalActionCompareCubit({required this.beforeRate, required this.afterRate, required this.isPhyscation}) : super(EmotionalActionCompareInitial()) {

    infoAudioUrl.value = afterRate < beforeRate?
        isPhyscation ?
          AsLocaleKeys.psCompareAudioImproved.tr() : AsLocaleKeys.eiCompareAudioImproved.tr():
        isPhyscation ? AsLocaleKeys.psCompareAudioNotImproved.tr() : AsLocaleKeys.eiCompareAudioNotImproved.tr();
  }
  final bool isPhyscation;
  final int beforeRate;
  final int afterRate;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isAudioPaused = ValueNotifier(false);
  ValueNotifier<int> emotionValue = ValueNotifier(-1);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  final EmotionalActionRepository repository = EmotionalActionRepository();

  Future<void> emotionalImpactandphysicalSensationStrategyAPI({
    required int rateAfter,
    required int rateBefore,
    required BuildContext context,
    required bool isPhysicalSensation,
    required ValueNotifier<bool>? isAudioPanelVisible,
    required ValueNotifier<String?> infoAudioUrl,
  }) async {
    emit(LoadingEmotionalState());
    try {
      final response = await repository.emotionalImpactandphysicalSensationStrategy(
        type: isPhysicalSensation ? 'psAS' : 'eiAS',
        rateAfter: rateAfter,
        rateBefore: rateBefore,
        context: navigatorKey.currentContext!,
        isPhysicalSensation: isPhysicalSensation,
      );
      // response.logD;
      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && (response.success ?? false) == true) {
        if (response.strategies != null) {
          Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;
          'User data ===> ${Injector.instance<AppDB>().userModel?.user.strategies?.psAs}'.logD;
        }

        await AppNavigation.nextScreen(
          context,
          EmotionalWellDonePage(
            isPhysicalSensation: isPhysicalSensation,
            isAudioPanelVisible:isAudioPanelVisible,
            infoAudioUrl: infoAudioUrl,
            rateAfter: rateAfter,
            rateBefore: rateBefore,
          ),
        );
      } else {
        await AppNavigation.nextScreen(
          context,
          EmotionalWellDonePage(
            isPhysicalSensation: isPhysicalSensation,
            isAudioPanelVisible:isAudioPanelVisible,
            infoAudioUrl: infoAudioUrl,
            rateAfter: rateAfter,
            rateBefore: rateBefore,
          ),
        );
      }
      emit(EmotionalActionCompareInitial());
    } catch (e) {
      emit(EmotionalActionCompareInitial());
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    emotionValue.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
