import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class BeforeAfterScoreWidget extends StatelessWidget {
  const BeforeAfterScoreWidget({
    super.key,
    this.color,
    this.isBefore = true,
    this.score = 0,
  });
  final Color? color;
  final bool isBefore;
  final int score;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppTextWidget(
          isBefore ? AsLocaleKeys.eiCompareBefore.tr() : AsLocaleKeys.eiCompareAfter.tr(),
          style: context.textTheme.titleSmall?.copyWith(
            color: color,fontSize: AppSize.sp16,
            fontWeight: FontWeight.w500,
          ),
        ),
        AppTextWidget(
          '$score',
          style: context.textTheme.headlineLarge?.copyWith(
            fontSize: AppSize.sp50,
            color: color,
          ),
        ),
      ],
    );
  }
}
