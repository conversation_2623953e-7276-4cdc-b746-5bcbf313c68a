import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_compare_page/cubit/emotional_action_compare_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_compare_page/widget/before_after_score_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmotionalActionComparePage extends StatelessWidget {
  EmotionalActionComparePage({
    required this.beforeRate,
    required this.affterRate,
    required this.infoAudioUrl,
    super.key,
    this.isPhysicalSensation = false,
    this.isAudioPanelVisible,
  });

  final int beforeRate;
  final int affterRate;
  final bool isPhysicalSensation;
  final ValueNotifier<bool>? isAudioPanelVisible;
  final ValueNotifier<String?> infoAudioUrl;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          EmotionalActionCompareCubit(isPhyscation: isPhysicalSensation, beforeRate: beforeRate, afterRate: affterRate),
      child: BlocBuilder<EmotionalActionCompareCubit, EmotionalActionCompareState>(
        builder: (context, state) {
          final ref = context.read<EmotionalActionCompareCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return ValueListenableBuilder(
                valueListenable: ref.isAudioPaused,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state is LoadingEmotionalState,
                    child: AppScaffold(
                      scaffoldKey: _scaffoldKey,
                      isAudioPanelVisible: ref.isAudioPannelVisible,
                      infoAudioUrl: ref.infoAudioUrl,
                      isManuallyPaused: ref.isAudioPaused,
                      appBar: CommonAppBar(
                        onPrefixTap: () {
                          _scaffoldKey.currentState?.openDrawer();
                        },
                        onSuffixTap: () {
                          if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                            ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                            isAudioPanelVisible?.value = !(isAudioPanelVisible?.value ?? false);
                          }
                        },
                      ),
                      drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                      body: ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Column(
                          children: [
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                    return CustomRawScrollbar(
                                      child: SingleChildScrollView(
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(
                                            minHeight: constraints.maxHeight,
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                              vertical: AppSize.h24,
                                              horizontal: AppSize.w24,
                                            ),
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  children: [
                                                    InformationPageHeadingWidget(
                                                      onBackArrowTap: () {
                                                        Navigator.pop(context);
                                                        infoAudioUrl.value = isPhysicalSensation
                                                            ? AsLocaleKeys.psRateAfterAudio.tr()
                                                            : AsLocaleKeys.eiRateAfterAudio.tr();
                                                      },
                                                      title: isPhysicalSensation
                                                          ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                                                          : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                                                      subtitle: isPhysicalSensation
                                                          ? AsLocaleKeys.psTitle.tr()
                                                          : AsLocaleKeys.eiTitle.tr(),
                                                      icon: isPhysicalSensation
                                                          ? Assets.icons.actionIcons.physicalSansations
                                                          : Assets.icons.actionIcons.emotionalImpact,
                                                      onInfoTap: () {
                                                        // if (isPhysicalSensation) {
                                                        //   if (ref.infoAudioUrl.value ==
                                                        //       AsLocaleKeys.psInfoPanelsInformationAudio.tr()) {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.psCompareAudioImproved.tr();
                                                        //   } else {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                        //   }
                                                        // } else {
                                                        //   if (ref.infoAudioUrl.value ==
                                                        //       AsLocaleKeys.eiInfoPanelsInformationAudio.tr()) {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.eiCompareAudioImproved.tr();
                                                        //   } else {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                        //   }
                                                        // }
                                                        final info = isPhysicalSensation
                                                            ? (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.psInfoPanelsInformationText,
                                                                context,
                                                              ) as List)
                                                                .join('\n\n')
                                                            : (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.eiInfoPanelsInformationText,
                                                                context,
                                                              ) as List)
                                                                .join('\n\n');
                                                    
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isAudioPaused.value = true;
                                                          ref.headerInfoText.value = null;
                                                          if (isPhysicalSensation) {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.psCompareAudioImproved.tr();
                                                          } else {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.eiCompareAudioImproved.tr();
                                                          }
                                                        } else {
                                                          ref.isAudioPaused.value = false;
                                                          if (isPhysicalSensation) {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                          } else {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                          }
                  
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      onLearnTap: () {
                                                        // if (isPhysicalSensation) {
                                                        //   if (ref.infoAudioUrl.value ==
                                                        //       AsLocaleKeys.psInfoPanelsLearnAudio.tr()) {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.psCompareAudioImproved.tr();
                                                        //   } else {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                        //   }
                                                        // } else {
                                                        //   if (ref.infoAudioUrl.value ==
                                                        //       AsLocaleKeys.eiInfoPanelsLearnAudio.tr()) {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.eiCompareAudioImproved.tr();
                                                        //   } else {
                                                        //     ref.infoAudioUrl.value =
                                                        //         AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                        //   }
                                                        // }
                  
                                                        final info = isPhysicalSensation
                                                            ? (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.psInfoPanelsLearnText,
                                                                context,
                                                              ) as List)
                                                                .join('\n\n')
                                                            : (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.eiInfoPanelsLearnText,
                                                                context,
                                                              ) as List)
                                                                .join('\n\n');
                                                        // ref.headerInfoText.value =
                                                        //     (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                        //             ref.headerInfoText.value == info)
                                                        //         ? null
                                                        //         : info;
                                                        if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                            ref.headerInfoText.value == info) {
                                                          ref.isAudioPaused.value = true;
                                                          ref.headerInfoText.value = null;
                                                          if (isPhysicalSensation) {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.psCompareAudioImproved.tr();
                                                          } else {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.eiCompareAudioImproved.tr();
                                                          }
                                                        } else {
                                                          ref.isAudioPaused.value = false;
                                                          if (isPhysicalSensation) {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                          } else {
                                                            ref.infoAudioUrl.value =
                                                                AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                          }
                  
                                                          ref.headerInfoText.value = info;
                                                        }
                                                      },
                                                      infoWidget: ValueListenableBuilder(
                                                        valueListenable: ref.headerInfoText,
                                                        builder: (
                                                          context,
                                                          headerInfoTextV,
                                                          _,
                                                        ) {
                                                          return CustomInfoWidget(
                                                            onCloseTap: () {
                                                              ref.isAudioPaused.value = true;
                                                              ref.headerInfoText.value = null;
                                                              if (isPhysicalSensation) {
                                                                // ref.infoAudioUrl.value =
                                                                //     AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                              } else {
                                                                // ref.infoAudioUrl.value =
                                                                //     AsLocaleKeys.eiCompareAudioImproved.tr();
                                                              }
                                                            },
                                                            visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                            margin: EdgeInsets.symmetric(
                                                              vertical: AppSize.h8,
                                                            ),
                                                            bodyText: headerInfoTextV,
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                    if (!isPhysicalSensation)
                                                      AppTextWidget(
                                                        AsLocaleKeys.eiComparePreText.tr(),
                                                        style: context.textTheme.labelSmall?.copyWith(
                                                          fontSize: AppSize.sp13,
                                                        ),
                                                      )
                                                    else
                                                      AppTextWidget(
                                                        AsLocaleKeys.psComparePreText.tr(),
                                                        style: context.textTheme.labelSmall?.copyWith(
                                                          fontSize: AppSize.sp13,
                                                        ),
                                                      ),
                                                    SpaceV(AppSize.h30),
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                      children: [
                                                        BeforeAfterScoreWidget(
                                                          score: beforeRate,
                                                          color: beforeRate <= 3
                                                              ? context.themeColors.greenColor
                                                              : beforeRate < 7
                                                                  ? context.themeColors.orangeColor
                                                                  : context.themeColors.redColor,
                                                        ),
                                                        BeforeAfterScoreWidget(
                                                          isBefore: false,
                                                          score: affterRate,
                                                          color: affterRate <= 3
                                                              ? context.themeColors.greenColor
                                                              : affterRate < 7
                                                                  ? context.themeColors.orangeColor
                                                                  : context.themeColors.redColor,
                                                        ),
                                                      ],
                                                    ),
                                                    SpaceV(AppSize.h30),
                                                    AppTextWidget(
                                                      affterRate < beforeRate // Todo: compare with previous result
                                                          // ignore: dead_code
                                                          ? isPhysicalSensation
                                                              ? (DynamicAssetLoader.getNestedValue(
                                                                  AsLocaleKeys.psComparePostTextImproved,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n')
                                                              : (DynamicAssetLoader.getNestedValue(
                                                                  AsLocaleKeys.eiComparePostTextImproved,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n')
                                                          : isPhysicalSensation
                                                              ? (DynamicAssetLoader.getNestedValue(
                                                                  AsLocaleKeys.psComparePostTextNotImproved,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n')
                                                              : (DynamicAssetLoader.getNestedValue(
                                                                  AsLocaleKeys.eiComparePostTextNotImproved,
                                                                  context,
                                                                ) as List)
                                                                  .join('\n\n'),
                                                      style: context.textTheme.labelSmall?.copyWith(
                                                        fontSize: AppSize.sp13,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SpaceV(AppSize.h12),
                                                CustomButton(
                                                  padding: EdgeInsets.zero,
                                                  isBottom: true,
                                                  title: CoreLocaleKeys.buttonsNext.tr(),
                                                  color: context.themeColors.blueColor,
                                                  inProgress: state is LoadingEmotionalState,
                                                  onTap: () async {
                                                    ref.infoAudioUrl.value = null;
                                                    await ref.emotionalImpactandphysicalSensationStrategyAPI(
                                                      rateAfter: affterRate,
                                                      rateBefore: beforeRate,
                                                      context: context,
                                                      isPhysicalSensation: isPhysicalSensation,
                                                      isAudioPanelVisible: isAudioPanelVisible,
                                                      infoAudioUrl: ref.infoAudioUrl,
                                                    );
                                                    ref.isAudioPaused.value = false;
                                                    // if (ref.emotionValue.value == -1) {
                                                    //   CustomSnackbar.showErrorSnackBar(
                                                    //     message: AsLocaleKeys.eiErrorsRequired.tr(),
                                                    //   );
                                                    // } else {
                                                    //   // AppNavigation.nextScreen(
                                                    //   //   context,
                                                    //   //   EmotionalActionVideosPage(),
                                                    //   // );
                                                    // }
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
