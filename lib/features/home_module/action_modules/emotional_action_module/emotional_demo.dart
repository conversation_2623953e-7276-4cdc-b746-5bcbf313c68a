// import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
// import 'package:flutter/material.dart';

// class MyWidget extends StatelessWidget {
//   const MyWidget({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Localization Test')),
//       body: FutureBuilder<dynamic>(
//         future: DynamicAssetLoader.getNestedValue('checkin.welcome.text.optional', context),
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }
//           if (snapshot.hasError) {
//             return Center(child: Text('Error: ${snapshot.error}'));
//           }
//           if (snapshot.hasData) {
//             final data = snapshot.data;
//             if (data is List) {
//               return ListView(
//                 children: data.map((item) => Text(item.toString())).toList(),
//               );
//             } else {
//               return Center(child: Text(data.toString()));
//             }
//           }
//           return const Center(child: Text('No data found'));
//         },
//       ),
//     );
//   }
// }
