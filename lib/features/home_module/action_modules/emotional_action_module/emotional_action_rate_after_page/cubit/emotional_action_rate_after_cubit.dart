import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'emotional_action_rate_after_state.dart';

class EmotionalActionRateAfterCubit extends Cubit<EmotionalActionRateAfterState> {
  EmotionalActionRateAfterCubit({required this.isPhyscation}) : super(EmotionalActionRateAfterInitial()) {

      infoAudioUrl.value =
        isPhyscation ? AsLocaleKeys.psRateAfterAudio.tr() : AsLocaleKeys.eiRateAfterAudio.tr();
  }
  final bool isPhyscation;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isRateAfterAudioPaused = ValueNotifier(false);
  ValueNotifier<bool> isRateAfterButtonClick = ValueNotifier(false);
  ValueNotifier<int> emotionAfterValue = ValueNotifier(-1);
  ValueNotifier<int> emotionBeforerValue = ValueNotifier(-1);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    emotionAfterValue.dispose();
    headerInfoText.dispose();
    emotionBeforerValue.dispose();
    return super.close();
  }
}
