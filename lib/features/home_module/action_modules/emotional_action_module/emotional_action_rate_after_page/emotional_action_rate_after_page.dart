
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_compare_page/emotional_action_compare_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_rate_after_page/cubit/emotional_action_rate_after_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmotionalActionRateAfterPage extends StatelessWidget {
  EmotionalActionRateAfterPage({
    required this.beforeRate,
    required this.infoAudioUrl,
    required this.isPhysicalSensation, required this.isSelectedVideo, super.key,
    this.isAudioPanelVisible,
  });
  final int beforeRate;
  final bool isPhysicalSensation;
  final ValueNotifier<bool>? isAudioPanelVisible;
  final ValueNotifier<String?> infoAudioUrl;
  final bool isSelectedVideo;

  final _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => EmotionalActionRateAfterCubit(isPhyscation: isPhysicalSensation),
      child: BlocBuilder<EmotionalActionRateAfterCubit, EmotionalActionRateAfterState>(
        builder: (context, state) {
          final ref = context.read<EmotionalActionRateAfterCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                scaffoldKey: _scaffoldKey,
                isManuallyPaused: ref.isRateAfterAudioPaused,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoAudioUrl,
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    _scaffoldKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                      isAudioPanelVisible?.value = !(isAudioPanelVisible?.value ?? false);
                    }
                  },
                ),
                drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return SingleChildScrollView(
                              child: ConstrainedBox(
                                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        children: [
                                          InformationPageHeadingWidget(
                                            onBackArrowTap: () {
                                              Navigator.pop(context);
                                              if(isSelectedVideo) {
                                                infoAudioUrl.value = null;
                                              } else {
                                                isPhysicalSensation?infoAudioUrl.value = null : infoAudioUrl.value = AsLocaleKeys.eiSelectVideoAudio.tr();
                                              }
                                            },
                                            title: isPhysicalSensation
                                                ? CoreLocaleKeys.titlesActionStrategiesPhysicalSensations.tr()
                                                : CoreLocaleKeys.titlesActionStrategiesEmotionalImpact.tr(),
                                            subtitle: isPhysicalSensation
                                                ? AsLocaleKeys.psTitle.tr()
                                                : AsLocaleKeys.eiTitle.tr(),
                                            icon: isPhysicalSensation
                                                ? Assets.icons.actionIcons.physicalSansations
                                                : Assets.icons.actionIcons.emotionalImpact,
                                            onInfoTap: () {
                                              // if (isPhysicalSensation) {
                                              //   if (ref.infoAudioUrl.value ==
                                              //       AsLocaleKeys.psInfoPanelsInformationAudio.tr()) {
                                              //     ref.infoAudioUrl.value = AsLocaleKeys.psRateAfterAudio.tr();
                                              //   } else {
                                              //     ref.infoAudioUrl.value =
                                              //         AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                              //   }
                                              // } else {
                                              //   if (ref.infoAudioUrl.value ==
                                              //       AsLocaleKeys.eiInfoPanelsInformationAudio.tr()) {
                                              //     ref.infoAudioUrl.value = AsLocaleKeys.eiRateAfterAudio.tr();
                                              //   } else {
                                              //     ref.infoAudioUrl.value =
                                              //         AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                              //   }
                                              // }

                                              final info = isPhysicalSensation
                                                  ? (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.psInfoPanelsInformationText,
                                                      context,
                                                    ) as List)
                                                      .join('\n\n')
                                                  : (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.eiInfoPanelsInformationText,
                                                      context,
                                                    ) as List)
                                                      .join('\n\n');

                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info) {
                                                ref.isRateAfterAudioPaused.value = true;
                                                ref.headerInfoText.value = null;
                                                if (isPhysicalSensation) {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.psRateAfterAudio.tr();
                                                } else {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.eiRateAfterAudio.tr();
                                                }
                                              } else {
                                                ref.isRateAfterAudioPaused.value = false;
                                                if (isPhysicalSensation) {
                                                  ref.infoAudioUrl.value =
                                                      AsLocaleKeys.psInfoPanelsInformationAudio.tr();
                                                } else {
                                                  ref.infoAudioUrl.value =
                                                      AsLocaleKeys.eiInfoPanelsInformationAudio.tr();
                                                }

                                                ref.headerInfoText.value = info;
                                              }
                                            },
                                            onLearnTap: () {
                                              final info = isPhysicalSensation
                                                  ? (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.psInfoPanelsLearnText,
                                                      context,
                                                    ) as List)
                                                      .join('\n\n')
                                                  : (DynamicAssetLoader.getNestedValue(
                                                      AsLocaleKeys.eiInfoPanelsLearnText,
                                                      context,
                                                    ) as List)
                                                      .join('\n\n');

                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info) {
                                                ref.isRateAfterAudioPaused.value = true;
                                                ref.headerInfoText.value = null;
                                                if (isPhysicalSensation) {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.psRateAfterAudio.tr();
                                                } else {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.eiRateAfterAudio.tr();
                                                }
                                              } else {
                                                ref.isRateAfterAudioPaused.value = false;
                                                if (isPhysicalSensation) {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.psInfoPanelsLearnAudio.tr();
                                                } else {
                                                  ref.infoAudioUrl.value = AsLocaleKeys.eiInfoPanelsLearnAudio.tr();
                                                }

                                                ref.headerInfoText.value = info;
                                              }
                                            },
                                            infoWidget: ValueListenableBuilder(
                                              valueListenable: ref.headerInfoText,
                                              builder: (context, headerInfoTextV, _) {
                                                return CustomInfoWidget(
                                                  onCloseTap: () {
                                                    ref.isRateAfterAudioPaused.value = true;

                                                    ref.headerInfoText.value = null;
                                                    ref.infoAudioUrl.value = isPhysicalSensation
                                                        ? AsLocaleKeys.psRateAfterAudio.tr()
                                                        : AsLocaleKeys.eiRateAfterAudio.tr();
                                                  },
                                                  visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                  margin: EdgeInsets.symmetric(
                                                    vertical: AppSize.h8,
                                                  ),
                                                  bodyText: headerInfoTextV,
                                                );
                                              },
                                            ),
                                          ),
                                          if (!isPhysicalSensation)
                                            AppTextWidget(
                                              AsLocaleKeys.eiRateAfterDescription.tr(),
                                              style: context.textTheme.labelSmall?.copyWith(
                                                fontSize: AppSize.sp13,
                                              ),
                                            )
                                          else
                                            AppTextWidget(
                                              AsLocaleKeys.psRateAfterDescription.tr(),
                                              style: context.textTheme.labelSmall?.copyWith(
                                                fontSize: AppSize.sp13,
                                              ),
                                            ),
                                          SpaceV(AppSize.h8),
                                          ValueListenableBuilder(
                                            valueListenable: ref.isRateAfterButtonClick,
                                            builder: (context, value, child) {
                                              return SliderScreen(
                                                selectedValue: ref.emotionAfterValue,
                                                firstText: !isPhysicalSensation
                                                    ? (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.eiRateBeforeLabels0,
                                                        context,
                                                      ) as List)
                                                        .first
                                                        .toString()
                                                    : (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.psRateBeforeLabels0,
                                                        context,
                                                      ) as List)
                                                        .first
                                                        .toString(),
                                                secondText: !isPhysicalSensation
                                                    ? (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.eiRateBeforeLabels0,
                                                        context,
                                                      ) as List)
                                                        .last
                                                        .toString()
                                                    : (DynamicAssetLoader.getNestedValue(
                                                        AsLocaleKeys.psRateBeforeLabels0,
                                                        context,
                                                      ) as List)
                                                        .last
                                                        .toString(),
                                                changeTextColor: true,
                                                reverseGradient: true,
                                                onSelect: (value) {
                                                  ref.emotionAfterValue.value = value;
                                                },
                                                isClick: ref.isRateAfterButtonClick.value,
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                      SpaceV(AppSize.h10),
                                      CustomButton(
                                        padding: EdgeInsets.zero,
                                        isBottom: true,
                                        title: CoreLocaleKeys.buttonsNext.tr(),
                                        color: context.themeColors.blueColor,
                                        onTap: () {
                                          ref.isRateAfterButtonClick.value = true;

                                          if (ref.emotionAfterValue.value == -1) {
                                            // CustomSnackbar.showErrorSnackBar(
                                            //   message: AsLocaleKeys.eiErrorsRequired.tr(),
                                            // );
                                          } else {
                                            ref.infoAudioUrl.value = null;
                                            AppNavigation.nextScreen(
                                              context,
                                              EmotionalActionComparePage(
                                                beforeRate: beforeRate,
                                                affterRate: ref.emotionAfterValue.value,
                                                isPhysicalSensation: isPhysicalSensation,
                                                isAudioPanelVisible: isAudioPanelVisible,
                                                infoAudioUrl: ref.infoAudioUrl,
                                              ),
                                            );
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
