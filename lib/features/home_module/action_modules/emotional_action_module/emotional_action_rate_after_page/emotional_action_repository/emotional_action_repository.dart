import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class EmotionalActionRepository {
  Future<StrategiesModel?> emotionalImpactandphysicalSensationStrategy({
    required int? rateBefore,
    required int? rateAfter,
    required String? type,
    required BuildContext context,
    required bool isPhysicalSensation,
  }) async {
    try {
      'type$type'.logD;
      final response = await APIFunction.postAPICall(
        {
          'type': type,
          'data': {
            'rateBefore': rateBefore,
            'rateAfter': rateAfter,
            //Todo: remove this static videoName
            if (!isPhysicalSensation) 'videoName': 'beach',
          },
        },
        apiName: EndPoints.strategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return StrategiesModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> psActionStrategy({
    required BuildContext context,
    required bool isPhysicalSensation,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isPhysicalSensation ? EndPoints.psActionStrategy : EndPoints.eiActionStrategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> psAndeiActionStrategyforEmail({
    required BuildContext context,
    required bool isPhysicalSensation,
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isPhysicalSensation
            ? '${EndPoints.psActionStrategy}?email=true'
            : '${EndPoints.eiActionStrategy}?email=true',
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
