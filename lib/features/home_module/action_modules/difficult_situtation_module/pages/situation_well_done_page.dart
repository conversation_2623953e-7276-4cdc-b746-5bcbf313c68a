import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/common_well_done_screen.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/pages/situation_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class SituationWellDonePage extends StatelessWidget {
  SituationWellDonePage({super.key});
  final controller = ConfettiController();

  @override
  Widget build(BuildContext context) {
    bool isNavigating = false;
    '>?>?>? ${AsLocaleKeys.lsDsSummaryAudioApp.tr()}'.logD;
    return BlocBuilder<DifficultSitutationCubit, DifficultSitutationState>(
      builder: (ctx, state) {
        final ref = ctx.read<DifficultSitutationCubit>();

        return ValueListenableBuilder(
          valueListenable: ref.infoWellDoneAudioUrl,
          builder: (context, value, child) {
            return CommonWellDoneScreen(
              controller: controller,
              onButtonTap: () {
                ref.clearWellDoneAudioData();
                AppNavigation.nextScreen(
                  context,
                  BlocProvider.value(
                    value: ref,
                    child: const SituationActionPlanPage(),
                  ),
                );
              },
              onBackArrowTap: () {
                ref.clearWellDoneAudioData();
                ref.customInfoWindowController.hideInfoWindow!();
                Navigator.pop(context);
                ref.customInfoWindowController.hideInfoWindow!();
              },
              icon: Assets.icons.actionIcons.situtation,
              onInfoTap: () {
                final info = (DynamicAssetLoader.getNestedValue(
                  AsLocaleKeys.lsDsInfoPanelsInformationText,
                  context,
                ) as List<dynamic>? ?? [])
                    .join('<br/><br/>');
                if (ref.headerWellDoneInfoText.value.isNotEmptyAndNotNull && ref.headerWellDoneInfoText.value == info) {
                  ref.isManuallyPaused.value = true;
                  ref.headerWellDoneInfoText.value = null;
                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudioApp.tr();//AsLocaleKeys.lsDsSummaryAudioApp.tr();
                } else {
                  ref.isManuallyPaused.value = false;
                  ref.headerVideoUrl.value = true;
                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                  ref.headerWellDoneInfoText.value = info;
                }
                'ref.isManuallyPaused.value ${ref.isManuallyPaused.value}'.logD;
                // final audio = AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                // log('audio $audio');
                // ref.infoWellDoneAudioUrl.value = ref.infoWellDoneAudioUrl.value == audio ? null : audio;
              },
              onLearnTap: () {
                ref.isManuallyPaused.value.logD;
                final info = (DynamicAssetLoader.getNestedValue(
                  AsLocaleKeys.lsDsInfoPanelsLearnText,
                  context,
                ) as List<dynamic>? ?? [])
                    .join('<br/><br/>');
                if (ref.headerWellDoneInfoText.value.isNotEmptyAndNotNull && ref.headerWellDoneInfoText.value == info) {
                  ref.isManuallyPaused.value = true;
                  ref.headerWellDoneInfoText.value = null;
                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudioApp.tr();
                } else {
                  ref.isManuallyPaused.value = false;
                  ref.headerVideoUrl.value = false;
                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                  ref.headerWellDoneInfoText.value = info;
                }

                // final audio = AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                // ref.infoWellDoneAudioUrl.value = ref.infoWellDoneAudioUrl.value == audio ? null : audio;
              },
              isManuallyPaused: ref.isManuallyPaused,
              infoWidget: ValueListenableBuilder(
                valueListenable: ref.headerWellDoneInfoText,
                builder: (context, headerInfoTextV, _) {
                  return CustomInfoWidget(
                    customWidget: Column(
                      children: [
                        Html(
                          data: ref.headerWellDoneInfoText.value ?? '',
                          style: {
                            'strong': Style(
                              fontSize: FontSize(AppSize.sp13),
                              color: context.themeColors.darkOrangeColor,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                            ),
                            'body': Style(
                              fontSize: FontSize(AppSize.sp13),
                              color: context.themeColors.darkOrangeColor,
                              fontFamily: 'Poppins',
                            ),
                          },
                        ),
                        ValueListenableBuilder<bool>(
                          valueListenable: ref.headerVideoUrl,
                          builder: (context, headerVideoUrlV, _) {
                            return Column(
                              children: [
                                if (headerVideoUrlV) ...[
                                  SpaceV(AppSize.h16),
                                  VideoPlayerScreen(
                                    onTap: () {
                                      ref.isManuallyPaused.value = true;
                                      //ref.infoWellDoneAudioUrl.value = null;
                                    },
                                    onVideoEnded: () async {
  if (isNavigating) return; // 👈 Prevent multiple calls
  isNavigating = true;

  await Future.delayed(const Duration(milliseconds: 300));

  if (Navigator.of(context).canPop()) {
    AppNavigation.previousScreen(context);
  }

  isNavigating = false; // Reset for next time
},
                                    imageList: [
                                      AsLocaleKeys.lsDsInfoPanelsInformationVideoposter.tr(),
                                    ],
                                    navigationFunction: () {},
                                    videoList: [AsLocaleKeys.lsDsInfoPanelsInformationVideo.tr()],
                                  ),
                                ],
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                    onCloseTap: () {
                      ref.isManuallyPaused.value = true;
                      ref.headerWellDoneInfoText.value = null;
                      ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudioApp.tr();
                    },
                    visible: headerInfoTextV.isNotEmptyAndNotNull,
                    margin: EdgeInsets.symmetric(
                      vertical: AppSize.h8,
                    ),
                    bodyText: headerInfoTextV,
                  );
                },
              ),
              isAudioPannelVisible: ref.isAudioPanelVisible,
              infoAudioUrl: ref.infoWellDoneAudioUrl,
              wellDoneDetailText: (DynamicAssetLoader.getNestedValue(
                AsLocaleKeys.lsDsSummaryTextApp,
                context,
              ) as List<dynamic>? ?? [])
                  .join(
                '\n\n',
              ),
              wellDoneTitleText: AsLocaleKeys.lsDsSummaryTitle.tr(),
              subtitle: AsLocaleKeys.lsDsTitle.tr(),
              scaffoldKey: ref.scaffoldWellDoneKey,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldWellDoneKey),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldWellDoneKey.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoWellDoneAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                  }
                },
              ),
            );
          },
        );
      },
    );
  }
}
