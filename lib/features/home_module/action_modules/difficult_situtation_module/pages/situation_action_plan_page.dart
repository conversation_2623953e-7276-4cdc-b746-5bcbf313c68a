import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class SituationActionPlanPage extends StatelessWidget {
  const SituationActionPlanPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DifficultSitutationCubit, DifficultSitutationState>(
      builder: (ctx, state) {
        final ref = ctx.read<DifficultSitutationCubit>();
        log('marker${state.marker}');
        return ValueListenableBuilder(
          valueListenable: ref.infoPlanAudioUrl,
          builder: (context, value, child) {
            return PopScope(
              onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  ref.clearPlanAudioData();
                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudio.tr();
                }
              },
              child: AppScaffold(
                resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldActionPlanKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                isManuallyPaused: ref.isManuallyPaused,
                infoAudioUrl: ref.infoPlanAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldActionPlanKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldActionPlanKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoPlanAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minHeight: constraints.maxHeight,
                                  ),
                                  child: ColoredBox(
                                    color: context.themeColors.whiteColor,
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20,
                                        top: AppSize.h24,
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              InformationPageHeadingWidget(
                                                onBackArrowTap: () {
                                                  ref.clearPlanAudioData();
                                                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudio.tr();
                                                  Navigator.pop(context);
                                                },
                                                title:
                                                    CoreLocaleKeys.titlesInformationStrategiesDifficultSituations.tr(),
                                                subtitle: AsLocaleKeys.lsDsTitle.tr(),
                                                icon: Assets.icons.actionIcons.situtation,
                                                onInfoTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsDsInfoPanelsInformationText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (ref.headerPlanInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerPlanInfoText.value == info) {
                                                    ref.isManuallyPaused.value = true;
                                                    ref.headerPlanInfoText.value = null;
                                                    ref.infoPlanAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                  } else {
                                                    ref.isManuallyPaused.value = false;
                                                    ref.infoPlanAudioUrl.value =
                                                        AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                                                    ref.headerPlanInfoText.value = info;
                                                  }
                                                },
                                                onLearnTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsDsInfoPanelsLearnText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (ref.headerPlanInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerPlanInfoText.value == info) {
                                                    ref.isManuallyPaused.value = true;
                                                    ref.headerPlanInfoText.value = null;
                                                    ref.infoPlanAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                  } else {
                                                    ref.isManuallyPaused.value = false;
                                                    ref.infoPlanAudioUrl.value =
                                                        AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                                                    ref.headerPlanInfoText.value = info;
                                                  }
                                                },
                                                infoWidget: ValueListenableBuilder(
                                                  valueListenable: ref.headerPlanInfoText,
                                                  builder: (context, headerPlanInfoTextV, _) {
                                                    return CustomInfoWidget(
                                                      customWidget: Column(
                                                        children: [
                                                          Html(
                                                            data: ref.headerPlanInfoText.value ?? '',
                                                            style: {
                                                              'strong': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontWeight: FontWeight.bold,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                              'body': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      onCloseTap: () {
                                                        ref.isManuallyPaused.value = true;
                                                        ref.headerPlanInfoText.value = null;
                                                        ref.infoPlanAudioUrl.value =
                                                            AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                      },
                                                      visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerPlanInfoTextV,
                                                    );
                                                  },
                                                ),
                                              ),
                                              AppTextWidget(
                                                AsLocaleKeys.lsDsActionPlanTitle.tr(),
                                                style: context.textTheme.titleSmall
                                                    ?.copyWith(fontSize: AppSize.sp14, fontWeight: FontWeight.w700),
                                              ),
                                              SpaceV(AppSize.h10),
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsDsActionPlanText,
                                                  context,
                                                ) as List)
                                                    .join('\n\n'),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h10),
                                              SizedBox(
                                                height: context.height * 0.5,
                                                child: GoogleMap(
                                                  onMapCreated: (GoogleMapController controller) {
                                                    ref.controller = controller;

                                                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                                                      if (ref.state.marker.isNotEmpty) {
                                                        final bounds = ref.calculateBounds(ref.state.marker);
                                                        try {
                                                          await ref.applyNumberedIconToAllMarkersWithList(ref.state.mapDetailList);
                                                          await controller.animateCamera(
                                                            CameraUpdate.newLatLngBounds(bounds, 100),
                                                          );
                                                        } catch (e) {
                                                          print('Error setting camera bounds: $e');
                                                        }
                                                      }
                                                    });
                                                  },
                                                  zoomControlsEnabled: false,
                                                  markers: ref.state.marker,
                                                  initialCameraPosition: ref.state.marker.isNotEmpty
                                                      ? CameraPosition(
                                                          target: ref.state.marker.first.position,
                                                          zoom: 14,
                                                        )
                                                      : const CameraPosition(
                                                          target: LatLng(-72.44564054136866, -14.852879270911215),
                                                          zoom: 5,
                                                        ),
                                                ),
                                              ),

                                              // SizedBox(
                                              //   height: context.height * 0.5,
                                              //   child: GoogleMap(
                                              //     onMapCreated: (GoogleMapController controller) async {
                                              //       ref.controller = controller;
                                              //       if (ref.state.marker.isNotEmpty) {
                                              //         final bounds = ref.calculateBounds(ref.state.marker);
                                              //         // Set the camera position to fit all markers without zoom animation
                                              //         await controller.moveCamera(
                                              //           CameraUpdate.newLatLngBounds(bounds, 50),
                                              //         ); // 50 is padding
                                              //       }
                                              //     },
                                              //     zoomControlsEnabled: false,
                                              //     markers: ref.getAllMarkers(),
                                              //     initialCameraPosition: const CameraPosition(
                                              //       target: LatLng(
                                              //         -72.44564054136866,
                                              //         -14.852879270911215,
                                              //       ), // Default location
                                              //     ),
                                              //   ),
                                              // ),
                                              Column(
                                                children: [
                                                  if (state.mapDetailList.isNotEmpty) ...{
                                                    ListView.builder(
                                                      shrinkWrap: true,
                                                      physics: const NeverScrollableScrollPhysics(),
                                                      itemCount: state.mapDetailList.length,
                                                      itemBuilder: (context, index) {
                                                        return Padding(
                                                          padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: const Color.fromRGBO(235, 235, 235, 1),
                                                              //color: context.themeColors.whiteColor,
                                                              borderRadius: BorderRadius.circular(AppSize.r4),
                                                              border: Border.all(
                                                                color: const Color(0xFFBDBDBD),
                                                              ),
                                                              // boxShadow: [
                                                              //   BoxShadow(
                                                              //     color: Colors.black
                                                              //         .withOpacity(0.10), // Very light shadow color
                                                              //     offset: const Offset(
                                                              //       0,
                                                              //       3,
                                                              //     ), // Slight vertical shadow (bottom only)
                                                              //     blurRadius: 6, // Subtle blur effect
                                                              //     spreadRadius: 1, // Light spreading of the shadow
                                                              //   ),
                                                              // ],
                                                            ),
                                                            child: Padding(
                                                              padding: EdgeInsets.only(
                                                                left: AppSize.w8,
                                                                right: AppSize.w12,
                                                                top: AppSize.h10,
                                                                bottom: AppSize.h10,
                                                              ),
                                                              child: Row(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  Stack(
                                                                    alignment: Alignment.center,
                                                                    children: [
                                                                      Assets.icons.redMarkerIcon.image(
                                                                        height: AppSize.h40,
                                                                        width: AppSize.w40,
                                                                      ),
                                                                      Positioned(
                                                                        top: 9,
                                                                        child: Text(
                                                                          '${index + 1}',
                                                                          style: context.textTheme.titleSmall?.copyWith(
                                                                            fontWeight: FontWeight.w500,
                                                                          ),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  SpaceH(AppSize.w10),
                                                                  Expanded(
                                                                    child: Column(
                                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                                      children: [
                                                                        AppTextWidget(
                                                                          state.mapDetailList[index].what ?? '',
                                                                          style: context.textTheme.titleSmall,
                                                                        ),
                                                                        SpaceV(AppSize.h10),
                                                                        AppTextWidget(
                                                                          (state.mapDetailList[index].why ?? 0) ==
                                                                                  (ref.situtationWhyList.length - 1)
                                                                              ? state.mapDetailList[index].customWhy ??
                                                                                  ''
                                                                              : ref.situtationWhyList[
                                                                                  state.mapDetailList[index].why ?? 0],
                                                                          style: context.textTheme.titleSmall?.copyWith(
                                                                            color: context.themeColors.redColor,
                                                                          ),
                                                                        ),
                                                                        SpaceV(AppSize.h10),
                                                                        AppTextWidget(
                                                                          (state.mapDetailList[index].how ??0) == (ref.situtationHowList.length - 1)?(state.mapDetailList[index].customHow ??'')
                                                                              : ref.situtationHowList[
                                                                          state.mapDetailList[index].how ?? 0],
                                                                          style: context
                                                                              .textTheme.titleSmall
                                                                              ?.copyWith(
                                                                            color: context
                                                                                .themeColors
                                                                                .greenBtnColor,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                    ),
                                                  },
                                                ],
                                              ),
                                            ],
                                          ),
                                          SpaceV(AppSize.h10),
                                          CustomYesNoButton(
                                            padding: EdgeInsets.zero,
                                            isDownLoad: true,
                                            isYesNoButton: true,
                                            inNoProgress: ValueNotifier(
                                              state.downloadPdfAPILoading || state.emailPdfAPILoading,
                                            ),
                                            onDownloadTap: () {
                                              CustomDownloadPopup.buildPopupMenu(
                                                context: context,
                                                onDownLoadPdf: () async {
                                                  await ref.dsActionStrategyforDownloadPdfApi(
                                                    context: context,
                                                    isEmail: false,
                                                  );
                                                },
                                                onEmailDownload: () async {
                                                  await ref.dsActionStrategyforDownloadPdfApi(
                                                    context: context,
                                                    isEmail: true,
                                                  );
                                                },
                                              );
                                            },
                                            exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                            agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                            onTapYes: () async {
                                              await LocationService.checkRiskyPlace(
                                                context: context,
                                              );
                                              ref.infoPlanAudioUrl.value = null;
                                              '>?>?>? 9'.logV;
                                              await AppNavigation.pushAndRemoveAllScreen(
                                                context,
                                                const MyDiagramPage(),
                                              );
                                            },
                                            onTapNo: () {},
                                            noButtonColor: context.themeColors.orangeColor,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
