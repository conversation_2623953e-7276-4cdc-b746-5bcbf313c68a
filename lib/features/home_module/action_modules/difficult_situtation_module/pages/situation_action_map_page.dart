import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_drop_down_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/google_map/google_map_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/widgets/add_risky_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart' as html;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

class SituationActionMapPage extends StatelessWidget {
  const SituationActionMapPage({super.key});

  @override
  Widget build(BuildContext context) {
    bool isNavigating = false;
    return BlocProvider(    
      create: (context)=>DifficultSitutationCubit()
        ..data()
        ..mapListUpdate(),
      child: BlocBuilder<DifficultSitutationCubit, DifficultSitutationState>(
        builder: (ctx, state) {
          final ref = ctx.read<DifficultSitutationCubit>();
          'marker +++ ${state.mapDetailList.length}'.logD;
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return ValueListenableBuilder(
                valueListenable: ref.curIndex,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isAPILoading,
                    child: AppScaffold(
                      //   resizeToAvoidBottomInset: false,
                      isManuallyPaused: ref.isManuallyPaused,
                      scaffoldKey: ref.scaffoldKey,
                      isAudioPanelVisible: ref.isAudioPanelVisible,
                      infoAudioUrl: ref.infoAudioUrl,
                      drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                      appBar: CommonAppBar(
                        onPrefixTap: () {
                          ref.scaffoldKey.currentState?.openDrawer();
                        },
                        onSuffixTap: () {
                          if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                            ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                          }
                        },
                      ),
                      body: Column(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(right: AppSize.w4),
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return CustomRawScrollbar(
                                    child: SingleChildScrollView(
                                      controller: ref.scrollController,
                                      child: ConstrainedBox(
                                        constraints: BoxConstraints(
                                          minHeight: constraints.maxHeight,
                                        ),
                                        child: ColoredBox(
                                          color: context.themeColors.whiteColor,
                                          child: Form(
                                            key: ref.key,
                                            child: ValueListenableBuilder(
                                              valueListenable: ref.isButtonClicked,
                                              builder: (context, buttonClick, child) {
                                                return Padding(
                                                  padding: EdgeInsets.only(
                                                    left: AppSize.w24,
                                                    right: AppSize.w24,
                                                    bottom: AppSize.h20,
                                                    top: AppSize.h24,
                                                  ),
                                                  child: Column(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Column(
                                                        children: [
                                                          InformationPageHeadingWidget(
                                                            onBackArrowTap: () {
                                                              ref.clearAudioData();
                                                              Navigator.pop(context);
                                                            },
                                                            title: 
                                                            CoreLocaleKeys
                                                                .titlesInformationStrategiesDifficultSituations
                                                                .tr(),
                                                            subtitle: AsLocaleKeys.lsDsTitle.tr(),//IsLocaleKeys.dsTitle.tr(),
                                                            icon: Assets.icons.actionIcons.situtation,
                                                            onInfoTap: () {
                                                              final info = (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.lsDsInfoPanelsInformationText,
                                                                context,
                                                              ) as List)
                                                                  .join('<br/><br/>');
                                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                  ref.headerInfoText.value == info) {
                                                                ref.isManuallyPaused.value = true;
                                                                ref.infoAudioUrl.value = null;
                                                                ref.headerInfoText.value = null;
                                                                ref.isAudioPanelVisible.value = false;
                                                              } else {
                                                                ref.isManuallyPaused.value = false;
                                                                ref.headerVideoUrl.value = true;
                                                                ref.infoAudioUrl.value =
                                                                    AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                                                                ref.headerInfoText.value = info;
                                                              }

                                                              // final audio = AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                                                              // log('audio $audio');
                                                              // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                                            },
                                                            onLearnTap: () {
                                                              final info = (DynamicAssetLoader.getNestedValue(
                                                                AsLocaleKeys.lsDsInfoPanelsLearnText,
                                                                context,
                                                              ) as List)
                                                                  .join('<br/><br/>');
                                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                  ref.headerInfoText.value == info) {
                                                                ref.isManuallyPaused.value = true;
                                                                ref.infoAudioUrl.value = null;
                                                                ref.headerInfoText.value = null;
                                                                ref.isAudioPanelVisible.value = false;
                                                              } else {
                                                                ref.isManuallyPaused.value = false;
                                                                ref.headerVideoUrl.value = false;
                                                                ref.infoAudioUrl.value =
                                                                    AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                                                                ref.headerInfoText.value = info;
                                                              }

                                                              // final audio = AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                                                              // ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
                                                            },
                                                            infoWidget: ValueListenableBuilder(
                                                              valueListenable: ref.headerInfoText,
                                                              builder: (context, headerInfoTextV, _) {
                                                                return CustomInfoWidget(
                                                                  customWidget: Column(
                                                                    children: [
                                                                      html.Html(
                                                                        data: ref.headerInfoText.value ?? '',
                                                                        style: {
                                                                          'strong': html.Style(
                                                                            fontSize: html.FontSize(AppSize.sp13),
                                                                            color: context.themeColors.darkOrangeColor,
                                                                            fontWeight: FontWeight.bold,
                                                                            fontFamily: 'Poppins',
                                                                          ),
                                                                          'body': html.Style(
                                                                            fontSize: html.FontSize(AppSize.sp13),
                                                                            color: context.themeColors.darkOrangeColor,
                                                                            fontFamily: 'Poppins',
                                                                          ),
                                                                        },
                                                                      ),
                                                                      ValueListenableBuilder<bool>(
                                                                        valueListenable: ref.headerVideoUrl,
                                                                        builder: (context, headerVideoUrlV, _) {
                                                                          return Column(
                                                                            children: [
                                                                              if (headerVideoUrlV) ...[
                                                                                SpaceV(AppSize.h16),
                                                                                VideoPlayerScreen(
                                                                                  onTap: () {
                                                                                    ref.isManuallyPaused.value = true;
                                                                                    //ref.infoAudioUrl.value = null;
                                                                                  },
                                                                                  

onVideoEnded: () async {
  if (isNavigating) return; // 👈 Prevent multiple calls
  isNavigating = true;

  await Future.delayed(const Duration(milliseconds: 300));

  if (Navigator.of(context).canPop()) {
    AppNavigation.previousScreen(context);
  }

  isNavigating = false; // Reset for next time
},

                                                                                  imageList: [
                                                                                    AsLocaleKeys
                                                                                        .lsDsInfoPanelsInformationVideoposter
                                                                                        .tr(),
                                                                                  ],
                                                                                  navigationFunction: () {},
                                                                                  videoList: [
                                                                                    AsLocaleKeys
                                                                                        .lsDsInfoPanelsInformationVideo
                                                                                        .tr(),
                                                                                  ],
                                                                                ),
                                                                              ],
                                                                            ],
                                                                          );
                                                                        },
                                                                      ),
                                                                    ],
                                                                  ),
                                                                  onCloseTap: () {
                                                                    ref.infoAudioUrl.value = null;
                                                                    ref.headerInfoText.value = null;
                                                                    ref.isAudioPanelVisible.value = false;
                                                                  },
                                                                  visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                                  margin: EdgeInsets.symmetric(
                                                                    vertical: AppSize.h8,
                                                                  ),
                                                                  bodyText: headerInfoTextV,
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                          ValueListenableBuilder(
                                                            valueListenable: ref.isPermission,
                                                            builder: (context, value, chile) {
                                                              if (ref.isPermission.value) {
                                                                return Column(
                                                                  children: [
                                                                    GestureDetector(
                                                                      onTap: () async{
                                                                          await LocationService.checkLocationForPermission(context: context);
                                                                      },
                                                                      child: Container(
                                                                        padding: const EdgeInsets.all(12),
                                                                        decoration: BoxDecoration(
                                                                          color: context.themeColors.lightBlueColor,
                                                                          borderRadius:
                                                                              BorderRadius.circular(AppSize.r4),
                                                                        ),
                                                                        child: Text(
                                                                          AsLocaleKeys.dsnoLocationPermission.tr(),
                                                                          style: context.textTheme.labelSmall?.copyWith(
                                                                            color: context.themeColors.blueColor,
                                                                            fontSize: AppSize.sp12,
                                                                          ),
                                                                          textAlign: TextAlign.center,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    SpaceV(AppSize.h20),
                                                                  ],
                                                                );
                                                              } else {
                                                                return const SizedBox();
                                                              }
                                                            },
                                                          ),

                                                          SizedBox(
                                                            height: context.height * 0.5,
                                                            child: GoogleMapPage(
                                                              myLocationAddress: '',
                                                              markers: state.marker,
                                                              locTextController: TextEditingController(),
                                                            ),
                                                          ),
                                                          SpaceV(AppSize.h14),
                                                          Column(
                                                            children: [
                                                              if (state.marker.isEmpty) ...{
                                                                AppTextWidget(
                                                                  AsLocaleKeys.lsDsClickToAdd.tr(),
                                                                  style: context.textTheme.titleSmall?.copyWith(),
                                                                ),
                                                              } else ...{
                                                                if (ref.riskyPlaceVisible.value) ...[
                                                                  SpaceV(AppSize.h14),
                                                                  Container(
                                                                    key: ref.addRiskyPlaceKey,
                                                                    child: AddRiskyWidget(
                                                                      ref: ref,
                                                                      globalKey: ref.key,
                                                                    ),
                                                                  ),
                                                                  SpaceV(AppSize.h6)
                                                                ],
                                                                if (state.marker.isNotEmpty) ...{
                                                                  ListView.builder(
                                                                    shrinkWrap: true,
                                                                    physics: const NeverScrollableScrollPhysics(),
                                                                    itemCount: state.mapDetailList.length,
                                                                    itemBuilder: (context, index) {
                                                                      return Padding(
                                                                        padding:
                                                                            EdgeInsets.symmetric(vertical: AppSize.h5),
                                                                        child: Container(
                                                                          decoration: BoxDecoration(
                                                                            color:  const Color.fromRGBO(235, 235, 235, 1),
                                                                            border: Border.all(
                                                                              color: const Color(0xFFBDBDBD),// border color
                                                                            ),
                                                                            borderRadius: BorderRadius.circular(AppSize.r4),
                                                                          ),  
                                                                          child: ValueListenableBuilder(
                                                                            valueListenable: ref.editingIndex,
                                                                            builder: (context, value, child) {
                                                                              'state.mapDetailList[index] ${state.mapDetailList[index].toJson()}'
                                                                                  .logV;
                                                                              return Padding(
                                                                                padding: EdgeInsets.only(
                                                                                  left: AppSize.w8,
                                                                                  right: AppSize.w12,
                                                                                  top: AppSize.h10,
                                                                                  bottom: AppSize.h10,
                                                                                ),
                                                                                child: Row(
                                                                                  crossAxisAlignment:
                                                                                      CrossAxisAlignment.start,
                                                                                  children: [
                                                                                    Stack(
                                                                                      alignment: Alignment.center,
                                                                                      children: [
                                                                                        Assets.icons.redMarkerIcon
                                                                                            .image(
                                                                                          height: AppSize.h40,
                                                                                          width: AppSize.w40,
                                                                                        ),
                                                                                        Positioned(
                                                                                          top: 9,
                                                                                          child: Text(
                                                                                            '${index + 1}',
                                                                                            style: context
                                                                                                .textTheme.titleSmall
                                                                                                ?.copyWith(
                                                                                              fontWeight:
                                                                                                  FontWeight.w500,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                    SpaceH(AppSize.w10),
                                                                                    Expanded(
                                                                                      flex: 8,
                                                                                      child: Column(
                                                                                        crossAxisAlignment:
                                                                                            CrossAxisAlignment.start,
                                                                                        children: [
                                                                                          if (ref.editingIndex.value ==
                                                                                              index) ...[
                                                                                            ValueListenableBuilder(
                                                                                              valueListenable:
                                                                                                  ref.isSaveIconClick,
                                                                                              builder: (
                                                                                                context,
                                                                                                value,
                                                                                                child,
                                                                                              ) {
                                                                                                return Column(
                                                                                                  children: [
                                                                                                    CustomOutlinedTextfield(
                                                                                                      controller: ref.riskyPlaceController,
                                                                                                      labelText:
                                                                                                          AsLocaleKeys
                                                                                                              .lsDsQuestionsWhat
                                                                                                              .tr(),
                                                                                                      isError: ref
                                                                                                              .isSaveIconClick
                                                                                                              .value &&
                                                                                                          ref
                                                                                                              .riskyPlaceController
                                                                                                              .text
                                                                                                              .isEmpty,

                                                                                                      onChanged: (p0) {
                                                                                                        ref.isSaveIconClick
                                                                                                            .notifyListeners();

                                                                                                      },
                                                                                                    ),
                                                                                                    if (ref.isSaveIconClick
                                                                                                            .value &&
                                                                                                        ref
                                                                                                            .riskyPlaceController
                                                                                                            .text
                                                                                                            .isEmpty)
                                                                                                      CustomErrorWidget(
                                                                                                        errorMessgaeText:
                                                                                                            AssessmentLocaleKeys
                                                                                                                .errorsRequiredMessage
                                                                                                                .tr(),
                                                                                                      ),

                                                                                                  ],
                                                                                                );
                                                                                              },
                                                                                            ),
                                                                                          ] else ...[
                                                                                            AppTextWidget(
                                                                                              state.mapDetailList[index]
                                                                                                      .what ??
                                                                                                  '',
                                                                                              style: context
                                                                                                  .textTheme.titleSmall
                                                                                                  ?.copyWith(),
                                                                                            ),
                                                                                          ],
                                                                                          SpaceV(AppSize.h10),
                                                                                          if (ref.editingIndex.value ==
                                                                                              index) ...[
                                                                                            ValueListenableBuilder(
                                                                                              valueListenable: ref
                                                                                                  .isCustomWhyFieldVisibleError,
                                                                                              builder: (
                                                                                                context,
                                                                                                isCustomWhyFieldVisibleError,
                                                                                                child,
                                                                                              ) {
                                                                                                return Column(
                                                                                                  children: [
                                                                                                    SpaceV(AppSize.h10),
                                                                                                    SizedBox(
                                                                                                      height:
                                                                                                          AppSize.h44,
                                                                                                      child: Column(
                                                                                                        children: [
                                                                                                          CustomDropDownListWidget(
                                                                                                            list: ref
                                                                                                                .situtationWhyList,
                                                                                                            donotWishCheckBoxValue:
                                                                                                                ValueNotifier(
                                                                                                              false,
                                                                                                            ),
                                                                                                            onChanged: ref
                                                                                                                .updateriskyPlace,
                                                                                                            labelText:
                                                                                                                AsLocaleKeys
                                                                                                                    .lsDsQuestionsWhyLabel
                                                                                                                    .tr(),
                                                                                                            selectedValue: ref.state.selecteRiskyPlaceValue !=
                                                                                                                        null &&
                                                                                                                    ref.state.selecteRiskyPlaceValue!
                                                                                                                        .isNotEmpty &&
                                                                                                                    ref.situtationWhyList
                                                                                                                        .contains(
                                                                                                                      ref.state.selecteRiskyPlaceValue,
                                                                                                                    )
                                                                                                                ? ref
                                                                                                                    .state
                                                                                                                    .selecteRiskyPlaceValue
                                                                                                                : null,
                                                                                                          ),
                                                                                                        ],
                                                                                                      ),
                                                                                                    ),
                                                                                                    if ((state
                                                                                                            .selecteRiskyPlaceValue) ==
                                                                                                        ref.situtationWhyList[ref
                                                                                                                .situtationWhyList
                                                                                                                .length -
                                                                                                            1]) ...{
                                                                                                      CustomOutlinedTextfield(
                                                                                                        controller: ref.customWhyTextController,
                                                                                                        inputFormatters: [
                                                                                                          LengthLimitingTextInputFormatter(
                                                                                                            300,
                                                                                                          ),
                                                                                                        ],
                                                                                                        onChanged:
                                                                                                            (p0) {
                                                                                                          ref.isCustomWhyFieldVisibleError.value = false;
                                                                                                        },
                                                                                                        hintText:CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                                                                        isError: isCustomWhyFieldVisibleError && (state
                                                                                                            .selecteRiskyPlaceValue) ==
                                                                                                            ref.situtationWhyList[ref
                                                                                                                .situtationWhyList
                                                                                                                .length -
                                                                                                                1],

                                                                                                      ),
                                                                                                    },
                                                                                                    if (isCustomWhyFieldVisibleError && (state
                                                                                                        .selecteRiskyPlaceValue) ==
                                                                                                        ref.situtationWhyList[ref
                                                                                                            .situtationWhyList
                                                                                                            .length -
                                                                                                            1]) ...{
                                                                                                      CustomErrorWidget(
                                                                                                        errorMessgaeText:
                                                                                                            AuthLocaleKeys
                                                                                                                .fieldRequiredError
                                                                                                                .tr(),
                                                                                                      ),
                                                                                                    },
                                                                                                  ],
                                                                                                );
                                                                                              },
                                                                                            ),
                                                                                          ] else ...[
                                                                                            AppTextWidget(
                                                                                              (state
                                                                                                              .mapDetailList[
                                                                                                                  index]
                                                                                                              .why ??
                                                                                                          0) ==
                                                                                                      (ref.situtationWhyList
                                                                                                              .length -
                                                                                                          1)
                                                                                                  ? state
                                                                                                          .mapDetailList[
                                                                                                              index]
                                                                                                          .customWhy ??
                                                                                                      ''
                                                                                                  : ref.situtationWhyList[
                                                                                                      state
                                                                                                              .mapDetailList[
                                                                                                                  index]
                                                                                                              .why ??
                                                                                                          0],
                                                                                              style: context
                                                                                                  .textTheme.titleSmall
                                                                                                  ?.copyWith(
                                                                                                color: context
                                                                                                    .themeColors
                                                                                                    .redColor,
                                                                                              ),
                                                                                            ),
                                                                                          ],
                                                                                          SpaceV(AppSize.h10),
                                                                                          if (ref.editingIndex.value ==
                                                                                              index) ...[
                                                                                            ValueListenableBuilder(
                                                                                              valueListenable: ref
                                                                                                  .isCustomHowFieldVisibleError,
                                                                                              builder: (
                                                                                                context,
                                                                                                isCustomHowFieldVisibleError,
                                                                                                child,
                                                                                              ) {
                                                                                                return Column(
                                                                                                  children: [
                                                                                                    SpaceV(AppSize.h10),
                                                                                                    SizedBox(
                                                                                                      height:
                                                                                                          AppSize.h44,
                                                                                                      child: Column(
                                                                                                        children: [
                                                                                                          CustomDropDownListWidget(
                                                                                                            list: ref.situtationHowList,
                                                                                                            donotWishCheckBoxValue: ValueNotifier(false),
                                                                                                            onChanged: ref.updateSelecteCopyPlace,
                                                                                                            labelText:
                                                                                                                AsLocaleKeys
                                                                                                                    .lsDsQuestionsHowLabel
                                                                                                                    .tr(),

                                                                                                            selectedValue: ref.state.selecteCopyPlaceValue !=
                                                                                                                        null &&
                                                                                                                    ref.state.selecteCopyPlaceValue!
                                                                                                                        .isNotEmpty &&
                                                                                                                    ref.situtationHowList
                                                                                                                        .contains(
                                                                                                                      ref.state.selecteCopyPlaceValue,
                                                                                                                    )
                                                                                                                ? ref
                                                                                                                    .state
                                                                                                                    .selecteCopyPlaceValue
                                                                                                                : null,
                                                                                                          ),
                                                                                                        ],
                                                                                                      ),
                                                                                                    ),
                                                                                                    if ((state
                                                                                                            .selecteCopyPlaceValue) ==
                                                                                                        ref.situtationHowList[ref
                                                                                                                .situtationHowList
                                                                                                                .length -
                                                                                                            1]) ...{
                                                                                                      CustomOutlinedTextfield(
                                                                                                        controller: ref.customHowTextController,
                                                                                                        inputFormatters: [
                                                                                                          LengthLimitingTextInputFormatter(
                                                                                                            300,
                                                                                                          ),
                                                                                                        ],
                                                                                                        hintText:CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                                                                        onChanged:(p0) {
                                                                                                          ref.isCustomHowFieldVisibleError.value = false;
                                                                                                        },
                                                                                                        isError: isCustomHowFieldVisibleError && (state
                                                                                                            .selecteCopyPlaceValue) ==
                                                                                                            ref.situtationHowList[ref
                                                                                                                .situtationHowList
                                                                                                                .length -
                                                                                                                1],
                                                                                                      ),
                                                                                                    },
                                                                                                    if (isCustomHowFieldVisibleError && (state
                                                                                                        .selecteCopyPlaceValue) ==
                                                                                                        ref.situtationHowList[ref
                                                                                                            .situtationHowList
                                                                                                            .length -
                                                                                                            1]) ...{
                                                                                                      CustomErrorWidget(
                                                                                                        errorMessgaeText:
                                                                                                            AuthLocaleKeys
                                                                                                                .fieldRequiredError
                                                                                                                .tr(),
                                                                                                      ),
                                                                                                    },
                                                                                                  ],
                                                                                                );
                                                                                              },
                                                                                            ),
                                                                                          ] else ...[
                                                                                            AppTextWidget(
                                                                                              (state.mapDetailList[index].how ??0) == (ref.situtationHowList.length - 1)?(state.mapDetailList[index]
                                                                                                          .customHow ??
                                                                                                      '')
                                                                                                  : ref.situtationHowList[
                                                                                                      state
                                                                                                              .mapDetailList[
                                                                                                                  index]
                                                                                                              .how ??
                                                                                                          0],
                                                                                              style: context
                                                                                                  .textTheme.titleSmall
                                                                                                  ?.copyWith(
                                                                                                color: context
                                                                                                    .themeColors
                                                                                                    .greenBtnColor,
                                                                                              ),
                                                                                            ),
                                                                                          ],
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                    const Spacer(),
                                                                                    Column(
                                                                                      mainAxisAlignment:
                                                                                          MainAxisAlignment.center,
                                                                                      children: [
                                                                                        if (ref.editingIndex.value ==
                                                                                            index)
                                                                                          InkWell(
                                                                                            onTap: () async {
                                                                                              if (ref
                                                                                                  .riskyPlaceController
                                                                                                  .text
                                                                                                  .isNotEmpty) {
                                                                                                ref.isSaveIconClick
                                                                                                    .value = false;
                                                                                                final isLastHowItem = state
                                                                                                        .selecteCopyPlaceValue ==
                                                                                                    ref.situtationHowList
                                                                                                        .last;
                                                                                                final isLastWhyItem = state.selecteRiskyPlaceValue ==
                                                                                                    ref.situtationWhyList
                                                                                                        .last;

                                                                                                // Handle "How" field validation
                                                                                                if (isLastHowItem &&
                                                                                                    ref.customHowTextController
                                                                                                        .text.isEmpty) {
                                                                                                  ref.isCustomHowFieldVisibleError
                                                                                                      .value = true;
                                                                                                } else {
                                                                                                  ref.isCustomHowFieldVisibleError
                                                                                                      .value = false;
                                                                                                }

                                                                                                // Handle "Why" field validation
                                                                                                if (isLastWhyItem &&
                                                                                                    ref.customWhyTextController
                                                                                                        .text.isEmpty) {
                                                                                                  ref.isCustomWhyFieldVisibleError
                                                                                                      .value = true;
                                                                                                } else {
                                                                                                  ref.isCustomWhyFieldVisibleError
                                                                                                      .value = false;
                                                                                                }

                                                                                                // If no errors, proceed with saving the risky place
                                                                                                if (!ref.isCustomHowFieldVisibleError
                                                                                                        .value &&
                                                                                                    !ref.isCustomWhyFieldVisibleError
                                                                                                        .value) {
                                                                                                  ref.saveEditedRiskyPlace(
                                                                                                    ref.riskyPlaceController
                                                                                                        .text,
                                                                                                    ref.situtationWhyList
                                                                                                        .indexOf(
                                                                                                      state.selecteRiskyPlaceValue ??
                                                                                                          '',
                                                                                                    ),
                                                                                                    ref.situtationHowList
                                                                                                        .indexOf(
                                                                                                      state.selecteCopyPlaceValue ??
                                                                                                          '',
                                                                                                    ),
                                                                                                    isLastHowItem
                                                                                                        ? ref
                                                                                                            .customHowTextController
                                                                                                            .text
                                                                                                        : '',
                                                                                                    isLastWhyItem
                                                                                                        ? ref
                                                                                                            .customWhyTextController
                                                                                                            .text
                                                                                                        : '',
                                                                                                  );
                                                                                                }

                                                                                                await ref
                                                                                                    .regenerateMarkers();
                                                                                              } else {
                                                                                                ref.isSaveIconClick
                                                                                                    .value = true;
                                                                                              }
                                                                                            },
                                                                                            child: Icon(
                                                                                              Icons.save,
                                                                                              color: context.themeColors
                                                                                                  .greyColor,
                                                                                            ),
                                                                                          )
                                                                                        else
                                                                                          InkWell(
                                                                                            onTap: () {
                                                                                              final currentList =
                                                                                                  state.mapDetailList;
                                                                                              if (index <
                                                                                                  currentList.length) {
                                                                                                ref.setEditingIndex(
                                                                                                  index,
                                                                                                );
                                                                                              }
                                                                                            },
                                                                                            child: Icon(
                                                                                              Icons.edit,
                                                                                              color: context.themeColors
                                                                                                  .greyColor,
                                                                                            ),
                                                                                          ),
                                                                                        SpaceV(AppSize.h20),
                                                                                        InkWell(
                                                                                          onTap: () async {
                                                                                            ref.removeRiskyPlace(
                                                                                              state.mapDetailList[index]
                                                                                                      .lat ??
                                                                                                  0.0,
                                                                                              state.mapDetailList[index]
                                                                                                      .lng ??
                                                                                                  0.0,
                                                                                            );
                                                                                            // ref.customInfoWindowController.hideInfoWindow!();
                                                                                            await ref
                                                                                                .regenerateMarkers();
                                                                                          },
                                                                                          child: Icon(
                                                                                            Icons.delete,
                                                                                            color: context
                                                                                                .themeColors.greyColor,
                                                                                          ),
                                                                                        ),
                                                                                      ],
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              );
                                                                            },
                                                                          ),
                                                                        ),
                                                                      );
                                                                    },
                                                                  ),
                                                                },
                                                              },
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                      SpaceV(AppSize.h8),
                                                      if (ref.isButtonClicked.value && state.mapDetailList.isEmpty)
                                                        CustomErrorWidget(
                                                          errorMessgaeText: AsLocaleKeys.lsDsErrorsMinimumOne.tr(),
                                                        ),

                                                      SpaceV(AppSize.h10),
                                                      Row(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          Expanded(
                                                            flex: 40,
                                                            child: GestureDetector(
                                                              onTap: ref.clearMyMap,
                                                              child: Container(
                                                                height: AppSize.h34,
                                                                decoration: BoxDecoration(
                                                                  color: context.themeColors.redColor,
                                                                  borderRadius: BorderRadius.circular(AppSize.r32),
                                                                ),
                                                                child: Center(
                                                                  child: Text(
                                                                    AsLocaleKeys.lsDsButtonsClear.tr(),
                                                                    style: context.textTheme.titleMedium?.copyWith(
                                                                      fontSize: AppSize.sp12,
                                                                      color: context.themeColors.scaffoldColor,
                                                                      fontWeight: FontWeight.w500,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          SpaceH(AppSize.h6),
                                                          Expanded(
                                                            flex: 60,
                                                            child: CustomButton(
                                                              padding: EdgeInsets.zero,
                                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                                              inProgress: state.isAPILoading,
                                                              onTap: () {
                                                                ref.clearAudioData();

                                                                if (state.mapDetailList.isNotEmpty) {
                                                                  final formattedData =
                                                                      convertToJsonFormat(state.mapDetailList);
                                                                  log('formattedData $formattedData');
                                                                  ref.situtationStrategyAPI(
                                                                    context: context,
                                                                    data: formattedData,
                                                                    ref: ref,
                                                                  );
                                                                } else {
                                                                  ref.isButtonClicked.value = true;
                                                                  ref.key.currentState?.validate();
                                                                  // CustomSnackbar.showErrorSnackBar(
                                                                  //   message: AsLocaleKeys.lsDsErrorsMinimumOne.tr(),
                                                                  // );
                                                                }
                                                              },
                                                              isBottom: true,
                                                              color: context.themeColors.blueColor,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      // SpaceV(AppSize.h10),
                                                    ],
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}

Map<String, dynamic> convertToJsonFormat(List<Datum> mapDetailList) {
  final dataList = <Map<String, dynamic>>[];

  for (final element in mapDetailList) {
    // Check if markers exist and process them
    // if (element.markers != null && element.markers!.isNotEmpty) {
    //   for (final marker in element.markers!) {
    dataList.add({
      'what': element.what ?? '', // Replace with appropriate field
      'why': element.why,
      // 'how': SitutationList.situtationHowList.indexOf(element.copyRiskyPlace ?? ''),
      'how': element.how,
      'lat': element.lat, // Use latitude directly
      'lng': element.lng, // Use longitude directly
      if (element.customHow?.isNotEmpty ?? false) 'customHow': element.customHow,
      if (element.customWhy?.isNotEmpty ?? false) 'customWhy': element.customWhy,
    });
    //}
    //   }
  }

  return {
    'type': 'dsAS',
    'data': dataList,
  };
}

class MapData {
  MapData({
    this.copyRiskyPlace,
    this.riskyPlace,
    this.markers,
    this.riskyPlaceText,
    this.markerId,
  });

  // fromJson method for deserialization
  factory MapData.fromJson(Map<String, dynamic> json) {
    return MapData(
      copyRiskyPlace: json['copyRiskyPlace'] as String?,
      riskyPlace: json['riskyPlace'] as String?,
      markers: (json['markers'] as List<dynamic>?)
          ?.map((markerJson) => _markerFromJson(markerJson as Map<String, dynamic>))
          .toSet(),
      riskyPlaceText: json['riskyPlaceText'] as String?,
      markerId: (json['markerId'] as List<dynamic>?)?.map((id) => MarkerId(id as String)).toList(),
    );
  }
  final String? copyRiskyPlace;
  final String? riskyPlace;
  final Set<Marker>? markers;
  final String? riskyPlaceText;
  final List<MarkerId>? markerId;

  // Define the copyWith method
  MapData copyWith({
    String? copyRiskyPlace,
    String? riskyPlace,
    Set<Marker>? markers,
    String? riskyPlaceText,
    List<MarkerId>? markerId,
  }) {
    return MapData(
      copyRiskyPlace: copyRiskyPlace ?? this.copyRiskyPlace,
      riskyPlace: riskyPlace ?? this.riskyPlace,
      markers: markers ?? this.markers,
      riskyPlaceText: riskyPlaceText ?? this.riskyPlaceText,
      markerId: markerId ?? this.markerId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'copyRiskyPlace': copyRiskyPlace,
      'riskyPlace': riskyPlace,
      'markers': markers?.map(_markerToJson).toList(),
      'riskyPlaceText': riskyPlaceText,
      'markerId': markerId?.map((id) => id.value).toList(),
    };
  }

  // Helper method to convert Marker to JSON
  static Map<String, dynamic> _markerToJson(Marker marker) {
    return {
      'markerId': marker.markerId.value,
      'position': {
        'latitude': marker.position.latitude,
        'longitude': marker.position.longitude,
      },
      'infoWindow': {
        'title': marker.infoWindow.title,
        'snippet': marker.infoWindow.snippet,
      },
    };
  }

  // Helper method to convert JSON to Marker
  static Marker _markerFromJson(Map<String, dynamic> json) {
    return Marker(
      markerId: MarkerId(json['markerId'] as String),
      position: LatLng(
        json['position']['latitude'] as double,
        json['position']['longitude'] as double,
      ),
      infoWindow: InfoWindow(
        title: json['infoWindow']['title'] as String?,
        snippet: json['infoWindow']['snippet'] as String?,
      ),
    );
  }
}
