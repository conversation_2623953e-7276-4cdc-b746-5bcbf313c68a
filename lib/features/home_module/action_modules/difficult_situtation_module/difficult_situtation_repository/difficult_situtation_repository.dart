import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class DifficultSitutationRepository {
  Future<StrategiesModel?> situtationStrategy({
    required Map<String, dynamic> data,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        data,
        apiName: EndPoints.strategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return StrategiesModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> situtationStrategyforDownload({
    required BuildContext context,
    required bool isEmail,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'email': isEmail,
          // 'map':
          //     'data:image/png;base64,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',
        },
        apiName:EndPoints.dsActionStrategy,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
