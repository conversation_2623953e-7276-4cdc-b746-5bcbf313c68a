import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/gestures.dart';
import 'package:flutter/foundation.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/widgets/add_risky_widget.dart';

class GoogleMapPage extends StatefulWidget {
  const GoogleMapPage({required this.myLocationAddress, required this.locTextController, super.key, this.markers});
  final String myLocationAddress;
  final TextEditingController locTextController;
  final Set<Marker>? markers;

  @override
  State<GoogleMapPage> createState() => _GoogleMapPageState();
}

class _GoogleMapPageState extends State<GoogleMapPage> with WidgetsBindingObserver {
  GoogleMapController? _mapController;
//function for location
  Future<void> getLocation(DifficultSitutationState state) async {
    final situtationCubit = context.read<DifficultSitutationCubit>();
    try {
      final location = await Geolocator.getCurrentPosition();
      if (mounted) {
        // await situtationCubit.controller?.animateCamera(
        //   CameraUpdate.newCameraPosition(
        //     CameraPosition(
        //       target: LatLng(location.latitude, location.longitude),
        //       zoom: 12,
        //     ),
        //   ),
        // );
        try {
          situtationCubit.placeMarks = await geo.placemarkFromCoordinates(location.latitude, location.longitude);
          final place = situtationCubit.placeMarks[0];
          ' situtationCubit.placeMarks ${place.toJson()}'.logD;
          // situtationCubit.textEditingController.text =
          //     '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}, ${place.country}';
        } on Exception {
          'e'.logD;
        }

        // Check if the address is not empty before adding a marker
        //      if (situtationCubit.textEditingController.text.isNotEmpty) {
        //  // Create a new mutable Set
        //     final mutableMarkers = <Marker>{...state.marker};
        //     mutableMarkers.add(
        //       Marker(
        //         icon: situtationCubit.customIconRed ?? BitmapDescriptor.defaultMarker,
        //         markerId: const MarkerId('Home'),
        //         position: LatLng(location.latitude, location.longitude),
        //       ),
        //     );
        //     situtationCubit.updateMarkers(mutableMarkers);
        //   //  state.m = mutableMarkers;

        //     Timer(const Duration(milliseconds: 300), () async {
        //       await situtationCubit.controller!.showMarkerInfoWindow(state.marker.elementAt(0).markerId);
        //     });
        //   }
      }
    } on Exception {
      'e'.logD;
    }
  }

  Future<void> fetchSuggestions(String input) async {
    final situtationCubit = context.read<DifficultSitutationCubit>();
    if (input.trim().isEmpty) {
      situtationCubit.suggestions.value.clear();
      FocusScope.of(context).unfocus();
      return;
    }

    final url =
        'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$input&key=AIzaSyDbsJmfsdTiHM9O56vuUXKD4MsZuC7T6M8';
    final response = await http.get(Uri.parse(url));
    final jsonData = json.decode(response.body);
    final predictions = jsonData['predictions'] as List;
    situtationCubit.suggestions.value = predictions.map((e) => e['description'] as String).toList();
  }

  Future<List<Uint8List>> getBytesFromAsset(
    String path,
    int width,
  ) async {
    final data = await rootBundle.load(path);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List(), targetWidth: width);
    final fi = await codec.getNextFrame();
    return [
      (await fi.image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List(),
    ];
  }

  Future<dynamic> createMarker(BuildContext ctx) async {
    final situtationCubit = ctx.read<DifficultSitutationCubit>();

    if (situtationCubit.customIconRed == null) {
      await getBytesFromAsset('assets/icons/red_marker_icon.png', 140).then((
        value,
      ) async {
        // situtationCubit.customIconRed = BitmapDescriptor.fromBytes(value[0]);
        situtationCubit.customIconRed = await situtationCubit.createCustomMarkerWithText(
          '${situtationCubit.state.marker.length}',
        );

        return situtationCubit.customIconRed;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    final situtationCubit = context.read<DifficultSitutationCubit>();
    '=====>${situtationCubit.state.marker}'.logV;

    createMarker(context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      situtationCubit.textEditingController.text = widget.myLocationAddress;

      // Fetch location if no address is provided
      if (situtationCubit.textEditingController.text.isEmpty) {
        await getLocation(situtationCubit.state);
      } else {
        final data = await AppCommonFunctions.getLatLongFromAddress(address: widget.myLocationAddress);
        if (mounted) {
          await situtationCubit.controller?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(data?.latitude ?? 0.0, data?.longitude ?? 0.0),
                //  zoom: 12,
              ),
            ),
          );
          try {
            situtationCubit.placeMarks =
                await geo.placemarkFromCoordinates(data?.latitude ?? 0.0, data?.longitude ?? 0.0);
            final place = situtationCubit.placeMarks[0];
            situtationCubit.textEditingController.text =
                '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}, ${place.country}';
          } on Exception {
            'exception '.logD;
          }
        }
      }
      // Robust: Try to zoom to user location if needed
      await situtationCubit.zoomToUserLocationIfNeeded();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      final situtationCubit = context.read<DifficultSitutationCubit>();
      situtationCubit.zoomToUserLocationIfNeeded();
    }
  }

  @override
  Widget build(BuildContext context) {
    final situtationCubit = context.read<DifficultSitutationCubit>();
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: ValueListenableBuilder(
        valueListenable: situtationCubit.suggestions,
        builder: (context, value, child) {
          return ValueListenableBuilder(
            valueListenable: situtationCubit.curIndex,
            builder: (context, value, child) {
              return Column(
                children: <Widget>[
                  Flexible(
                    child: Stack(
                      children: <Widget>[
                        GoogleMap(
                          zoomControlsEnabled: false,
                          myLocationButtonEnabled: false,
                          compassEnabled: false,
                          mapToolbarEnabled: false,
                          // gestureRecognizers: const <Factory<OneSequenceGestureRecognizer>>{
                          //   Factory<ScaleGestureRecognizer>(ScaleGestureRecognizer.new),
                          //   Factory<TapGestureRecognizer>(TapGestureRecognizer.new),
                          //   Factory<PanGestureRecognizer>(PanGestureRecognizer.new),
                          // },
                          gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
  const Factory<ScaleGestureRecognizer>(ScaleGestureRecognizer.new),
  const Factory<TapGestureRecognizer>(TapGestureRecognizer.new),
  const Factory<PanGestureRecognizer>(PanGestureRecognizer.new),
  Factory<OneSequenceGestureRecognizer>(() => EagerGestureRecognizer()),
},

                          onMapCreated: (GoogleMapController controller) async {
                            situtationCubit.controller = controller;
                            situtationCubit.customInfoWindowController.googleMapController = controller;

                            if (situtationCubit.state.marker.isNotEmpty) {
                              final bounds = situtationCubit.calculateBounds(situtationCubit.state.marker);
                              await controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
                            } else {
                              // Fallback to default if not zooming to user location
                              final lat = countryData['lat'] as double;
                              final lng = countryData['lng'] as double;
                              final zoom = countryData['zoom'] as double;
                              await controller.animateCamera(
                                CameraUpdate.newCameraPosition(
                                  CameraPosition(
                                    target: LatLng(lat, lng),
                                    zoom: zoom - 2,
                                  ),
                                ),
                              );
                              // Robust: Try to zoom to user location if needed (controller is now ready)
                              await situtationCubit.zoomToUserLocationIfNeeded();
                            }
                          },
                          onCameraMove: (position) {
                            situtationCubit.customInfoWindowController.onCameraMove!();
                          },
                          onTap: (argument) async {
                            situtationCubit.customInfoWindowController.hideInfoWindow!();
                            if (!situtationCubit.riskyPlaceVisible.value) {
                              // Add new marker
                              await situtationCubit.addMarker(argument, context);
                            } else {
                              // Move existing marker to new position
                              await situtationCubit.moveCurrentMarker(argument, context);
                            }
                          },
                          markers: widget.markers ?? situtationCubit.getAllMarkers(),
                          initialCameraPosition: _initialCameraPosition(),
                        ),
                        ValueListenableBuilder<bool>(
                          valueListenable: situtationCubit.isShowLoading,
                          builder: (context, isLoading, child) {
                            if (!isLoading) return SizedBox.shrink();
                            return Container(
                              child:  Center(
                                child: AppLoader(isShowLoader: isLoading, child: SizedBox()), // Replace with AppLoader if you have a custom loader
                              ),
                            );
                          },
                        ),
                        Container(
                          padding: EdgeInsets.all(AppSize.w10),
                          width: MediaQuery.of(context).size.width * 0.6,
                          child: Column(
                            children: [
                              ValueListenableBuilder(
                                valueListenable: situtationCubit.input,
                                builder: (context, value, child) {
                                  return CustomOutlinedTextfield(
                                    fillColor: Colors.white,
                                    borderRadius: 0,
                                    controller: situtationCubit.textEditingController,
                                    hintText: AsLocaleKeys.lsDsLocation.tr(),
                                    onTap: () {
                                      if (situtationCubit.input.value.isNotEmpty) {
                                        situtationCubit.input.value = '';
                                      }
                                    },
                                    onChanged: (value) {
                                      log('valuevalue$value');
                                      situtationCubit.input.value = value ?? '';
                                      fetchSuggestions(situtationCubit.input.value);
                                    },
                                  );
                                },
                              ),
                              if (situtationCubit.suggestions.value.isNotEmpty &&
                                  situtationCubit.input.value.isNotEmpty) // Display suggestions dropdown
                                Container(
                                  height: AppSize.h200,
                                  padding: EdgeInsets.all(AppSize.w10),
                                  color: Colors.white,
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    itemCount: situtationCubit.suggestions.value.length,
                                    itemBuilder: (context, index) {
                                      return ListTile(
                                        title: Text(situtationCubit.suggestions.value[index]),
                                        onTap: () async {
                                          situtationCubit.customInfoWindowController.hideInfoWindow!();
                                          situtationCubit.riskyPlaceVisible.value = false;

                                          final LatLng? selectedLatLng = await situtationCubit.onSuggestionSelected2(
                                            situtationCubit.suggestions.value[index],context
                                          );

                                          situtationCubit.suggestions.value.clear();

                                          // Zoom in more if we got a valid location
                                          if (selectedLatLng != null && situtationCubit.controller != null) {
                                            await situtationCubit.controller!.animateCamera(
                                              CameraUpdate.newCameraPosition(
                                                CameraPosition(
                                                  target: selectedLatLng,
                                                  zoom: 15.0, // or 16.0 for even closer
                                                ),
                                              ),
                                            );
                                          }
                                        },
                                      );
                                    },
                                  ),
                                ),
                            ],
                          ),
                        ),
                        CustomInfoWindow(
                          controller: situtationCubit.customInfoWindowController,
                          height: AppSize.h100,
                          width: AppSize.w150,
                          offset: 85,
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class MarkerIconHelper {
  // Method to load custom icons for markers
  static Future<BitmapDescriptor> loadCustomIcon(String assetPath, int size) async {
    final bytes = await getBytesFromAsset(assetPath, size);
    return BitmapDescriptor.fromBytes(bytes);
  }
}

const Map<String, dynamic> countryMap = {
  'UK': {'lat': 55.3781, 'lng': -3.4360, 'zoom': 5.0}, // United Kingdom
  'US': {'lat': 37.0902, 'lng': -95.7129, 'zoom': 4.0}, // USA
  'AU': {'lat': -25.2744, 'lng': 133.7751, 'zoom': 0.0}, // Australia
  'CA': {'lat': 56.1304, 'lng': -106.3468, 'zoom': 4.0}, // Canada
};

String countryCode = navigatorKey.currentContext!.locale.countryCode?.toUpperCase() ??
    'UK'; // Replace with actual method to get the country code
Map<String, dynamic> countryData = (countryMap[countryCode] ?? countryMap['UK']) as Map<String, dynamic>;

CameraPosition _initialCameraPosition() {
  final lat = countryData['lat'] as double;
  final lng = countryData['lng'] as double;
  final zoom = countryData['zoom'] as double;
  return CameraPosition(
    target: LatLng(lat, lng),
    zoom: zoom - 2, // Use the same logic as your fallback
  );
}

Future<Uint8List> getBytesFromAsset(String path, int width) async {
  final data = await rootBundle.load(path);
  final codec = await ui.instantiateImageCodec(
    data.buffer.asUint8List(),
    targetWidth: width,
  );
  final frameInfo = await codec.getNextFrame();
  return (await frameInfo.image.toByteData(format: ui.ImageByteFormat.png))!.buffer.asUint8List();
}
