// import 'package:breakingfree_v2/extensions/ext_build_context.dart';
// import 'package:breakingfree_v2/features/home_module/action_module/difficult_situtation_module/google_map/place_provider.dart';
// import 'package:breakingfree_v2/features/home_module/action_module/difficult_situtation_module/google_map/suggetion_model.dart';
// import 'package:breakingfree_v2/res/appnavigation.dart';
// import 'package:breakingfree_v2/res/dimension.dart';
// import 'package:flutter/material.dart';

// class AddressSearch extends SearchDelegate<Suggestion> {
//   List<Suggestion> suggestions = []; // Store suggestions

//   @override
//   List<Widget> buildActions(BuildContext context) {
//     return [
//       IconButton(
//         icon: const Icon(Icons.clear),
//         onPressed: () {
//           query = '';
//           suggestions.clear(); // Clear suggestions when the query is cleared
//           showSuggestions(context); // Refresh the suggestions list
//         },
//       ),
//     ];
//   }

//   @override
//   Widget buildLeading(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         if (FocusScope.of(context).isFirstFocus) {
//           FocusScope.of(context).unfocus();
//         }
//         AppNavigation.previousScreen(context);
//       },
//       child: Container(
//         margin: EdgeInsets.all(AppSize.h6),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(AppSize.sp14),
//         ),
//         child: const Icon(Icons.arrow_back),
//       ),
//     );
//   }

//   @override
//   ThemeData appBarTheme(BuildContext context) {
//     final theme = Theme.of(context);
//     return theme.copyWith(
//       brightness: Brightness.light,
//       textTheme: theme.textTheme.copyWith(
//         titleLarge: theme.textTheme.titleSmall?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
//       ),
//       inputDecorationTheme: theme.inputDecorationTheme.copyWith(
//         contentPadding: EdgeInsets.symmetric(vertical: AppSize.h14, horizontal: AppSize.w10),
//         hintStyle: theme.textTheme.titleSmall?.copyWith(color: Colors.grey),
//         labelStyle: theme.textTheme.titleSmall?.copyWith(color: Colors.black),
//         isDense: true,
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppSize.sp12),
//           borderSide: const BorderSide(color: Colors.grey),
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppSize.sp12),
//           borderSide: const BorderSide(color: Colors.grey),
//         ),
//       ),
//       appBarTheme: theme.appBarTheme.copyWith(
//         backgroundColor: Colors.white,
//         titleSpacing: AppSize.w10,
//       ),
//     );
//   }

//   @override
//   Widget buildSuggestions(BuildContext context) {
//     if (query.isEmpty) {
//       return _buildPlaceholder(context, 'Enter your address');
//     } else {
//       return FutureBuilder<List<Suggestion>>(
//         future: PlaceApiProvider().fetchSuggestions(
//           input: query,
//           lang: Localizations.localeOf(context).languageCode,
//         ),
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return _buildLoadingIndicator(context);
//           } else if (snapshot.hasError) {
//             return _buildErrorText(context, 'Error loading suggestions');
//           } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
//             suggestions = snapshot.data!; // Update suggestions
//             return _buildSuggestionList(context, suggestions);
//           } else {
//             return _buildErrorText(context, 'No suggestions found');
//           }
//         },
//       );
//     }
//   }

//   @override
//   Widget buildResults(BuildContext context) {
//     return _buildPlaceholder(context, 'Your address not found');
//   }

//   Widget _buildPlaceholder(BuildContext context, String text) {
//     return Container(
//       height: MediaQuery.of(context).size.height,
//       width: double.infinity,
//       color: Colors.white,
//       padding: EdgeInsets.all(AppSize.h16),
//       child: Text(
//         text,
//         style: context.textTheme.titleLarge,
//       ),
//     );
//   }

//   Widget _buildLoadingIndicator(BuildContext context) {
//     return const Center(
//       child: CircularProgressIndicator(),
//     );
//   }

//   Widget _buildErrorText(BuildContext context, String text) {
//     return Center(
//       child: Text(
//         text,
//         style: context.textTheme.titleLarge?.copyWith(color: Colors.red),
//       ),
//     );
//   }

//   @override
//   Widget _buildSuggestionList(BuildContext context, List<Suggestion> suggestions) {
//     return ListView.builder(
//       itemCount: suggestions.length,
//       itemBuilder: (context, index) {
//         return ListTile(
//           title: Text(
//             suggestions[index].description,
//             style: context.textTheme.titleMedium,
//           ),
//           onTap: () {
//             // Close the search and return the selected suggestion
//             close(context, suggestions[index]);
//           },
//         );
//       },
//     );
//   }
// }
