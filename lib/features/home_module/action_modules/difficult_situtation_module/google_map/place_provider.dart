// ignore_for_file: avoid_dynamic_calls

import 'dart:async';
import 'dart:convert';

import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/google_map/suggetion_model.dart';
import 'package:http/http.dart';

class PlaceApiProvider {
  final client = Client();
  final String apiKey = 'ADD API KEY';

  Future<List<Suggestion>> fetchSuggestions({
    required String input,
    required String lang,
  }) async {
    final request = 'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$input&language=$lang&key=$apiKey';
    final response = await client.get(Uri.parse(request));

    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      if (result['status'] == 'OK') {
        return (result['predictions'] as List)
            .map<Suggestion>((p) => Suggestion(p['place_id'].toString(), p['description'].toString()))
            .toList();
      }
      if (result['status'] == 'ZERO_RESULTS') {
        return [];
      }
      throw Exception(result['error_message']);
    } else {
      throw Exception('Failed to fetch suggestion');
    }
  }
}
