import 'dart:developer';
import 'dart:ui' as ui;
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/difficult_situtation_repository/difficult_situtation_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/google_map/google_map_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/pages/situation_well_done_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/widgets/custom_info_window.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/location_service/cubit/location_service_cubit.dart';
import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:get_it/get_it.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:geolocator/geolocator.dart';

part 'difficult_situtation_cubit.freezed.dart';
part 'difficult_situtation_state.dart';

class DifficultSitutationCubit extends Cubit<DifficultSitutationState> {
  DifficultSitutationCubit() : super(DifficultSitutationState()) {

    listener = AppLifecycleListener(
      onResume: () {
        WidgetsBinding.instance.addPostFrameCallback(
          (timeStamp) async {
            final status = await Permission.locationWhenInUse.status;
            'checkLocationForPermission ===> $status'.logD;

            if (!status.isGranted) {
              isPermission.value = true;
            } else {
              isPermission.value = false;
            }
          },
        );
      },
    );

  }


  final DifficultSitutationRepository repository = DifficultSitutationRepository();
  final ScrollController scrollController = ScrollController();
  final GlobalKey addRiskyPlaceKey = GlobalKey();
  ValueNotifier<bool> isRiskyButton = ValueNotifier(false);
  ValueNotifier<bool> isAnotherRiskyButton = ValueNotifier(false);
  ValueNotifier<bool> isAnotherCopingButton = ValueNotifier(false);
//audio variable for situation screen
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  AuthRepository authRepository = AuthRepository();

//audio variable for situation action plan  screen
  ValueNotifier<String?> headerPlanInfoText = ValueNotifier(null);
  ValueNotifier<String?> infoPlanAudioUrl = ValueNotifier(AsLocaleKeys.lsDsActionPlanAudio.tr());
  // ValueNotifier<bool> isAudioPlanPanelVisible = ValueNotifier(false);

  ValueNotifier<String?> infoWellDoneAudioUrl = ValueNotifier(AsLocaleKeys.lsDsSummaryAudioApp.tr());
  ValueNotifier<bool> isAudioWellDonePanelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerWellDoneInfoText = ValueNotifier(null);
  ValueNotifier<bool> headerVideoUrl = ValueNotifier(false);
  ValueNotifier<bool> isPermission = ValueNotifier(false);
  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isSaveIconClick = ValueNotifier(false);
  ValueNotifier<bool> isRiskyPlaceSelect = ValueNotifier(false);
  ValueNotifier<bool> isCustomWhyFieldVisible = ValueNotifier(false);
  ValueNotifier<bool> isCustomHowFieldVisible = ValueNotifier(false);
  ValueNotifier<bool> isCustomHowFieldVisibleError = ValueNotifier(false);
  ValueNotifier<bool> isCustomWhyFieldVisibleError = ValueNotifier(false);
  ValueNotifier<bool> isCopingSelect = ValueNotifier(false);
  ValueNotifier<String?> isSelected = ValueNotifier(null);
  TextEditingController customWhyTextController = TextEditingController();
  TextEditingController customHowTextController = TextEditingController();

  ValueNotifier<bool> isZoom = ValueNotifier(true);
  ValueNotifier<bool> shouldZoomToUserLocation = ValueNotifier(false);

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final scaffoldWellDoneKey = GlobalKey<ScaffoldState>();
  final scaffoldActionPlanKey = GlobalKey<ScaffoldState>();
  late final AppLifecycleListener listener;

  //map screen variables
  GoogleMapController? controller;
  String address = '';
  List<geo.Placemark> placeMarks = [];
  ValueNotifier<bool> isShowLoading = ValueNotifier(false);
  ValueNotifier<List<String>> suggestions = ValueNotifier([]);
  ValueNotifier<String> input = ValueNotifier('');
  BitmapDescriptor? customIconRed;

  ValueNotifier<bool> riskyPlaceVisible = ValueNotifier(false);
  ValueNotifier<int> editingIndex = ValueNotifier(-1);
  ValueNotifier<int> curIndex = ValueNotifier(0);

  final markers = <Marker>[];

  final TextEditingController riskyPlaceController = TextEditingController();
  final TextEditingController textEditingController = TextEditingController();

  final CustomInfoWindowController customInfoWindowController = CustomInfoWindowController();

  ValueNotifier<bool> situtationInfoVisible = ValueNotifier(false);
  GlobalKey<FormState> key = GlobalKey<FormState>();

  Marker? currentMarker;

  List<String> situtationWhyList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsDsQuestionsWhyOptions,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  List<String> situtationHowList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsDsQuestionsHowOptions,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  LatLng? pendingLatLng;

  void clearAudioData() {
    isManuallyPaused.value = false;
    headerInfoText.value = null;
    infoAudioUrl.value = null;
    //isAudioPanelVisible.value = false;
  }

  void clearWellDoneAudioData() {
    headerWellDoneInfoText.value = null;
    infoWellDoneAudioUrl.value = null;
    infoPlanAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
    // isAudioWellDonePanelVisible.value = false;
  }

  void clearPlanAudioData() {
    headerPlanInfoText.value = null;
    infoPlanAudioUrl.value = null;
    // isAudioPlanPanelVisible.value = false;
  }

  void updateMarkers(Set<Marker> markers) {
    emit(state.copyWith(marker: markers));
  }

  void updateSelecteCopyPlace(String? newDrugValue) {
    if(newDrugValue ==  situtationHowList[situtationHowList.length -1]){
      isCustomHowFieldVisibleError.value = false;
      isCustomHowFieldVisible.value = false;
    }
    emit(state.copyWith(selecteCopyPlaceValue: newDrugValue ?? ''));

  }

  void updateriskyPlace(String? newDrugValue) {
    if(newDrugValue ==  situtationWhyList[situtationWhyList.length -1]) {
      isCustomWhyFieldVisibleError.value = false;
      isCustomWhyFieldVisible.value = false;

    }
    emit(state.copyWith(selecteRiskyPlaceValue: newDrugValue ?? ''));

  }

  void updateMarkerIndex(int? markerIndex) {
    emit(state.copyWith(markerIndex: markerIndex ?? 0));
  }

  Set<Marker> getAllMarkers() { 
    return Set<Marker>.from(state.marker);
  }

double getEstimatedZoomLevelForArea(double areaInKm) {
  if (areaInKm > 1000) return 4.0; // country
  if (areaInKm > 500) return 6.0; // large state
  if (areaInKm > 100) return 8.0; // region
  if (areaInKm > 10) return 12.0; // city
  return 15.0; // street
}



  Future<BitmapDescriptor> createCustomMarkerWithText(String text) async {
    // Load base image from assets
    final byteData = await rootBundle.load('assets/icons/red_marker_icon.png');
    final markerImageBytes = byteData.buffer.asUint8List();

    // Decode image
    final codec = await ui.instantiateImageCodec(markerImageBytes, targetWidth: 130);
    final fi = await codec.getNextFrame();
    final baseImage = fi.image;

    // Prepare canvas
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();

    // Draw marker image
    canvas.drawImage(baseImage, Offset.zero, paint);

    final textStyle = navigatorKey.currentContext!.textTheme.titleSmall?.copyWith(
      fontWeight: FontWeight.w500,
      fontSize: 40,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: ui.TextDirection.rtl,
    );

    textPainter.layout();
    final xCenter = (baseImage.width - textPainter.width - 2) / 2;
    final yCenter = (baseImage.height - textPainter.height - 42) / 2;
    textPainter.paint(canvas, Offset(xCenter, yCenter));

    // Convert to image
    final finalImage = await recorder.endRecording().toImage(
          baseImage.width + 40,
          baseImage.height + 40,
        );

    final pngBytes = await finalImage.toByteData(format: ui.ImageByteFormat.png);
    return BitmapDescriptor.fromBytes(pngBytes!.buffer.asUint8List());
  }


  Future<void> updateMarkerPosition(LatLng newPosition) async {
    try {
      final existingMarkers = Set<Marker>.from(state.marker);
      final markerToUpdate = currentMarker;

      if (markerToUpdate != null) {
        // Remove the old marker
        existingMarkers.removeWhere((marker) => marker.markerId == markerToUpdate.markerId);

        final updatedMarker = Marker(
          markerId: markerToUpdate.markerId,
          position: newPosition,
          icon: markerToUpdate.icon,
          onTap: () {
            final tappedIndex = int.parse(markerToUpdate.markerId.value.split('_')[1]);
            'Tapped marker index: $tappedIndex'.logV;

            final mapDetail = state.mapDetailList[tappedIndex];

            customInfoWindowController.addInfoWindow!(
              CustomInfoWindowDialog(
                onTap: () {
                  customInfoWindowController.hideInfoWindow!();
                },
                what: mapDetail.what.toString(),
                why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)?mapDetail.customWhy ?? '': situtationWhyList[mapDetail.why ?? 0],
                // why: situtationWhyList[mapDetail.why ?? 0],
                how: (mapDetail.how??0) == (situtationHowList.length - 1)?(mapDetail.customHow??''): situtationHowList[mapDetail.how??0],
                // how: situtationHowList[mapDetail.how ?? 0],
              ),
              newPosition,
            );
          },
        );

          // Add updated marker
        existingMarkers.add(updatedMarker);

        // Update current marker reference
        currentMarker = updatedMarker;

        // Update markers state
        updateMarkers(existingMarkers);

        // Optionally animate camera
        await controller?.animateCamera(CameraUpdate.newLatLng(newPosition));
      }
    } catch (e) {
      log('Error updating marker position: $e');
    }
  }


  Future<void> addMarker(LatLng position, BuildContext context) async {
    try {
      // Create a new marker that allows map tapping
      final index = state.mapDetailList.length;
      final tempId = MarkerId('marker_$index');
      final newMarker = Marker(
        markerId: tempId,
        position: position,
        icon: await createCustomMarkerWithText(''),
        onTap: () {
          // Show info window when marker is tapped
          final mapDetail = state.mapDetailList.isNotEmpty && index < state.mapDetailList.length 
              ? state.mapDetailList[index] 
              : null;
          if (mapDetail != null) {
            customInfoWindowController.addInfoWindow!(
              CustomInfoWindowDialog(
                onTap: () {
                  customInfoWindowController.hideInfoWindow!();
                },
                what: mapDetail.what.toString(),
                why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)?mapDetail.customWhy ?? '': situtationWhyList[mapDetail.why ?? 0],
                how: (mapDetail.how??0) == (situtationHowList.length - 1)?(mapDetail.customHow??''): situtationHowList[mapDetail.how??0],
              ),
              position,
            );
          }
        },
        // Allow map tapping to pass through
        consumeTapEvents: false,
      );
      
      final existingMarkers = Set<Marker>.from(state.marker)..add(newMarker);
      currentMarker = newMarker;
      riskyPlaceVisible.value = true;
      updateMarkers(existingMarkers);
      emit(state.copyWith(mapDetailList: state.mapDetailList));
      FocusScope.of(navigatorKey.currentContext!).unfocus();
    } catch (e) {
      log('Error preparing to add marker: $e');
    }
  }

  Future<void> moveCurrentMarker(LatLng newPosition, BuildContext context) async {
  try {
    if (currentMarker != null) {
      // Step 1: Clone existing markers and remove the current one
      final updatedMarkers = Set<Marker>.from(state.marker);
      updatedMarkers.remove(currentMarker!);

      // Step 2: Create a unique MarkerId using current timestamp
      final uniqueId = DateTime.now().millisecondsSinceEpoch;
      final tempId = MarkerId('marker_$uniqueId');

      // Step 3: Create the new marker with new position and id
      final index = state.mapDetailList.length;
      final newMarker = Marker(
        markerId: tempId,
        position: newPosition,
        icon: await createCustomMarkerWithText(''),
        onTap: () {
          // Optional: Handle marker tap and show custom info window
          final mapDetail = state.mapDetailList.isNotEmpty && index < state.mapDetailList.length
              ? state.mapDetailList[index]
              : null;

          if (mapDetail != null) {
            customInfoWindowController.addInfoWindow!(
              CustomInfoWindowDialog(
                onTap: () {
                  customInfoWindowController.hideInfoWindow!();
                },
                what: mapDetail.what.toString(),
                why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)
                    ? mapDetail.customWhy ?? ''
                    : situtationWhyList[mapDetail.why ?? 0],
                how: (mapDetail.how ?? 0) == (situtationHowList.length - 1)
                    ? mapDetail.customHow ?? ''
                    : situtationHowList[mapDetail.how ?? 0],
              ),
              newPosition,
            );
          }
        },
        consumeTapEvents: false,
      );

      // Step 4: Add the new marker and assign it to currentMarker
      updatedMarkers.add(newMarker);
      currentMarker = newMarker;

      // Step 5: Update the markers on map
      updateMarkers(updatedMarkers);
    }
  } catch (e) {
    log('Error moving current marker: $e');
  }
}



  // Future<void> moveCurrentMarker(LatLng newPosition, BuildContext context) async {
  //   try {
  //     if (currentMarker != null) {
  //       // Remove the current marker
  //       final updatedMarkers = Set<Marker>.from(state.marker);
  //       updatedMarkers.remove(currentMarker!);
        
  //       // Create new marker at the new position
  //       final index = state.mapDetailList.length;
  //       final tempId = MarkerId('marker_$index');
  //       final newMarker = Marker(
  //         markerId: tempId,
  //         position: newPosition,
  //         icon: await createCustomMarkerWithText(''),
  //         onTap: () {
  //           // Show info window when marker is tapped
  //           final mapDetail = state.mapDetailList.isNotEmpty && index < state.mapDetailList.length 
  //               ? state.mapDetailList[index] 
  //               : null;
  //           if (mapDetail != null) {
  //             customInfoWindowController.addInfoWindow!(
  //               CustomInfoWindowDialog(
  //                 onTap: () {
  //                   customInfoWindowController.hideInfoWindow!();
  //                 },
  //                 what: mapDetail.what.toString(),
  //                 why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)?mapDetail.customWhy ?? '': situtationWhyList[mapDetail.why ?? 0],
  //                 how: (mapDetail.how??0) == (situtationHowList.length - 1)?(mapDetail.customHow??''): situtationHowList[mapDetail.how??0],
  //               ),
  //               newPosition,
  //             );
  //           }
  //         },
  //         consumeTapEvents: false,
  //       );
        
  //       // Add the new marker and update current marker
  //       updatedMarkers.add(newMarker);
  //       currentMarker = newMarker;
  //       updateMarkers(updatedMarkers);
        
  //       // Removed camera animation - marker will move without scrolling the map
  //     }
  //   } catch (e) {
  //     log('Error moving current marker: $e');
  //   }
  // }

Future<void> zoomToFitAllMarkers(GoogleMapController controller, List<Marker> markers) async {
  if (markers.isEmpty) return;

  if (markers.length == 1) {
    // Only one marker, zoom in closer
    await controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: markers.first.position,
          zoom: 15.0, // closer zoom
        ),
      ),
    );
  } else {
    // Multiple markers, calculate bounds
    LatLngBounds bounds = _createBoundsFromMarkers(markers);
    await controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50)); // padding 50
  }
}


LatLngBounds _createBoundsFromMarkers(List<Marker> markers) {
  double x0 = markers.first.position.latitude;
  double x1 = markers.first.position.latitude;
  double y0 = markers.first.position.longitude;
  double y1 = markers.first.position.longitude;

  for (Marker marker in markers) {
    var lat = marker.position.latitude;
    var lng = marker.position.longitude;

    if (lat < x0) x0 = lat;
    if (lat > x1) x1 = lat;
    if (lng < y0) y0 = lng;
    if (lng > y1) y1 = lng;
  }

  return LatLngBounds(
    southwest: LatLng(x0, y0),
    northeast: LatLng(x1, y1),
  );
}


Future<LatLng?> onSuggestionSelected2(String suggestion,BuildContext context) async {
  // Perform geocoding / fetch lat-lng for the suggestion
  final LatLng position = await AppCommonFunctions.getLatLongFromAddress(address:suggestion) ?? LatLng(0.0, 0.0);
  
  // Add marker, update state, etc...
  await addMarker(position,context); // for example
  return position;
}


  Future<void> onSuggestionSelected(String suggestion) async {
    textEditingController.text = suggestion;
    input.value = suggestion;

    final data = await AppCommonFunctions.getLatLongFromAddress(address: suggestion);

    final latLng = LatLng(data?.latitude ?? 0.0, data?.longitude ?? 0.0);

    // Animate to searched location
    await controller?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: latLng, zoom: 12),
      ),
    );

    final existingMarkers = Set<Marker>.from(state.marker);
    final index = state.mapDetailList.length;
    final tempId = MarkerId('marker_$index');

    final newMarker = Marker(
      markerId: tempId,
      position: latLng,
      icon: await createCustomMarkerWithText(''),
      onTap: () {
        final mapDetail = state.mapDetailList[index];
        customInfoWindowController.addInfoWindow!(
          CustomInfoWindowDialog(
            onTap: () {
              customInfoWindowController.hideInfoWindow!();
            },
            what: mapDetail.what.toString(),
            why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)?mapDetail.customWhy ?? '': situtationWhyList[mapDetail.why ?? 0],
            // why: situtationWhyList[mapDetail.why ?? 0],
            how: (mapDetail.how??0) == (situtationHowList.length - 1)?(mapDetail.customHow??''): situtationHowList[mapDetail.how??0],
            // how: situtationHowList[mapDetail.how ?? 0],
          ),
          latLng,
        );
      },
    );

    existingMarkers.add(newMarker);
    currentMarker = newMarker;
    riskyPlaceVisible.value = true;

    updateMarkers(existingMarkers);
    emit(state.copyWith(mapDetailList: state.mapDetailList));

    // final mapDetail = state.mapDetailList.isNotEmpty ? state.mapDetailList.last : null;
    // if (mapDetail != null) {
    //   customInfoWindowController.addInfoWindow!(
    //     CustomInfoWindowDialog(
    //       onTap: () => customInfoWindowController.hideInfoWindow!(),
    //       what: mapDetail.what ?? '',
    //       why: situtationWhyList[mapDetail.why ?? 0],
    //       how: situtationHowList[mapDetail.how ?? 0],
    //     ),
    //     latLng,
    //   );
    // }

    // Remove focus from search
    FocusScope.of(navigatorKey.currentContext!).unfocus();
  }


/*
  LatLngBounds calculateBounds(Set<Marker> markers) {

    var minLat = markers.first.position.latitude;
    var maxLat = markers.first.position.latitude;
    var minLng = markers.first.position.longitude;
    var maxLng = markers.first.position.longitude;

    for (final marker in markers) {
      final lat = marker.position.latitude;
      final lng = marker.position.longitude;

      if (lat < minLat) minLat = lat;
      if (lat > maxLat) maxLat = lat;
      if (lng < minLng) minLng = lng;
      if (lng > maxLng) maxLng = lng;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }
*/



Future<void> applyNumberedIconToAllMarkersWithList(List<Datum> mapDetailList) async {
  final updatedMarkers = <Marker>{};
  for (var i = 0; i < mapDetailList.length; i++) {
    final datum = mapDetailList[i];
    final markerId = MarkerId('${datum.lat}-${datum.lng}');
    final marker = Marker(
      markerId: markerId,
      position: LatLng(datum.lat!, datum.lng!),
      icon: await createCustomMarkerWithText('${i + 1}'),
      onTap: () {
        customInfoWindowController.addInfoWindow!(
          CustomInfoWindowDialog(
            onTap: () {
              customInfoWindowController.hideInfoWindow!();
            },
            what: datum.what.toString(),
            why: (datum.why ?? 0) == (situtationWhyList.length - 1)?datum.customWhy ?? '': situtationWhyList[datum.why ?? 0],
            how: (datum.how??0) == (situtationHowList.length - 1)?(datum.customHow??''): situtationHowList[datum.how??0],
          ),
          LatLng(datum.lat!, datum.lng!),
        );
      },
    );
    updatedMarkers.add(marker);
  }
  updateMarkers(updatedMarkers);
}



  LatLngBounds calculateBounds(Set<Marker> markers) {
    'marker calculateBounds ${markers.map(
      (e) => '${e.position}',
    )}'
        .logV;
    if (markers.isEmpty) {
      const defaultLat = 53.6878378;
      const defaultLng = -1.3827848;

      return LatLngBounds(
        southwest: const LatLng(defaultLat - 0.001, defaultLng - 0.001),
        northeast: const LatLng(defaultLat + 0.001, defaultLng + 0.001),
      );
    }
    double? minLat;
    double? maxLat;
    double? minLng;
    double? maxLng;

    for (final marker in markers) {
      final position = marker.position;
      if (minLat == null || position.latitude < minLat) minLat = position.latitude;
      if (maxLat == null || position.latitude > maxLat) maxLat = position.latitude;
      if (minLng == null || position.longitude < minLng) minLng = position.longitude;
      if (maxLng == null || position.longitude > maxLng) maxLng = position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  Future<void> data() async {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      'checkLocationForPermission ${Injector.instance<AppDB>().userModel?.user.strategies?.dsAs}'.logD;
      if ((Injector.instance<AppDB>().userModel?.user.app?.alerts?.situations ?? true) == true) {
        await LocationService.checkLocationForPermission(context: navigatorKey.currentContext!);
      }
      await checkPermissionStatus();
      // await LocationService.checkRiskyPlace(context: navigatorKey.currentContext!, isFromInitial: true);
    });
  }

  Future<void> checkPermissionStatus() async {
    final status = await Permission.locationWhenInUse.status;
    'checkLocationForPermission ===> $status'.logD;

    if (!status.isGranted) {
      isPermission.value = true;
    } else {
      isPermission.value = false;
    }
  }

  Future<void> permissionAsk() async {
    final status = await Permission.locationWhenInUse.status;
    'checkLocationForPermission$status'.logD;

    final backgroundTrack =
        await BlocProvider.of<LocationServiceCubit>(navigatorKey.currentContext!).location.isBackgroundModeEnabled();
    try {
      if (!status.isGranted) {
        final status = await Permission.locationWhenInUse.request();

        if (status.isGranted) {
          final statusPoint = await Permission.locationAlways.request();

          if (statusPoint.isGranted) {
            if (!backgroundTrack) {
              await BlocProvider.of<LocationServiceCubit>(navigatorKey.currentContext!)
                  .enableBackgroundTracking(granted: true);
            }
          } else {
            'always permisiion == ++ $status'.logD;
          }
        } else {
          isPermission.value = true;
          'always permisiion ==+ $status'.logD;
        }
      } else {
        'checkLocationForPermission ++ else'.logD;
        final status = await Permission.locationAlways.status;
        if (!status.isGranted) {
          final status = await Permission.locationAlways.request();
          if (status.isGranted) {
            if (!backgroundTrack) {
              'always permisiion $status'.logD;
              await BlocProvider.of<LocationServiceCubit>(navigatorKey.currentContext!)
                  .enableBackgroundTracking(granted: true);
            }
          } else {
            'always permisiion ==++ == $status'.logD;
          }
        }
      }
    } catch (e) {
      '_locationPermissionInIOS$e'.logD;
    }
    return;
  }



  Future<void> mapListUpdate() async {
    final entries = <MapEntry<DateTime, List<Map<String, dynamic>>>>[];
    ' Injector.instance<AppDB>().userModel?.user.strategies?.dsAs:${ Injector.instance<AppDB>().userModel?.user.strategies?.dsAs}'.logV;
    Injector.instance<AppDB>().userModel?.user.strategies?.dsAs?.forEach((element) {
      final time = element.time;
      final data = element.data;
      'element data>>>> ${element.data?.map((e) => e.toJson())}'.logV;

      if (time != null && data != null) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(time);
        entries.add(MapEntry(dateTime, data.map((datum) => datum.toJson()).toList()));
      }
    });

    entries.sort((a, b) => b.key.compareTo(a.key));
    final mostRecentEntry = entries.isNotEmpty ? entries.first : null;

    final datumList = <Datum>[];
    final markerList = <Marker>{};

    if (mostRecentEntry != null) {
      final mostRecentDataList = mostRecentEntry.value;

      for (var i = 0; i < mostRecentDataList.length; i++) {
        final mostRecentData = mostRecentDataList[i];

        final updatedDatum = Datum(
          what: mostRecentData['what'] as String,
          why: mostRecentData['why'] as int,
          how: mostRecentData['how'] as int,
          lat: mostRecentData['lat'] as double,
          lng: mostRecentData['lng'] as double,
          customHow: (mostRecentData['customHow'] ?? '') as String,
          customWhy: (mostRecentData['customWhy'] ?? '') as String,
        );

        datumList.add(updatedDatum);

        final markerId = MarkerId('${updatedDatum.lat}-${updatedDatum.lng}');
        final marker = Marker(
          markerId: markerId,
          position: LatLng(updatedDatum.lat!, updatedDatum.lng!),
          icon: await createCustomMarkerWithText('${i + 1}'),
          onTap: () {
            customInfoWindowController.addInfoWindow!(
              CustomInfoWindowDialog(
                onTap: () {
                  customInfoWindowController.hideInfoWindow!();
                },
                what: updatedDatum.what.toString(),
                why: (updatedDatum.why ?? 0) == (situtationWhyList.length - 1)?updatedDatum.customWhy ?? '': situtationWhyList[updatedDatum.why ?? 0],
                // why: situtationWhyList[mapDetail.why ?? 0],
                how: (updatedDatum.how??0) == (situtationHowList.length - 1)?(updatedDatum.customHow??''): situtationHowList[updatedDatum.how??0],
                // how: situtationHowList[mapDetail.how ?? 0],
              ),
              LatLng(updatedDatum.lat!, updatedDatum.lng!),
            );
            // customInfoWindowController.addInfoWindow!(
            //   CustomInfoWindowDialog(
            //     onTap: () => customInfoWindowController.hideInfoWindow!(),
            //     what: updatedDatum.what.toString(),
            //     why: situtationWhyList[updatedDatum.why ?? 0],
            //     how: situtationHowList[updatedDatum.how ?? 0],
            //   ),
            //   LatLng(updatedDatum.lat!, updatedDatum.lng!),
            // );
          },
        );

        markerList.add(marker);
      }
    }
    emit(state.copyWith(mapDetailList: datumList, marker: markerList));
    await controller?.animateCamera(CameraUpdate.newLatLngBounds(calculateBounds(markerList), 100));

  }


  Future<void> addDatumWithMarker(Datum datum) async {
    'datum ${datum}'.logV;
    final index = state.mapDetailList.length + 1;

    final markerId = MarkerId('${datum.lat}-${datum.lng}');
    final marker = Marker(
      markerId: markerId,
      position: LatLng(datum.lat!, datum.lng!),
      icon: await createCustomMarkerWithText( '$index'),
      onTap: () {
        final tappedIndex = index - 1;
        final mapDetail = state.mapDetailList[tappedIndex];
        customInfoWindowController.addInfoWindow!(
          CustomInfoWindowDialog(
            onTap: () {
              customInfoWindowController.hideInfoWindow!();
            },
            what: mapDetail.what.toString(),
            why: (mapDetail.why ?? 0) == (situtationWhyList.length - 1)?mapDetail.customWhy ?? '': situtationWhyList[mapDetail.why ?? 0],
            // why: situtationWhyList[mapDetail.why ?? 0],
            how: (mapDetail.how??0) == (situtationHowList.length - 1)?(mapDetail.customHow??''): situtationHowList[mapDetail.how??0],
            // how: situtationHowList[mapDetail.how ?? 0],
          ),
          LatLng(datum.lat!, datum.lng!),
        );
        // customInfoWindowController.addInfoWindow!(
        //   CustomInfoWindowDialog(
        //     onTap: () => customInfoWindowController.hideInfoWindow!(),
        //     what: mapDetail.what.toString(),
        //     why: situtationWhyList[mapDetail.why ?? 0],
        //     how: situtationHowList[mapDetail.how ?? 0],
        //   ),
        //   LatLng(datum.lat!, datum.lng!),
        // );
      },
    );

    emit(
      state.copyWith(
        mapDetailList: List<Datum>.from(state.mapDetailList)..add(datum),
        marker: Set<Marker>.from(state.marker)..add(marker),
      ),
    );
  }

  Future<void> addMapgData() async {
    final position = currentMarker?.position;
    if (position == null) return;
    final whyIndex = situtationWhyList.indexOf(state.selecteRiskyPlaceValue ?? '');
    final howIndex = situtationHowList.indexOf(state.selecteCopyPlaceValue ?? '');

    final params = {
      'how': howIndex,
      'why': whyIndex,
      'what': riskyPlaceController.text,
      'lat': position.latitude,
      'lng': position.longitude,
    };

    if (customHowTextController.text.isNotEmpty) {
      params['customHow'] = customHowTextController.text;
    }
    if (customWhyTextController.text.isNotEmpty) {
      params['customWhy'] = customWhyTextController.text;
    }

    final updatedMapList = List<Datum>.from(state.mapDetailList)..add(Datum.fromJson(params));
    Injector.instance<AppDB>().mapData = updatedMapList;

    // Re-number all markers after add
    await applyNumberedIconToAllMarkersWithList(updatedMapList);

    isButtonClicked.value = true;
    emit(
      state.copyWith(
        mapDetailList: updatedMapList,
        marker: getAllMarkers(),
      ),
    );
    riskyPlaceVisible.value = false;
    clearMapData();
    if (state.marker.length >= 2) {
      final bounds = calculateBounds(state.marker);
      await controller?.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 80),
      );
    }
    Future.delayed(const Duration(milliseconds: 100), () {
      isButtonClicked.value = false;
    });
  }

  void clearMapData() {
    emit(
      state.copyWith(
        selecteCopyPlaceValue: '',
        selecteRiskyPlaceValue: '',
      ),
    );
    riskyPlaceController.text = '';
    customHowTextController.text = '';
    customWhyTextController.text = '';
    textEditingController.text = '';
    isCustomHowFieldVisible.value = false;
    isCustomWhyFieldVisible.value = false;
    currentMarker = null;
  }

  void removeRiskyPlace(double lat, double lng) async {
    if (state.mapDetailList.isEmpty) {
      return;
    }
    final updatedMapDetailList = state.mapDetailList.where((mapData) {
      return mapData.lat != lat || mapData.lng != lng;
    }).toList();
    final updatedMarkers = state.marker.where((marker) {
      return marker.position.latitude != lat || marker.position.longitude != lng;
    }).toSet();
    riskyPlaceController.clear();
    editingIndex.value = -1;
    emit(
      state.copyWith(
        selecteCopyPlaceValue: '',
        selecteRiskyPlaceValue: '',
        mapDetailList: updatedMapDetailList, // Update the map data list
        marker: updatedMarkers, // Update the markers set
      ),
    );
    // Re-number all markers after remove
    await applyNumberedIconToAllMarkersWithList(updatedMapDetailList);
    customInfoWindowController.hideInfoWindow!();
    if (updatedMarkers.isNotEmpty) {
      final bounds = calculateBounds(updatedMarkers);
      controller?.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    } else {
      controller?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(lat, lng),
          ),
        ),
      );
    }
    textEditingController.text = '';
  }

  void clearMyMap() async {
    isRiskyButton.value = false;
    riskyPlaceVisible.value = false;
    isCopingSelect.value = false;
    // Clear the risky place controller and reset editing index
    riskyPlaceController.clear();
    editingIndex.value = -1;
    textEditingController.text = '';
    // Emit the updated state with empty map details and markers
    emit(
      state.copyWith(
        selecteCopyPlaceValue: '',
        selecteRiskyPlaceValue: '',
        mapDetailList: [], // Clear map data list
        marker: {}, // Clear markers set
      ),
    );
    await applyNumberedIconToAllMarkersWithList([]);

    // Hide any active info windows
    customInfoWindowController.hideInfoWindow!();
    final zoom = countryData['zoom'] as double;
    // Reset camera to default position
    controller?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(countryData['lat'] as double, countryData['lng'] as double),
          zoom: zoom - 2,//countryData['zoom'] as double,
        ),
      ),
    );

    'All markers and map data cleared.'.logV;
  }

  // void clearMapData() {

  // }

  void cancelOnTap() {
    final markerList = state.marker.toList();

    if (markerList.isNotEmpty) {
      final lastMarker = markerList.last;

      final updatedMarkers = Set<Marker>.from(state.marker)..remove(lastMarker);

      emit(
        state.copyWith(
          selecteCopyPlaceValue: '',
          selecteRiskyPlaceValue: '',
          marker: updatedMarkers,
        ),
      );
      // controller?.animateCamera(CameraUpdate.newLatLngBounds(calculateBounds(updatedMarkers), 100));
    } else {}
    riskyPlaceController.text = '';
    customHowTextController.text = '';
    customWhyTextController.text = '';
    isCustomWhyFieldVisible.value = false;
    isCustomHowFieldVisible.value = false;


  }

  void editRiskyPlace(
    int index,
    String newValue,
    int riskyPlaceValue,
    int copyRiskyPlace,
    String customHow,
    String customWhy,
  ) {
    if (index < 0 || index >= state.mapDetailList.length) {
      return;
    }

    final updatedMapDetailList = List<Datum>.from(state.mapDetailList);

    updatedMapDetailList[index] = updatedMapDetailList[index].copyWith(
      what: newValue,
      why: riskyPlaceValue,
      how: copyRiskyPlace,
      customHow: customHow.isNotEmpty == true ? customHow : null,
      customWhy: customWhy.isNotEmpty == true ? customWhy : null,
    );

    emit(
      state.copyWith(
        mapDetailList: updatedMapDetailList,
      ),
    );
  }

  void setEditingIndex(int index) {
    editingIndex.value = index;
    riskyPlaceController.text = state.mapDetailList[index].what ?? '';
    customHowTextController.text = state.mapDetailList[index].customHow ?? '';
    customWhyTextController.text = state.mapDetailList[index].customWhy ?? '';
    'customHow ===> ${customHowTextController.text}'.logV;
    'customHow ===> ${state.mapDetailList[index].toJson()}'.logV;
    emit(
      state.copyWith(
        selecteRiskyPlaceValue: situtationWhyList[state.mapDetailList[index].why ?? 0],
        selecteCopyPlaceValue: situtationHowList[state.mapDetailList[index].how ?? 0],
      ),
    );
  }

  void saveEditedRiskyPlace(
    String riskyPlaceText,
    int riskyPlaceValue,
    int copyRiskyPlace,
    String customHow,
    String customWhy,
  ) {
    if (editingIndex.value != -1) {
      editRiskyPlace(
        editingIndex.value,
        riskyPlaceText,
        riskyPlaceValue,
        copyRiskyPlace,
        customHow,
        customWhy,
      );
      editingIndex.value = -1; // Reset the editing index
      riskyPlaceController.clear(); // Clear the controller
      customHowTextController.clear();
      customWhyTextController.clear();
      emit(
        state.copyWith(
          selecteCopyPlaceValue: '',
          selecteRiskyPlaceValue: '',
        ),
      );
    }
  }

  Future<void> dsActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
  }) async {
    isEmail ? emit(state.copyWith(emailPdfAPILoading: true)) : emit(state.copyWith(downloadPdfAPILoading: true));
    try {
      final response = await repository.situtationStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Managing your risky situations.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? emit(state.copyWith(emailPdfAPILoading: false)) : emit(state.copyWith(downloadPdfAPILoading: false));
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? emit(state.copyWith(emailPdfAPILoading: false)) : emit(state.copyWith(downloadPdfAPILoading: false));
    }
  }

  Future<void> situtationStrategyAPI({
    required DifficultSitutationCubit ref,
    required BuildContext context,
    required Map<String, dynamic> data,
  }) async {
    emit(state.copyWith(isAPILoading: true));

    try {
      final response = await repository.situtationStrategy(
        context: context,
        data: data,
      );
      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && (response.success ?? false) == true) {
        response.strategies.logD;
        Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;
        await authRepository.getUserData(context: context);

        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(value: ref, child: SituationWellDonePage()),
        );
        editingIndex.value = -1;
      } else {
        await AppNavigation.nextScreen(
          context,
          BlocProvider.value(value: ref, child: SituationWellDonePage()),
        );
      }
      emit(state.copyWith(isAPILoading: false));
    } catch (e) {
      emit(state.copyWith(isAPILoading: false));
    }
  }

  Future<void> regenerateMarkers() async {
    await applyNumberedIconToAllMarkersWithList(state.mapDetailList);
  }

  Future<void> zoomToUserLocationIfNeeded() async {
    ////// zoomToUserLocationIfNeeded called
    if (controller == null) {
      '////// controller is null, returning'.logD;
      return;
    }
    // Wait a short time to allow state restoration
    await Future.delayed(const Duration(milliseconds: 250));
    '////// after delay, marker count: ${state.marker.length}'.logD;
    if (state.marker.isNotEmpty) {
      '////// markers exist, not zooming'.logD;
      return;
    }
    final permission = await Geolocator.checkPermission();
    '////// permission: $permission'.logD;
    if (permission != LocationPermission.always && permission != LocationPermission.whileInUse) {
      '////// permission not granted, returning'.logD;
      return;
    }
    isShowLoading.value = true;
    try {
      final position = await Geolocator.getCurrentPosition();
      '////// got user position: ${position.latitude}, ${position.longitude}'.logD;
      await controller!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(position.latitude, position.longitude),
            zoom: 16.0,
          ),
        ),
      );
      '////// animated to user location'.logD;
    } catch (e) {
      '////// error getting user location: $e'.logD;
    } finally {
      isShowLoading.value = false;
      '////// loader hidden'.logD;
    }
  }

  @override
  Future<void> close() {
    controller?.dispose();
    riskyPlaceController.dispose();
    textEditingController.dispose();
    editingIndex.dispose();
    riskyPlaceVisible.dispose();
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    headerInfoText.dispose();
    isManuallyPaused.dispose();
    headerVideoUrl.dispose();
    suggestions.dispose();
    infoPlanAudioUrl.dispose();
    infoWellDoneAudioUrl.dispose();
    input.dispose();
    situtationInfoVisible.dispose();
    clearMapData();
    scrollController.dispose();
    listener.dispose();
    customInfoWindowController.dispose();
    return super.close();
  }
}
