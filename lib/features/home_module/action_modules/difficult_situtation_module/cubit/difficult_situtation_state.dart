part of 'difficult_situtation_cubit.dart';

@freezed
class DifficultSitutationState with _$DifficultSitutationState {
  factory DifficultSitutationState({
    @Default({}) Set<Marker> marker,
    @Default(null) String? selecteRiskyPlaceValue,
    @Default(null) String? selecteCopyPlaceValue,
    @Default([]) List<Datum> mapDetailList,
    @Default(0) int markerIndex,
    @Default(false) bool isAPILoading,
    @Default(false) bool emailPdfAPILoading,
    @Default(false) bool downloadPdfAPILoading,
    
  }) = _DifficultSitutationState;
}
