import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../../res/dimension.dart';

class CustomInfoWindowDialog extends StatelessWidget {
  const CustomInfoWindowDialog({required this.what, required this.why, required this.how, super.key, this.onTap});

  final void Function()? onTap;
  final String what;
  final String why;
  final String how;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: context.themeColors.whiteColor,
          borderRadius: BorderRadius.circular(10)
      ),
      child:  Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: onTap,
                  child: Icon(Icons.close,size: 20,color: context.themeColors.greyColor,),),
              ],
            ),
            Text(what,style: context.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp10
             ),
            ),
            SizedBox(height: AppSize.h5,),
            Flexible(
              child: SingleChildScrollView(
                child: Text(why,style: context.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: context.themeColors.redColor,
                    fontSize: AppSize.sp10
                ),
                ),
              ),
            ),
            SizedBox(height: AppSize.h5,),
            Flexible(
              child: SingleChildScrollView(
                child: Text(how,style: context.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: context.themeColors.greenBtnColor,
                    fontSize: AppSize.sp10
                 ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DialogClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const radius = 10.0;
    const triangleHeight = 10.0;
    const triangleWidth = 20.0;

    final path = Path();

    // Start from top-left
    path..moveTo(0, radius)

    // Top-left corner
    ..quadraticBezierTo(0, 0, radius, 0);

    // Top-right corner
    path..lineTo(size.width - radius, 0)
    ..quadraticBezierTo(size.width, 0, size.width, radius)

    // Right side down
    ..lineTo(size.width, size.height - triangleHeight - radius);
    path.quadraticBezierTo(
      size.width,
      size.height - triangleHeight,
      size.width - radius,
      size.height - triangleHeight,
    );

    // Bottom right to triangle start
    path.lineTo((size.width + triangleWidth) / 2, size.height - triangleHeight);

    // Triangle
    path.lineTo(size.width / 2, size.height); // Triangle tip
    path.lineTo((size.width - triangleWidth) / 2, size.height - triangleHeight);

    // Bottom-left curve
    path.lineTo(radius, size.height - triangleHeight);
    path.quadraticBezierTo(0, size.height - triangleHeight, 0, size.height - triangleHeight - radius);

    // Back to top-left
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

