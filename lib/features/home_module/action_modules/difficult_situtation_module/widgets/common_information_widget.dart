// import 'dart:developer';

// import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
// import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
// import 'package:breakingfree_v2/extensions/ext_build_context.dart';
// import 'package:breakingfree_v2/extensions/ext_string_null.dart';
// import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
// import 'package:breakingfree_v2/features/home_module/action_module/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
// import 'package:breakingfree_v2/features/home_module/action_module/keys/as_locale_keys.dart';
// import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
// import 'package:breakingfree_v2/gen/assets.gen.dart';
// import 'package:breakingfree_v2/res/dimension.dart';
// import 'package:breakingfree_v2/res/space_box.dart';
// import 'package:breakingfree_v2/utils/core_locale_keys.dart';
// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';

// class CommonSituationInformationWidget extends StatelessWidget {
//   const CommonSituationInformationWidget({
//     required this.ref,
//     super.key,
//     this.onBackArrowTap,
//   });
//   final DifficultSitutationCubit ref;
//   final void Function()? onBackArrowTap;

//   @override
//   Widget build(BuildContext context) {
//     return InformationPageHeadingWidget(
//       onBackArrowTap: onBackArrowTap,
//       title: CoreLocaleKeys.titlesInformationStrategiesDifficultSituations.tr(),
//       subtitle: AsLocaleKeys.lsDsTitle.tr(),
//       icon: Assets.icons.infoPage.difficultSituation,
//       onInfoTap: () {
//         const info = '''
// Welcome to the strategy of managing your risky situations!

// This strategy will help you identify and deal with the dangerous situations that could potentially cause you to lapse.

// Watch the video to learn how to use it, and click on the lightbulb icon to learn more about it.''';
//         ref.headerInfoText.value =
//             (ref.headerInfoText.value.isNotEmptyAndNotNull && ref.headerInfoText.value == info) ? null : info;
//         ref.headerVideoUrl = ValueNotifier(true);

//         final audio = AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
//         log('audio $audio');
//         ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
//       },
//       onLearnTap: () {
//         const info = '''
// This strategy is about planning ahead to stop yourself making ‘seemingly irrelevant decisions’.\n
// These are the choices you make that may seem harmless but, unless you stay aware, could lead you into high risk situations. And then you might become tempted to lapse.\n
// So use this strategy to think very carefully about the area you live in and RECOGNISE all the situations that could be risky for you.\n
// These could be in places where you’d be likely to come under pressure from others. Or where you’d feel stressed, anxious or low. Or where your memories could trigger cravings and urges.\n
// Once you’ve identified where these risky situations are located, it’s much easier to AVOID them completely. This is always the safest thing to do.\n
// But in case any of these places can’t be avoided, you can also plan how you will COPE positively with each one. This will help you stay safe and keep your recovery on track!''';
//         ref.headerInfoText.value =
//             (ref.headerInfoText.value.isNotEmptyAndNotNull && ref.headerInfoText.value == info) ? null : info;
//         ref.headerVideoUrl = ValueNotifier(false);
//         final audio = AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
//         ref.infoAudioUrl.value = ref.infoAudioUrl.value == audio ? null : audio;
//       },
//       infoWidget: ValueListenableBuilder(
//         valueListenable: ref.headerInfoText,
//         builder: (context, headerInfoTextV, _) {
//           return CustomInfoWidget(
//             customWidget: Column(
//               children: [
//                 AppTextWidget(
//                   ref.headerInfoText.value ?? '',
//                   style: context.textTheme.titleSmall?.copyWith(
//                     color: context.themeColors.darkOrangeColor,
//                     fontSize: AppSize.sp13,
//                   ),
//                 ),
//                 if (ref.headerVideoUrl.value != false) ...[
//                   SpaceV(AppSize.h16),
//                   VideoPlayerScreen(
//                     imageList: const [
//                       'https://d24v3ngjgcwbka.cloudfront.net/images/welcome.poster.png',
//                     ],
//                     //  onTap: onPlayTap,
//                     navigationFunction: () {},
//                     videoList: [AsLocaleKeys.lsDsInfoPanelsInformationVideo.tr()],
//                   ),
//                 ],
//               ],
//             ),
//             onCloseTap: () {
//               ref.headerInfoText.value = null;
//               ref.infoAudioUrl.value = null;
//             },
//             visible: headerInfoTextV.isNotEmptyAndNotNull,
//             margin: EdgeInsets.symmetric(
//               vertical: AppSize.h8,
//             ),
//             bodyText: headerInfoTextV,
//           );
//         },
//       ),
//     );
//   }
// }
