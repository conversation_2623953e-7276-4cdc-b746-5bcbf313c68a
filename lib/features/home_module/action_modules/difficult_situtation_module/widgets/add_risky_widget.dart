import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_drop_down_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_drop_down_widget2.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/widgets/custom_textfield.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AddRiskyWidget extends StatelessWidget {
  const AddRiskyWidget({
    required this.ref,
    required this.globalKey,
    super.key,
  });

  final DifficultSitutationCubit ref;
  final GlobalKey<FormState> globalKey;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ref.riskyPlaceVisible,
      builder: (context, value, child) {
        //Todod:Added statci text

        return ValueListenableBuilder(
          valueListenable: ref.isRiskyButton,
          builder: (context, value, child) {
            return Container(
              decoration: BoxDecoration(
                color: const Color.fromRGBO(235, 235, 235, 1),
                //   borderRadius: BorderRadius.circular(AppSize.r4),
                border: Border.all(
                  color: const Color(0xFFBDBDBD), // border color
                ),
                borderRadius: BorderRadius.circular(AppSize.r4),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //  SpaceV(AppSize.h20),
                    AppTextWidget(
                      AsLocaleKeys.lsDsAddRiskyPlace.tr(),
                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500),
                    ),
                    SpaceV(AppSize.h20),
                    QuestionRowWidget(
                      questionText: AsLocaleKeys.lsDsQuestionsWhat.tr(),
                    ),
                    SpaceV(AppSize.h20),
                    ValueListenableBuilder(
                      valueListenable: ref.isRiskyButton,
                      builder: (context, value, child) {
                        return Padding(
                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                          child: Column(
                            children: [
                              CustomOutlinedTextfield(
                                hintText: CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                onChanged: (p0) {
                                  ref.isRiskyButton.notifyListeners();
                                },
                                controller: ref.riskyPlaceController,
                                isError: ref.isRiskyButton.value && ref.riskyPlaceController.text.isEmpty,
                                // validator: (p0) => riskyplaceValidator().call(p0),
                              ),
                              if (ref.isRiskyButton.value && ref.riskyPlaceController.text.isEmpty)
                                CustomErrorWidget(
                                  spacing: AppSize.h5,
                                  errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                    SpaceV(AppSize.h20),
                    QuestionRowWidget(
                      questionText: AsLocaleKeys.lsDsQuestionsWhyLabel.tr(),
                    ),
                    SpaceV(AppSize.h20),
                    ValueListenableBuilder(
                      valueListenable: ref.isRiskyPlaceSelect,
                      builder: (context, value, child) {
                        return ValueListenableBuilder(
                          valueListenable: ref.isCustomWhyFieldVisible,
                          builder: (context,isCustomWhyFieldVisible,child) {
                            return Padding(
                              padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                              child: Column(
                                children: [
                                  CustomDropDownListWidget2(
                                    list: ref.situtationWhyList,
                                    donotWishCheckBoxValue: ValueNotifier(false),
                                    onChanged: (p0) {
                                      ref.isRiskyPlaceSelect.value = true;
                                      ref.updateriskyPlace(p0);
                                      // if (p0 == ref.situtationWhyList.last) {
                                      //   ref.isCustomWhyFieldVisible.value = true;
                                      // } else {
                                      //   ref.isCustomWhyFieldVisible.value = false;
                                      // }
                                    },
                                    selectedValue: ref.state.selecteRiskyPlaceValue != null &&
                                            ref.state.selecteRiskyPlaceValue!.isNotEmpty &&
                                            DrugAndUnitList.drugList.contains(ref.state.selecteRiskyPlaceValue)
                                        ? ref.state.selecteRiskyPlaceValue
                                        : null,
                                    isButtonClicked: ref.isRiskyButton.value,
                                    isChecked: false,
                                    isSelectedValuechange: ref.isRiskyPlaceSelect.value,
                                    isError: (ref.state.selecteRiskyPlaceValue == null ||
                                            ref.state.selecteRiskyPlaceValue!.isEmpty) &&
                                        ref.isRiskyButton.value &&
                                        ref.isRiskyPlaceSelect.value,
                                  ),
                                  if ((ref.state
                                      .selecteRiskyPlaceValue) ==
                                      ref.situtationWhyList[ref
                                          .situtationWhyList
                                          .length -
                                          1]) ...{
                                    SizedBox(height: AppSize.h10,),
                                    CustomOutlinedTextfield(
                                      controller: ref.customWhyTextController,
                                      inputFormatters: [
                                        LengthLimitingTextInputFormatter(
                                          300,
                                        ),
                                      ],
                                      onChanged:
                                          (p0) {
                                        ref.isCustomWhyFieldVisible.value = false;
                                      },
                                      hintText:CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                      isError: isCustomWhyFieldVisible && (ref.state
                                          .selecteRiskyPlaceValue) ==
                                          ref.situtationWhyList[ref
                                              .situtationWhyList
                                              .length -
                                              1],

                                    ),
                                  },
                                  if (isCustomWhyFieldVisible && (ref.state.selecteRiskyPlaceValue) == ref.situtationWhyList[ref.situtationWhyList.length - 1]) ...{
                                    CustomErrorWidget(
                                      errorMessgaeText:AuthLocaleKeys.fieldRequiredError.tr(),
                                    ),
                                  },

                            /*      ValueListenableBuilder<bool>(
                                    valueListenable: ref.isCustomWhyFieldVisible,
                                    builder: (context, isVisible, child) {
                                      if (isVisible) {
                                        return Padding(
                                          padding: EdgeInsets.only(top: AppSize.h16),
                                          child: CustomOutlinedTextfield(
                                            controller: ref.customWhyTextController,
                                            hintText: AsLocaleKeys.lsDsQuestionsWhyCustomLabel.tr(),
                                          ),
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),*/
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    // if( ref.situtationWhyList.)
                    SpaceV(AppSize.h20),
                    ValueListenableBuilder(
                      valueListenable: ref.situtationInfoVisible,
                      builder: (context, situtationInfoVisible, child) {
                        return QuestionRowWidget(
                          helpRefuseWidget: Padding(
                            padding: EdgeInsets.only(right: AppSize.w28, top: AppSize.h6),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                AppTextWidget(
                                  AsLocaleKeys.lsDsQuestionsHowIconLabel.tr(),
                                  style: context.textTheme.titleSmall?.copyWith(
                                    color: context.themeColors.greenBtnColor,
                                  ),
                                ),
                                SpaceH(AppSize.w4),
                                AppSvgAsset(
                                  svgAsset: Assets.icons.greenPolygonIcon,
                                  size: AppSize.sp10,
                                  // color: color ?? context.themeColors.orangeColor,
                                ),
                              ],
                            ),
                          ),
                          questionText: AsLocaleKeys.lsDsQuestionsHowLabel.tr(),
                          custominfoWidget: CustomIconButton(
                            onTap: () {
                              ref.situtationInfoVisible.value = !ref.situtationInfoVisible.value;
                            },
                            iconPath: Assets.icons.situationHelpIcon,
                          ),
                          oninfoTap: () {},
                          infoWidget: CustomInfoWidget(
                            textColor: context.themeColors.whiteColor,
                            bodyColor: context.themeColors.whiteColor,
                            bgColor: context.themeColors.greenBtnColor,
                            padding: EdgeInsets.only(left: AppSize.w12, right: AppSize.w12, top: AppSize.h10),
                            visible: situtationInfoVisible,
                            onCloseTap: () {
                              ref.situtationInfoVisible.value = false;
                            },
                            bodyText: (DynamicAssetLoader.getNestedValue(
                              AsLocaleKeys.lsDsQuestionsHowInfoPanelHtml,
                              context,
                            ) as List)
                                .expand(
                                  (element) => element is List ? element.map((e) => '• $e').toList() : [element],
                                )
                                .join('\n\n'),
                          ),
                        );
                      },
                    ),
                    SpaceV(AppSize.h10),
                    ValueListenableBuilder(
                      valueListenable: ref.isCopingSelect,
                      builder: (context, value, child) {
                        return ValueListenableBuilder(
                          valueListenable: ref.isCustomHowFieldVisible,
                          builder: (context,isCustomHowFieldVisible,child) {
                            return Padding(
                              padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                              child: Column(
                                children: [
                                  CustomDropDownListWidget2(
                                    list: ref.situtationHowList,
                                    donotWishCheckBoxValue: ValueNotifier(false),
                                    onChanged: (p0) {
                                      ref.isCopingSelect.value = true;
                                      ref.updateSelecteCopyPlace(p0);
                                      // if (p0 == ref.situtationHowList.last) {
                                      //   ref.isCustomHowFieldVisible.value = true;
                                      // } else {
                                      //   ref.isCustomHowFieldVisible.value = false;
                                      // }
                                    },
                                    selectedValue: ref.state.selecteCopyPlaceValue != null &&
                                            ref.state.selecteCopyPlaceValue!.isNotEmpty &&
                                            DrugAndUnitList.drugList.contains(ref.state.selecteCopyPlaceValue)
                                        ? ref.state.selecteCopyPlaceValue
                                        : null,
                                    isButtonClicked: ref.isRiskyButton.value,
                                    isChecked: false,
                                    isSelectedValuechange: ref.isCopingSelect.value,
                                    isError: (ref.state.selecteCopyPlaceValue == null ||
                                            ref.state.selecteCopyPlaceValue!.isEmpty) &&
                                        ref.isRiskyButton.value &&
                                        ref.isCopingSelect.value,
                                  ),
                                  if ((ref.state
                                      .selecteCopyPlaceValue) ==
                                      ref.situtationHowList[ref
                                          .situtationHowList
                                          .length -
                                          1]) ...{
                                    SizedBox(height: AppSize.h10,),
                                    CustomOutlinedTextfield(
                                      controller: ref.customHowTextController,
                                      inputFormatters: [
                                        LengthLimitingTextInputFormatter(
                                          300,
                                        ),
                                      ],
                                      hintText:CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                      onChanged:(p0) {
                                        ref.isCustomHowFieldVisible.value = false;
                                      },
                                      isError: isCustomHowFieldVisible && (ref.state
                                          .selecteCopyPlaceValue) ==
                                          ref.situtationHowList[ref
                                              .situtationHowList
                                              .length -
                                              1],
                                    ),
                                  },
                                  if (isCustomHowFieldVisible && (ref.state.selecteCopyPlaceValue) == ref.situtationHowList[ref.situtationHowList.length - 1]) ...{
                                    CustomErrorWidget(
                                      errorMessgaeText:AuthLocaleKeys.fieldRequiredError.tr(),
                                    ),
                                  },
                                  /*ValueListenableBuilder<bool>(
                                    valueListenable: ref.isCustomHowFieldVisible,
                                    builder: (context, isVisible, child) {
                                      if (isVisible) {
                                        return Padding(
                                          padding: EdgeInsets.only(top: AppSize.h16),
                                          child: CustomOutlinedTextfield(
                                            controller: ref.customHowTextController,
                                            hintText: AsLocaleKeys.lsDsQuestionsHowCustomLabel.tr(),
                                          ),
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),*/
                                ],
                              ),
                            );
                          },
                        );
                      },
                    ),
                    SpaceV(AppSize.h20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CustomRoundedButton(
                          title: AssessmentLocaleKeys.drugsQuestionsListButtonsCancel.tr(),
                          fillColor: context.themeColors.greyColor.withOpacity(0.3),
                          titleColor: context.themeColors.blackColor,
                          onTap: () {
                            ref.isRiskyButton.value = false;
                            ref.riskyPlaceVisible.value = false;
                            ref.isCopingSelect.value = false;
                            log('as ${ref.state.selecteCopyPlaceValue}');
                            log('ass ${ref.state.marker}');
                            log('aaaa ${ref.state.selecteRiskyPlaceValue}');
                            ref.cancelOnTap();
                          },
                        ),
                        SpaceH(AppSize.w10),
                        CustomRoundedButton(
                          title: AssessmentLocaleKeys.drugsQuestionsListButtonsSave.tr(),
                          onTap: () async {
                            final isLastHowItem = ref.state
                                .selecteCopyPlaceValue ==
                                ref.situtationHowList
                                    .last;
                            final isLastWhyItem = ref.state.selecteRiskyPlaceValue ==
                                ref.situtationWhyList
                                    .last;
                            

                            if (isLastHowItem &&
                                ref.customHowTextController
                                    .text.isEmpty) {
                              ref.isCustomHowFieldVisible
                                  .value = true;
                            } else {
                              ref.isCustomHowFieldVisible
                                  .value = false;
                            }

                            if (isLastWhyItem &&
                                ref.customWhyTextController
                                    .text.isEmpty) {
                              ref.isCustomWhyFieldVisible
                                  .value = true;
                            } else {
                              ref.isCustomWhyFieldVisible
                                  .value = false;
                            }
                            // ref.isButtonClicked.value = true;
                            // globalKey.currentState?.validate();
                            if (ref.riskyPlaceController.text != '' &&
                                (ref.state.selecteCopyPlaceValue ?? '').isNotEmpty &&
                                (ref.state.selecteRiskyPlaceValue ?? '').isNotEmpty && ! ref.isCustomWhyFieldVisible
                                .value && ! ref.isCustomHowFieldVisible
                                .value ) {

                              ref.isRiskyButton.value = false;
                              ref.isRiskyPlaceSelect.value = false;
                              ref.isCopingSelect.value = false;

                              ref.riskyPlaceController.text.logD;
                              ref.state.selecteCopyPlaceValue.logD;
                              ref.state.selecteRiskyPlaceValue.logD;
                              ref.addMapgData();
                              
                            } else {
                              ref.isRiskyButton.value = true;
                              ref.isRiskyPlaceSelect.value = true;
                              ref.isCopingSelect.value = true;
                              //CustomSnackbar.showErrorSnackBar(message: AsLocaleKeys.lsDsErrorsRequired.tr());
                              '///unValid'.logV;
                              '///${ref.state.selecteCopyPlaceValue}'.logV;
                              '///${ref.state.selecteRiskyPlaceValue}'.logV;
                            }
                            await ref.zoomToFitAllMarkers(ref.controller!, ref.getAllMarkers().toList());
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
