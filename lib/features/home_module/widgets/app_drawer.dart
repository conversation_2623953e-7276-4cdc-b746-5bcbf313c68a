import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/my_data_module/pages/my_data_page.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/pages/accessbility_page.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/home_module/find_meeting_module/pages/find_meeting_page.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/pages/my_alert_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_clock_module/pages/my_recovery_clock_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/pages/my_recovery_toolkit_page.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/pages/setting_page.dart';
import 'package:breakingfree_v2/features/home_module/view_tutorial_module/pages/view_tutorial_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/features/home_module/widgets/sidebar_item_widget.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AppDrawer extends StatelessWidget {
   AppDrawer({required this.scaffoldKey, super.key, this.infoAudioUrl});
  final GlobalKey<ScaffoldState> scaffoldKey;
   String? infoAudioUrl;
  void onSidebarMenuTap(BuildContext context, Widget screen) {
    scaffoldKey.currentState?.closeDrawer();
    AppNavigation.replaceScreen(context, screen);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: double.infinity,
        constraints: BoxConstraints(maxWidth: context.width * .6),
        decoration: BoxDecoration(
          color: context.themeColors.whiteColor,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(AppSize.r14),
            bottomRight: Radius.circular(AppSize.r14),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SpaceV(AppSize.h16),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                child: GestureDetector(
                  onTap: scaffoldKey.currentState?.closeDrawer,
                  child: Icon(
                    Icons.menu,
                    color: context.themeColors.greyColor,
                    size: AppSize.h24,
                  ),
                ),
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuDashboard.tr(),
                iconPath: Assets.icons.sidemenu.dashboard,
                onTap: () {
                  onSidebarMenuTap(context,  const DashboardPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuDiagram.tr(),
                iconPath: Assets.icons.sidemenu.myDiagram,
                onTap: () {
                  infoAudioUrl = null;
                  onSidebarMenuTap(context, const MyDiagramPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuToolkit.tr(),
                iconPath: Assets.icons.sidemenu.myRecoveryToolkit,
                onTap: () {
                  onSidebarMenuTap(context, const MyRecoveryToolkitPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuClock.tr(),
                iconPath: Assets.icons.sidemenu.myRecoveryClock,
                onTap: () {
                  onSidebarMenuTap(context, const MyRecoveryClockPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuTutorial.tr(),
                iconPath: Assets.icons.sidemenu.viewTutorial,
                onTap: () {
                  onSidebarMenuTap(context, const ViewTutorialPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuMeetings.tr(),
                iconPath: Assets.icons.sidemenu.findMeetings,
                onTap: () {
                  onSidebarMenuTap(context, const FindMeetingPage());
                },
              ),
              SpaceV(AppSize.h10),
              Divider(
                height: AppSize.h1,
                color: context.themeColors.blackColor.withOpacity(0.12),
              ),
              SpaceV(AppSize.h10),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuAlerts.tr(),
                iconPath: Assets.icons.sidemenu.myAlert,
                onTap: () {
                  onSidebarMenuTap(context, const MyAlertPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuSettings.tr(),
                iconPath: Assets.icons.sidemenu.mySettings,
                onTap: () {
                  onSidebarMenuTap(context, SettingPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuData.tr(),
                iconPath: Assets.icons.sidemenu.myData,
                onTap: () {
                  '??? from here 5'.logD;
                  // onSidebarMenuTap(
                  //   context,
                  //   MyDataPage(
                  //     showExtraOptions: true,
                  //   ),
                  // );
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuAccessibility.tr(),
                iconPath: Assets.icons.sidemenu.accessibility,
                onTap: () async {
                  onSidebarMenuTap(context, const AccessbilityPage());
                },
              ),
              SpaceV(AppSize.h16),
              SideBarItemWidget(
                name: CoreLocaleKeys.sideMenuLogOut.tr(),
                iconPath: Assets.icons.sidemenu.logout,
                onTap: () async {
                  await LogOutDialog.showLogOutDialog(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
