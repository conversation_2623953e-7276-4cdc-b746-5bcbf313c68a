// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class SideBarItemWidget extends StatelessWidget {
  const SideBarItemWidget({
    required this.name,
    required this.iconPath,
    super.key,
    this.size,
    this.onTap,
  });
  final String name;
  final String iconPath;
  final double? size;
  final void Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
        child: ColoredBox(
          color: Colors.transparent,
          child: Row(
            children: [
              AppSvgAsset(
                svgAsset: iconPath,
                size: size ?? AppSize.h28,
              ),
              SpaceH(AppSize.w16),
              Flexible(
                child: AppTextWidget(
                  name,
                  maxLines: 2,
                  style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
