import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LogOutDialog {
  static Future<void> showLogOutDialog(BuildContext context) async {
    await showDialog<void>(
      context: context,
      barrierColor: const Color.fromRGBO(104, 196, 172, 1).withOpacity(0.5), // Custom teal transparent overlay color

      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(AppSize.r4)),
          ),
          backgroundColor: Colors.white, // Popup background color
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                CoreLocaleKeys.logOutTitle.tr(),
                style: context.textTheme.titleSmall?.copyWith(fontSize: AppSize.sp14),
              ),
              SpaceV(AppSize.h12),
              Divider(
                height: 1,
                color: context.themeColors.blackColor,
              ),
              SpaceV(AppSize.h4),
            ],
          ),
          content: Text(
            CoreLocaleKeys.logOutText.tr(),
            style: context.textTheme.titleSmall,
          ),

          actions: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomRoundedButton(
                  title: CoreLocaleKeys.logOutButtonsNo.tr(),
                  height: AppSize.h34,
                  width: AppSize.w60,
                  fillColor: context.themeColors.redColor,
                  onTap: () {
                    Navigator.of(context).pop(); // Close the dialog
                  },
                ),
                SpaceH(AppSize.w10),
                BlocBuilder<SignupCubit, SignupState>(
                  builder: (ctx, state) {
                    return CustomRoundedButton(
                      title: CoreLocaleKeys.logOutButtonsYes.tr(),
                      height: AppSize.h34,
                      width: AppSize.w60,
                      isLoading: state is LogOutLoadingState,
                      loaderPadding: 0,
                      onTap: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        ctx.read<SignupCubit>().logOutApi(context).then(
                          (value) async {
                            await clearAllScheduledNotifications();
                            final langugaeList = Injector.instance<AppDB>().langugaeModel?.languages ?? [];
                            final tempLanguageModel = Injector.instance<AppDB>().langugaeModel;
                            final selectedLangugae = Injector.instance<AppDB>().selectedLangugae;
                            final localizedJson = Injector.instance<AppDB>().localizedJson;

                            '====> ++ ${Injector.instance<AppDB>().langugaeModel?.languages}'.logD;
                            //Injector.instance<AppDB>().userModel = null;
                            //Injector.instance<AppDB>().userModelCopy = null;
                            Injector.instance<AppDB>().isAppLogin = false;
                            await Injector.instance<AppDB>().clearData();
                            Navigator.of(context).pop();
                            Injector.instance<AppDB>().baseUrl = EndPoints.baseUrl;
                            Injector.instance<AppDB>().langugaeModel = tempLanguageModel;
                            Injector.instance<AppDB>().langugaeModel?.languages = langugaeList;
                            Injector.instance<AppDB>().selectedLangugae = selectedLangugae;
                            Injector.instance<AppDB>().localizedJson = localizedJson;
                            ctx.read<SignupCubit>().stopTimer();
                            '====>${Injector.instance<AppDB>().langugaeModel?.languages}'.logV;
                            await prefs.clear();
                            WidgetsBinding.instance.addPostFrameCallback(
                              (timeStamp) async {
                                'Locale already set: ${EasyLocalization.of(navigatorKey.currentContext!)?.locale}'.logD;

                                final savedLang = Injector.instance<AppDB>().selectedLangugae;

                                if (savedLang?.isNotEmpty ?? false) {
                                  final localeParts = savedLang!.replaceAll('-', '_').split('_');
                                  final savedLocale =
                                      Locale(localeParts[0], localeParts.length > 1 ? localeParts[1] : null);

                                  final currentLocale = EasyLocalization.of(navigatorKey.currentContext!)?.locale;

                                  if (savedLocale != currentLocale) {
                                    await EasyLocalization.of(navigatorKey.currentContext!)?.setLocale(savedLocale);
                                    'Locale updated to: $savedLocale'.logI;
                                  } else {
                                    'Locale already set: $currentLocale'.logD;
                                  }
                                }
                              },
                            );

                            'Locale already set: ${Injector.instance<AppDB>().selectedLangugae}'.logD;
                            await ctx.read<AccessbilityCubit>().setAccessibilityBold(false);
                            await ctx.read<AccessbilityCubit>().setAccessibilityContrast(1);
                            await ctx.read<AccessbilityCubit>().setAccessibilityFontSize(1);
                                       // _debouncer.run(ref.accessibilityApi);
                            await AppNavigation.pushAndRemoveAllScreen(context, const LoginPage());
                          },
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}


final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

Future<void> clearAllScheduledNotifications() async {
  await flutterLocalNotificationsPlugin.cancelAll();
}
