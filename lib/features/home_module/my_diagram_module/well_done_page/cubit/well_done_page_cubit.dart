import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'well_done_page_cubit.freezed.dart';
part 'well_done_page_state.dart';

class WellDonePageCubit extends Cubit<WellDonePageState> {
  WellDonePageCubit() : super(const WellDonePageState.initial()) {
    infoAudioUrl.value = IsLocaleKeys.wellDoneAudio.tr();
  }

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
