import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_confetti_animation.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/summary_page/summary_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/well_done_page/cubit/well_done_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:confetti/confetti.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class WellDonePage extends StatelessWidget {
  WellDonePage({
    super.key,
    this.diagramState = MyDiagramStates.difficultSituation,
  });
  final MyDiagramStates diagramState;
  final controller = ConfettiController();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => WellDonePageCubit(),
      child: Builder(
        builder: (context) {
          final ref = context.read<WellDonePageCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return PopScope(
                onPopInvokedWithResult: (didPop, result) {
                  if (didPop) {
                    ref.isManuallyPaused.value = true;
                    ref.headerInfoText.value = null;
                    ref.infoAudioUrl.value = null;
                  }
                },
                child: AppScaffold(
                  scaffoldKey: _scaffoldKey,
                  isAudioPanelVisible: ref.isAudioPanelVisible,
                  infoAudioUrl: ref.infoAudioUrl,
                  isManuallyPaused: ref.isManuallyPaused,
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      _scaffoldKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                      }
                    },
                  ),
                  drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                  body: ColoredBox(
                    color: context.themeColors.whiteColor,
                    child: Column(
                      children: [
                        Center(child: CustomConfettiAnimation(controller: controller)),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minHeight: constraints.maxHeight,
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            InformationPageHeadingWidget(
                                              title: diagramState.title ?? '',
                                              subtitle: diagramState.subTitle ?? '',
                                              icon: diagramState.iconPath,
                                              onInfoTap: () {
                                                final info = diagramState.getInfoText.tr().replaceAll('\n', '<br/>');
                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.isManuallyPaused.value = true;
                                                  ref.infoAudioUrl.value = IsLocaleKeys.wellDoneAudio.tr();
                                                  ref.headerInfoText.value = null;
                                                  ref.isAudioPanelVisible.value = false;
                                                } else {
                                                  ref.isManuallyPaused.value = false;
                                                  ref.infoAudioUrl.value = diagramState.getInfoAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              onLearnTap: () {
                                                final info = diagramState.getLearnText.tr().replaceAll('\n', '<br/>');
                                                if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                    ref.headerInfoText.value == info) {
                                                  ref.isManuallyPaused.value = true;
                                                  ref.infoAudioUrl.value = IsLocaleKeys.wellDoneAudio.tr();
                                                  ref.headerInfoText.value = null;
                                                  ref.isAudioPanelVisible.value = false;
                                                } else {
                                                  ref.isManuallyPaused.value = false;
                                                  ref.infoAudioUrl.value = diagramState.getLearnAudio.tr();
                                                  ref.headerInfoText.value = info;
                                                }
                                              },
                                              infoWidget: ValueListenableBuilder(
                                                valueListenable: ref.headerInfoText,
                                                builder: (context, headerInfoTextV, _) {
                                                  return CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w8,
                                                      right: AppSize.w8,
                                                    ),
                                                    customWidget: Column(
                                                      children: [
                                                        Html(
                                                          data: ref.headerInfoText.value ?? '',
                                                          style: {
                                                            'strong': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontWeight: FontWeight.bold,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                            'body': Style(
                                                              fontSize: FontSize(AppSize.sp13),
                                                              color: context.themeColors.darkOrangeColor,
                                                              fontFamily: 'Poppins',
                                                            ),
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                    onCloseTap: () {
                                                      ref.isManuallyPaused.value = true;
                                                      ref.headerInfoText.value = null;
                                                      ref.infoAudioUrl.value = IsLocaleKeys.wellDoneAudio.tr();
                                                    },
                                                    visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                    margin: EdgeInsets.symmetric(
                                                      vertical: AppSize.h8,
                                                    ),
                                                    bodyText: headerInfoTextV,
                                                  );
                                                },
                                              ),
                                            ),
                                            AppTextWidget(
                                              IsLocaleKeys.wellDoneTitle.tr(),
                                              textAlign: TextAlign.center,
                                              style: context.textTheme.labelSmall?.copyWith(
                                                fontSize: AppSize.sp13,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SpaceV(AppSize.h16),
                                            AppTextWidget(
                                              (DynamicAssetLoader.getNestedValue(
                                                IsLocaleKeys.wellDoneText,
                                                context,
                                              ) as List)
                                                  .join('\n\n'),
                                              style: context.textTheme.labelSmall?.copyWith(
                                                fontSize: AppSize.sp13,
                                              ),
                                            ),
                                          ],
                                        ),
                                        CustomButton(
                                          padding: EdgeInsets.zero,
                                          isBottom: true,
                                          title: IsLocaleKeys.buttonsNext.tr(),
                                          color: context.themeColors.blueColor,
                                          onTap: () async {
                                            ref.infoAudioUrl.value = null;
                                            await AppNavigation.nextScreen(
                                              context,
                                              SummaryPage(
                                                diagramState: diagramState,
                                              ),
                                            );
                                            ref.infoAudioUrl.value = IsLocaleKeys.wellDoneAudio.tr();
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
