class DiagramLocaleKeys {
  // title keys
  static const title = 'diagram.title';
  static const titlePersonalised = 'diagram.titlePersonalised';
  static const toggleLabel = 'diagram.toggleLabel';

  // info panels keys
  static const infoPanelsDiagramText = 'diagram.infoPanels.diagram.text';
  static const infoPanelsDiagramAudio = 'diagram.infoPanels.diagram.audio';
  static const infoPanelsInformationText = 'diagram.infoPanels.information.text';
  static const infoPanelsInformationColourBlind = 'diagram.infoPanels.information.colour-blind';
  static const infoPanelsInformationAudio = 'diagram.infoPanels.information.audio';
  static const infoPanelsLearnText = 'diagram.infoPanels.learn.text';
  static const infoPanelsLearnAudio = 'diagram.infoPanels.learn.audio';
  static const infoPanelsLearnVideoSrc = 'diagram.infoPanels.learn.video.src';
  static const infoPanelsLearnVideoposter = 'diagram.infoPanels.learn.video.poster';

  // area names keys
  static const areaNamesDifficultSituations = 'diagram.areaNames.difficultSituations';
  static const areaNamesNegativeThoughts = 'diagram.areaNames.negativeThoughts';
  static const areaNamesEmotionalImpact = 'diagram.areaNames.emotionalImpact';
  static const areaNamesLifestyle = 'diagram.areaNames.lifestyle';
  static const areaNamesUnhelpfulBehaviours = 'diagram.areaNames.unhelpfulBehaviours';
  static const areaNamesPhysicalSensations = 'diagram.areaNames.physicalSensations';

  // keywords - difficult situations keys
  static const keywordsDifficultSituationsConflict = 'diagram.keywords.difficultSituations.conflict';
  static const keywordsDifficultSituationsWork = 'diagram.keywords.difficultSituations.work';
  static const keywordsDifficultSituationsMoney = 'diagram.keywords.difficultSituations.money';
  static const keywordsDifficultSituationsRisks = 'diagram.keywords.difficultSituations.risks';
  static const keywordsDifficultSituationsPressure = 'diagram.keywords.difficultSituations.pressure';

  // keywords - negative thoughts keys
  static const keywordsNegativeThoughtsCope = 'diagram.keywords.negativeThoughts.cope';
  static const keywordsNegativeThoughtsGood = 'diagram.keywords.negativeThoughts.good';
  static const keywordsNegativeThoughtsHealth = 'diagram.keywords.negativeThoughts.health';
  static const keywordsNegativeThoughtsTrust = 'diagram.keywords.negativeThoughts.trust';
  static const keywordsNegativeThoughtsControl = 'diagram.keywords.negativeThoughts.control';

  // keywords - physical sensations keys
  static const keywordsPhysicalSensationsCraving = 'diagram.keywords.physicalSensations.craving';
  static const keywordsPhysicalSensationsShakes = 'diagram.keywords.physicalSensations.shakes';
  static const keywordsPhysicalSensationsCramps = 'diagram.keywords.physicalSensations.cramps';
  static const keywordsPhysicalSensationsNausea = 'diagram.keywords.physicalSensations.nausea';
  static const keywordsPhysicalSensationsTiredness = 'diagram.keywords.physicalSensations.tiredness';

  // keywords - unhelpful behaviours keys
  static const keywordsUnhelpfulBehavioursActive = 'diagram.keywords.unhelpfulBehaviours.active';
  static const keywordsUnhelpfulBehavioursAggressive = 'diagram.keywords.unhelpfulBehaviours.aggressive';
  static const keywordsUnhelpfulBehavioursAvoid = 'diagram.keywords.unhelpfulBehaviours.avoid';
  static const keywordsUnhelpfulBehavioursCare = 'diagram.keywords.unhelpfulBehaviours.care';
  static const keywordsUnhelpfulBehavioursPolice = 'diagram.keywords.unhelpfulBehaviours.police';

  // keywords - lifestyle keys
  static const keywordsLifestyleHealth = 'diagram.keywords.lifestyle.health';
  static const keywordsLifestyleHousing = 'diagram.keywords.lifestyle.housing';
  static const keywordsLifestyleWork = 'diagram.keywords.lifestyle.work';
  static const keywordsLifestyleLeisure = 'diagram.keywords.lifestyle.leisure';
  static const keywordsLifestyleRelationships = 'diagram.keywords.lifestyle.relationships';

  // keywords - emotional impact keys
  static const keywordsEmotionalImpactNervous = 'diagram.keywords.emotionalImpact.nervous';
  static const keywordsEmotionalImpactWorry = 'diagram.keywords.emotionalImpact.worry';
  static const keywordsEmotionalImpactDown = 'diagram.keywords.emotionalImpact.down';
  static const keywordsEmotionalImpactBad = 'diagram.keywords.emotionalImpact.bad';
  static const keywordsEmotionalImpactInterest = 'diagram.keywords.emotionalImpact.interest';

  // other keys
  static const noIssues = 'diagram.noIssues';

  // labels keys
  static const labelsAccessibilityLabels = 'diagram.labels.accessibilityLabels';
  static const labelsRed = 'diagram.labels.red';
  static const labelsAmber = 'diagram.labels.amber';
  static const labelsGreen = 'diagram.labels.green';

  // buttons keys
  static const buttonsToolkit = 'diagram.buttons.toolkit';
  static const buttonsDashboard = 'diagram.buttons.dashboard';
  static const buttonsAction = 'diagram.buttons.action';
  static const buttonsInformation = 'diagram.buttons.information';

  // pdf file name key
  static const pdfFileName = 'diagram.pdfFileName';
}
