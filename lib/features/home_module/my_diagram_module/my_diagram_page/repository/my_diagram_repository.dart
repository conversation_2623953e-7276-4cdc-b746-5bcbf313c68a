import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class MyDiagramRepository {
  Future<Response<Map<String, dynamic>>?> progressReportActionStrategyforDownload({
    required BuildContext context,
    required bool isEmail,
    required bool isDashboard
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: isDashboard?
            '${EndPoints.progressReportActionStrategy}?email=$isEmail&timezone=$timeZoneName'
              : '${EndPoints.pdfDiagram}?email=$isEmail&timezone=$timeZoneName' ,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          // CustomSnackbar.showErrorSnackBar(
          //   message: data?['message'] as String,
          // );
          return null;
        }
      } else {
        if (response is DioException) {
          // CustomSnackbar.showErrorSnackBar(
          //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          // );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
