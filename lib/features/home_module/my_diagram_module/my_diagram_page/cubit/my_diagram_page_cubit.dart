import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/checkin_processer_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/repository/my_diagram_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'my_diagram_page_cubit.freezed.dart';
part 'my_diagram_page_state.dart';

class MyDiagramPageCubit extends Cubit<MyDiagramPageState> {
  MyDiagramPageCubit() : super(const MyDiagramPageState.initial());

  ValueNotifier<MyDiagramStates?> expandedState = ValueNotifier(null);
  ValueNotifier<bool> isExpandAll = ValueNotifier(false);
  ValueNotifier<bool> viewColorName = ValueNotifier(false);
  ValueNotifier<bool> showPrivacyPopup = ValueNotifier(false);

  ValueNotifier<DiagramInfoPannel?> diagramInfo = ValueNotifier(null);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isDownloadFailed = ValueNotifier(false);

  ValueNotifier<bool> isManuallyPaused =ValueNotifier(false);

  final controller = ScrollController();

  final authRepository = AuthRepository();
  final repository = MyDiagramRepository();

  int dsScore = 0;
  int ntScore = 0;
  int eiScore = 0;
  int lsScore = 0;
  int ubScore = 0;
  int psScore = 0;

  Future<void> progressReportActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
  }) async {
    isEmail ? emit(const MyDiagramPageState.emailPdfLoading()) : emit(const MyDiagramPageState.downloadPdfLoading());

    //   isEmail ? emit(state.copyWith(isEmailPdfAPILoading: true)) : emit(state.copyWith(isDownloadPdfAPILoading: true));
    try {
      final response = await repository.progressReportActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
        isDashboard: false
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'My progress report.pdf',
            );
          }
        } else {
          // CustomSnackbar.showSucessSnackBar(
          //   message: 'Email sent successfully',
          // );
        }
      } else {
        isDownloadFailed.value = true;
      }
      emit(const MyDiagramPageState.initial());
    } catch (e) {
      // isDownloadFailed.value = true;
      // CustomSnackbar.showErrorSnackBar(
      //   message: e.toString(),
      // );
      emit(const MyDiagramPageState.initial());
    }
  }

  void getDiagramNodeColor() {
    final checkins = Injector.instance<AppDB>().userModel?.user.checkins;
    final entries1 = <MapEntry<DateTime, Checkin>>[];
    'checkins $checkins'.logD;
    if (checkins != null && checkins.isNotEmpty) {
// Sort the check-ins by date (assuming the Checkin has a 'date' field)
      final data1 = CheckinProcessor.processCheckins1<Checkin>(
        entries1,
        checkins,
      )

// Sort the data by the DateTime in descending order (most recent first)
      ..sort((a, b) => b.key.compareTo(a.key)); // Sort by DateTime (b is more recent)

      if (data1.isNotEmpty) {
        final mostRecentCheckin = data1.first.value; // Get the Checkin from the most recent entry

        final checkin = mostRecentCheckin.toJson();
        final checkinData = Checkin.fromJson(checkin);

        'b ===>${checkinData.rate?.toJson().values}'.logD;

        // Assign values to scores
        dsScore = checkinData.rate?.ds ?? 0;
        ntScore = checkinData.rate?.nt ?? 0;
        eiScore = checkinData.rate?.ei ?? 0;
        lsScore = checkinData.rate?.ls ?? 0;
        psScore = checkinData.rate?.ps ?? 0;
        ubScore = checkinData.rate?.ub ?? 0;

        'dsScore $dsScore'.logD;
        'eiScore $eiScore'.logD;
        'lsScore $lsScore'.logD;
        'psScore $psScore'.logD;
        'ubScore $ubScore'.logD;
      }
    } else {
      'checkins ++ $checkins'.logD;

      final data = Injector.instance<AppDB>().userModel?.user.assessment;
      dsScore = data?.ds?.rate ?? 0;
      ntScore = data?.nt?.rate ?? 0;
      eiScore = data?.ei?.rate ?? 0;
      lsScore = data?.ls?.rate ?? 0;
      psScore = data?.ps?.rate ?? 0;
      ubScore = data?.ub?.rate ?? 0;
      'dsScore $dsScore'.logD;
      'eiScore $eiScore'.logD;
      'lsScore $lsScore'.logD;
      'psScore $psScore'.logD;
      'ubScore $ubScore'.logD;
    }
    'Scores ===> ${[dsScore, ntScore, eiScore, lsScore, psScore, ubScore].any(
      (element) => element <= 2,
    )}'
        .logD;
  }

  

  Future<void> userData(BuildContext context) async {
    await authRepository.getUserData(context: context);
  }

  Future<void> navigateToPopup({required bool isEmail, required BuildContext context}) async {
    showPrivacyPopup.value = true;
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        controller.jumpTo(
          controller.position.maxScrollExtent,
          // duration: const Duration(milliseconds: 100),
          // curve: Curves.linear,
        );
      },
    );
    await progressReportActionStrategyforDownloadPdfApi(context: context, isEmail: isEmail);
  }

  @override
  Future<void> close() {
    expandedState.dispose();
    isExpandAll.dispose();
    viewColorName.dispose();
    showPrivacyPopup.dispose();
    diagramInfo.dispose();
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    return super.close();
  }
}
