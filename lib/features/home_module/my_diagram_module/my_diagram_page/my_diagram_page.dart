import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/dashboard_page.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/cubit/my_diagram_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/diagram_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/expand_switch.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/eye_button.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/graph_edges.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/graph_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/learning_diagram_info_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/privacy_message_popup.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MyDiagramPage extends StatefulWidget {
  const MyDiagramPage({super.key});

  @override
  State<MyDiagramPage> createState() => _MyDiagramPageState();
}

class _MyDiagramPageState extends State<MyDiagramPage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final List<GlobalKey> widgetKeys = List.generate(7, (_) => GlobalKey());

  @override
  void initState() {
    super.initState();

    updateOffset();
  }

  void updateOffset() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) => setState(() {}));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => MyDiagramPageCubit()
            ..userData(context)
            ..getDiagramNodeColor(),
        ),
      ],
      child: BlocBuilder<MyDiagramPageCubit, MyDiagramPageState>(
        builder: (context, state) {
          final ref = context.read<MyDiagramPageCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              'ref.infoAudioUrl+++++ ${ref.infoAudioUrl}'.logV;
              // 'ref.infoAudioUrl+++++ ${appBarVolumeStatusV}'.logV;
              return AppScaffold(
                isManuallyPaused: ref.isManuallyPaused,
                scaffoldKey: _scaffoldKey,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoAudioUrl,
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    _scaffoldKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                    }
                    // ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                  },
                ),
                drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                child: Padding(
                                  padding: EdgeInsets.only(right: AppSize.w4),
                                  child: SingleChildScrollView(
                                    controller: ref.controller,
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                      child: ColoredBox(
                                        color: context.themeColors.whiteColor,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(vertical: AppSize.h24, horizontal: AppSize.w24),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  AppTextWidget(
                                                    CoreLocaleKeys.titlesMyDiagram.tr(),
                                                    textAlign: TextAlign.center,
                                                    style: context.textTheme.titleLarge
                                                        ?.copyWith(fontWeight: FontWeight.w600),
                                                  ),
                                                  SpaceV(AppSize.h8),
                                                  ValueListenableBuilder(
                                                    valueListenable: ref.isExpandAll,
                                                    builder: (context, isExpandAllV, _) {
                                                      return Row(
                                                        children: [
                                                          Expanded(
                                                            child: Text(
                                                              isExpandAllV
                                                                  ? DiagramLocaleKeys.titlePersonalised.tr()
                                                                  : DiagramLocaleKeys.title.tr(),
                                                              textAlign: TextAlign.center,
                                                              style: context.textTheme.labelSmall?.copyWith(
                                                                fontSize: AppSize.sp13,
                                                                fontWeight: FontWeight.w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                  SpaceV(AppSize.h16),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          ExpandSwitch(
                                                            isExpandAll: ref.isExpandAll,
                                                            onChanged: (value) {
                                                              ref.isExpandAll.value = value;
                                                              if (value) {
                                                                ref.expandedState.value = null;
                                                              }
                                                              updateOffset();
                                                            },
                                                          ),
                                                          CustomIconButton(
                                                            iconPath: Assets.icons.myDiagramOrange,
                                                            size: AppSize.h24,
                                                            onTap: () {
                                                              ref.diagramInfo.value =
                                                                  ref.diagramInfo.value == DiagramInfoPannel.diagram
                                                                      ? null
                                                                      : DiagramInfoPannel.diagram;

                                                              if (ref.diagramInfo.value == DiagramInfoPannel.diagram) {
                                                                ref.infoAudioUrl.value =
                                                                    DiagramLocaleKeys.infoPanelsDiagramAudio.tr();
                                                              } else if (ref.infoAudioUrl.value ==
                                                                  DiagramLocaleKeys.infoPanelsDiagramAudio.tr()) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      Row(
                                                        children: [
                                                          CustomIconButton(
                                                            assetIcon: Assets.icons.infoIcon,
                                                            size: AppSize.h24,
                                                            onTap: () {
                                                              'ref.diagramInfo.value ${ref.diagramInfo.value}'.logV;
                                                              '///life'.logV;
                                                              ref.diagramInfo.value =
                                                                  ref.diagramInfo.value == DiagramInfoPannel.info
                                                                      ? null
                                                                      : DiagramInfoPannel.info;

                                                              if (ref.diagramInfo.value == DiagramInfoPannel.info) {
                                                                ref.infoAudioUrl.value =
                                                                    DiagramLocaleKeys.infoPanelsInformationAudio.tr();
                                                              } else if (ref.infoAudioUrl.value ==
                                                                  DiagramLocaleKeys.infoPanelsInformationAudio.tr()) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                          ),
                                                          SpaceH(AppSize.w4),
                                                          CustomIconButton(
                                                            iconPath: Assets.icons.idea,
                                                            size: AppSize.h24,
                                                            onTap: () {
                                                              ref.diagramInfo.value =
                                                                  ref.diagramInfo.value == DiagramInfoPannel.idea
                                                                      ? null
                                                                      : DiagramInfoPannel.idea;

                                                              if (ref.diagramInfo.value == DiagramInfoPannel.idea) {
                                                                ref.infoAudioUrl.value =
                                                                    DiagramLocaleKeys.infoPanelsLearnAudio.tr();
                                                              } else if (ref.infoAudioUrl.value ==
                                                                  DiagramLocaleKeys.infoPanelsLearnAudio.tr()) {
                                                                ref.infoAudioUrl.value = null;
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  ValueListenableBuilder(
                                                    valueListenable: ref.diagramInfo,
                                                    builder: (context, diagramInfoV, _) {
                                                      return diagramInfoV != null
                                                          ? Padding(
                                                              padding: EdgeInsets.symmetric(
                                                                vertical: AppSize.h16,
                                                              ),
                                                              child: CustomInfoWidget(
                                                                onCloseTap: () {
                                                                  ref.diagramInfo.value = null;
                                                                  final audioUrls = [
                                                                    DiagramLocaleKeys.infoPanelsDiagramAudio.tr(),
                                                                    DiagramLocaleKeys.infoPanelsInformationAudio.tr(),
                                                                    DiagramLocaleKeys.infoPanelsLearnAudio.tr(),
                                                                  ];
                                                                  'ref.infoAudioUrl.value ${ref.infoAudioUrl.value}'
                                                                      .logV;
                                                                  'ref.infoAudioUrl.value ${audioUrls.contains(
                                                                    ref.infoAudioUrl.value,
                                                                  )}'
                                                                      .logV;
                                                                  if (audioUrls.contains(
                                                                    ref.infoAudioUrl.value,
                                                                  )) {
                                                                    ref.infoAudioUrl.value = null;
                                                                  } else {
                                                                    ref.infoAudioUrl.value = null;
                                                                  }
                                                                },
                                                                bodyText: diagramInfoV == DiagramInfoPannel.diagram
                                                                    ? (DynamicAssetLoader.getNestedValue(
                                                                        DiagramLocaleKeys.infoPanelsDiagramText,
                                                                        context,
                                                                      ) as List)
                                                                        .join('\n\n')
                                                                    : null,
                                                                customWidget: diagramInfoV == DiagramInfoPannel.info
                                                                    ? const DiagramInfoWidget()
                                                                    : diagramInfoV == DiagramInfoPannel.idea
                                                                        ? LearnDiagramInfoWidget(
                                                                          
                                                                            navigationFunction: () {
                                                                              'updateOffset navigationFunction'.logV;
                                                                            },
                                                                            isFromLightBuilb1: () {
                                                                              'isFromLightBuilb1 == $updateOffset'.logV;
                                                                              updateOffset();
                                                                            },
                                                                            onVideoEnded: () async {
                                          await Future.delayed(const Duration(milliseconds: 300));
                                          if (Navigator.of(context).canPop()) {
                                            Navigator.of(context).pop();
                                          }
                                        },
                                                                            // onVideoEnded: () {
                                                                            //   '///helllo'.logV;
                                                                            //   'updateOffset $updateOffset'.logV;
                                                                            //   updateOffset();
                                                                            // },
                                                                            onPlayTap: () {
                                                                              updateOffset();
                                                                              ref.isManuallyPaused.value = false;
                                                                              //ref.infoAudioUrl.value = null;
                                                                            },
                                                                          )
                                                                        : null,
                                                              ),
                                                            )
                                                          : const SizedBox.shrink();
                                                    },
                                                  ),
                                                  SpaceV(AppSize.h8),
                                                  Divider(
                                                    height: 1,
                                                    color: context.themeColors.greyColor,
                                                  ),
                                                  SpaceV(AppSize.h32),
                                                  Stack(
                                                    key: widgetKeys[0],
                                                    children: [
                                                      EyeButton(
                                                        viewColorName: ref.viewColorName,
                                                        onTap: () {
                                                          ref.viewColorName.value = !ref.viewColorName.value;
                                                          updateOffset();
                                                        },
                                                      ),
                                                      GraphEdges(wKeys: widgetKeys),
                                                      GraphWidget(
                                                        expandedState: ref.expandedState,
                                                        isExpandAll: ref.isExpandAll,
                                                        viewColorName: ref.viewColorName,
                                                        updateOffset: updateOffset,
                                                        widgetKeys: widgetKeys,
                                                        ref: ref,
                                                      ),
                                                    ],
                                                  ),
                                                  SpaceV(AppSize.h16),
                                                  ValueListenableBuilder(
                                                    valueListenable: ref.showPrivacyPopup,
                                                    builder: (context, showPrivacyPopupV, _) {
                                                      return Column(
                                                        children: [
                                                          PrivacyMessagePopup(
                                                            visible: showPrivacyPopupV,
                                                            onCloseTap: () {
                                                              ref.showPrivacyPopup.value = false;
                                                            },
                                                          ),
                                                          SpaceV(AppSize.h14),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                ],
                                              ),
                                              ValueListenableBuilder(
                                                  valueListenable:ref.isDownloadFailed,
                                                  builder: (context, isDownloadFailed, _){
                                                    if(isDownloadFailed) {
                                                      return CustomErrorWidget(errorMessgaeText: DashboardLocaleKeys.errorsNoCheckins.tr());
                                                    } else {
                                                      return const SizedBox.shrink();
                                                    }
                                                  }
                                              ),

                                              CustomYesNoButton(
                                                padding: EdgeInsets.zero,
                                                isDownLoad: true,
                                                isShowArrowForward: false,
                                                exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                                agreeText: CoreLocaleKeys.sideMenuDashboard.tr(),
                                                inNoProgress: ValueNotifier(
                                                  state.maybeWhen(
                                                        downloadPdfLoading: () => true,
                                                        orElse: () => false,
                                                      ) ||
                                                      state.maybeWhen(
                                                        emailPdfLoading: () => true,
                                                        orElse: () => false,
                                                      ),
                                                ),
                                                // inYesProgress: ValueNotifier(
                                                //   state.maybeWhen(
                                                //     emailPdfLoading: () => true,
                                                //     orElse: () => false,
                                                //   ),
                                                // ),
                                                onDownloadTap: () {
                                                  ref.isDownloadFailed.value = false;
                                                  CustomDownloadPopup.buildPopupMenu(
                                                    context: context,
                                                    onDownLoadPdf: () async {
                                                      await ref.navigateToPopup(context: context, isEmail: false);
                                                    },
                                                    onEmailDownload: () async {
                                                      await ref.navigateToPopup(context: context, isEmail: true);
                                                    },
                                                  );
                                                },
                                                onTapYes: () {
                                                  ref.isDownloadFailed.value = false;
                                                  AppNavigation.replaceScreen(context,  DashboardPage());
                                                  //   ref.navigateToPopup(context: context, isEmail: true);
                                                },
                                                onTapNo: () {
                                                  //  ref.navigateToPopup(context: context, isEmail: false);
                                                },
                                                noButtonColor: context.themeColors.orangeColor, isYesNoButton: true,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
