import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/pages/situation_action_map_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_impact_action_page/emotional_impact_action_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_behaviour_calendar_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/information_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/cubit/my_diagram_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/diagram_element_widget.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class GraphWidget extends StatelessWidget {
  const GraphWidget({
    required this.viewColorName,
    required this.isExpandAll,
    required this.expandedState,
    required this.widgetKeys,
    required this.ref,
    this.updateOffset,
    super.key,
  });
  final ValueNotifier<bool> viewColorName;
  final ValueNotifier<bool> isExpandAll;
  final ValueNotifier<MyDiagramStates?> expandedState;
  final void Function()? updateOffset;
  final List<GlobalKey> widgetKeys;
  final MyDiagramPageCubit ref;

  void onNodeTap(MyDiagramStates value) {
    expandedState.value = expandedState.value == value ? null : value;
    updateOffset?.call();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: viewColorName,
      builder: (context, viewColorNameV, _) {
        return ValueListenableBuilder(
          valueListenable: isExpandAll,
          builder: (context, isExpandAllV, _) {
            return ValueListenableBuilder(
              valueListenable: expandedState,
              builder: (context, expandedStateV, _) {
                return Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        DiagramElementWidget(
                          isFullExpanded: expandedStateV == MyDiagramStates.difficultSituation,
                          isPartialExpanded: isExpandAllV,
                          isColorNameVisible: viewColorNameV,
                          score: ref.dsScore,
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(),
                            );
                          },
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;

                            ref.infoAudioUrl.value = null;

                            AppNavigation.nextScreen(context,  SituationActionMapPage());
                          },
                          onTap: () => onNodeTap(MyDiagramStates.difficultSituation),
                          containerKey: widgetKeys[1],
                          name: DiagramLocaleKeys.areaNamesDifficultSituations.tr(),
                          expandedTextList: [
                            

                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.conflict == 1)
                              DiagramLocaleKeys.keywordsDifficultSituationsConflict.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.risks == 1)
                              DiagramLocaleKeys.keywordsDifficultSituationsRisks.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.work == 1)
                              DiagramLocaleKeys.keywordsDifficultSituationsWork.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.money == 1)
                              DiagramLocaleKeys.keywordsDifficultSituationsMoney.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.pressure == 1)
                              DiagramLocaleKeys.keywordsDifficultSituationsPressure.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ds?.conflict == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ds?.risks == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ds?.work == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ds?.money == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ds?.pressure == 0)
                              DiagramLocaleKeys.noIssues.tr(),
                          ]
                        ),
                      ],
                    ),
                    SpaceV(AppSize.h40),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DiagramElementWidget(
                          containerKey: widgetKeys[2],
                          score: ref.ntScore,
                          name: DiagramLocaleKeys.areaNamesNegativeThoughts.tr(),
                          isColorNameVisible: viewColorNameV,
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(context, const MyThoughtsPage());
                          },
                          expandedTextList: [
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.good == 1)
                              DiagramLocaleKeys.keywordsNegativeThoughtsGood.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.control == 1)
                              DiagramLocaleKeys.keywordsNegativeThoughtsControl.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.health == 1)
                              DiagramLocaleKeys.keywordsNegativeThoughtsHealth.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.cope == 1)
                              DiagramLocaleKeys.keywordsNegativeThoughtsCope.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.trust == 1)
                              DiagramLocaleKeys.keywordsNegativeThoughtsTrust.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.nt?.good == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.nt?.control == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.nt?.health == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.nt?.cope == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.nt?.trust == 0)
                              DiagramLocaleKeys.noIssues.tr(),
                          ],
                          // expandedTextList: [
                          //   DiagramLocaleKeys.keywordsNegativeThoughtsControl,
                          //   DiagramLocaleKeys.keywordsNegativeThoughtsCope,
                          //   DiagramLocaleKeys.keywordsNegativeThoughtsTrust,
                          // ].map((key) => key.tr()).toList(),
                          isFullExpanded: expandedStateV == MyDiagramStates.negativeThoughts,
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(
                                diagramState: MyDiagramStates.negativeThoughts,
                              ),
                            );
                          },
                          isPartialExpanded: isExpandAllV,
                          onTap: () => onNodeTap(MyDiagramStates.negativeThoughts),
                        ),
                        SpaceH(AppSize.w20),
                        DiagramElementWidget(
                          containerKey: widgetKeys[3],
                          score: ref.eiScore,
                          name: DiagramLocaleKeys.areaNamesEmotionalImpact.tr(),
                          isColorNameVisible: viewColorNameV,
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              EmotionalImpactActionPage(),
                            );
                          },
                          isFullExpanded: expandedStateV == MyDiagramStates.emotionalImpact,
                          isPartialExpanded: isExpandAllV,
                          onTap: () => onNodeTap(MyDiagramStates.emotionalImpact),
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(
                                diagramState: MyDiagramStates.emotionalImpact,
                              ),
                            );
                          },
                          expandedTextList: [
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.nervous ?? 0 ) >= 1 )
                              DiagramLocaleKeys.keywordsEmotionalImpactNervous.tr(),
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.worry ?? 0) >= 1)
                              DiagramLocaleKeys.keywordsEmotionalImpactWorry.tr(),
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.down ?? 0) >= 1)
                              DiagramLocaleKeys.keywordsEmotionalImpactDown.tr(),
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.bad ?? 0) >= 1)
                              DiagramLocaleKeys.keywordsEmotionalImpactBad.tr(),
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.interest ?? 0) >= 1)
                              DiagramLocaleKeys.keywordsEmotionalImpactInterest.tr(),
                            if((Injector.instance<AppDB>().userModel?.user.assessment?.ei?.nervous ?? 0) < 1 && 
                                (Injector.instance<AppDB>().userModel?.user.assessment?.ei?.worry ?? 0) < 1 && 
                                (Injector.instance<AppDB>().userModel?.user.assessment?.ei?.down ?? 0) < 1 && 
                                (Injector.instance<AppDB>().userModel?.user.assessment?.ei?.bad ?? 0)< 1 && 
                                (Injector.instance<AppDB>().userModel?.user.assessment?.ei?.interest ?? 0) < 1)
                              DiagramLocaleKeys.noIssues.tr(),
                          ],
                          // expandedTextList: [
                          //   DiagramLocaleKeys.keywordsEmotionalImpactWorry,
                          //   DiagramLocaleKeys.keywordsEmotionalImpactBad,
                          //   DiagramLocaleKeys.keywordsEmotionalImpactWorry,
                          // ].map((key) => key.tr()).toList(),
                        ),
                      ],
                    ),
                    SpaceV(AppSize.h40),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        DiagramElementWidget(
                          containerKey: widgetKeys[4],
                          score: ref.lsScore,
                          isColorNameVisible: viewColorNameV,
                          padding: EdgeInsets.only(
                            left: AppSize.w10,
                            right: AppSize.w10,
                            top: AppSize.h20,
                            bottom: AppSize.h20,
                          ),
                          name: DiagramLocaleKeys.areaNamesLifestyle.tr(),
                          isFullExpanded: expandedStateV == MyDiagramStates.lifestyle,
                          isPartialExpanded: isExpandAllV,
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              const MyLifestylePage(),
                            );
                          },
                          onTap: () => onNodeTap(MyDiagramStates.lifestyle),
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(
                                diagramState: MyDiagramStates.lifestyle,
                              ),
                            );
                          },
                          expandedTextList: [
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.health == 1)
                              DiagramLocaleKeys.keywordsLifestyleHealth.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.work == 1)
                              DiagramLocaleKeys.keywordsLifestyleWork.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.leisure == 1)
                              DiagramLocaleKeys.keywordsLifestyleLeisure.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.relationships == 1)
                              DiagramLocaleKeys.keywordsLifestyleRelationships.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.housing == 1)
                              DiagramLocaleKeys.keywordsLifestyleHousing.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ls?.health == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ls?.work == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ls?.leisure == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ls?.relationships == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ls?.housing == 0)
                              DiagramLocaleKeys.noIssues.tr(),
                          ],
                          // expandedTextList: [
                          //   DiagramLocaleKeys.keywordsLifestyleHealth,
                          //   DiagramLocaleKeys.keywordsLifestyleLeisure,
                          //   DiagramLocaleKeys.keywordsLifestyleRelationships,
                          //   DiagramLocaleKeys.keywordsLifestyleHousing,
                          // ].map((key) => key.tr()).toList(),
                        ),
                      ],
                    ),
                    SpaceV(
                      [
                        MyDiagramStates.negativeThoughts,
                        MyDiagramStates.emotionalImpact,
                      ].contains(expandedStateV)
                          ? AppSize.h160
                          : AppSize.h40,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DiagramElementWidget(
                          containerKey: widgetKeys[5],
                          score: ref.ubScore,
                          isColorNameVisible: viewColorNameV,
                          name: DiagramLocaleKeys.areaNamesUnhelpfulBehaviours.tr(),
                          isFullExpanded: expandedStateV == MyDiagramStates.unhelpfulBehaviours,
                          isPartialExpanded: isExpandAllV,
                          onTap: () => onNodeTap(MyDiagramStates.unhelpfulBehaviours),
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              const UnhelpfulBehaviourCalendarPage(),
                            );
                          },
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(
                                diagramState: MyDiagramStates.unhelpfulBehaviours,
                              ),
                            );
                          },
                          expandedTextList: [
                             if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.aggressive == 1)
                              DiagramLocaleKeys.keywordsUnhelpfulBehavioursAggressive.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.avoid == 1)
                              DiagramLocaleKeys.keywordsUnhelpfulBehavioursAvoid.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.active == 1)
                              DiagramLocaleKeys.keywordsUnhelpfulBehavioursActive.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.care == 1)
                              DiagramLocaleKeys.keywordsUnhelpfulBehavioursCare.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.police == 1)
                              DiagramLocaleKeys.keywordsUnhelpfulBehavioursPolice.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ub?.aggressive == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ub?.avoid == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ub?.active == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ub?.care == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ub?.police == 0)
                              DiagramLocaleKeys.noIssues.tr(),
                          ],
                          // expandedTextList: [
                          //   DiagramLocaleKeys.keywordsUnhelpfulBehavioursActive,
                          //   DiagramLocaleKeys.keywordsUnhelpfulBehavioursPolice,
                          // ].map((key) => key.tr()).toList(),
                        ),
                        SpaceH(AppSize.w20),
                        DiagramElementWidget(
                          containerKey: widgetKeys[6],
                          score: ref.psScore,
                          isColorNameVisible: viewColorNameV,
                          name: DiagramLocaleKeys.areaNamesPhysicalSensations.tr(),
                          isFullExpanded: expandedStateV == MyDiagramStates.physicalSensations,
                          isPartialExpanded: isExpandAllV,
                          onActionTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              EmotionalImpactActionPage(
                                isPhysicalSensation: true,
                              ),
                            );
                          },
                          onInformationTap: () {
                            ref.infoAudioUrl.value = null;
                            ref.infoAudioUrl.value = null;
                            AppNavigation.nextScreen(
                              context,
                              InformationPage(
                                diagramState: MyDiagramStates.physicalSensations,
                              ),
                            );
                          },
                          onTap: () => onNodeTap(MyDiagramStates.physicalSensations),
                          expandedTextList: [
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.craving == 1)
                              DiagramLocaleKeys.keywordsPhysicalSensationsCraving.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.shakes == 1)
                              DiagramLocaleKeys.keywordsPhysicalSensationsShakes.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.cramps == 1)
                              DiagramLocaleKeys.keywordsPhysicalSensationsCramps.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.nausea == 1)
                              DiagramLocaleKeys.keywordsPhysicalSensationsNausea.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.tiredness == 1)
                              DiagramLocaleKeys.keywordsPhysicalSensationsTiredness.tr(),
                            if(Injector.instance<AppDB>().userModel?.user.assessment?.ps?.craving == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ps?.shakes == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ps?.cramps == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ps?.nausea == 0 && 
                                Injector.instance<AppDB>().userModel?.user.assessment?.ps?.tiredness == 0)
                              DiagramLocaleKeys.noIssues.tr(),
                          ],
                          // expandedTextList: [
                          //   DiagramLocaleKeys.keywordsPhysicalSensationsCraving,
                          //   DiagramLocaleKeys.keywordsPhysicalSensationsCramps,
                          //   DiagramLocaleKeys.keywordsPhysicalSensationsNausea,
                          // ].map((key) => key.tr()).toList(),
                        ),
                      ],
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
}
