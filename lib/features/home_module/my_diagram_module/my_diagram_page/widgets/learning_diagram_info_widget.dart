import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LearnDiagramInfoWidget extends StatelessWidget {
  const LearnDiagramInfoWidget({
    super.key,
    this.onPlayTap, this.onVideoEnded, this.navigationFunction, this.isFromLightBuilb1,
  });
  final void Function()? onPlayTap;
  final void Function()? onVideoEnded;
  final void Function()? navigationFunction;
  final void Function()? isFromLightBuilb1;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppTextWidget(
          (DynamicAssetLoader.getNestedValue(
            DiagramLocaleKeys.infoPanelsLearnText,
            context,
          ) as List)
              .join('\n\n'),
          style: context.textTheme.titleSmall?.copyWith(
            color: context.themeColors.darkOrangeColor,
          ),
        ),
        SpaceV(AppSize.h16),
        VideoPlayerScreen(
          imageList: [
            DiagramLocaleKeys.infoPanelsLearnVideoposter.tr(),
          ],
          onTap: onPlayTap,
          navigationFunction: navigationFunction ?? () {

          },
          isFromLightBuilb1: isFromLightBuilb1,
          isFromLightBuilb: true,
          onVideoEnded:onVideoEnded,
          videoList: [DiagramLocaleKeys.infoPanelsLearnVideoSrc.tr()],
        ),
      ],
    );
  }
}
