// ignore_for_file: avoid_positional_boolean_parameters

import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:flutter/material.dart';

class ExpandSwitch extends StatelessWidget {
  const ExpandSwitch({
    required this.isExpandAll,
    super.key,
    this.onChanged,
  });

  final ValueNotifier<bool> isExpandAll;
  final void Function(bool)? onChanged;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isExpandAll,
      builder: (context, isExpandAllV, _) {
        return Theme(
          data: ThemeData(
            useMaterial3: false,
          ),
          child: Switch(
            activeTrackColor:
                context.themeColors.switchThumbColor.withOpacity(.5),
            activeColor: context.themeColors.switchThumbColor,
            inactiveThumbColor: context.themeColors.switchThumbColor,
            inactiveTrackColor: context.themeColors.blackColor.withOpacity(.38),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            value: isExpandAllV,
            onChanged: onChanged,
          ),
        );
      },
    );
  }
}
