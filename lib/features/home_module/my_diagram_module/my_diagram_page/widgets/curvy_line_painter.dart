import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class CurvyLinePainter extends CustomPainter {
  CurvyLinePainter({
    required this.startOffset,
    required this.endOffset,
    this.mirror = false,
  });

  final Offset startOffset;
  final Offset endOffset;
  final bool mirror;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke;

    final controlPoint = Offset(
      (startOffset.dx + endOffset.dx) / 2,
      (startOffset.dy + endOffset.dy) / 2,
    );
    final path = Path()
      ..moveTo(startOffset.dx, startOffset.dy)
      ..quadraticBezierTo(
        controlPoint.dx + (mirror ? AppSize.w80 : -AppSize.w80), //Curve
        controlPoint.dy,
        endOffset.dx,
        endOffset.dy,
      );
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
