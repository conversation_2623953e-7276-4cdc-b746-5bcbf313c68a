import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/action_btn.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class DiagramElementWidget extends StatelessWidget {
  const DiagramElementWidget({
    required this.name,
    super.key,
    this.containerKey,
    this.onTap,
    this.isFullExpanded = false,
    this.isPartialExpanded = false,
    this.isColorNameVisible = false,
    this.expandedTextList,
    this.onInformationTap,
    this.onActionTap,
    this.score = 0,
    this.padding,
  });
  final String name;
  final List<String>? expandedTextList;
  final Key? containerKey;

  final void Function()? onTap;
  final void Function()? onInformationTap;
  final void Function()? onActionTap;
  final bool isFullExpanded;
  final bool isPartialExpanded;
  final bool isColorNameVisible;
  final int score;
  final EdgeInsets? padding;

  DiagramBtnColor _getSelectedColor(int score) {
    'score $score'.logD;
    if (score >= 0 && score <= 2) {
      return DiagramBtnColor.green;
    } else if (score >= 3 && score <= 6) {
      return DiagramBtnColor.orange;
    } else if (score >= 7 && score <= 10) {
      return DiagramBtnColor.red;
    } else {
      // Handle out-of-range scores if necessary
      return DiagramBtnColor.orange;
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedColor = _getSelectedColor(score);

    return Flexible(
      child: GestureDetector(
        onTap: isPartialExpanded ? null : onTap,
        child: Container(
          key: containerKey,
          width: AppSize.w140,
          padding: padding ?? EdgeInsets.all(AppSize.h10),
          decoration: BoxDecoration(
            color: selectedColor.color,
            borderRadius: BorderRadius.circular(AppSize.r15),
          ),
          child: Column(
            children: [
              AppTextWidget(
                name,
                textAlign: TextAlign.center,
                maxLines: 2,
                style: context.textTheme.titleMedium?.copyWith(
                  color: context.themeColors.whiteColor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (isColorNameVisible)
                AppTextWidget(
                  '(${selectedColor.name})',
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.themeColors.whiteColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              if (isFullExpanded || isPartialExpanded) ...[
                if (expandedTextList?.isNotEmpty ?? false)
                  ListView.builder(
                    itemCount: expandedTextList?.length ?? 0,
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                    itemBuilder: (context, index) {
                      return AppTextWidget(
                        "${expandedTextList![index]}${index == (expandedTextList!.length) - 1 ? '' : ','}",
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        style: context.textTheme.labelSmall?.copyWith(
                          color: context.themeColors.whiteColor,
                          overflow: TextOverflow.ellipsis,
                          fontSize: AppSize.sp10,
                        ),
                      );
                    },
                  ),
                if (isFullExpanded) ...[
                  ActionBtn(
                    // width: AppSize.w100,
                    name: DiagramLocaleKeys.buttonsInformation.tr(),
                    onInformationTap: onInformationTap,
                  ),
                  SpaceV(AppSize.h5),
                  ActionBtn(
                    // width: AppSize.w100,
                    name: DiagramLocaleKeys.buttonsAction.tr(),
                    onInformationTap: onActionTap,
                  ),
                ],
              ],
            ],
          ),
        ),
      ),
    );
  }
}
