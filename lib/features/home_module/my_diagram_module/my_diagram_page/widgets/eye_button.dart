import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class EyeButton extends StatelessWidget {
  const EyeButton({
    required this.viewColorName,
    super.key,
    this.onTap,
  });

  final ValueNotifier<bool> viewColorName;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: GestureDetector(
        onTap: onTap,
        child: ValueListenableBuilder(
          valueListenable: viewColorName,
          builder: (context, viewColorNameV, _) {
            return Icon(
              viewColorNameV
                  ? Icons.visibility_off_outlined
                  : Icons.visibility_outlined,
              color: context.themeColors.blackColor.withOpacity(.54),
              size: AppSize.h22,
            );
          },
        ),
      ),
    );
  }
}
