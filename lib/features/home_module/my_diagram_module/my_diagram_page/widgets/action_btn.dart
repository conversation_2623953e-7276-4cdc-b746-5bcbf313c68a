import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class ActionBtn extends StatelessWidget {
  const ActionBtn({
    required this.name,
    required this.onInformationTap,
    super.key,
    this.width,
  });
  final String name;
  final double? width;
  final void Function()? onInformationTap;

  @override
  Widget build(BuildContext context) {
    return CustomRoundedButton(
      width: MediaQuery.of(context).size.width * 0.30,
      title: name,
      verticalPadding: AppSize.h3,
      horizontalPadding: AppSize.w10,
      titleTextStyle: context.textTheme.bodySmall?.copyWith(
        color: context.themeColors.whiteColor,
        fontWeight: FontWeight.w400,
        overflow: TextOverflow.ellipsis,
        fontSize: 1,
      ),
      onTap: onInformationTap,
      fillColor: context.themeColors.diagramBtnColor,
      radius: AppSize.r10,
      strokeWidth: AppSize.w2,
    );
  }
}
