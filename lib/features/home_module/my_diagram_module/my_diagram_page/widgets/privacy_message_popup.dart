import 'package:breakingfree_v2/custom_widgets/custom_launcher.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class PrivacyMessagePopup extends StatelessWidget {
  const PrivacyMessagePopup({
    super.key,
    this.visible = false,
    this.onCloseTap,
  });
  final bool visible;
  final void Function()? onCloseTap;
  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: context.themeColors.lightBrownColor,
              borderRadius: BorderRadius.circular(AppSize.r12),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h12),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: AppSize.w12, top: AppSize.h3),
                        child: Icon(
                          Icons.warning_amber_outlined,
                          size: AppSize.sp28,
                          color: context.themeColors.brownColor,
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(left: AppSize.w6, right: AppSize.w30, top: AppSize.h4),
                          child: Text.rich(
                            textAlign: TextAlign.center,
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.themeColors.darkBrownColor,
                              fontWeight: FontWeight.w400,
                              fontSize: AppSize.sp12,
                            ),
                            TextSpan(
                              text: '${(DynamicAssetLoader.getNestedValue(
                                CoreLocaleKeys.dataWarningText,
                                context,
                              ) as List).join('\n')} ',

                              // 'Please take care to keep your data private and secure.\nFor more information, see ',
                              children: [
                                TextSpan(
                                  text: CoreLocaleKeys.dataWarningLinkText.tr(),
                                  style: context.textTheme.titleSmall?.copyWith(
                                    color: const Color.fromRGBO(33, 65, 129, 1),
                                    decoration: TextDecoration.underline,
                                    fontWeight: FontWeight.w400,
                                    fontSize: AppSize.sp12,
                                    decorationThickness: 1.3,
                                    height: 1.6,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      'current locale ${context.locale}'.logV;
                                      if (context.locale == const Locale('en', 'GB') ||
                                          context.locale == const Locale('en', 'UK')) {
                                        CustomLauncher.launchURL(
                                          'https://breakingfreeonline.com/terms-standalone/skip',
                                        );
                                      } else if (context.locale == const Locale('en', 'US') ||
                                          context.locale == const Locale('es', 'US')) {
                                        CustomLauncher.launchURL(
                                          'https://breakingfreeonline.us/eula-standalone/skip',
                                        );
                                      } else if (context.locale == const Locale('en', 'CA') ||
                                          context.locale == const Locale('fr', 'CA')) {
                                        CustomLauncher.launchURL(
                                          'https://breakingfreeonline.ca/eula-standalone/skip',
                                        );
                                      } else if (context.locale == const Locale('en', 'AU')) {
                                        CustomLauncher.launchURL(
                                          ' https://breakingfreeonline.com.au/terms-standalone/skip',
                                        );
                                      }

                                      // Todo: Open link
                                    },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: AppSize.w8, right: AppSize.w8, top: AppSize.h6, bottom: AppSize.h8),
            child: Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: onCloseTap,
                child: Icon(
                  Icons.close,
                  size: AppSize.sp18,
                  color: const Color.fromRGBO(117, 95, 66, 1),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
