import 'package:flutter/material.dart';

class StraightLinePainter extends CustomPainter {
  StraightLinePainter({
    required this.startOffset,
    required this.endOffset,
    this.isDashed = false,
  });

  final Offset startOffset;
  final Offset endOffset;
  final bool isDashed;

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black // Line color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5; // Line width

    if (isDashed) {
      _drawDashedLine(canvas, startOffset, endOffset, paint);
    } else {
      canvas.drawLine(startOffset, endOffset, paint);
    }
  }

  // Helper function to draw a dashed line
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    const dashWidth = 12;
    const dashSpace = 8;
    final totalLength = (end - start).distance;
    final dashCount = (totalLength / (dashWidth + dashSpace)).floor();

    final direction = (end - start).direction;
    final dashVector = Offset.fromDirection(direction, dashWidth.toDouble());
    final spaceVector = Offset.fromDirection(direction, dashSpace.toDouble());

    var currentPoint = start;

    for (var i = 0; i < dashCount; i++) {
      canvas.drawLine(currentPoint, currentPoint + dashVector, paint);
      currentPoint += dashVector + spaceVector;
    }

    // Draw remaining part if there's a partial dash
    if ((end - currentPoint).distance > 0) {
      canvas.drawLine(currentPoint, end, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
