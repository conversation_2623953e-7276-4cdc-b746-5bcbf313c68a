import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/curvy_line_painter.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/straight_line_painter.dart';
import 'package:flutter/material.dart';

class CustomLineWidget extends StatelessWidget {
  const CustomLineWidget(
    this.start,
    this.end, {
    super.key,
    this.isDashed = false,
    this.isCurvy = false,
    this.mirror = false,
  });
  final Offset start;
  final Offset end;
  final bool isDashed;
  final bool isCurvy;
  final bool mirror;

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: isCurvy
          ? CurvyLinePainter(
              startOffset: start,
              endOffset: end,
              mirror: mirror,
            )
          : StraightLinePainter(
              isDashed: isDashed,
              startOffset: start,
              endOffset: end,
            ),
    );
  }
}
