import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class DiagramInfoWidget extends StatelessWidget {
  const DiagramInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Html(
          data: (DynamicAssetLoader.getNestedValue(
            DiagramLocaleKeys.infoPanelsInformationText,
            context,
          ) as List)
              .join('<br><br>'),
          style: {
            'h1': Style(
              color: context.themeColors.darkOrangeColor,
              fontSize: FontSize(AppSize.sp18),
              fontFamily: 'Poppins',
              fontWeight: FontWeight.w600,
            ),
            'p': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins',color: context.themeColors.darkOrangeColor,),
            'body': Style(fontSize: FontSize(AppSize.sp13), fontFamily: 'Poppins',color: context.themeColors.darkOrangeColor,),
          },
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: AppSize.h12,
          ),
          child: Divider(
            height: 1,
            color: context.themeColors.blackColor.withOpacity(0.12),
          ),
        ),
        Row(
          children: [
            Flexible(
              child: AppTextWidget(
              DiagramLocaleKeys.infoPanelsInformationColourBlind.tr(),
                style: context.textTheme.titleSmall?.copyWith(color: context.themeColors.darkOrangeColor),
              ),
            ),
            SpaceH(AppSize.w8),
            Icon(
              Icons.visibility_outlined,
              color: context.themeColors.blackColor.withOpacity(.54),
              size: AppSize.h22,
            ),
          ],
        ),
        // Text.rich(
        //   style: context.textTheme.titleSmall
        //       ?.copyWith(color: context.themeColors.darkOrangeColor),
        //   TextSpan(
        //     text:
        //         'Welcome to your personal diagram!\n\nIt shows you the areas of potential difficulty that could affect your recovery:\n\n',
        //     children: [
        //       TextSpan(
        //         text: 'RED AREAS ',
        //         style: context.textTheme.titleSmall?.copyWith(
        //           color: context.themeColors.redColor,
        //           fontWeight: FontWeight.w600,
        //         ),
        //       ),
        //       const TextSpan(
        //         text:
        //             'are the most important ones to focus on because they’re having a major impact on you.\n\n',
        //       ),
        //       TextSpan(
        //         text: 'AMBER AREAS ',
        //         style: context.textTheme.titleSmall?.copyWith(
        //           color: context.themeColors.amberColor,
        //           fontWeight: FontWeight.w600,
        //         ),
        //       ),
        //       const TextSpan(
        //         text:
        //             'are ones you should definitely work on too as they’re still having a significant impact on you.\n\n',
        //       ),
        //       TextSpan(
        //         text: 'GREEN AREAS ',
        //         style: context.textTheme.titleSmall?.copyWith(
        //           color: context.themeColors.greenBtnColor,
        //           fontWeight: FontWeight.w600,
        //         ),
        //       ),
        //       const TextSpan(
        //         text:
        //             'are not having much impact on you now but exploring these will help you build your resilience.\n\nYour diagram is also your gateway to all the proven strategies in Breaking Free.\n\nClick on any area of the diagram to see the particular challenges you’re facing in that area.\n\nThen use the buttons to go straight to the INFORMATION strategy or the ACTION strategy.\n\nThese strategies will help you resolve your issues and move on with your recovery journey.\n\nSo keep using Breaking Free until every area of your diagram is green!',
        //       ),
        //     ],
        //   ),
        // ),
        // Padding(
        //   padding: EdgeInsets.symmetric(
        //     vertical: AppSize.h12,
        //   ),
        //   child: Divider(
        //     height: 1,
        //     color: context.themeColors.blackColor.withOpacity(0.12),
        //   ),
        // ),
        // Row(
        //   children: [
        //     Flexible(
        //       child: AppTextWidget(
        //         'If you are colour blind, click on the eye icon below:',
        //         style: context.textTheme.titleSmall
        //             ?.copyWith(color: context.themeColors.darkOrangeColor),
        //       ),
        //     ),
        //     Icon(
        //       Icons.visibility_outlined,
        //       color: context.themeColors.blackColor.withOpacity(.54),
        //       size: AppSize.h22,
        //     ),
        //   ],
        // ),
      ],
    );
  }
}
