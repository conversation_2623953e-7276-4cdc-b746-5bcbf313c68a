import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/custom_line_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class GraphEdges extends StatelessWidget {
  const GraphEdges({required this.wKeys, super.key});

  final List<GlobalKey> wKeys;

  Offset centerOf(GlobalKey wKey) {
    final canvasBox = wKeys[0].currentContext?.findRenderObject() as RenderBox?;
    final widgetBox = wKey.currentContext?.findRenderObject() as RenderBox?;

    if (widgetBox != null &&
        canvasBox != null &&
        widgetBox.hasSize &&
        canvasBox.hasSize) {
      final centerOffset = widgetBox.localToGlobal(Offset.zero) -
          canvasBox.localToGlobal(Offset.zero) +
          Offset(widgetBox.size.width / 2, AppSize.h30);
      return centerOffset;
    }
    return Offset.zero;
  }

  @override
  Widget build(BuildContext context) {
    final centers = wKeys.map(centerOf).toList();
    return Column(
      children: [
        CustomLineWidget(centers[1], centers[2], isDashed: true),
        CustomLineWidget(centers[1], centers[3], isDashed: true),
        CustomLineWidget(centers[2], centers[3]),
        CustomLineWidget(centers[2], centers[4]),
        CustomLineWidget(centers[3], centers[4]),
        CustomLineWidget(centers[4], centers[5]),
        CustomLineWidget(centers[4], centers[6]),
        CustomLineWidget(centers[5], centers[6]),
        CustomLineWidget(centers[2], centers[5], isCurvy: true),
        CustomLineWidget(centers[3], centers[6], isCurvy: true, mirror: true),
      ],
    );
  }
}
