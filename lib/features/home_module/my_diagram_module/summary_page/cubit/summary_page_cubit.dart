import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/information_repository/information_repository.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'summary_page_cubit.freezed.dart';
part 'summary_page_state.dart';

class SummaryPageCubit extends Cubit<SummaryPageState> {
  SummaryPageCubit() : super(const SummaryPageState.initial()) {
    infoAudioUrl.value = IsLocaleKeys.summaryAudio.tr();
  }

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  InformationRepository informationRepository = InformationRepository();

  Future<void> informationActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required String area,
  }) async {
    isEmail ? emit(const SummaryPageState.emailPdfLoading()) : emit(const SummaryPageState.downloadPdfLoading());

    //   isEmail ? emit(state.copyWith(isEmailPdfAPILoading: true)) : emit(state.copyWith(isDownloadPdfAPILoading: true));
    try {
      final response = await informationRepository.informationActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
        area: area,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              area == 'ds'
                  ? 'Understanding your difficult situations.pdf'
                  : area == 'nt'
                      ? 'Understanding your negative thoughts.pdf'
                      : area == 'ei'
                          ? 'Understanding your emotions.pdf'
                          : area == 'ps'
                              ? 'Understanding your physical sensations.pdf'
                              : area == 'ub'
                                  ? 'Understanding your unhelpful behaviours.pdf'
                                  : area == 'ls'
                                      ? 'Understanding your lifestyle.pdf'
                                      : 'Understanding your lifestyle.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      emit(const SummaryPageState.initial());
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(const SummaryPageState.initial());
    }
  }

  @override
  Future<void> close() {
    isManuallyPaused.dispose();
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    headerInfoText.dispose();

    return super.close();
  }
}
