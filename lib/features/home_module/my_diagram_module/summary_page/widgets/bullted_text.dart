import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class BulletedTextWidget extends StatelessWidget {
  const BulletedTextWidget({
    required this.text,
    super.key,
  });
  final String text;
  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: AppSize.h4,left: AppSize.w16),
          child: Assets.icons.bullet.image(
            height: AppSize.h16,
            width: AppSize.h16,
          ),
        ),
        SpaceH(AppSize.w12),
        Flexible(
          child: AppTextWidget(
            text,
            style: context.textTheme.labelSmall?.copyWith(
              fontSize: AppSize.sp13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
