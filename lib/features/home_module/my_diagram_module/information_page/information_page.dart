import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/cubit/information_page_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/info_slider_on_video_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/well_done_page/well_done_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class InformationPage extends StatelessWidget {
  InformationPage({
    super.key,
    this.diagramState = MyDiagramStates.difficultSituation,
  });
  final MyDiagramStates diagramState;

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final regExp = RegExp('<[^>]*>');

  @override
  Widget build(BuildContext context) {
    
    return BlocProvider(
      create: (context) => InformationPageCubit(),
      child: BlocBuilder<InformationPageCubit, InformationPageState>(
        builder: (context, state) {
          final ref = context.read<InformationPageCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              '/// ref.infoAudioUrl = ${ref.infoAudioUrl.value}'.logD;
              return PopScope(
                onPopInvokedWithResult: (didPop, result) {
                  if (didPop) {
                    ref.clearAudioData();
                  }
                },
                child: AppScaffold(
                  scaffoldKey: _scaffoldKey,
                  isAudioPanelVisible: ref.isAudioPanelVisible,
                  infoAudioUrl: ref.infoAudioUrl,
                  isManuallyPaused: ref.isManuallyPaused,
                  appBar: CommonAppBar(
                    onPrefixTap: () {
                      _scaffoldKey.currentState?.openDrawer();
                    },
                    onSuffixTap: () {
                      if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                        ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                      }
                    },
                  ),
                  drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                  body: Column(
                    children: [
                      Expanded(
                        child: ColoredBox(
                          color: context.themeColors.whiteColor,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(
                                      minHeight: constraints.maxHeight,
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(vertical: AppSize.h24),
                                      child: Column(
                                        children: [
                                          InformationPageHeadingWidget(
                                            title: diagramState.title ?? '',
                                            subtitle: diagramState.subTitle ?? '',
                                            icon: diagramState.iconPath,
                                            onBackArrowTap: () {
                                              ref.clearAudioData();
                                              Navigator.pop(context);
                                            },
                                            onInfoTap: () {
                                              final info = diagramState.getInfoText.tr().replaceAll('\n', '<br/>');
                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info) {
                                                //ref.isManuallyPaused.value = true;
                                                ref.infoAudioUrl.value = diagramState.getInfoAudio.tr();
                                                ref.headerInfoText.value = null;
                                                ref.isAudioPanelVisible.value = false;
                                              } else {
                                                //ref.isManuallyPaused.value = false;
                                                ref.infoAudioUrl.value = diagramState.getInfoAudio.tr();
                                                ref.headerInfoText.value = info;
                                              }
                                            },
                                            onLearnTap: () {
                                              final info = diagramState.getLearnText.tr().replaceAll('\n', '<br/>');
                                              if (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                  ref.headerInfoText.value == info) {
                                                //ref.isManuallyPaused.value = true;
                                                ref.infoAudioUrl.value = null;
                                                ref.headerInfoText.value = null;
                                                ref.isAudioPanelVisible.value = false;
                                              } else {
                                                //ref.isManuallyPaused.value = false;
                                                ref.headerVideoUrl.value = true;
                                                ref.infoAudioUrl.value = diagramState.getLearnAudio.tr();
                                                ref.headerInfoText.value = info;
                                              }
                                            },
                                            infoWidget: ValueListenableBuilder(
                                              valueListenable: ref.headerInfoText,
                                              builder: (context, headerInfoTextV, _) {
                                                return CustomInfoWidget(
                                                  padding: EdgeInsets.only(
                                                    left: AppSize.w8,
                                                    right: AppSize.w8,
                                                  ),
                                                  customWidget: Column(
                                                    children: [
                                                      Html(
                                                        data: ref.headerInfoText.value ?? '',
                                                        style: {
                                                          'strong': Style(
                                                            fontSize: FontSize(AppSize.sp13),
                                                            color: context.themeColors.darkOrangeColor,
                                                            fontWeight: FontWeight.bold,
                                                            fontFamily: 'Poppins',
                                                          ),
                                                          'body': Style(
                                                            fontSize: FontSize(AppSize.sp13),
                                                            color: context.themeColors.darkOrangeColor,
                                                            fontFamily: 'Poppins',
                                                          ),
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                  onCloseTap: () {
                                                    ref.isManuallyPaused.value = true;
                                                    ref.headerInfoText.value = null;
                                                    ref.infoAudioUrl.value = null;
                                                  },
                                                  visible: headerInfoTextV.isNotEmptyAndNotNull,
                                                  margin: EdgeInsets.symmetric(
                                                    vertical: AppSize.h8,
                                                  ),
                                                  bodyText: headerInfoTextV,
                                                );
                                              },
                                            ),
                                          ),
                                          SpaceV(AppSize.h8),
                                          InfoSliderOnVideoWidget(
                                            selectedPage: ref.selectedPage,
                                            swippedOnce: ref.swippedOnce,
                                            infoList: diagramState.data,
                                            onPageChanged: (index, value, isEnded) {
                                              ref.infoAudioUrl.value = diagramState.data[index].audio.tr();
                                              '/// ${diagramState.data[index].audio.tr()}'.logV;
                                              '///hh ${diagramState.data[index].text.tr()}'.logV;
                                              if (isEnded && !ref.isProgressCompleted.value) {
                                                ref.isProgressCompleted.value = true;
                                              }
                                            },
                                            videoLink: diagramState.getVideoList[ref.completionCount.value % 3],
                                          ),
                                          SpaceV(AppSize.h30),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                      ValueListenableBuilder(
                        valueListenable: ref.isProgressCompleted,
                        builder: (context, isProgressCompletedV, _) {
                          return ColoredBox(
                            color: context.themeColors.whiteColor,
                            child: CustomButton(
                              inProgress: state.maybeMap(
                                orElse: () => false,
                                loading: (value) => true,
                              ),
                              isDisable: !isProgressCompletedV,
                              disableColor: context.themeColors.blueColor.withOpacity(0.7),
                              disableTextColor: context.themeColors.whiteColor,
                              isBottom: true,
                              title: IsLocaleKeys.buttonsNext.tr(),
                              color: context.themeColors.blueColor,
                              onTap: () async {
                                ref.infoAudioUrl.value = null;

                                await ref.informationStrategyAPI(context: context, type: diagramState.typeName).then(
                                  (value) {
                                    AppNavigation.nextScreen(
                                      context,
                                      WellDonePage(diagramState: diagramState),
                                    );
                                    ref.isProgressCompleted.value = false;
                                    ref.selectedPage.value = 0;
                                    ref.swippedOnce.value = false;
                                  },
                                );
                                ref.updateProgress(diagramState.getVideoList.length);
                              },
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  // String _removeHtmlTags(String htmlText) {
  //   final regex = RegExp('<[^>]*>', multiLine: true, caseSensitive: false);
  //   return htmlText.replaceAll(regex, '');
  // }
}
