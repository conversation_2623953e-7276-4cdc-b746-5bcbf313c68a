import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class IndicatorWidget extends StatelessWidget {
  const IndicatorWidget({
    super.key,
    this.count = 6,
    this.selectedIndex = -1,
  });
  final int count;
  final int selectedIndex;
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      scrollDirection: Axis.horizontal,
      itemCount: count,
      itemBuilder: (context, index) {
        return CircleAvatar(
          backgroundColor: index == selectedIndex
              ? context.themeColors.greenColor
              : context.themeColors.greyColor.withOpacity(.6),
          radius: AppSize.r5,
        );
      },
      separatorBuilder: (context, index) => SpaceH(AppSize.w4),
    );
  }
}
