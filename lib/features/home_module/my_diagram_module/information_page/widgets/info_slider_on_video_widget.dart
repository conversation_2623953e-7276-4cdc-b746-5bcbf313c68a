// ignore_for_file: avoid_positional_boolean_parameters

import 'dart:ui';

import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/fade_in_on_create.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/model/info_text_audio_model.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/indicator_widget.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class InfoSliderOnVideoWidget extends StatelessWidget {
  InfoSliderOnVideoWidget({
    required this.videoLink,
    required this.infoList,
    super.key,
    this.onPageChanged,
    required this.selectedPage,
    required this.swippedOnce,
  });

  final String? videoLink;
  final List<InfoTextAudioModel> infoList;
  final void Function(int, InfoTextAudioModel, bool)? onPageChanged;

  final ValueNotifier<bool> swippedOnce;// = ValueNotifier(false);

  final ValueNotifier<int> selectedPage;// = ValueNotifier(-1);

  @override
  Widget build(BuildContext context) {
    '????? videoLink = ${videoLink ?? ''}'.logV;
    return Column(
      children: [
        SizedBox(
          height: AppSize.h12,
          child: ValueListenableBuilder(
            valueListenable: selectedPage,
            builder: (context, selectedPageV, _) => IndicatorWidget(selectedIndex: selectedPageV),
          ),
        ),
        SpaceV(AppSize.h8),
        GestureDetector(
          onPanUpdate: (details) {
            if (!swippedOnce.value) {
              swippedOnce.value = true;
              selectedPage.value = 0;
              onPageChanged?.call(0, infoList[0], false);
            }
          },
          child: SizedBox(
            height: AppSize.h300,
            child: Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                  child: CustomVideoPlayer(
                    key: ValueKey(videoLink),
                    file: videoLink ?? '',
                    image: '',
                    showThumbnail: false,
                    autoPlay: true,
                    loop: true,
                    fullscreen: false,
                    showControls: false,
                    isExpanded: true,
                  ),
                ),
                /*VideoBlockTextOverlay(
                  videoUrl: videoLink ?? '',
                  textList: infoList.map<InfoTextAudioModel>((e) => e).toList(),
                ),*/
                ValueListenableBuilder(
                  valueListenable: swippedOnce,
                  builder: (context, swippedOnceV, _) {
                    if (!swippedOnceV) {
                      return Positioned(
                        bottom: AppSize.h20,
                        child: AppSvgAsset(
                          svgAsset: Assets.icons.infoPage.swipe,
                          color: context.themeColors.whiteColor,
                          size: AppSize.h80,
                        ),
                      );
                    } else if (infoList.isNotEmpty) {
                      return FadeInOnCreate(
                        child: PageView.builder(
                          physics: const BouncingScrollPhysics(),
                          itemCount: infoList.length,
                          onPageChanged: (value) {
                            selectedPage.value = value;
                            onPageChanged?.call(
                              value,
                              infoList[value],
                              value == infoList.length - 1,
                            );
                          },
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: AppSize.h60,
                                horizontal: AppSize.w30,
                              ),
                              child: ClipRRect(
                                child: Container(
                                  padding: EdgeInsets.all(AppSize.h8),
                                  alignment: AlignmentDirectional.center,
                                  decoration: BoxDecoration(
                                    color: context.themeColors.whiteColor.withOpacity(0.8),
                                    // gradient: LinearGradient(
                                    //   begin: AlignmentDirectional.topCenter,
                                    //   end: AlignmentDirectional.bottomCenter,
                                    //   colors: [
                                    //     context.themeColors.dividerColor
                                    //         .withOpacity(.8),
                                    //     context.themeColors.whiteColor
                                    //         .withOpacity(0.5),
                                    //     context.themeColors.dividerColor
                                    //         .withOpacity(.8),
                                    //   ],
                                    // ),
                                    borderRadius: BorderRadius.circular(
                                      AppSize.r10,
                                    ),
                                  ),
                                  child: Center(
                                    child: AppTextWidget(
                                      infoList[index].text,
                                      textAlign: TextAlign.center,
                                      style: context.textTheme.titleLarge?.copyWith(
                                        color: context.themeColors.darkBlue,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class VideoBlockTextOverlay extends StatefulWidget {
  const VideoBlockTextOverlay({required this.textList, required this.videoUrl, super.key});
  final List<InfoTextAudioModel> textList; // 6 texts
  final String videoUrl;

  @override
  State<VideoBlockTextOverlay> createState() => _VideoBlockTextOverlayState();
}

class _VideoBlockTextOverlayState extends State<VideoBlockTextOverlay> {
  int currentIndex = -1;

  void nextText() {
    if (currentIndex < 5) {
      setState(() {
        currentIndex++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          child: CustomVideoPlayer(
            key: ValueKey(widget.videoUrl),
            file: widget.videoUrl,
            image: '',
            showThumbnail: false,
            autoPlay: true,
            loop: true,
            fullscreen: false,
            showControls: false,
            isExpanded: true,
          ),
        ),
        SizedBox(
          height: 500,
          child: GridView.builder(
            itemCount: widget.textList.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3, // 2 columns
            ),
            itemBuilder: (context, index) {
              return ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: AppSize.w6,
                    sigmaY: AppSize.w6,
                  ),
                  child: Container(
                    padding: EdgeInsets.all(AppSize.h8),
                    alignment: AlignmentDirectional.center,
                    decoration: BoxDecoration(
                      color: context.themeColors.whiteColor.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(
                        AppSize.r10,
                      ),
                    ),
                    child: index <= currentIndex
                        ? Center(
                            child: AppTextWidget(
                              widget.textList[index].text,
                              textAlign: TextAlign.center,
                              style: context.textTheme.titleLarge?.copyWith(
                                color: context.themeColors.darkBlue,
                              ),
                            ),
                          )
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: ElevatedButton(
            onPressed: nextText,
            child: const Text('Next'),
          ),
        ),
      ],
    );
  }
}

