import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class InformationPageHeadingWidget extends StatelessWidget {
  const InformationPageHeadingWidget({
    required this.title,
    required this.subtitle,
    required this.icon,
    super.key,
    this.onInfoTap,
    this.onLearnTap,
    this.infoWidget,
    this.onBackArrowTap,
  });
  final String title;
  final String subtitle;
  final String icon;
  final VoidCallback? onInfoTap;
  final VoidCallback? onLearnTap;
  final VoidCallback? onBackArrowTap;
  final Widget? infoWidget;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: AppSize.w40,
              child: CustomBackArrowButton(
                padding: 0,
                onTap: onBackArrowTap,
              ),
            ),
            Expanded(
              child: AppTextWidget(
                title,
                textAlign: TextAlign.center,
                style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
              ),
            ),
            SpaceH(AppSize.w40),
          ],
        ),
        SpaceV(AppSize.h8),
        AppTextWidget(
          subtitle,
          textAlign: TextAlign.center,
          maxLines: 1,
          style: context.textTheme.labelSmall?.copyWith(
            fontSize: AppSize.sp13,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SpaceV(AppSize.h16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                CustomIconButton(
                  iconPath: icon,
                  size: AppSize.h36,
                ),
              ],
            ),
            Row(
              children: [
                CustomIconButton(
                  assetIcon: Assets.icons.infoIcon,
                  size: AppSize.h24, //AppSize.h30,
                  onTap: onInfoTap,
                ),
                SpaceH(AppSize.w4),
                CustomIconButton(
                  iconPath: Assets.icons.idea,
                  size: AppSize.h24, //AppSize.h30,
                  onTap: onLearnTap,
                ),
              ],
            ),
          ],
        ),
        SpaceV(AppSize.h4),
        if (infoWidget != null) infoWidget ?? const SizedBox.shrink(),
        SpaceV(AppSize.h4),
        Divider(
          height: 1,
          color: context.themeColors.greyColor.withOpacity(.6),
        ),
        SpaceV(AppSize.h20),
      ],
    );
  }
}
