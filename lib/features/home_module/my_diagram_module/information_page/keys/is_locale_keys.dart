class IsLocaleKeys {
  // ds (Difficult Situations) keys
  static const dsTitle = 'is.ds.title';
  static const dsVideos = 'is.ds.videos';
  static const dsSlidesText = 'is.ds.slides';
  static const dsSlidesAudio = 'is.ds.slides.audio';
  static const dsInfoText = 'is.ds.infoPanels.information.text';
  static const dsLearnText = 'is.ds.infoPanels.learn.text';
  static const dsInfoAudio = 'is.ds.infoPanels.information.audio';
  static const dsInfoLearnAudio = 'is.ds.infoPanels.learn.audio';

  // nt (Negative Thoughts) keys
  static const ntTitle = 'is.nt.title';
  static const ntVideos = 'is.nt.videos';
  static const ntSlidesText = 'is.nt.slides';
  static const ntSlidesAudio = 'is.nt.slides.audio';
  static const ntInfoText = 'is.nt.infoPanels.information.text';
  static const ntLearnText = 'is.nt.infoPanels.learn.text';
  static const ntInfoAudio = 'is.nt.infoPanels.information.audio';
  static const ntInfoLearnAudio = 'is.nt.infoPanels.learn.audio';

  // ei (Emotions) keys
  static const eiTitle = 'is.ei.title';
  static const eiVideos = 'is.ei.videos';
  static const eiSlidesText = 'is.ei.slides';
  static const eiSlidesAudio = 'is.ei.slides.audio';
  static const eiInfoText = 'is.ei.infoPanels.information.text';
  static const eiLearnText = 'is.ei.infoPanels.learn.text';
  static const eiInfoAudio = 'is.ei.infoPanels.information.audio';
  static const eiInfoLearnAudio = 'is.ei.infoPanels.learn.audio';

  // ps (Physical Sensations) keys
  static const psTitle = 'is.ps.title';
  static const psVideos = 'is.ps.videos';
  static const psSlidesText = 'is.ps.slides';
  static const psSlidesAudio = 'is.ps.slides.audio';
  static const psInfoText = 'is.ps.infoPanels.information.text';
  static const psLearnText = 'is.ps.infoPanels.learn.text';
  static const psInfoAudio = 'is.ps.infoPanels.information.audio';
  static const psInfoLearnAudio = 'is.ps.infoPanels.learn.audio';

  // ub (Unhelpful Behaviours) keys
  static const ubTitle = 'is.ub.title';
  static const ubVideos = 'is.ub.videos';
  static const ubSlidesText = 'is.ub.slides';
  static const ubSlidesAudio = 'is.ub.slides.audio';
  static const ubInfoText = 'is.ub.infoPanels.information.text';
  static const ubLearnText = 'is.ub.infoPanels.learn.text';
  static const ubInfoAudio = 'is.ub.infoPanels.information.audio';
  static const ubInfoLearnAudio = 'is.ub.infoPanels.learn.audio';

  // ls (Lifestyle) keys
  static const lsTitle = 'is.ls.title';
  static const lsVideos = 'is.ls.videos';
  static const lsSlidesText = 'is.ls.slides';
  static const lsSlidesAudio = 'is.ls.slides.audio';
  static const lsInfoText = 'is.ls.infoPanels.information.text';
  static const lsLearnText = 'is.ls.infoPanels.learn.text';
  static const lsInfoAudio = 'is.ls.infoPanels.information.audio';
  static const lsInfoLearnAudio = 'is.ls.infoPanels.learn.audio';
  // wellDone keys
  static const wellDoneTitle = 'is.wellDone.title';
  static const wellDoneAudio = 'is.wellDone.audio';
  static const wellDoneText = 'is.wellDone.text';

  // summary keys
  static const summaryTitle = 'is.summary.title';
  static const summaryAudio = 'is.summary.audio';
  static const summaryText = 'is.summary.text';

  // buttons keys
  static const buttonsBack = 'is.buttons.back';
  static const buttonsStart = 'is.buttons.start';
  static const buttonsNext = 'is.buttons.next';
  static const buttonsFinish = 'is.buttons.finish';
  static const buttonsSummary = 'is.buttons.summary';
  static const buttonsDiagram = 'is.buttons.diagram';

  static const String situationActionMapTitle = 'situation_action_map_title';
  static const String situationActionMapText = 'situation_action_map_text';
}
