import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/information_repository/information_repository.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'information_page_cubit.freezed.dart';
part 'information_page_state.dart';

class InformationPageCubit extends Cubit<InformationPageState> {
  InformationPageCubit() : super(const InformationPageState.initial()) {
    isProgressCompleted.value = false;
  }
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<bool> headerVideoUrl = ValueNotifier(false);
  ValueNotifier<bool> isProgressCompleted = ValueNotifier(false);
  ValueNotifier<bool> isShowSituation = ValueNotifier(false);
  ValueNotifier<bool> isShowVideoLoading = ValueNotifier(false);
  ValueNotifier<int> selectedIndex = ValueNotifier(0);
  ValueNotifier<int> completionCount = ValueNotifier(Injector.instance<AppDB>().videoIndex);

  ValueNotifier<bool> swippedOnce = ValueNotifier(false);
  ValueNotifier<int> selectedPage = ValueNotifier(0);


  InformationRepository informationRepository = InformationRepository();

  void clearAudioData() {
    isManuallyPaused.value = false;
    headerInfoText.value = null;
    infoAudioUrl.value = null;
    isAudioPanelVisible.value = false;
  }

  void setVideoUrl(String? url) {
    infoAudioUrl.value = url;
  }

  void resetProgress() {
    completionCount.value = 0;
    isProgressCompleted.value = false;
    infoAudioUrl.value = null;
  }

  void updateProgress(int totalVideos) {
    if (completionCount.value < totalVideos - 1) {
      completionCount.value += 1;
    } else {
      completionCount.value = 0;
    }
    Injector.instance<AppDB>().videoIndex = completionCount.value;
  }

  Future<void> informationStrategyAPI({
    required BuildContext context,
    required String type,
  }) async {
    emit(const InformationPageState.loading());
    try {
      final response = await informationRepository.informationStrategy(
        context: context,
        type: type,
      );
      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && (response.success ?? false) == true) {
        if (response.strategies != null) {
          Injector.instance<AppDB>().userModel?.user.strategies = response.strategies;

          await authRepository.getUserData(context: context);

          'User data ===> ${Injector.instance<AppDB>().userModel?.user.strategies?.dsAs}'.logD;
        }
      } else {}
      emit(const InformationPageState.initial());
    } catch (e) {
      emit(const InformationPageState.initial());
    }
  }

  @override
  Future<void> close() {
    isManuallyPaused.dispose();
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    isProgressCompleted.dispose();
    return super.close();
  }
}
