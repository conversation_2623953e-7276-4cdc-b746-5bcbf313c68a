import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/find_meeting_module/cubit/find_meeting_cubit.dart';
import 'package:breakingfree_v2/features/home_module/find_meeting_module/keys/find_meeting_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class FindMeetingPage extends StatelessWidget {
  const FindMeetingPage({super.key});

  @override
  Widget build(BuildContext context) {
    '=====> data ${(DynamicAssetLoader.getNestedValue(
      MeetingsLocaleKeys.services,
      context,
    ) as List).map((e) => e['name'])}'
        .logD;
    return BlocProvider(
      create: (context) => FindMeetingCubit()..statsAPI(context, 'meetingFinderViewed'),
      child: BlocBuilder<FindMeetingCubit, FindMeetingState>(
        builder: (ctx, state) {
          final ref = ctx.read<FindMeetingCubit>();
          // if((ref.infoAudioUrl.value??'').isNotEmpty && !ref.isAudioPannelVisible.value) {
          //   appBarVolumeStatus = appBarVolumeStatus == VolumeIconStatus.neutral ? VolumeIconStatus.playing:VolumeIconStatus.neutral;
          // }
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, infoAudioUrl, child) {
              return AppScaffold(
                resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldKey,
                isAudioPanelVisible: ref.isAudioPannelVisible,
                infoAudioUrl: ref.infoAudioUrl,
                drawer: AppDrawer(
                  scaffoldKey: ref.scaffoldKey,
                  infoAudioUrl: ref.infoAudioUrl.value,
                ),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                    }
                  },
                ),
                body: Padding(
                  padding: EdgeInsets.only(right: AppSize.w4),
                  child: CustomRawScrollbar(
                    child: SingleChildScrollView(
                      child: ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                left: AppSize.w24,
                                right: AppSize.w24,
                                bottom: AppSize.h20,
                                top: AppSize.h24,
                              ),
                              child: SizedBox(
                                width: MediaQuery.of(context).size.width,
                                child: Column(
                                  children: [
                                    AppTextWidget(
                                      CoreLocaleKeys.titlesFindMeetings.tr(),
                                      style: context.textTheme.titleLarge?.copyWith(
                                        fontSize: AppSize.sp18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    SpaceV(AppSize.h5),
                                    AppTextWidget(
                                      MeetingsLocaleKeys.title.tr(),
                                      textAlign: TextAlign.center,
                                      style: context.textTheme.titleSmall?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    SpaceV(AppSize.h14),
                                    Divider(
                                      height: 1,
                                      color: context.themeColors.greyColor.withOpacity(.6),
                                    ),
                                    SpaceV(AppSize.h26),
                                    AppTextWidget(
                                      (DynamicAssetLoader.getNestedValue(
                                        MeetingsLocaleKeys.text,
                                        context,
                                      ) as List)
                                          .join('\n\n'),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h40),
                                    AppCachedNetworkImage(imageUrl: MeetingsLocaleKeys.mapSrc.tr()),
                                    SpaceV(AppSize.h40),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics: const BouncingScrollPhysics(),
                                      itemBuilder: (context, index) {
                                        final nameList = DynamicAssetLoader.getNestedValue(
                                          MeetingsLocaleKeys.services,
                                          context,
                                        ) as List;
                                        'nameList ====>$nameList'.logD;

                                        final names = nameList.map((e) {
                                          return e;
                                        }).toList();

                                        final typeList = [
                                          'meetingFinderLink1Clicked',
                                          'meetingFinderLink2Clicked',
                                          'meetingFinderLink3Clicked',
                                          'meetingFinderLink4Clicked',
                                        ];
                                        'names ====>$names'.logD;

                                        return Column(
                                          children: [
                                            MeetingDetailWidget(
                                              imageUrl: nameList[index]['logo']['src'] as String,
                                              text: nameList[index]['name'] as String,
                                              url: nameList[index]['link'] as String,
                                              ref: ref,
                                              type: typeList[index],
                                            ),
                                            SpaceV(AppSize.h26),
                                          ],
                                        );
                                      },
                                      itemCount: (DynamicAssetLoader.getNestedValue(
                                        MeetingsLocaleKeys.services,
                                        context,
                                      ) as List)
                                          .length,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class MeetingDetailWidget extends StatelessWidget {
  const MeetingDetailWidget({
    required this.ref,
    super.key,
    this.imageUrl,
    this.text,
    this.url,
    this.type,
  });
  final String? imageUrl;
  final String? text;
  final String? url;
  final String? type;
  final FindMeetingCubit ref;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppCachedNetworkImage(
              imageUrl: imageUrl ?? 'https://d24v3ngjgcwbka.cloudfront.net/images/meeting-finder.AA.jpg',
              height: AppSize.w40,
              width: AppSize.w40,
            ),
            SpaceH(AppSize.w15),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTextWidget(
                  text?.tr() ?? 'Alcoholics Anonymous',
                  style: context.textTheme.titleSmall,
                ),
                GestureDetector(
                  onTap: () {
                    ref.statsAPI(context, type ?? 'meetingFinderViewed');
                    launchUrl(
                      mode: LaunchMode.externalApplication,
                      Uri.parse(url ?? 'http://www.alcoholics-anonymous.org.uk/aa-meetings/find-a-meeting'),
                    );
                  },
                  child: AppTextWidget(
                    MeetingsLocaleKeys.linkText.tr(),
                    style: context.textTheme.titleSmall?.copyWith(
                      fontSize: AppSize.sp12,
                      color: context.themeColors.greenColor,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
