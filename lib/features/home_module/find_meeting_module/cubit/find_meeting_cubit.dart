import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/find_meeting_module/keys/find_meeting_locale_keys.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'find_meeting_cubit.freezed.dart';
part 'find_meeting_state.dart';

class FindMeetingCubit extends Cubit<FindMeetingState> {
  FindMeetingCubit() : super(const FindMeetingState.initial()) {
    infoAudioUrl.value = MeetingsLocaleKeys.audio.tr();
  }

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  final AuthRepository authRepository = AuthRepository();

  final scaffoldKey = GlobalKey<ScaffoldState>();


  Future<void> statsAPI(BuildContext context,String type) async {
    emit(const FindMeetingState.loading());
    try {
      final response = await authRepository.stats(
        type:type ,
        context: context,
      );
      if (response != null && response.data!['success'] == true) {

        emit(const FindMeetingState.initial());
      } else {
        emit(const FindMeetingState.initial());
      }
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(const FindMeetingState.initial());
    }
  }

    @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
