import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/view_tutorial_module/cubit/viewtutorial_cubit.dart';
import 'package:breakingfree_v2/features/home_module/view_tutorial_module/keys/tutorial_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ViewTutorialPage extends StatelessWidget {
  const ViewTutorialPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ViewtutorialCubit(),
      child: BlocBuilder<ViewtutorialCubit, ViewtutorialState>(
        builder: (ctx, state) {
          final ref = ctx.read<ViewtutorialCubit>();
          return AppScaffold(
            isManuallyPaused: ref.isManuallyPaused,
            resizeToAvoidBottomInset: false,
            scaffoldKey: ref.scaffoldKey,
            isAudioPanelVisible: ref.isAudioPannelVisible,
            infoAudioUrl: ref.infoAudioUrl,
            drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
            appBar: CommonAppBar(
              onPrefixTap: () {
                ref.scaffoldKey.currentState?.openDrawer();
              },
              onSuffixTap: () {
                if(ref.isVideoEnded.value){
                  ref.isVideoEnded.value = true;
                }
                if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                  ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                }
              },
            ),
            body: ColoredBox(
              color: context.themeColors.whiteColor,
              child: Column(
                children: [
                  SingleChildScrollView(
                    child: Padding(
                      padding:
                          EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, bottom: AppSize.h20, top: AppSize.h24),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Column(
                          children: [
                            AppTextWidget(
                              TutorialLocaleKeys.title.tr(),
                              style: context.textTheme.titleSmall?.copyWith(
                                fontSize: AppSize.sp14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SpaceV(AppSize.h14),
                            Divider(
                              height: 1,
                              color: context.themeColors.greyColor.withOpacity(.6),
                            ),
                            SpaceV(AppSize.h16),
                            AppTextWidget(
                               (DynamicAssetLoader.getNestedValue(
                                    TutorialLocaleKeys.text,
                                    context,
                                  ) as List)
                                      .join('\n\n'),
                             
                              style: context.textTheme.titleSmall,
                            ),
                            SpaceV(AppSize.h30),
                            Column(
                              children: [
                                VideoPlayerScreen(
                                  imageList: [TutorialLocaleKeys.videoPosters.tr()],
                                  
                                  // onVideoEnded: (){
                                  //   AppNavigation.previousScreen(context);
                                  //   //ref.infoAudioUrl.value = TutorialLocaleKeys.audio.tr();
                                  //   ref.isVideoEnded.value = true;
                                  //   VolumeIconStatus.playing;
                                  // },
                                  onVideoEnded: () async {
                                    if (ref.isNavigatingFromVideo) return;
                                    ref.isNavigatingFromVideo = true;

                                    await Future.delayed(const Duration(milliseconds: 300));

                                    if (Navigator.of(context).canPop()) {
                                      Navigator.of(context).pop();
                                    }
                                    ref.isManuallyPaused.value = true;
                                    ref.isVideoEnded.value = true;
                                    ref.isNavigatingFromVideo = false; 
                                  },
                                  
                                  onTap: () {
                                    ref.isManuallyPaused.value = true;
                                    //ref.infoAudioUrl.value = null;
                                    //ref.statsAPI(context);
                                  },
                                  isFromLightBuilb1: () {
                                    ref.infoAudioUrl.value = TutorialLocaleKeys.audio.tr();
                                  },
                                  isFromLightBuilb: true,
                                  //  onTap: onPlayTap,
                                  navigationFunction: () {},
                                  videoList: [TutorialLocaleKeys.videoSrc.tr()],
                                ),
                              ],
                            ),
                            SpaceV(AppSize.h16),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
