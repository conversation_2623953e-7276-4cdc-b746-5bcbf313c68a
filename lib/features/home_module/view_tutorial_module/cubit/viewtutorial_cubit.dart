import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/view_tutorial_module/keys/tutorial_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'viewtutorial_cubit.freezed.dart';
part 'viewtutorial_state.dart';

class ViewtutorialCubit extends Cubit<ViewtutorialState> {
  ViewtutorialCubit() : super(const ViewtutorialState.initial()) {
    infoAudioUrl.value = TutorialLocaleKeys.audio.tr();
  }

  bool isNavigatingFromVideo = false;

  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<bool> isVideoEnded = ValueNotifier(false);
   bool isPaused = false;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final AuthRepository authRepository = AuthRepository();

  Future<void> statsAPI(BuildContext context) async {
    emit(const ViewtutorialState.loading());
    try {
      final response = await authRepository.stats(
        type: 'tutorialVideo',
        context: context,
      );
      if (response != null && response.data!['success'] == true) {
        emit(const ViewtutorialState.initial());
      } else {
        emit(const ViewtutorialState.initial());
      }
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      emit(const ViewtutorialState.initial());
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
