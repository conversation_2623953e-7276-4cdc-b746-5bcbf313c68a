import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/notification_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class MyAlertRepository {
  Future<Response<Map<String, dynamic>>?> myAlert({
    required BuildContext context,
    required bool progressChecks,
    required bool situations,
    required bool activities,
    required bool lifestyle,
  }) async {
    try {
      final formData = {
        'alerts': {
          'progressChecks': progressChecks,
          'situations': situations,
          'activities': activities,
          'commitments': lifestyle,
        },
      };
      final response = await APIFunction.postAPICall(
        formData,
        apiName: EndPoints.alerts,
        context: context,
      );
      '///response = ${response}'.logD;
      '///response progress = ${progressChecks}'.logD;
      '///response situations = ${situations}'.logD;
      '///response activities = ${activities}'.logD;
      '///response commitments = ${lifestyle}'.logD;
      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<NotificationModel?> notification({
    required BuildContext context,
   
  }) async {
    try {
      final response = await APIFunction.getAPICall(
        apiName: EndPoints.notificationData,
        context: context,
      );

      '|||||||| res = ${response}'.logD;

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        '|||||||| data = ${data}'.logD;
        '|||||||| response.statusCode = ${response.statusCode}'.logD;
        '|||||||| data?["success"] = ${data?["success"]}'.logD;
        if (response.statusCode == 200 && data?['success'] == true) {
          return NotificationModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
