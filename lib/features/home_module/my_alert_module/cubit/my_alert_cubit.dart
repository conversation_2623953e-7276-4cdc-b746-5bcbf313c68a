import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/common_functions.dart/common_functions.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/repository/my_alert_repository.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/notification_service/notification_helper.dart';
import 'package:breakingfree_v2/res/app_constant.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:permission_handler/permission_handler.dart';

part 'my_alert_cubit.freezed.dart';
part 'my_alert_state.dart';

class MyAlertCubit extends Cubit<MyAlertState> {
  MyAlertCubit() : super(MyAlertState()){
    planningState.value = ButtonState.noEnabled;
    situationState.value = ButtonState.noEnabled;
    lifeGoalState.value = ButtonState.noEnabled;
  }
  late final AppLifecycleListener listener;

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  ValueNotifier<bool> progressCheckAlert = ValueNotifier(false);
  ValueNotifier<bool> situtationAlert = ValueNotifier(false);
  ValueNotifier<bool> planningAlert = ValueNotifier(false);
  ValueNotifier<bool> lifeAlert = ValueNotifier(false);
  ValueNotifier<bool> isShowAlert = ValueNotifier(false);

  ValueNotifier<bool> progressCheckWarning = ValueNotifier(true);
  ValueNotifier<bool> riskySituationWarning = ValueNotifier(true);
  ValueNotifier<bool> planingTimeWarning = ValueNotifier(true);
  ValueNotifier<bool> lifeGoalWarning = ValueNotifier(true);

  AuthRepository authRepository = AuthRepository();
  ValueNotifier<bool> isGrantedLocation = ValueNotifier(false);
  ValueNotifier<bool> isGrantedNotification = ValueNotifier(false);
  ValueNotifier<bool> isGrantedLocationLabel = ValueNotifier(false);
  ValueNotifier<bool> isGrantedNotificationLabel = ValueNotifier(false);

  ValueNotifier<ButtonState> progressCheckState = ValueNotifier(ButtonState.noEnabled);
  ValueNotifier<ButtonState> planningState = ValueNotifier(ButtonState.noEnabled);
  ValueNotifier<ButtonState> situationState = ValueNotifier(ButtonState.noEnabled);
  ValueNotifier<ButtonState> lifeGoalState = ValueNotifier(ButtonState.noEnabled);
  final MyAlertRepository alertRepository = MyAlertRepository();

  final scaffoldKey = GlobalKey<ScaffoldState>();


  void initalData() async{
    isGrantedLocation.value = await Permission.locationAlways.status.isGranted;
    isGrantedNotification.value = await Permission.notification.status.isGranted;
    progressCheckState.value = (Injector.instance<AppDB>().userModel?.user.app?.alerts?.progressChecks ?? false) //&& (isGrantedLocation.value || isGrantedNotification.value)
        ? ButtonState.yesEnabled
        : ButtonState.noEnabled;
    '////// progressCheckState.value ${progressCheckState.value}'.logD;
    situationState.value = (Injector.instance<AppDB>().userModel?.user.app?.alerts?.situations ?? false) //&& (isGrantedLocation.value || isGrantedNotification.value)
        ? ButtonState.yesEnabled
        : ButtonState.noEnabled;
    '////// situationState.value ${situationState.value}'.logD;
    planningState.value = (Injector.instance<AppDB>().userModel?.user.app?.alerts?.activities ?? false) //&& (isGrantedLocation.value || isGrantedNotification.value)
        ? ButtonState.yesEnabled
        : ButtonState.noEnabled;
    '////// planningState.value ${planningState.value}'.logD;
    lifeGoalState.value = (Injector.instance<AppDB>().userModel?.user.app?.alerts?.commitments ?? false) //&& (isGrantedLocation.value || isGrantedNotification.value)
        ? ButtonState.yesEnabled
        : ButtonState.noEnabled;
    '////// lifeGoalState.value ${lifeGoalState.value}'.logD;
    'situationState.value ${situationState.value}'.logD;
    'planningState.value ${planningState.value}'.logD;
    'lifeGoalState.value ${lifeGoalState.value}'.logD;
    'alerts.value ${Injector.instance<AppDB>().userModel?.user.app?.alerts?.toJson()}'.logD;
    'situations.value ${Injector.instance<AppDB>().userModel?.user.app?.alerts?.situations}'.logD;
  }

  Future<void> setProgressCheckAlert({required bool value}) async {
    emit(state.copyWith(isMyProgressCheckAlert: value));
    //Injector.instance<AppDB>().isMySituationAlert = value;

    //  'Injector.instance<AppDB>().isMySituationAlert${Injector.instance<AppDB>().isMySituationAlert}'.logD;
    return;
  }

  Future<void> setMySituationAlert({required bool value}) async {
    emit(state.copyWith(isMySituationAlert: value));
    //Injector.instance<AppDB>().isMySituationAlert = value;

    //  'Injector.instance<AppDB>().isMySituationAlert${Injector.instance<AppDB>().isMySituationAlert}'.logD;
    return;
  }

  Future<void> setPlanningAlert({required bool value}) async {
    emit(state.copyWith(isMyActionAlert: value));
    // Injector.instance<AppDB>().isMyActionAlert = value;
    //  'Injector.instance<AppDB>().isMySituationAlert${Injector.instance<AppDB>().isMySituationAlert}'.logD;
    return;
  }

  Future<void> setMyLifestyleAlert({required bool value}) async {
    emit(state.copyWith(isMyLifestyleAlert: value));
    // Injector.instance<AppDB>().isMyLifestyleAlert = value;
    // 'Injector.instance<AppDB>().isMySituationAlert${Injector.instance<AppDB>().isMyLifestyleAlert}'.logD;
    return;
  }

  int getWeekdayNumber(String day) {
  switch (day.toUpperCase()) {
    case 'MON': return DateTime.monday;
    case 'TUE': return DateTime.tuesday;
    case 'WED': return DateTime.wednesday;
    case 'THUR': return DateTime.thursday;
    case 'FRI': return DateTime.friday;
    case 'SAT': return DateTime.saturday;
    case 'SUN': return DateTime.sunday;
    default: return DateTime.monday; // fallback
  }
}


  Future<void> myAlertCall({
    required String alertType,
    bool isNo = false,
  }) async {
    if (alertType == 'progress') {
      if (isNo) {
        emit(state.copyWith(isProgressCheckAlertNoLoading: true));
      } else {
        emit(state.copyWith(isProgressCheckAlertLoading: true));
      }
    } 
    else if (alertType == 'situation') {
      if (isNo) {
        emit(state.copyWith(isMySituationAlertNoLoading: true));
      } else {
        emit(state.copyWith(isMySituationAlertLoading: true));
      }
    } else if (alertType == 'planning') {
      if (isNo) {
        emit(state.copyWith(isPlanningAlertNoLoading: true));
      } else {
        emit(state.copyWith(isPlanningNoLoading: true));
      }
    } else if (alertType == 'activity') {
      if (isNo) {
        emit(state.copyWith(isMyActionAlertNoLoading: true));
      } else {
        emit(state.copyWith(isMyActionNoLoading: true));
      }
    }
    try {
      final policies = await alertRepository.myAlert(
        context: navigatorKey.currentContext!,
        progressChecks: state.isMyProgressCheckAlert,
        activities: state.isMyActionAlert,
        lifestyle: state.isMyLifestyleAlert,
        situations: state.isMySituationAlert,
      );

      if (policies != null && policies.data != null) {
        await authRepository.getUserData(context: navigatorKey.currentContext);
        // Injector.instance<AppDB>().isMyLifestyleAlert = userModel?.user.app?.alerts?.commitments;
        // Injector.instance<AppDB>().isMySituationAlert = userModel?.user.app?.alerts?.situations;
        // Injector.instance<AppDB>().isMyActionAlert = userModel?.user.app?.alerts?.activities;

        if (alertType == 'progress') {
          if (isNo) {
            emit(state.copyWith(isProgressCheckAlertNoLoading: false));
          } else {
            emit(state.copyWith(isProgressCheckAlertLoading: false));
          }
        }
        else if (alertType == 'situation') {
          if (isNo) {
            emit(state.copyWith(isMySituationAlertNoLoading: false));
          } else {
            emit(state.copyWith(isMySituationAlertLoading: false));
          }
        } else if (alertType == 'planning') {
          if (isNo) {
            emit(state.copyWith(isPlanningAlertNoLoading: false));
          } else {
            emit(state.copyWith(isPlanningNoLoading: false));
          }
        } else if (alertType == 'activity') {
          if (isNo) {
            emit(state.copyWith(isMyActionAlertNoLoading: false));
          } else {
            emit(state.copyWith(isMyActionNoLoading: false));
          }
        }
      } else {
        if (alertType == 'progress') {
          if (isNo) {
            emit(state.copyWith(isProgressCheckAlertNoLoading: false));
          } else {
            emit(state.copyWith(isProgressCheckAlertLoading: false));
          }
        } 
        else if (alertType == 'situation') {
          if (isNo) {
            emit(state.copyWith(isMySituationAlertNoLoading: false));
          } else {
            emit(state.copyWith(isMySituationAlertLoading: false));
          }
        } else if (alertType == 'planning') {
          if (isNo) {
            emit(state.copyWith(isPlanningAlertNoLoading: false));
          } else {
            emit(state.copyWith(isPlanningNoLoading: false));
          }
        } else if (alertType == 'activity') {
          if (isNo) {
            emit(state.copyWith(isMyActionAlertNoLoading: false));
          } else {
            emit(state.copyWith(isMyActionNoLoading: false));
          }
        }
      }
    } catch (e) {
      if (alertType == 'progress') {
        if (isNo) {
          emit(state.copyWith(isProgressCheckAlertNoLoading: false));
        } else {
          emit(state.copyWith(isProgressCheckAlertLoading: false));
        }
      }
      if (alertType == 'situation') {
        if (isNo) {
          emit(state.copyWith(isMyActionAlertNoLoading: false));
        } else {
          emit(state.copyWith(isMyActionNoLoading: false));
        }
      } else if (alertType == 'planning') {
        if (isNo) {
          emit(state.copyWith(isPlanningAlertNoLoading: false));
        } else {
          emit(state.copyWith(isPlanningNoLoading: false));
        }
      } else if (alertType == 'activity') {
        if (isNo) {
          emit(state.copyWith(isMyActionAlertNoLoading: false));
        } else {
          emit(state.copyWith(isMyActionNoLoading: false));
        }
      }
    }
  }

  Future<void> setPlanningScheduleNotification() async {
    var notificationID = 1;
    final response = await alertRepository.notification(
      context: navigatorKey.currentContext!,
    );
    final activities = response?.activities;
    '||||||||| activities scheduling notification for activity ${activities?.length}: '.logD;

    '||||||||| activities = ${activities}'.logD;

    // Ensure activities are not null or empty
    if (activities == null || activities.isEmpty) return;

    // Use a for loop to handle async operations correctly
    for (final element in activities) {
      '||||||||| date = ${DateTime.parse(element.date.toString() ?? "2021-09-28T10:30:00+05:30")}'.logD;
      '||||||||| time = ${element.time}'.logD;
      '||||||||| weekday = ${element.day}'.logD;
      final notificationTime = element.date ?? DateTime.parse('2021-09-28T10:30:00+05:30');
      //final fallback = DateTime.parse('2021-09-28T10:30:00+05:30');

final date = element.date;
final timeString = element.time; // example: "12:00"

//DateTime notificationTime;

// if (date != null && timeString != null) {
//   //final dateOnly = element.date?.toLocal().toIso8601String().split('T')[0]; // "2025-07-31"

//   //notificationTime = DateTime.parse("$dateOnly $timeString");
// } else {
//   //notificationTime = fallback;
// }
      final date2 = notificationTime.toString().replaceAll('Z', '').trim();
      'data ===> \n$date'.logD;
      
      await CommonFunction.schedulingNotificationWithDays(
        title: "It's time to do my activities..",
        body: element.name ?? "",
        payLoad: AppConstants.planningNotification,
        notificationID: notificationID,
        hour: int.parse(element.time!.split(":")[0]),
        weekDay: getWeekdayNumber(element.day!),
        day: element.day!,
        minute: int.parse(element.time!.split(":")[1]),
      );


      // Schedule the notification
      // await CommonFunction.setScheduleOneTimeNotification(
      //   date: dateOriginal,
      //   body: element.name ?? '',
      //   title: "It's time to do my activities..",
      //   id: notificationID,
      //   payload: AppConstants.activitityNotification,
      //   isDaily: true,
      // );

      notificationID++; // Increment the notification ID for the next notification
    }
  }

  Future<void> scheduleActivityNotifications() async {
    var notificationID = 1;
    final response = await alertRepository.notification(
      context: navigatorKey.currentContext!,
    );
    if (response != null) {}

    final activities = response?.commitments;
    'activities scheduling notification for activity ${activities?.length}: '.logD;

    // Ensure activities are not null or empty
    if (activities == null || activities.isEmpty) return;
    response?.commitments?.forEach(
      (element) async {
        final notificationTime = element.date ?? DateTime.parse('2021-09-28T10:30:00+05:30');

        'Original notification time: $notificationTime'.logD;

        // Format the DateTime object

        await CommonFunction.setScheduleOneTimeNotification(
          date: notificationTime.toString(),
          title: "It's time to take my next step..",
          body: element.name ?? '',
          id: notificationID,
          payload: AppConstants.planningNotification,
        );

        notificationID++;
      },
    );
  }

  Future<void> cancelNotification({required String notificationCategory}) async {
    await LocalNotificationHelper.notification.pendingNotificationRequests().then((value) {
      for (final element in value) {
        if (element.payload == notificationCategory) {
          LocalNotificationHelper.notification.cancel(element.id);
        }
      }
    });
  }

  @override
  Future<void> close() {
    // TODO: implement close
    planningState.dispose();
    situationState.dispose();
    lifeGoalState.dispose();
    return super.close();
  }
}
