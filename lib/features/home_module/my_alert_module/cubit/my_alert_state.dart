part of 'my_alert_cubit.dart';

@freezed
class MyAlertState with _$MyAlertState {
  factory MyAlertState({
    @Default(false) bool isMyProgressCheckAlert,
    @Default(false) bool isMySituationAlert,
    @Default(false) bool isMyActionAlert,
    @Default(false) bool isMyLifestyleAlert,
    @Default(false) bool isProgressCheckAlertLoading,
    @Default(false) bool isProgressCheckAlertNoLoading,
    @Default(false) bool isMySituationAlertLoading,
    @Default(false) bool isMySituationAlertNoLoading,
    @Default(false) bool isPlanningAlertNoLoading,
    @Default(false) bool isPlanningNoLoading,
    @Default(false) bool isMyActionAlertNoLoading,
    @Default(false) bool isMyActionNoLoading,
  }) = _MyAlertState;
}
