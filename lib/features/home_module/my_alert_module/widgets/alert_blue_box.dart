import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class AlertBox extends StatelessWidget {
  const AlertBox({
    super.key,
    this.visible = true,
    this.onTap,
  });
  final bool visible;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: Container(
        decoration: BoxDecoration(
          color: context.themeColors.lightBlueColor,
          borderRadius: BorderRadius.circular(AppSize.r4),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w6, vertical: AppSize.h10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: const Color.fromRGBO(27, 137, 208, 1),
                    size: AppSize.sp24,
                  ),
                  SpaceH(AppSize.w6),
                  Expanded(
                    child: Column(
                      children: [
                        AppTextWidget(
                          'You have enabled risky place alerts, but Breaking Free does not have full permissions. For more consistent notifications, select “Always allow” for location alerts in your device settings.',
                          style: context.textTheme.labelSmall?.copyWith(
                            color: context.themeColors.blueColor,
                            // fontSize: AppSize.sp10,
                          ),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: onTap,
                    child: Icon(
                      Icons.close,
                      color: context.themeColors.greyColor,
                      size: AppSize.sp18,
                    ),
                  ),
                ],
              ),
              SpaceV(AppSize.h10),
            ],
          ),
        ),
      ),
    );
  }
}

