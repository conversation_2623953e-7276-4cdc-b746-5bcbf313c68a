
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

class AlertBox extends StatelessWidget {
   AlertBox({
    super.key,
    this.visible = true,
    required this.isGrantedLocationLabel,
    this.onTap,
    required this.text,
  });
  final bool visible;
  final void Function()? onTap;
  bool isGrantedLocationLabel = false; 
  final String text;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: visible,
      child: InkWell(
        onTap: () async{
          '>?>?>? openAppSettings STARTED'.logD;
          await openAppSettings();
          '>?>?>? openAppSettings ENDED'.logD;
          
        },
        child: Container(
          decoration: BoxDecoration(
            color: context.themeColors.lightBlueColor,
            borderRadius: BorderRadius.circular(AppSize.r8),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.info_outline_rounded,
                      color: const Color.fromRGBO(27, 137, 208, 1),
                      size: AppSize.sp24,
                    ),
                    SpaceH(AppSize.w6),
                    Expanded(
                      child: Column(
                        children: [
                          AppTextWidget(
                            text,
                            style: context.textTheme.labelSmall?.copyWith(
                              color: context.themeColors.blueColor,
                              // fontSize: AppSize.sp10,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

