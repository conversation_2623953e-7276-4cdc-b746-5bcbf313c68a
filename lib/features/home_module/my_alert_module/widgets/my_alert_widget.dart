// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class MyAlertWidget extends StatelessWidget {
  const MyAlertWidget({
    required this.title,
    required this.iconPath,
    required this.ref,
    super.key,
    this.infoTap,
    this.onYesTap,
    this.onNoTap,
    this.infoWidget,
    this.currentState,
    this.isNoLoading = false,
    this.isYesLoading = false, this.button,
  });
  final String title;
  final String iconPath;
  final void Function()? infoTap;
  final void Function()? onYesTap;
  final void Function()? onNoTap;
  final MyAlertCubit ref;
  final CustomInfoWidget? infoWidget;
  final ValueNotifier<ButtonState>? currentState;
  final bool isNoLoading;
  final bool isYesLoading;
  final Widget? button;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: AppTextWidget(
                title,
                style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500, fontSize: AppSize.sp14),
              ),
            ),
            CustomIconButton(
              onTap: infoTap,
              assetIcon: Assets.icons.infoIcon,
              size: AppSize.sp28,
            ),
          ],
        ),
        if (infoWidget != null && (infoWidget?.visible ?? false)) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: AppSize.h8),
            child: infoWidget,
          ),
        ],
        SpaceV(AppSize.h14),
        Row(
          children: [
            CustomIconButton(
              iconPath: iconPath,
              size: AppSize.h44,
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
             button??     CustomAssessmentButton(
                    buttonFirstText: DataJsonKeys.buttonNo.tr(),
                    buttonSecondText: DataJsonKeys.buttonYes.tr(),
                    currentState: currentState,
                    onNoTap: onNoTap,
                    onYesTap: onYesTap,
                    isNoLoading: isNoLoading,
                    isYesLoading: isYesLoading,
                    loaderPadding: 0,
                  ),
                  // CustomRoundedButton(
                  //   width: AppSize.w76,
                  //   title: CoreLocaleKeys.buttonsYes.tr(),
                  //   onTap: onYesTap,
                  // ),
                  // SpaceH(AppSize.w10),
                  // CustomRoundedButton(
                  //   width: AppSize.w76,
                  //   title: CoreLocaleKeys.buttonsNo.tr(),
                  //   onTap: onNoTap,
                  // ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
