class AppLocaleKey {
  static const progressCheckTitle = 'app.notifications.progressCheck.title';
  static const progressCheckMessage = 'app.notifications.progressCheck.message';
  static const progressCheckButtonStart = 'app.notifications.progressCheck.buttons.start';

  static const assessmentNotificationText1 = 'app.allowNotifications.text1';
  static const assessmentNotificationText2 = 'app.allowNotifications.text2';
  static const assessmentNotificationText3 = 'app.allowNotifications.text3';
  static const assessmentNotificationAllow = 'app.allowNotifications.buttons.allow';
  static const assessmentNotificationDontAllow = 'app.allowNotifications.buttons.dontAllow';
  static const assessmentNotificationProgressCheck = 'app.allowNotifications.list.progressChecks';
  static const assessmentNotificationSituations = 'app.allowNotifications.list.situations';
  static const assessmentNotificationActivities = 'app.allowNotifications.list.activities';
  static const assessmentNotificationLifestyle = 'app.allowNotifications.list.lifestyle';


  static const progressCheckInfoText = 'app.alerts.progressChecks.info.text';
  static const progressCheckAudio = 'app.alerts.progressChecks.info.audio';
  static const progressCheckNoFullPermission = 'app.alerts.progressChecks.noFullPermission';

  static const situationsTitle = 'app.alerts.situations.title';
  static const situationsInfoText = 'app.alerts.situations.info.text';
  static const situationsInfoAudio = 'app.alerts.situations.info.audio';
  static const situationsNoFullPermission = 'app.alerts.situations.noFullPermission';


  static const activitiesTitle = 'app.alerts.activities.title';
  static const activitiesInfoText = 'app.alerts.activities.info.text';
  static const activitiesInfoAudio = 'app.alerts.activities.info.audio';
  static const activitiesNoFullPermission = 'app.alerts.activities.noFullPermission';

  static const lifestyleTitle = 'app.alerts.lifestyle.title';
  static const lifestyleInfoText = 'app.alerts.lifestyle.info.text';
  static const lifestyleInfoAudio = 'app.alerts.lifestyle.info.audio';
  static const lifestyleNoFullPermission = 'app.alerts.lifestyle.noFullPermission';

  static const notificationsAlertTitle = 'app.alerts.progressChecks.title';
  static const notificationsAlertText = 'app.alerts.progressChecks.text';

  static const fontSizeLabel = 'app.accessibility.fontSize';
  static const fontBoldLabel = 'app.accessibility.fontBold';
  static const contrastLabel = 'app.accessibility.contrast';

    static const appLocaleSetupCountry = 'app.appLocaleSetup.country';
  static const appLocaleSetupChooseCountry = 'app.appLocaleSetup.chooseCountry';
  static const appLocaleSetupLanguage = 'app.appLocaleSetup.language';
  static const appLocaleSetupChooseLanguage = 'app.appLocaleSetup.chooseLanguage';
  static const appLocaleSetupCountryWarning = 'app.appLocaleSetup.countryWarning';

  static const appLocaleSetupSubmitButton = 'app.appLocaleSetup.submitButton';
  static const appLocaleSetupCountryWarningButton = 'app.appLocaleSetup.countryWarningButton';

  static const noFullPermissionText = 'app.alerts.situations.noFullPermission';
  static const contrast = 'app.accessibility.contrast';

  static const appEnableNotificationTitle = 'app.allowNotifications.enableNotifications.title';
  static const appEnableNotificationText = 'app.allowNotifications.enableNotifications.text';

  static const appEnableLocationTitle = 'app.allowNotifications.enableLocation.title';
  static const appEnableLocationText = 'app.allowNotifications.enableLocation.text';

  static const appSettingButton = 'app.allowNotifications.buttons.settings';
  static const appCancelButton = 'app.allowNotifications.buttons.cancel';

}
