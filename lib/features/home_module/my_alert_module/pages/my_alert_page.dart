import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/widgets/location_alert_box_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/widgets/my_alert_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/location_service/cubit/location_service_cubit.dart';
import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/app_constant.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

class MyAlertPage extends StatefulWidget {
  const MyAlertPage({super.key});

  @override
  State<MyAlertPage> createState() => _MyAlertPageState();
}

class _MyAlertPageState extends State<MyAlertPage> with WidgetsBindingObserver {
  // late final AppLifecycleListener listener;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    'onInactive ==='.logV;
    context.read<MyAlertCubit>().infoAudioUrl.value = null;
    context.read<MyAlertCubit>().progressCheckAlert.value = false;
    context.read<MyAlertCubit>().situtationAlert.value = false;
    context.read<MyAlertCubit>().planningAlert.value = false;
    context.read<MyAlertCubit>().lifeAlert.value = false;
    
    checkLocationPermission();

    // if(Injector.instance<AppDB>().newUser) {
    //   context.read<MyAlertCubit>().situationState.value = ButtonState.noEnabled;
    //   context.read<MyAlertCubit>().planningState.value = ButtonState.noEnabled;
    //   context.read<MyAlertCubit>().lifeGoalState.value = ButtonState.noEnabled;
    // }

    // listener = AppLifecycleListener(
    //   onInactive: () {
    //     'onInactive'.logV;
    //   },
    //   onPause: () {
    //     'onPause'.logV;
    //   },
    //   onRestart: () {
    //     'onRestart'.logV;
    //   },
    //   onResume: () async {
    //     await checkLocationPermission();
    //   },
    // );
  }
  @override
void didChangeAppLifecycleState(AppLifecycleState state) async {
  '>?>?>? resumed'.logD;
  if (state == AppLifecycleState.resumed) {
    context.read<MyAlertCubit>().isGrantedLocation.value = await Permission.locationAlways.status.isGranted;
          context.read<MyAlertCubit>().isGrantedLocationLabel.value = !context.read<MyAlertCubit>().isGrantedLocationLabel.value;
    context.read<MyAlertCubit>().isGrantedNotification.value = await Permission.notification.status.isGranted;
          context.read<MyAlertCubit>().isGrantedNotificationLabel.value = !context.read<MyAlertCubit>().isGrantedNotificationLabel.value;
  }
}
  @override
  void dispose() {
    // listener.dispose();
    super.dispose();
  }

  Future<void> checkLocationPermission() async {
    context.read<MyAlertCubit>().isGrantedLocation.value = await Permission.locationAlways.status.isGranted;
    context.read<MyAlertCubit>().isGrantedNotification.value = await  Permission.notification.status.isGranted;
    '>?>?>? Location Permission status: ${context.read<MyAlertCubit>().isGrantedLocation.value}'.logD;
    '>?>?>? Notification Permission status: ${context.read<MyAlertCubit>().isGrantedNotification}'.logD;
    //'>?>?>? Location Permission status granted? : ${status.isGranted}'.logD;
    // final granted = await LocationService.checkLocationForPermissionWithoutPopUp(
    //   navigatorKey.currentContext!,
    //   riskyPlacesEnabled: true,
    //   isButtonClick: false,
    // );

    context.read<MyAlertCubit>().isShowAlert.value = !context.read<MyAlertCubit>().isGrantedLocation.value; //!granted;

  }


  @override
  Widget build(BuildContext context) {
    '///response = ${Injector.instance<AppDB>().userModel?.user.app?.alerts?.toJson()}'.logV;
    return BlocBuilder<MyAlertCubit, MyAlertState>(
      builder: (ctx, state) {
        final ref = ctx.read<MyAlertCubit>();
      
        return ValueListenableBuilder(
          valueListenable: ref.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              resizeToAvoidBottomInset: false,
              scaffoldKey: ref.scaffoldKey,
              isAudioPanelVisible: ref.isAudioPannelVisible,
              infoAudioUrl: ref.infoAudioUrl,
              drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
              appBar: CommonAppBar(
                onPrefixTap: () {
                  ref.scaffoldKey.currentState?.openDrawer();
                },
                onSuffixTap: () {
                  if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                    ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                  }
                },
              ),
              body: ValueListenableBuilder(
                valueListenable: ref.isGrantedLocationLabel,
                builder: (context, value, child)  {
                  '///response = current progress = ${ref.progressCheckState.value}'.logV;
                  '///response = current situation = ${ref.situationState.value}'.logV;
                  '///response = current activity = ${ref.planningState.value}'.logV;
                  '///response = current commitment = ${ref.lifeGoalState.value}'.logV;
                  return ValueListenableBuilder(
                    valueListenable: ref.isGrantedNotificationLabel,
                    builder: (context, value, child) {
                      return ColoredBox(
                        color: context.themeColors.whiteColor,
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: CustomRawScrollbar(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  SingleChildScrollView(
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w26,
                                        right: AppSize.w26,
                                        bottom: AppSize.h20,
                                        top: AppSize.h24,
                                      ),
                                      child: SizedBox(
                                        // width: MediaQuery.of(context).size.width,
                                        // height: MediaQuery.of(context).size.height,
                                        child: Column(
                                          children: [
                                            AppTextWidget(
                                              CoreLocaleKeys.titlesAlerts.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                fontSize: AppSize.sp16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SpaceV(AppSize.h14),
                                            Divider(
                                              height: 1,
                                              color: context.themeColors.greyColor.withOpacity(.6),
                                            ),
                                            SpaceV(AppSize.h40),
                                            ValueListenableBuilder(
                                              valueListenable: ref.progressCheckAlert,
                                              builder: (context, value, child) {
                                                return MyAlertWidget(
                                                  isNoLoading: state.isProgressCheckAlertNoLoading,
                                                  isYesLoading: state.isProgressCheckAlertLoading,
                                                  infoTap: () {
                                                    ref.progressCheckAlert.value = !ref.progressCheckAlert.value;
                                                    if (ref.progressCheckAlert.value) {
                                                      ref.infoAudioUrl.value =
                                                          AppLocaleKey.progressCheckAudio.tr(); // Assign specific URL
                                                      ref.situtationAlert.value = false;
                                                      ref.planningAlert.value = false;
                                                      ref.lifeAlert.value = false;
                                                    } else {
                                                      ref.infoAudioUrl.value = ''; // Assign specific URL
                                                    }
                                                    log('ProgressCheck Alert: ${ref.progressCheckAlert.value}');
                                                  },
                                                  ref: ref,
                                                  iconPath: Assets.icons.dashboard.updateReport,
                                                  title: AppLocaleKey.notificationsAlertTitle.tr(),
                                                  currentState: ref.progressCheckState,
                                                  onNoTap: () async {
                                                    '///no'.logD;
                                                     await checkLocationPermission();
                                                    ref.progressCheckState.value = ButtonState.yesEnabled;
                                                    await ref.setProgressCheckAlert(value: true);
                                                    await ref.myAlertCall(isNo: true, alertType: 'progress');
                      
                                                    final granted =
                                                        await LocationService.checkNotificationForPermissionWithoutPopUp(
                                                      context,isButtonClick: true
                                                    );
                      
                                                    // if (!granted) {
                                                    //   ref.isShowAlert.value = true;
                                                    // } else {
                                                    //   ref.isShowAlert.value = false;
                                                    // }
                                                  },
                                                  onYesTap: () async {
                                                    '///yes'.logD;
                                                     checkLocationPermission();
                                                    ref.progressCheckState.value = ButtonState.noEnabled;
                                                    await ref.setProgressCheckAlert(value: false);
                                                    await ref.myAlertCall(alertType: 'progress');
                                                   
                                                    // await Location().enableBackgroundMode(enable: false);
                                                    await BlocProvider.of<LocationServiceCubit>(context).positions?.cancel();
                                                    await LocationService.cancelNotification(
                                                      notificationCategory: AppConstants.progressCheckNotification,
                                                    );
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w12,
                                                      right: AppSize.w12,
                                                      top: AppSize.h10,
                                                    ),
                                                    visible: value,
                                                    onCloseTap: () {
                                                      ref.infoAudioUrl.value = null;
                                                       ref.progressCheckAlert.value = false;
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      AppLocaleKey.progressCheckInfoText,
                                                      context,
                                                    ) as List<dynamic>? ?? [])
                                                        .join('\n\n'),
                                                  ),
                                                );
                                              },
                                            ),
                                            ValueListenableBuilder(
                                              valueListenable: ref.progressCheckWarning, 
                                              builder: (context, value, child) {
                                                return Column(           
                                                  children: [
                                                    ref.progressCheckState.value == ButtonState.yesEnabled &&  ref.isGrantedNotification.value == false ? SpaceV(AppSize.h20) : SizedBox(),
                                                    AlertBox(
                                                      text: AppLocaleKey.progressCheckNoFullPermission.tr(),
                                                      isGrantedLocationLabel: ref.isGrantedLocationLabel.value,
                                                      visible: ref.progressCheckState.value == ButtonState.yesEnabled && ref.isGrantedNotification.value == false,
                                                      onTap: () {
                                                        ref.progressCheckWarning.value = !ref.progressCheckWarning.value;
                                                      },
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                            SpaceV(AppSize.h30),
                                            ValueListenableBuilder(
                                              valueListenable: ref.situtationAlert,
                                              builder: (context, value, child) {
                                                return MyAlertWidget(
                                                  isNoLoading: state.isMySituationAlertNoLoading,
                                                  isYesLoading: state.isMySituationAlertLoading,
                                                  infoTap: () {
                                                    ref.situtationAlert.value = !ref.situtationAlert.value;
                                                    if (ref.situtationAlert.value) {
                                                      ref.infoAudioUrl.value =
                                                          AppLocaleKey.situationsInfoAudio.tr(); // Assign specific URL
                                                      ref.progressCheckAlert.value = false;
                                                      ref.planningAlert.value = false;
                                                      ref.lifeAlert.value = false;
                                                    } else {
                                                      ref.infoAudioUrl.value = ''; // Assign specific URL
                                                    }
                                                    log('Situtation Alert: ${ref.situtationAlert.value}');
                                                  },
                                                  ref: ref,
                                                  iconPath: Assets.icons.actionIcons.mySituation,
                                                  title: AppLocaleKey.situationsTitle.tr(),
                                                  currentState: ref.situationState,
                                                  onNoTap: () async {
                                                    await checkLocationPermission();
                                                    ref.situationState.value = ButtonState.yesEnabled;
                                                    await ref.setMySituationAlert(value: true);
                                                    await ref.myAlertCall(isNo: true, alertType: 'situation');
                      
                                                    final granted =
                                                        await LocationService.checkLocationForPermissionWithoutPopUp(
                                                      context,
                                                      riskyPlacesEnabled: true,
                                                      isButtonClick: true,
                                                    );
                      
                                                    if (!granted) {
                                                      ref.isShowAlert.value = true;
                                                    } else {
                                                      ref.isShowAlert.value = false;
                                                    }
                                                  },
                                                  onYesTap: () async {
                                                    checkLocationPermission();
                                                    ref.situationState.value = ButtonState.noEnabled;
                                                    await ref.setMySituationAlert(value: false);
                                                    await ref.myAlertCall(alertType: 'situation');
                                                    // await Location().enableBackgroundMode(enable: false);
                                                    await BlocProvider.of<LocationServiceCubit>(context).positions?.cancel();
                                                    await LocationService.cancelNotification(
                                                      notificationCategory: AppConstants.riskyPlaceNotification,
                                                    );
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w12,
                                                      right: AppSize.w12,
                                                      top: AppSize.h10,
                                                    ),
                                                    visible: value,
                                                    onCloseTap: () {
                                                      ref.infoAudioUrl.value = null;
                                                      ref.situtationAlert.value = false;
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      AppLocaleKey.situationsInfoText,
                                                      context,
                                                    ) as List<dynamic>? ?? [])
                                                        .join('\n\n'),
                                                  ),
                                                );
                                              },
                                            ),
                                            ValueListenableBuilder(
                                              valueListenable: ref.riskySituationWarning, 
                                              builder: (context, value, child) {
                                                '?????????????? val = ${ref.situationState.value == ButtonState.yesEnabled }'.logV;
                                                '?????????????? val 2 = ${ref.isGrantedLocation.value == false}'.logV;
                                                '?????????????? val 3 = ${ref.isGrantedNotification.value == false}'.logV;
                                                return Column(           
                                                  children: [
                                                    ref.situationState.value == ButtonState.yesEnabled && (ref.isGrantedLocation.value == false || ref.isGrantedNotification.value == false ) ? SpaceV(AppSize.h20) : SizedBox(),
                                                    AlertBox(
                                                      text: AppLocaleKey.situationsNoFullPermission.tr(),
                                                      isGrantedLocationLabel: ref.isGrantedLocationLabel.value,
                                                      visible: ref.situationState.value == ButtonState.yesEnabled && (ref.isGrantedLocation.value == false || ref.isGrantedNotification.value == false),
                                                      onTap: () {
                                                        ref.riskySituationWarning.value = !ref.riskySituationWarning.value;
                                                      },
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                            SpaceV(AppSize.h30),
                                            ValueListenableBuilder(
                                              valueListenable: ref.planningAlert,
                                              builder: (context, value, child) {
                                                return MyAlertWidget(
                                                  isNoLoading: state.isPlanningAlertNoLoading,
                                                  isYesLoading: state.isPlanningNoLoading,
                                                  infoTap: () {
                                                    ref.planningAlert.value = !ref.planningAlert.value;
                                                    if (ref.planningAlert.value) {
                                                      ref.infoAudioUrl.value = AppLocaleKey.activitiesInfoAudio.tr();
                                                      // Close other alerts
                                                      ref.progressCheckAlert.value = false;
                                                      ref.situtationAlert.value = false;
                                                      ref.lifeAlert.value = false;
                                                    }
                                                    log('Planning Alert: ${ref.planningAlert.value}');
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w12,
                                                      right: AppSize.w12,
                                                      top: AppSize.h10,
                                                    ),
                                                    visible: value,
                                                    onCloseTap: () {
                                                      ref.infoAudioUrl.value = null;
                                                      ref.planningAlert.value = false;
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      AppLocaleKey.activitiesInfoText,
                                                      context,
                                                    ) as List<dynamic>? ?? [])
                                                        .join('\n\n'),
                                                  ),
                                                  ref: ref,
                                                  currentState: ref.planningState,
                                                  onNoTap: () async {
                                                    ref.planningState.value = ButtonState.yesEnabled;
                                                    await ref.setPlanningAlert(value: true);
                                                    await ref.myAlertCall(isNo: true, alertType: 'planning');
                                                    await ref.setPlanningScheduleNotification();
                                                  },
                                                  onYesTap: () {
                                                    ref.planningState.value = ButtonState.noEnabled;
                                                    ref
                                                      ..setPlanningAlert(value: false)
                                                      ..myAlertCall(alertType: 'planning')
                                                      ..cancelNotification(
                                                        notificationCategory: AppConstants.activitityNotification,
                                                      );
                                                  },
                                                  iconPath: Assets.icons.actionIcons.planning,
                                                  title: AppLocaleKey.activitiesTitle.tr(),
                                                );
                                              },
                                            ),
                                            ValueListenableBuilder(
                                              valueListenable: ref.planingTimeWarning, 
                                              builder: (context, value, child) {
                                                return Column(
                                                  children: [
                                                    ref.planningState.value == ButtonState.yesEnabled && ref.isGrantedNotification.value == false ? SpaceV(AppSize.h20) : SizedBox(),
                                                    AlertBox(
                                                      text: AppLocaleKey.activitiesNoFullPermission.tr(),
                                                      isGrantedLocationLabel: ref.isGrantedLocationLabel.value,
                                                      visible: ref.planningState.value == ButtonState.yesEnabled && ref.isGrantedNotification.value == false, //ref.isGrantedLocation.value == false,
                                                      onTap: () {
                                                        ref.planingTimeWarning.value = !ref.planingTimeWarning.value;
                                                      },
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                            SpaceV(AppSize.h30),
                                            ValueListenableBuilder(
                                              valueListenable: ref.lifeAlert,
                                              builder: (context, value, child) {
                                                return MyAlertWidget(
                                                  isNoLoading: state.isMyActionAlertNoLoading,
                                                  isYesLoading: state.isMyActionNoLoading,
                                                  ref: ref,
                                                  iconPath: Assets.icons.actionIcons.lifestyle,
                                                  title: AppLocaleKey.lifestyleTitle.tr(),
                                                  currentState: ref.lifeGoalState,
                                                  onNoTap: () async {
                                                    ref.lifeGoalState.value = ButtonState.yesEnabled;
                                                    await ref.setMyLifestyleAlert(value: true);
                                                    await ref.myAlertCall(isNo: true, alertType: 'activity');
                                                    //  await LocalNotificationHelper.localNotificationHelper.zonedScheduleNotification();
                                                    await ref.scheduleActivityNotifications();
                                                  },
                                                  onYesTap: () {
                                                    ref.lifeGoalState.value = ButtonState.noEnabled;
                                                    ref
                                                      ..setMyLifestyleAlert(value: false)
                                                      ..myAlertCall(alertType: 'activity')
                                                      ..cancelNotification(
                                                        notificationCategory: AppConstants.planningNotification,
                                                      );
                                                  },
                                                  infoTap: () {
                                                    ref.lifeAlert.value = !ref.lifeAlert.value;
                                                    if (ref.lifeAlert.value) {
                                                      ref.infoAudioUrl.value = AppLocaleKey.lifestyleInfoAudio.tr();
                                                      ref.progressCheckAlert.value = false;
                                                      ref.situtationAlert.value = false;
                                                      ref.planningAlert.value = false;
                                                    }
                                                    log('Life Alert: ${ref.lifeAlert.value}');
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w12,
                                                      right: AppSize.w12,
                                                      top: AppSize.h10,
                                                    ),
                                                    visible: value,
                                                    onCloseTap: () {
                                                      ref.infoAudioUrl.value = null;
                                                      ref.lifeAlert.value = false;
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      AppLocaleKey.lifestyleInfoText,
                                                      context,
                                                    ) as List<dynamic>? ?? [])
                                                        .join('\n\n'),
                                                  ),
                                                );
                                              },
                                            ),
                                            ValueListenableBuilder(
                                              valueListenable: ref.lifeGoalWarning, 
                                              builder: (context, value, child) {
                                                return Column(
                                                  children: [
                                                    ref.lifeGoalState.value == ButtonState.yesEnabled && ref.isGrantedNotification.value == false? SpaceV(AppSize.h20) : SizedBox(),
                                                    AlertBox(
                                                      text: AppLocaleKey.lifestyleNoFullPermission.tr(),
                                                      isGrantedLocationLabel: ref.isGrantedLocationLabel.value,
                                                      visible: ref.lifeGoalState.value == ButtonState.yesEnabled && ref.isGrantedNotification.value == false, //ref.isGrantedLocation.value == false,
                                                      onTap: () {
                                                        ref.lifeGoalWarning.value = !ref.lifeGoalWarning.value;
                                                      },
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                            //SpaceV(AppSize.h40),
                                            // ValueListenableBuilder(
                                            //   valueListenable: ref.isShowAlert,
                                            //   builder: (context, isShowAlert, child) {
                                            //     return AlertBox(
                                            //       visible: isShowAlert,
                                            //       onTap: () {},
                                            //     );
                                            //   },
                                            // ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
