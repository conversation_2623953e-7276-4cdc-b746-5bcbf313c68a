// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CustomDialog extends StatelessWidget {
  const CustomDialog({
    required this.child,
    super.key,
  });
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccessbilityCubit, AccessbilityState>(
      builder: (ctx, state) {
        return Container(
          // padding: state.accessibilityContrastValue.roundToDouble() == 1
          //     ? null
          //     : const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
          margin: MediaQuery.of(context).size.width > 700
              ? const EdgeInsets.symmetric(horizontal: 100)
              : const EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
            color: state.accessibilityContrastValue.roundToDouble() == 2 ? context.themeColors.whiteColor : null,
            border: state.accessibilityContrastValue.roundToDouble() == 2
                ? Border.all()
                : state.accessibilityContrastValue.roundToDouble() == 1
                    ? Border.all(color: Colors.black38)
                    : null,
            borderRadius: BorderRadius.circular(8),
            boxShadow: state.accessibilityContrastValue.roundToDouble() == 2
                ? null
                : state.accessibilityContrastValue.roundToDouble() == 1
                    ? [
                        BoxShadow(
                          color: context.themeColors.whiteColor,
                          offset: const Offset(0, 1), //(x,y)
                          blurRadius: 5,
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: context.themeColors.whiteColor,
                          offset: const Offset(0, 1), //(x,y)
                          blurRadius: 5,
                        ),
                      ],
          ),
          child: Container(
            // margin: MediaQuery.of(context).size.width > 700
            //     ? const EdgeInsets.symmetric(horizontal: 100)
            //     : const EdgeInsets.symmetric(horizontal: 15),
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 5),
            decoration: BoxDecoration(color: context.themeColors.whiteColor, borderRadius: BorderRadius.circular(8)),
            child: child,
          ),
        );
      },
    );
  }
}
