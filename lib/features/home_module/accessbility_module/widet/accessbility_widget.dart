// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/model/debouncer.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AccessbilityWidget extends StatelessWidget {
  AccessbilityWidget({
    super.key,
    this.title,
    this.isContrast = false,
  });
  final String? title;
  final bool isContrast;
  final _debouncer = Debouncer(milliseconds: 1000);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
          child: AppTextWidget(
            title ?? 'Font size',
            //style:context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500, fontSize: AppSize.sp14),
            style: context.textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: AppSize.sp13,
                ),

    ),
        ),
        BlocBuilder<AccessbilityCubit, AccessbilityState>(
          builder: (ctx, state) {
            final ref = ctx.read<AccessbilityCubit>();
            return Slider(
              activeColor: context.themeColors.greenColor,
              inactiveColor: context.themeColors.darkGreyColor,
              thumbColor: context.themeColors.greenColor,
              value: isContrast ? state.accessibilityContrastValue : state.accessibilityFontSize,
              onChangeEnd: (v) async {},
              max: isContrast ? 2 : 1.2,
              min: isContrast ? 0 : 1,
              onChanged: (value) async {
                value.logD;
                if (isContrast) {
                  await ref.setAccessibilityContrast(value);
                  _debouncer.run(ref.accessibilityApi);
                } else {
                  await ref.setAccessibilityFontSize(value);
                  _debouncer.run(ref.accessibilityApi);
                }
              },
            );
          },
        ),
      ],
    );
  }
}
