import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/accessbility_repository.dart/accessbility_repository.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'accessbility_cubit.freezed.dart';
part 'accessbility_state.dart';

class AccessbilityCubit extends Cubit<AccessbilityState> {
  AccessbilityCubit() : super(AccessbilityState());

  final accessbilityRepository = AccessbilityRepository();

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);
  AuthRepository authRepository = AuthRepository();

  final scaffoldKey = GlobalKey<ScaffoldState>();

  void setInitialValue() {
    'accessbility_cubit.accessbility  ${Injector.instance<AppDB>().accessibilityFontSize}'.logD;

    // Injector.instance<AppDB>().accessibilityContrastValue =
    //     double.parse(Injector.instance<AppDB>().userModel?.user.app?.accessibility?.contrast?.toString() ?? '0.0');
    // Injector.instance<AppDB>().accessibilityFontSize =
    //     double.parse(Injector.instance<AppDB>().userModel?.user.app?.accessibility?.fontSize?.toString() ?? '0.0');
    emit(
      state.copyWith(
        accessibilityFontSize: Injector.instance<AppDB>().accessibilityFontSize ?? 1,
        accessibilityContrastValue: Injector.instance<AppDB>().accessibilityContrastValue ?? 0,
        accessibilityIsBoldText: Injector.instance<AppDB>().accessibilityIsBoldText ?? false,
      ),
    );
  }

  Future<void> setAccessibilityFontSize(double value) async {
    emit(state.copyWith(accessibilityFontSize: value));
    Injector.instance<AppDB>().accessibilityFontSize = value;
  }

  Future<void> setAccessibilityContrast(double value) async {
    emit(state.copyWith(accessibilityContrastValue: value));
    Injector.instance<AppDB>().accessibilityContrastValue = value;
  }

  Future<void> setAccessibilityBold(bool value) async {
    emit(state.copyWith(accessibilityIsBoldText: value));
    Injector.instance<AppDB>().accessibilityIsBoldText = value;
  }

  Future<void> accessibilityApi() async {
    try {
      final response = await accessbilityRepository.accessibility(
        contrast: state.accessibilityContrastValue,
        fontBold: state.accessibilityIsBoldText,
        fontSize: state.accessibilityFontSize,
        context: navigatorKey.currentContext!,
      );
      if (response != null && response.data?['success'] == true) {
        await authRepository.getUserData(context: navigatorKey.currentContext);
        Injector.instance<AppDB>().accessibilityContrastValue =
            double.parse(Injector.instance<AppDB>().userModel?.user.app?.accessibility?.contrast?.toString() ?? '0.0');
        Injector.instance<AppDB>().accessibilityFontSize =
            double.parse(Injector.instance<AppDB>().userModel?.user.app?.accessibility?.fontSize?.toString() ?? '0.0');

        Injector.instance<AppDB>().accessibilityIsBoldText =
            Injector.instance<AppDB>().userModel?.user.app?.accessibility?.fontBold;

        '**=======> ${Injector.instance<AppDB>().userModel?.user.app?.accessibility?.contrast}'.logD;
        '**=======>  ${Injector.instance<AppDB>().userModel?.user.app?.accessibility?.fontBold}'.logD;
        '**=======>  ${Injector.instance<AppDB>().userModel?.user.app?.accessibility?.fontSize}'.logD;

        '+++++++ ${state.accessibilityContrastValue}'.logD;
        '+++++++ ${state.accessibilityIsBoldText}'.logD;

        '=======> ${Injector.instance<AppDB>().accessibilityContrastValue}'.logD;
        '=======> ${Injector.instance<AppDB>().accessibilityFontSize}'.logD;
        '=======> ${Injector.instance<AppDB>().accessibilityIsBoldText}'.logD;
      }
    } catch (e) {
      'e$e'.logD;
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    headerInfoText.dispose();
    return super.close();
  }
}
