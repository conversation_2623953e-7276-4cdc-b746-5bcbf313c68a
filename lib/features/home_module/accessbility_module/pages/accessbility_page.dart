import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/cubit/accessbility_cubit.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/model/debouncer.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/widet/accessbility_widget.dart';
import 'package:breakingfree_v2/features/home_module/accessbility_module/widet/custom_dialog.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AccessbilityPage extends StatefulWidget {
  const AccessbilityPage({super.key});

  @override
  State<AccessbilityPage> createState() => _AccessbilityPageState();
}

class _AccessbilityPageState extends State<AccessbilityPage> {
  final _debouncer = Debouncer(milliseconds: 1000);

  @override
  void initState() {
    BlocProvider.of<AccessbilityCubit>(context).setInitialValue();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccessbilityCubit, AccessbilityState>(
      builder: (ctx, state) {
        final ref = ctx.read<AccessbilityCubit>();
        return AppScaffold(
          resizeToAvoidBottomInset: false,
          scaffoldKey: ref.scaffoldKey,
          isAudioPanelVisible: ref.isAudioPannelVisible,
          infoAudioUrl: ref.infoAudioUrl,
          drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
          appBar: CommonAppBar(
            onPrefixTap: () {
              ref.scaffoldKey.currentState?.openDrawer();
            },
            onSuffixTap: () {
              if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
              }
            },
          ),
          body: ColoredBox(
            color: context.themeColors.whiteColor,
            child: ColoredBox(
            color: context.themeColors.whiteColor,
            child: Column(
                children: [
                  SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: AppSize.w24,
                        right: AppSize.w28,
                        bottom: AppSize.h24,
                        top: AppSize.h24,
                      ),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        //child: CustomDialog(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Center(
                                child: AppTextWidget(
                                            CoreLocaleKeys.accessibility.tr(),
                                            style: context.textTheme.titleLarge?.copyWith(
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                              ),
                              SpaceV(AppSize.h7),
                              const CustomDivider(),
                              // if (state.accessibilityContrastValue.roundToDouble() == 2) ...{
                              //   const Divider(
                              //     color: Colors.black,
                              //     height: 1,
                              //     //  height: 15.sp,
                              //   ),
                              // },
                              // if (state.accessibilityContrastValue.roundToDouble() != 2) ...{
                              //   Divider(
                              //     height: 1,
                              //     color: context.themeColors.greyColor.withOpacity(.6),
                              //   ),
                              // },
                              // Divider(
                              //   height: 1,
                              //   color: context.themeColors.greyColor.withOpacity(.6),
                              // ),
                              SpaceV(AppSize.h16),
                              AccessbilityWidget(title: AppLocaleKey.fontSizeLabel.tr()),
                              SpaceV(AppSize.h16),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    AppTextWidget(
                                      AppLocaleKey.fontBoldLabel.tr(),
                                      style: context.textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: AppSize.sp13,
                ),
                                      // style: context.textTheme.titleSmall?.copyWith(
                                      //   fontSize: AppSize.sp14,
                                      //   fontWeight: FontWeight.w500,
                                      // ),
                                    ),
                                    CupertinoSwitch(
                                      value: state.accessibilityIsBoldText,
                                      activeColor: context.themeColors.greenColor,
                                      onChanged: (value) async {
                                        await ref.setAccessibilityBold(value);
                                        _debouncer.run(ref.accessibilityApi);
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              SpaceV(AppSize.h16),
                              AccessbilityWidget(
                                title: AppLocaleKey.contrast.tr(),
                                isContrast: true,
                              ),
                            ],
                          ),
                        //),
                      ),
                    ),
                  ),
                ],
            ),
            ),
          ),
        );
      },
    );
  }
}
