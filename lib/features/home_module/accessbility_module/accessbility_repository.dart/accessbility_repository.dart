import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class AccessbilityRepository {
  Future<Response<Map<String, dynamic>>?> accessibility({
    required bool? fontBold,
    required double? fontSize,
    required double? contrast,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'accessibility': {
            'fontSize': fontSize,
            'fontBold': fontBold,
            'contrast': contrast,
          },
        },
        apiName: EndPoints.accessibility,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
