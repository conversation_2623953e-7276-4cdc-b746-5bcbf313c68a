import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/repository/setting_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

part 'setting_state.dart';

class SettingCubit extends Cubit<SettingState> {
  SettingCubit() : super(SettingInitial());

  final SettingRepository repository = SettingRepository();
  AuthRepository authRepository = AuthRepository();
  final emailKey = GlobalKey<FormState>();
  final passwordKey = GlobalKey<FormState>();

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);
  ValueNotifier<bool> isENLanguageLoading = ValueNotifier(false);

  ValueNotifier<bool> isNameInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> isEmailInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> isPasswordInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> isSupporterInfoVisible = ValueNotifier(false);

  ValueNotifier<bool> isShowCurrentPassword = ValueNotifier(false);
  ValueNotifier<bool> isShowNewPassword = ValueNotifier(false);
  ValueNotifier<bool> isShowConfirmPassword = ValueNotifier(false);

  ValueNotifier<bool> isNameValid = ValueNotifier(false);
  ValueNotifier<bool> isEmailValid = ValueNotifier(false);
  ValueNotifier<bool> isPasswordValid = ValueNotifier(false);
  ValueNotifier<bool> isMySupporterValid = ValueNotifier(false);

  ValueNotifier<SupporterFieldState> supporter1FieldState = ValueNotifier(SupporterFieldState.addBtn);
  ValueNotifier<SupporterFieldState> supporter2FieldState = ValueNotifier(SupporterFieldState.addBtn);
  ValueNotifier<SupporterFieldState> supporter3FieldState = ValueNotifier(SupporterFieldState.addBtn);

  final nameController = TextEditingController(text: Injector.instance<AppDB>().userModel?.user.nameOnCertificate);

  final currentEmailController = TextEditingController();
  final newEmailController = TextEditingController();
  final confirmEmailController = TextEditingController();

  ValueNotifier<String> currentEmailError = ValueNotifier('');
  ValueNotifier<String> newEmailError = ValueNotifier('');
  ValueNotifier<String> confirmEmailError = ValueNotifier('');

  final currentPasswordController = TextEditingController();
  final newPasswordlController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  ValueNotifier<String> currentPasswordError = ValueNotifier('');
  ValueNotifier<String> newPasswordError = ValueNotifier('');
  ValueNotifier<String> confirmPasswordError = ValueNotifier('');

  final firstSupporterEmailController = TextEditingController();
  final secondSupporterEmailController = TextEditingController();
  final thirdSupporterEmailController = TextEditingController();

  final firstSupporterConfirmEmailController = TextEditingController();
  final secondSupporterConfirmEmailController = TextEditingController();
  final thirdSupporterConfirmEmailController = TextEditingController();

  ValueNotifier<bool> showPassword = ValueNotifier(false);
  ValueNotifier<bool> showNewPassword = ValueNotifier(false);
  ValueNotifier<bool> showConfirmNewPassword = ValueNotifier(false);

  ValueNotifier<String> firstSupporterEmailError = ValueNotifier('');
  ValueNotifier<String> firstSupporterConfirmEmailError = ValueNotifier('');

  ValueNotifier<String> secondSupporterEmailError = ValueNotifier('');
  ValueNotifier<String> secondSupporterConfirmEmailError = ValueNotifier('');

  ValueNotifier<String> thirdSupporterEmailError = ValueNotifier('');
  ValueNotifier<String> thirdSupporterConfirmEmailError = ValueNotifier('');




    void togglevisibility() {
    showPassword.value = !showPassword.value;
  }

   void toggleNewPasswordvisibility() {
    showNewPassword.value = !showNewPassword.value;
  }

   void toggleConfirmNewPasswordvisibility() {
    showConfirmNewPassword.value = !showConfirmNewPassword.value;
  }

  bool currentEmailValid() {
    emit(SettingInitial());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(currentEmailController.text);
    // Email
    if (currentEmailController.text.trim().isEmpty) {
      currentEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      currentEmailError.value = '';
    } else if (!emailValid) {
      currentEmailError.value = SettingLocaleKeys.errorsCurrentEmailInvalid.tr();
      isValid = false;
      currentEmailError.value = '';
    } else {
      currentEmailError.value = '';
    }
    return isValid;
  }


  bool newEmailValid() {
    emit(SettingInitial());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(newEmailController.text);
    // Email
    if (newEmailController.text.trim().isEmpty) {
      newEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      newEmailError.value = '';
    } else if (!emailValid) {
      newEmailError.value = SettingLocaleKeys.errorsNewEmailInvalid.tr();
      isValid = false;
      newEmailError.value = '';
    } else {
      newEmailError.value = '';
    }
    return isValid;
  }

  bool confirmEmailValid() {
    emit(SettingInitial());
    var isValid = true;

   // Email
    if (confirmEmailController.text.trim().isEmpty) {
      confirmEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      confirmEmailError.value = '';
    } else if (newEmailController.text.trim().toLowerCase() != confirmEmailController.text.trim().toLowerCase()) {
      confirmEmailError.value = SettingLocaleKeys.errorsEmailMatch.tr();
      isValid = false;
      confirmEmailError.value = '';
    } else {
      confirmEmailError.value = '';
    }
    return isValid;
  }

  bool currentPasswordValid(){
    emit(SettingInitial());
    var isValid = true;
    if (currentPasswordController.text.isEmpty) {
      currentPasswordError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      currentPasswordError.value = '';
    } else {
      currentPasswordError.value = '';
    }
    return isValid;
  }

  bool newPasswordValid(){
    emit(SettingInitial());
    var isValid = true;
    if (newPasswordlController.text.isEmpty) {
      newPasswordError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      newPasswordError.value = '';
    } else {
      newPasswordError.value = '';
    }
    return isValid;
  }

  bool confirmPasswordValid(){
    emit(SettingInitial());
    var isValid = true;
    if (confirmPasswordController.text.isEmpty) {
      confirmPasswordError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      confirmPasswordError.value = '';
    } else if (confirmPasswordController.text.length < 8) {
      confirmPasswordError.value =  AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
      confirmPasswordError.value = '';
    } else if (confirmPasswordController.text != newPasswordlController.text) {
      confirmPasswordError.value =  AuthLocaleKeys.signUpPasswordNotMatch.tr();
      isValid = false;
      confirmPasswordError.value = '';
    }
    else {
      confirmPasswordError.value = '';
    }
    return isValid;
  }

  bool validateEmailFields(){
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(currentEmailController.text);
    // Email
    if (currentEmailController.text.trim().isEmpty) {
      currentEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else if (!emailValid) {
      currentEmailError.value = SettingLocaleKeys.errorsCurrentEmailInvalid.tr();
      isValid = false;
    } else {
      currentEmailError.value = '';
    }
    final newEmailValid = RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(newEmailController.text);
    if (newEmailController.text.trim().isEmpty) {
      newEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else if (!newEmailValid) {
      newEmailError.value = SettingLocaleKeys.errorsNewEmailInvalid.tr();
      isValid = false;
    } else {
      newEmailError.value = '';
    }

    if (confirmEmailController.text.trim().isEmpty) {
      confirmEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else if (newEmailController.text.trim().toLowerCase() != confirmEmailController.text.trim().toLowerCase()) {
      confirmEmailError.value = SettingLocaleKeys.errorsEmailMatch.tr();
      isValid = false;
    } else {
      confirmEmailError.value = '';
    }

    return isValid;

  }

  bool validatePasswordFields(){
    bool isValid = true;
    if (currentPasswordController.text.isEmpty) {
      currentPasswordError.value = AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else {
      currentPasswordError.value = '';
    }

    if (newPasswordlController.text.isEmpty) {
      newPasswordError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else {
      newPasswordError.value = '';
    }

    if (confirmPasswordController.text.isEmpty) {
      confirmPasswordError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
    } else if (confirmPasswordController.text.length < 8) {
      confirmPasswordError.value =  AuthLocaleKeys.signUpPasswordMinLength.tr();
      isValid = false;
    }else if (confirmPasswordController.text != newPasswordlController.text) {
      confirmPasswordError.value =  AuthLocaleKeys.signUpPasswordNotMatch.tr();
      isValid = false;
    }else {
      confirmPasswordError.value = '';
    }
    return isValid;
  }

  bool firstSupportersEmailValid() {
    emit(SettingInitial());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(firstSupporterEmailController.text);
    // Email
    if (firstSupporterEmailController.text.trim().isEmpty) {
      firstSupporterEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      firstSupporterEmailError.value = '';
    } else if (!emailValid) {
      firstSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
      isValid = false;
      firstSupporterEmailError.value = '';
    } else {
      firstSupporterEmailError.value = '';
    }

    return isValid;
  }

  bool secondSupportersEmailValid(){
    emit(SettingInitial());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(secondSupporterEmailController.text);

    if (secondSupporterEmailController.text.trim().isEmpty) {
      secondSupporterEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      secondSupporterEmailError.value = '';
    } else if (!emailValid) {
      secondSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
      isValid = false;
      secondSupporterEmailError.value = '';
    } else {
      secondSupporterEmailError.value = '';
    }
    return isValid;
  }

  bool thirdSupportersEmailValid(){
    emit(SettingInitial());
    var isValid = true;
    final emailValid =
    RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(thirdSupporterEmailController.text);

    if (thirdSupporterEmailController.text.trim().isEmpty) {
      thirdSupporterEmailError.value =  AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      thirdSupporterEmailError.value = '';
    } else if (!emailValid) {
      thirdSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
      isValid = false;
      thirdSupporterEmailError.value = '';
    } else {
      thirdSupporterEmailError.value = '';
    }

    return isValid;
  }

  bool firstSupportersConfirmEmailValid() {
    emit(SettingInitial());
    var isValid = true;

    if (firstSupporterConfirmEmailController.text.trim().isEmpty) {
      firstSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      firstSupporterConfirmEmailError.value = '';

    } else if (firstSupporterEmailController.text.trim().toLowerCase() != firstSupporterConfirmEmailController.text.trim().toLowerCase()) {
      firstSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
      isValid = false;
      firstSupporterConfirmEmailError.value = '';
    } else {
      firstSupporterConfirmEmailError.value = '';
    }
    return isValid;
  }

  bool secondSupportersConfirmEmailValid() {
    emit(SettingInitial());
    var isValid = true;

    if (secondSupporterConfirmEmailController.text.trim().isEmpty) {
      secondSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      secondSupporterConfirmEmailError.value = '';
    } else if (secondSupporterEmailController.text.trim().toLowerCase() != secondSupporterConfirmEmailController.text.trim().toLowerCase()) {
      secondSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
      isValid = false;
      secondSupporterConfirmEmailError.value = '';
    } else {
      secondSupporterConfirmEmailError.value = '';
    }
    return isValid;
  }

  bool thirdSupportersConfirmEmailValid() {
    emit(SettingInitial());
    var isValid = true;

    if (thirdSupporterConfirmEmailController.text.trim().isEmpty) {
      thirdSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
      isValid = false;
      thirdSupporterConfirmEmailError.value = '';
    } else if (thirdSupporterEmailController.text.trim().toLowerCase() != thirdSupporterConfirmEmailController.text.trim().toLowerCase()) {
      thirdSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
      isValid = false;
      thirdSupporterConfirmEmailError.value = '';
    } else {
      thirdSupporterConfirmEmailError.value = '';
    }
    return isValid;
  }

  bool validateSupporterEmailFields() {
    var isValid = true;
    
    // First supporter validation
    if (supporter1FieldState.value == SupporterFieldState.enterEmail) {
      final firstEmailValid = RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(firstSupporterEmailController.text);
      
      if (firstSupporterEmailController.text.trim().isEmpty) {
        firstSupporterEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (!firstEmailValid) {
        firstSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
        isValid = false;
      } else if ((firstSupporterEmailController.text.trim().toLowerCase() == secondSupporterEmailController.text.trim().toLowerCase()) ||
          (firstSupporterEmailController.text.trim().toLowerCase() == thirdSupporterEmailController.text.trim().toLowerCase())) {
        firstSupporterEmailError.value = SettingLocaleKeys.errorsSupporterDuplicate.tr();
        isValid = false;
      } else {
        firstSupporterEmailError.value = '';
      }

      if (firstSupporterConfirmEmailController.text.trim().isEmpty) {
        firstSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (firstSupporterEmailController.text.trim().toLowerCase() != firstSupporterConfirmEmailController.text.trim().toLowerCase()) {
        firstSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
        isValid = false;
      } else {
        firstSupporterConfirmEmailError.value = '';
      }
    }

    // Second supporter validation
    if (supporter2FieldState.value == SupporterFieldState.enterEmail) {
      final secondEmailValid = RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(secondSupporterEmailController.text);
      
      if (secondSupporterEmailController.text.trim().isEmpty) {
        secondSupporterEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (!secondEmailValid) {
        secondSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
        isValid = false;
      } else if ((secondSupporterEmailController.text.trim().toLowerCase() == firstSupporterEmailController.text.trim().toLowerCase()) ||
          (secondSupporterEmailController.text.trim().toLowerCase() == thirdSupporterEmailController.text.trim().toLowerCase())) {
        secondSupporterEmailError.value = SettingLocaleKeys.errorsSupporterDuplicate.tr();
        isValid = false;
      } else {
        secondSupporterEmailError.value = '';
      }

      if (secondSupporterConfirmEmailController.text.trim().isEmpty) {
        secondSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (secondSupporterEmailController.text.trim().toLowerCase() != secondSupporterConfirmEmailController.text.trim().toLowerCase()) {
        secondSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
        isValid = false;
      } else {
        secondSupporterConfirmEmailError.value = '';
      }
    }

    // Third supporter validation
    if (supporter3FieldState.value == SupporterFieldState.enterEmail) {
      final thirdEmailValid = RegExp(r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+").hasMatch(thirdSupporterEmailController.text);
      
      if (thirdSupporterEmailController.text.trim().isEmpty) {
        thirdSupporterEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (!thirdEmailValid) {
        thirdSupporterEmailError.value = SettingLocaleKeys.errorsSupporterInvalid.tr();
        isValid = false;
      } else if ((thirdSupporterEmailController.text.trim().toLowerCase() == firstSupporterEmailController.text.trim().toLowerCase()) ||
          (thirdSupporterEmailController.text.trim().toLowerCase() == secondSupporterEmailController.text.trim().toLowerCase())) {
        thirdSupporterEmailError.value = SettingLocaleKeys.errorsSupporterDuplicate.tr();
        isValid = false;
      } else {
        thirdSupporterEmailError.value = '';
      }

      if (thirdSupporterConfirmEmailController.text.trim().isEmpty) {
        thirdSupporterConfirmEmailError.value = AuthLocaleKeys.fieldRequiredError.tr();
        isValid = false;
      } else if (thirdSupporterEmailController.text.trim().toLowerCase() != thirdSupporterConfirmEmailController.text.trim().toLowerCase()) {
        thirdSupporterConfirmEmailError.value = SettingLocaleKeys.errorsSupporterMatch.tr();
        isValid = false;
      } else {
        thirdSupporterConfirmEmailError.value = '';
      }
    }

    return isValid;
  }



/*
  bool passwordValid() {
    var isValid = true;

    if (passwordController.text.isEmpty) {
      passwordError.value = AuthLocaleKeys.logInPasswordRequired.tr();
      isValid = false;
      emit(LoginInitialState());
    } else {
      passwordError.value = '';
    }
    return isValid;
  }
*/

  Future<void> changeUserNameApi({required String nameText}) async {
    emit(LoadingSettingState());
    try {
      final response = await repository.changeUserName(
        value: nameText,
        context: navigatorKey.currentContext!,
      );
      if (response != null && response.data?['success'] == true) {
        isNameValid.value = true;
        //CustomSnackbar.showSucessSnackBar(message: SettingLocaleKeys.nameSuccess.tr());
      }
      emit(SettingInitial());
    } catch (e) {
      emit(SettingInitial());
    }
  }

  Future<void> changeUserEmailApi({required String currentEmailText, required String newEmailText}) async {
    emit(LoadingEmailSettingState());
    try {
      final response = await APIFunction.postAPICall(
        {
          'currentEmail': currentEmailText,
          'newEmail': newEmailText,
        },
        apiName: EndPoints.changeUserEmail,
        context: navigatorKey.currentContext,
      );
      FocusManager.instance.primaryFocus?.unfocus();

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          currentEmailController.clear();
          newEmailController.clear();
          confirmEmailController.clear();
          isEmailValid.value = true;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return;
        }

      }
      else {
        'response email'.logV;

        if (response is DioException) {

          if (Map<String, dynamic>.from(response.response!.data as Map)['message'] as String == 'emailExists') {
            emit(EmailExistState(SettingLocaleKeys.errorsEmailExists.tr()));
          } else if(Map<String, dynamic>.from(response.response!.data as Map)['message'] as String == 'currentEmailIncorrect') {
            emit(EmailIncorrectState(SettingLocaleKeys.errorsCurrentEmailIncorrect.tr()));
          }
          else {
            // CustomSnackbar.showErrorSnackBar(
            //   message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
            // );
          }

        }
        return;
      }
      emit(SettingInitial());
    } catch (e) {
      emit(SettingInitial());
    }
    // try {
    //   final response = await repository.changeUserEmail(
    //     currentEmailText: currentEmailText,
    //     newEmailText: newEmailText,
    //     context: navigatorKey.currentContext!,
    //   );
    //   response.logD;
    //   FocusManager.instance.primaryFocus?.unfocus();
    //
    //
    //
    //   if (response != null && response.data?['success'] == true) {
    //     // emailKey.currentState?.reset();
    //     currentEmailController.clear();
    //     newEmailController.clear();
    //     confirmEmailController.clear();
    //     isEmailValid.value = true;
    //     // CustomSnackbar.showSucessSnackBar(
    //     //   message: (DynamicAssetLoader.getNestedValue(
    //     //     SettingLocaleKeys.emailSuccess,
    //     //     navigatorKey.currentContext!,
    //     //   ) as List)
    //     //       .join('\n\n'),
    //     // );
    //
    //   }
    //   else{
    //       if (Map<String, dynamic>.from(response!.data! as Map)['message'] as String == 'emailExists') {
    //         emit(EmailExistState(''));
    //       } else if(Map<String, dynamic>.from(response.data! as Map)['message'] as String == 'currentEmailIncorrect') {
    //         emit(EmailIncorrectState(''));
    //       }
    //       else {
    //         CustomSnackbar.showErrorSnackBar(
    //           message: Map<String, dynamic>.from(response.data! as Map)['message'] as String,
    //         );
    //       }
    //
    //   }
    //   emit(SettingInitial());
    // } catch (e) {
    //   emit(SettingInitial());
    // }
  }

  Future<void> changeUserPasswordApi({required String currentPassword, required String newPassword}) async {
    emit(LoadingPasswordSettingState());
    try {
      final response = await repository.changeUserPassword(
        context: navigatorKey.currentContext!,
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && response.data?['success'] == true) {
        passwordKey.currentState?.reset();
        currentPasswordController.clear();
        newPasswordlController.clear();
        confirmPasswordController.clear();
        isPasswordValid.value = true;
        //CustomSnackbar.showSucessSnackBar(message: SettingLocaleKeys.passwordSuccess.tr());
      }else{
        emit(IncorrectPasswordState(SettingLocaleKeys.errorsCurrentPasswordIncorrect.tr()));
      }
      // emit(SettingInitial());
    } catch (e) {
      if (e is AppException) {
        emit(IncorrectPasswordState(SettingLocaleKeys.errorsCurrentPasswordIncorrect.tr()));
      } else {
        emit(IncorrectPasswordState('Something went wrong, please try again.'));
      }
    }
  }

  Future<void> changeUserSupporterAPI({required List<String> supporterList}) async {
    emit(LoadingSuppoterSettingState());
    try {
      final response = await repository.changeUserSupporter(
        context: navigatorKey.currentContext!,
        supporterList: supporterList
            .where(
              (element) => element.trim().isNotEmpty,
            )
            .toList(),
      );

      FocusManager.instance.primaryFocus?.unfocus();

      if (response != null && response.data?['success'] == true) {
        // Clear all controllers and reset their states
        firstSupporterEmailController.clear();
        secondSupporterEmailController.clear();
        thirdSupporterEmailController.clear();
        firstSupporterConfirmEmailController.clear();
        secondSupporterConfirmEmailController.clear();
        thirdSupporterConfirmEmailController.clear();
        supporter1FieldState.value = SupporterFieldState.addBtn;
        supporter2FieldState.value = SupporterFieldState.addBtn;
        supporter3FieldState.value = SupporterFieldState.addBtn;

        // Parse supporters list from the response
        final supporters = response.data?['supporters'] as List<dynamic>?;

        if (supporters != null && supporters.isNotEmpty) {
          // Assign supporters to fields sequentially
          for (var i = 0; i < supporters.length; i++) {
            final supporterEmail = supporters[i].toString();
            if (i == 0 && firstSupporterEmailController.text.isEmpty) {
              firstSupporterEmailController.text = supporterEmail;
              supporter1FieldState.value = SupporterFieldState.savedEmail;
            } else if (i == 1 && secondSupporterEmailController.text.isEmpty) {
              secondSupporterEmailController.text = supporterEmail;
              supporter2FieldState.value = SupporterFieldState.savedEmail;
            } else if (i == 2 && thirdSupporterEmailController.text.isEmpty) {
              thirdSupporterEmailController.text = supporterEmail;
              supporter3FieldState.value = SupporterFieldState.savedEmail;
            }
          }
        }
        isMySupporterValid.value = true;
        // Show success snackbar
        //CustomSnackbar.showSucessSnackBar(message: SettingLocaleKeys.supportersSuccess.tr());
      }

      emit(SettingInitial());
    } catch (e) {
      emit(SettingInitial());
    }
  }

  Future<void> userLanguageApi(String lang) async {
    try {
      final response = await repository.userLanguage(
        lang: lang,
        context: navigatorKey.currentContext!,
      );
      if (response != null && response.data?['success'] == true) {
        await authRepository.getUserData(context: navigatorKey.currentContext);
        Injector.instance<AppDB>().selectedLangugae = lang;

        '**=======> ${Injector.instance<AppDB>().selectedLangugae}'.logD;
        '**=======> response = ${response.toString()}'.logD;
      }
    } catch (e) {
      'e$e'.logD;
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    isNameInfoVisible.dispose();
    isEmailInfoVisible.dispose();
    isPasswordInfoVisible.dispose();
    isSupporterInfoVisible.dispose();
    supporter1FieldState.dispose();
    supporter2FieldState.dispose();
    supporter3FieldState.dispose();
    nameController.dispose();
    currentEmailController.dispose();
    newEmailController.dispose();
    currentPasswordController.dispose();
    confirmEmailController.dispose();
    confirmPasswordController.dispose();
    newPasswordlController.dispose();
    firstSupporterEmailController.dispose();
    secondSupporterEmailController.dispose();
    thirdSupporterEmailController.dispose();
    currentEmailError.dispose();
    newEmailError.dispose();
    confirmEmailError.dispose();
    currentPasswordError.dispose();
    newPasswordError.dispose();
    confirmPasswordError.dispose();
    return super.close();
  }
}
