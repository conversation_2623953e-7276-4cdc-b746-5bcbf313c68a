part of 'setting_cubit.dart';

@immutable
sealed class SettingState {}

final class SettingInitial extends SettingState {}

final class LoadingSettingState extends SettingState {}

final class LoadingEmailSettingState extends SettingState {}

final class LoadingPasswordSettingState extends SettingState {}

final class LoadingSuppoterSettingState extends SettingState {}

final class EmailIncorrectState extends SettingState {
  final String incorrectMessage;
  EmailIncorrectState(this.incorrectMessage);
}

final class EmailExistState extends SettingState {
  final String emailExistMessage;
  EmailExistState(this.emailExistMessage);
}

final class IncorrectPasswordState extends SettingState {
  final String passwordMessage;
  IncorrectPasswordState(this.passwordMessage);
}

