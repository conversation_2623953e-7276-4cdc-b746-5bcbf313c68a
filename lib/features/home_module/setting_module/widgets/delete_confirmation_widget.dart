import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class DeleteConfirmationWidget extends StatelessWidget {
  const DeleteConfirmationWidget({
    required this.onDeleteYesTap,
    required this.onCloseDialogueTap,
    this.isVisible = false,
    super.key,
  });

  final void Function()? onDeleteYesTap;
  final void Function()? onCloseDialogueTap;
  final bool isVisible;

  @override
  Widget build(BuildContext context) {
    return isVisible
        ? Padding(
            padding: EdgeInsets.only(top: AppSize.h16),
            child: CustomInfoWidget(
              bgColor: context.themeColors.lightBrownColor,
              customWidget: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: AppSize.w40,
                        alignment: Alignment.centerLeft,
                        child: Icon(
                          Icons.warning_amber_outlined,
                          size: AppSize.sp24,
                          color: context.themeColors.brownColor,
                        ),
                      ),
                      Expanded(
                        child: AppTextWidget(
                          SettingLocaleKeys.supportersRemoveTitle.tr(),
                          textAlign: TextAlign.center,
                          style: context.textTheme.labelSmall?.copyWith(
                            color: context.themeColors.darkBrownColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      SpaceH(AppSize.w40),
                    ],
                  ),
                  SpaceV(AppSize.h16),
                  AppTextWidget(

                    (DynamicAssetLoader.getNestedValue(
                      SettingLocaleKeys.supportersRemoveText,
                      context,
                    ) as List)
                        .join('\n\n'),
                    // 'Are you sure you want to remove this person as a supporter?\n\nIf you do this, you will no longer be able to send them your information summaries, action plans, and progress reports and certificates.\n\nOnce you have removed this person, you will be able to add them again as a supporter at any time in the future should you wish to.',
                    textAlign: TextAlign.center,
                    style: context.textTheme.labelSmall?.copyWith(
                      color: context.themeColors.darkBrownColor,
                    ),
                  ),
                  SpaceV(AppSize.h16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomRoundedButton(
                        onTap: onDeleteYesTap,
                        width: AppSize.w64,
                        title: SettingLocaleKeys.buttonsYes.tr(),
                      ),
                      SpaceH(AppSize.w8),
                      CustomRoundedButton(
                        onTap: onCloseDialogueTap,
                        width: AppSize.w64,
                        title: SettingLocaleKeys.buttonsNo.tr(),
                        fillColor: context.themeColors.redColor,
                      ),
                    ],
                  ),
                ],
              ),
              showShadow: false,
              onCloseTap: onCloseDialogueTap,
            ),
          )
        : const SizedBox.shrink();
  }
}
