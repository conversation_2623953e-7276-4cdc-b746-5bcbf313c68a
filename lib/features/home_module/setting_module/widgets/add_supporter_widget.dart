import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class AddSupporterWidget extends StatelessWidget {
  const AddSupporterWidget({
    required this.name,
    super.key,
    this.onAddTap,
  });

  final void Function()? onAddTap;
  final String name;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onAddTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.w14,
          vertical: AppSize.h8,
        ),
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            AppSize.r4,
          ),
          border: Border.all(
            color: context.themeColors.greyColor,
          ),
        ),
        child: AppTextWidget(
          name,
          style: context.textTheme.titleSmall?.copyWith(
            color: context.themeColors.greenColor,
          ),
        ),
      ),
    );
  }
}
