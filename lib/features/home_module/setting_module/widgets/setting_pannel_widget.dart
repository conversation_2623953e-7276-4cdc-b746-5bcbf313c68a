import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class SettingPannelWidget extends StatelessWidget {
  const SettingPannelWidget({
    required this.heading,
    super.key,
    this.onInfoTap,
    this.onUpdateTap,
    this.infoWidget,
    this.textFields,
    this.isShowInfo,
    this.isShowUpdate,
    this.isLoading = false,
    this.isError,
    this.name,
  });
  final String heading;
  final void Function()? onInfoTap;
  final void Function()? onUpdateTap;
  final CustomInfoWidget? infoWidget;
  final List<Widget>? textFields;
  final bool isLoading;
  final bool? isShowInfo;
  final bool? isShowUpdate;
  final bool? isError;
  final String? name;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Column(
        children: [
          Row(
            children: [
              AppTextWidget(
                heading,
                style: context.textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: AppSize.sp13,
                ),
              ),
              SpaceH(AppSize.w8),
              Visibility(
                visible: isShowInfo ?? true,
                child: CustomIconButton(
                  onTap: onInfoTap,
                  assetIcon: Assets.icons.infoIcon,
                ),
              ),
            ],
          ),
          if (infoWidget != null && (infoWidget?.visible ?? false)) ...[
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h8),
              child: infoWidget,
            ),
          ],
          if (textFields?.isNotEmpty ?? false) ...[
            SpaceV(AppSize.h24),
            ...textFields!,
            SpaceV(AppSize.h16),
          ],
          Visibility(
            visible: isError ?? false,
            child: Column(
              children: [
                Row(
                  children: [
                    SpaceH(AppSize.h2),
                      if(name == 'name')
                      AppTextWidget(
                      SettingLocaleKeys.nameSuccess.tr(),
                      style: context.textTheme.titleSmall?.copyWith(
                        color: context.themeColors.greenColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                      if(name == 'email')
                        Expanded(
                          child: AppTextWidget(
                            (DynamicAssetLoader.getNestedValue(
                              SettingLocaleKeys.emailSuccess,
                              context,
                            ) as List)
                              .join('\n\n'),
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.themeColors.greenColor,fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                      if(name == 'password')
                        AppTextWidget(SettingLocaleKeys.passwordSuccess.tr(),
                          style: context.textTheme.titleSmall?.copyWith(
                            color: context.themeColors.greenColor,fontWeight: FontWeight.w500,
                          ),
                        ),

                      if(name == 'supporters')
                        Expanded(
                          child: AppTextWidget(SettingLocaleKeys.supportersSuccess.tr(),
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.themeColors.greenColor,fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                  ],
                ),
                SpaceV(AppSize.h5),
              ],
            ),
          ),
          Visibility(
            visible: isShowUpdate ?? true,
            child: Align(
              alignment: Alignment.centerRight,
              child: CustomRoundedButton(
                title: SettingLocaleKeys.buttonsUpdate.tr(),
                onTap: onUpdateTap,
                isLoading: isLoading,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
