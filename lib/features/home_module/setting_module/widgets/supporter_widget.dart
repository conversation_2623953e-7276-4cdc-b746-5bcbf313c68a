import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/cubit/setting_cubit.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/widgets/add_supporter_widget.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/widgets/delete_confirmation_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class SupporterWidget extends StatefulWidget {
  const SupporterWidget({
    required this.addBtnName,
    required this.emailLabelText,
    required this.confirmEmailLabelText,
    required this.state,
    required this.ref,
    required this.emailError,
    required this.confirmEmailError, super.key,
    this.supporterFieldState = SupporterFieldState.addBtn,
    this.onAddTap,
    this.emailController,
    this.confirmEmailController,
    this.onSuffixDeleteTap,
    this.onDeleteYesTap,
    this.onCloseDialogueTap,
    this.emailValidator,
    this.confirmValidator,
    this.onChanged,
    this.onConfirmChanged,
  });
  final SupporterFieldState supporterFieldState;
  final String addBtnName;
  final String emailLabelText;
  final String confirmEmailLabelText;
  final TextEditingController? emailController;
  final TextEditingController? confirmEmailController;
  final void Function()? onAddTap;
  final void Function()? onSuffixDeleteTap;
  final void Function()? onDeleteYesTap;
  final void Function()? onCloseDialogueTap;
  final String? Function(String?)? emailValidator;
  final String? Function(String?)? confirmValidator;
  final void Function(String?)? onChanged;
  final void Function(String?)? onConfirmChanged;
  final SettingState state;
  final SettingCubit ref;
  final ValueNotifier<String> emailError;
  final ValueNotifier<String> confirmEmailError;


  

  @override
  State<SupporterWidget> createState() => _SupporterWidgetState();

  
}

class _SupporterWidgetState extends State<SupporterWidget> {
  @override
  Widget build(BuildContext context) {
    return widget.supporterFieldState == SupporterFieldState.addBtn
        ? AddSupporterWidget(
            onAddTap: widget.onAddTap,
            name: widget.addBtnName,
          )
        : Column(
            children: [
              ValueListenableBuilder(
                valueListenable:widget.emailError ,
                builder: (context,emailError,child) {
                  return Column(
                    children: [
                      CustomOutlinedTextfield(
                      validator: (value) => widget.emailValidator?.call(value?.trim().toLowerCase()),
                      controller: widget.emailController,
                      onChanged:widget.onChanged,
                      isError: widget.emailError.value != '',
                      textAction: TextInputAction.next,
                      // onChanged: (value) {
                      //   final normalized = value?.trim().toLowerCase();
                      //   if (value != normalized) {
                      //     // Use setState or ValueNotifier to trigger a rebuild
                      //     WidgetsBinding.instance.addPostFrameCallback((_) {
                      //       widget.emailController!.value = widget.emailController!.value.copyWith(
                      //         text: normalized,
                      //         selection: TextSelection.collapsed(offset: normalized?.length ?? 0),
                      //       );
                      //     });
                      //   }
                      // },
                      readOnly: (widget.supporterFieldState == SupporterFieldState.savedEmail) ||
                          widget.supporterFieldState == SupporterFieldState.confirmDelete,
                      suffixIcon: (widget.supporterFieldState == SupporterFieldState.savedEmail) ||
                              widget.supporterFieldState == SupporterFieldState.confirmDelete
                          ? GestureDetector(
                              onTap: widget.onSuffixDeleteTap,
                              child: Icon(
                                Icons.delete,
                                size: AppSize.sp20,
                                color: context.themeColors.blackColor.withOpacity(0.54),
                              ),
                            )
                          : null,
                      labelText: widget.emailLabelText,
                                ),
                      if(emailError != '')
                        CustomErrorWidget(errorMessgaeText: emailError)
                      else if(widget.state is EmailIncorrectState && widget.ref.validateEmailFields())
                        const CustomErrorWidget(errorMessgaeText:'')
                      else
                        const SizedBox()

                    ],
                  );
                },
              ),

              DeleteConfirmationWidget(
                onDeleteYesTap: widget.onDeleteYesTap,
                onCloseDialogueTap: widget.onCloseDialogueTap,
                isVisible: widget.supporterFieldState == SupporterFieldState.confirmDelete,
              ),
             if (widget.supporterFieldState == SupporterFieldState.enterEmail) ...[
                SpaceV(AppSize.h16),
                ValueListenableBuilder(
                  valueListenable: widget.confirmEmailError,
                  builder: (context,confirmEmailError,child) {
                    return Column(
                      children: [
                        CustomOutlinedTextfield(
                          controller: widget.confirmEmailController,
                          labelText: widget.confirmEmailLabelText,
                          onChanged: widget.onConfirmChanged,
                          isError: widget.confirmEmailError.value != '',
                          textAction: TextInputAction.done,
                          validator: (value) => widget.confirmValidator?.call(value?.trim().toLowerCase()),
                        ),
                        if(confirmEmailError != '')
                          CustomErrorWidget(errorMessgaeText: confirmEmailError)
                        else if(widget.state is EmailIncorrectState && widget.ref.validateEmailFields())
                          const CustomErrorWidget(errorMessgaeText:'')
                        else
                          const SizedBox()
                      ],
                    );
                  },
                ),
            ],


            ],
          );
  }
}
