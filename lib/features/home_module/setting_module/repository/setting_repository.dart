import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/cubit/setting_cubit.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

final class SettingRepository {
  Future<Response<Map<String, dynamic>>?> changeUserName({
    required String? value,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'nameOnCertificate': value,
        },
        apiName: EndPoints.changeUserName,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> changeUserEmail({
    required String? currentEmailText,
    required String? newEmailText,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'currentEmail': currentEmailText,
          'newEmail': newEmailText,
        },
        apiName: EndPoints.changeUserEmail,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        final message = data?['message'] as String;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          throw AppException(message);
        }

      }
      else {
        'response email'.logV;

        if (response is DioException) {
          final message = Map<String, dynamic>.from(response.response!.data as Map)['message'] as String;
          throw AppException(
              message.contains('emailExists') ?
               SettingLocaleKeys.errorsEmailExists.tr() :
                message.contains('currentEmailIncorrect')?
                 SettingLocaleKeys.errorsCurrentEmailIncorrect.tr():
                   message,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      if (e is AppException) rethrow;

      throw AppException('Something went wrong. Please try again.');
    }
  }

  Future<Response<Map<String, dynamic>>?> changeUserPassword({
    required String? currentPassword,
    required String? newPassword,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
        apiName: EndPoints.changeUserPassword,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        final message = data?['message'] as String? ?? 'Something went wrong';

        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          throw AppException(message);
        }
      } else {
        if (response is DioException) {
          final message = Map<String, dynamic>.from(response.response!.data as Map)['message'] as String;
          throw AppException(message);

        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      if (e is AppException) rethrow;

      throw AppException('Something went wrong. Please try again.');
    }
  }

  Future<Response<Map<String, dynamic>>?> changeUserSupporter({
    required List<String>? supporterList,
    required BuildContext context,
  }) async {
    try {
      supporterList.logD;
      final response = await APIFunction.postAPICall(
        {
          'supporters': supporterList,
        },
        apiName: EndPoints.changeUserSupporter,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          if (Map<String, dynamic>.from(response.response!.data as Map)['message'] as String == 'supporterYourself') {
            CustomSnackbar.showErrorSnackBar(
              message: SettingLocaleKeys.errorsSupporterYourself.tr(),
            );
          } else {
            CustomSnackbar.showErrorSnackBar(
              message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
            );
          }
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<Response<Map<String, dynamic>>?> userLanguage({
    required String? lang,
    required BuildContext context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'lang': lang,
        },
        apiName: EndPoints.userLanguage,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return response;
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

}
