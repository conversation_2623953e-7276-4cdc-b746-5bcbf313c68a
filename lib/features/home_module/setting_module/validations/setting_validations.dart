import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/validations/confirm_validator.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:form_field_validator/form_field_validator.dart';

class SettingValidations {
  static MultiValidator nameValidator() => MultiValidator([
        MaxLengthValidator(
          100,
          errorText: SettingLocaleKeys.errorsNameMaxLength.tr(),
        ),
      ]);

  static MultiValidator emailValidator({required String invalidEmailError}) => MultiValidator([
        RequiredValidator(
          errorText: SettingLocaleKeys.errorsRequired.tr(),
        ),
        EmailValidator(
          errorText: invalidEmailError,
        ),
      ]);

  static MultiValidator confirmEmailValidator({
    required String notMatchError,
    required String email,
  }) =>
      MultiValidator([
        RequiredValidator(
          errorText: SettingLocaleKeys.errorsRequired.tr(),
        ),
        ConfirmValidator(
          errorText: notMatchError,
          matchString: email,
        ),
      ]);

  static MultiValidator passwordValidator() => MultiValidator([
        RequiredValidator(
          errorText: SettingLocaleKeys.errorsRequired.tr(),
        ),
        MaxLengthValidator(
          100,
          errorText: SettingLocaleKeys.errorsPasswordMaxLength.tr(),
        ),
        MinLengthValidator(
          8,
          errorText: SettingLocaleKeys.errorsPasswordMinLength.tr(),
        ),
      ]);

  static MultiValidator confirmPasswordValidator({
    required String notMatchError,
    required String password,
  }) =>
      MultiValidator([
        RequiredValidator(
          errorText: SettingLocaleKeys.errorsRequired.tr(),
        ),
        ConfirmValidator(
          errorText: notMatchError,
          matchString: password,
        ),
      ]);

  static MultiValidator supporterEmailValidator({
    required List<String> otherSupporterMails,
  }) =>
      MultiValidator([
        RequiredValidator(
          errorText: SettingLocaleKeys.errorsRequired.tr(),
        ),
        EmailValidator(
          errorText: SettingLocaleKeys.errorsSupporterInvalid.tr(),
        ),
        ConfirmValidator(
          errorText: SettingLocaleKeys.errorsSupporterDuplicate.tr(),
          isMatchList: true,
          matchStringList: otherSupporterMails.map((email) => email.trim().toLowerCase()).toList(),
        ),
      ]);

  static MultiValidator confirmSupporterEmailValidator({
  required String supporterEmail,
}) =>
    MultiValidator([
      RequiredValidator(
        errorText: SettingLocaleKeys.errorsRequired.tr(),
      ),
      ConfirmValidator(
        errorText: SettingLocaleKeys.errorsSupporterMatch.tr(),
        matchString: supporterEmail.trim().toLowerCase(),
      ),
    ]);
    static String getNormalizedEmail(TextEditingController controller) {
    return controller.text.trim().toLowerCase();
  }
}
