import 'package:form_field_validator/form_field_validator.dart';

class ConfirmValidator extends TextFieldValidator {
  ConfirmValidator({
    required String errorText,
    this.matchString,
    this.matchStringList,
    this.isMatchList = false,
  }) : super(errorText);

  String? matchString;
  List<String>? matchStringList;
  bool isMatchList;

  @override
  bool get ignoreEmptyValues => true;

  @override
  bool isValid(String? value) {
    if (value == null) return false;

    return true;
  }

  @override
  String? call(String? value) {
    if (isMatchList) {
      if (matchStringList?.contains(value) ?? false) {
        return errorText;
      }
    } else if (value != matchString) {
      return errorText;
    }
    return null;
  }
}
