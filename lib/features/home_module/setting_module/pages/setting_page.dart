import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/cubit/setting_cubit.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/keys/setting_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/validations/setting_validations.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/widgets/setting_pannel_widget.dart';
import 'package:breakingfree_v2/features/home_module/setting_module/widgets/supporter_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/features/authentication_module/language_module/widgets/dropdown_selector.dart';

class SettingPage extends StatelessWidget {
  SettingPage({super.key});

  final nameKey = GlobalKey<FormState>();
  final supporterKey = GlobalKey<FormState>();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SettingCubit(),
      child: BlocBuilder<SettingCubit, SettingState>(
        builder: (context, state) {
          final ref = context.read<SettingCubit>();
          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, result) {
              if (!didPop) {
                AppNavigation.nextScreen(context, MyDiagramPage());
              }
            },
            child: ValueListenableBuilder(
              valueListenable: ref.infoAudioUrl,
              builder: (context, value, child) {
                return ValueListenableBuilder(
                  valueListenable: ref.isENLanguageLoading,
                  builder: (context, isENLanguageLoading, child) {
                    return AppScaffold(
                      scaffoldKey: _scaffoldKey,
                      drawer: AppDrawer(scaffoldKey: _scaffoldKey),
                      isAudioPanelVisible: ref.isAudioPannelVisible,
                      infoAudioUrl: ref.infoAudioUrl,
                      appBar: CommonAppBar(
                        onPrefixTap: () {
                          _scaffoldKey.currentState?.openDrawer();
                        },
                        onSuffixTap: () {
                          if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                            ref.isAudioPannelVisible.value = !ref.isAudioPannelVisible.value;
                          }
                        },
                      ),
                      body: AppLoader(
                        isShowLoader: isENLanguageLoading,
                        child: ColoredBox(
                          color: context.themeColors.whiteColor,
                          child: SafeArea(
                            child: Padding(
                              padding: EdgeInsets.only(right: AppSize.w4),
                              child: CustomRawScrollbar(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                  child: SingleChildScrollView(
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(vertical: AppSize.w24),
                                      child: Column(
                                        children: [
                                          AppTextWidget(
                                            CoreLocaleKeys.sideMenuSettings.tr(),
                                            style: context.textTheme.titleLarge?.copyWith(
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          SpaceV(AppSize.h10),
                                          const CustomDivider(),
                                          Visibility(
                                            visible: (Injector.instance<AppDB>().langugaeModel?.languages?.length ?? 0) > 1,
                                            child: Column(
                                              children: [
                                                SettingPannelWidget(
                                                  heading: SettingLocaleKeys.chooseLanguage.tr(),
                                                  isShowInfo: false,
                                                  isShowUpdate: false,
                                                  textFields: [
                                                    DropdownSelector(
                                                      onSelected: (selectedLabel) async {
                                                        // Log the selected value and corresponding language code
                                                        'Selected Language: $selectedLabel'.logD;
                                                        ref.isENLanguageLoading.value = true;
                                                        final selectedLanguage = Injector.instance<AppDB>()
                                                            .langugaeModel
                                                            ?.languages
                                                            ?.firstWhere(
                                                              (e) => e.label == selectedLabel,
                                                        );
                                  
                                                        if (selectedLanguage != null) {
                                                          // Log the value of the selected language
                                                          'Language Value: ${selectedLanguage.value}'.logD;
                                  
                                                          // Parse language and country code from the selected value (e.g., "en-US" to "en" and "US")
                                                          final localeParts = selectedLanguage.value?.split(
                                                            '-',
                                                          );
                                                          if (localeParts != null &&
                                                              localeParts.length == 2) {
                                                            final locale = Locale(
                                                              localeParts[0],
                                                              localeParts[1],
                                                            );
                                  
                                                            // Set the new locale
                                                            await context.setLocale(
                                                              locale,
                                                            );
                                                            final data1 = locale.toString().replaceAll(
                                                              '_',
                                                              '-',
                                                            );
                                                            Injector.instance<AppDB>().selectedLangugae = data1;
                                                            await ref.userLanguageApi(data1);
                                                            'Locale changed to: $locale'.logD;
                                                          } else {
                                                            'Invalid language code format: ${selectedLanguage.value}'
                                                                .logD;
                                                          }
                                                          ref.isENLanguageLoading.value = false;
                                                        }
                                                      },
                                  
                                                      width: MediaQuery.of(context).size.width * 0.6,
                                                      selectedOption: Injector.instance<AppDB>()
                                                          .langugaeModel
                                                          ?.languages
                                                          ?.firstWhere(
                                                            (element) =>
                                                                element.value ==
                                                                Injector.instance<AppDB>().selectedLangugae,
                                                          )
                                                          .label,
                                                      options: Injector.instance<AppDB>()
                                                              .langugaeModel
                                                              ?.languages
                                                              ?.map(
                                                                (e) => e.label,
                                                              )
                                                              .toList() ??
                                                          [],
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          ValueListenableBuilder(
                                            valueListenable: ref.isNameInfoVisible,
                                            builder: (context, isNameInfoVisibleV, _) {
                                              return Form(
                                                key: nameKey,
                                                child: SettingPannelWidget(
                                                  isError: ref.isNameValid.value,
                                                  name: 'name',
                                                  heading: SettingLocaleKeys.nameTitle.tr(),
                                                  isLoading: state is LoadingSettingState,
                                                  onUpdateTap: () {
                                                    ref.nameController.text.logD;
                                                    'onTap'.logD;
                                  
                                                    if (nameKey.currentState!.validate()) {
                                                      ref.nameController.text.logD;
                                                      ref.changeUserNameApi(nameText: ref.nameController.text);
                                                    }
                                                  },
                                                  onInfoTap: () {
                                                    ref.isNameInfoVisible.value = !ref.isNameInfoVisible.value;
                                  
                                                    if (ref.isNameInfoVisible.value) {
                                                      ref.infoAudioUrl.value = SettingLocaleKeys.nameInfoAudio.tr();
                                                    } else if (ref.infoAudioUrl.value ==
                                                        SettingLocaleKeys.nameInfoAudio.tr()) {
                                                      ref.infoAudioUrl.value = null;
                                                    }
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    visible: isNameInfoVisibleV,
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w8,
                                                      right: AppSize.w8,
                                                    ),                                                  // showShadow: false,
                                                    onCloseTap: () {
                                                      ref.isNameInfoVisible.value = false;
                                                      if (ref.infoAudioUrl.value == SettingLocaleKeys.nameInfoAudio.tr()) {
                                                        ref.infoAudioUrl.value = null;
                                                      }
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      SettingLocaleKeys.nameInfoText,
                                                      context,
                                                    ) as List)
                                                        .join('\n\n'),
                                                  ),
                                                  textFields: [
                                                    CustomOutlinedTextfield(
                                                      controller: ref.nameController,
                                                      validator: (value) =>
                                                          SettingValidations.nameValidator().call(value?.trim()),
                                                      labelText: SettingLocaleKeys.nameLabel.tr(),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                          SpaceV(AppSize.h14),
                                          const CustomDivider(color: Color(0x1F000000)),
                                          SpaceV(AppSize.h10),
                                          ValueListenableBuilder(
                                            valueListenable: ref.isEmailInfoVisible,
                                            builder: (context, isEmailInfoVisibleV, _) {
                                              return Form(
                                                key: ref.emailKey,
                                                child: SettingPannelWidget(
                                                  isError: ref.isEmailValid.value,
                                                  name: 'email',
                                                  heading: SettingLocaleKeys.emailTitle.tr(),
                                                  isLoading: state is LoadingEmailSettingState,
                                                  onUpdateTap: () async {
                                                    // if (ref.emailKey.currentState!.validate()) {
                                                    if(ref.validateEmailFields()) {
                                                      ref.currentEmailController.text.logD;
                                                      ref.newEmailController.text.logD;
                                                      await ref.changeUserEmailApi(
                                                        currentEmailText: ref.currentEmailController.text,
                                                        newEmailText: ref.newEmailController.text,
                                                      );
                                                    }
                                                  },
                                                  onInfoTap: () {
                                                    ref.isEmailInfoVisible.value = !ref.isEmailInfoVisible.value;
                                  
                                                    if (ref.isEmailInfoVisible.value) {
                                                      ref.infoAudioUrl.value = SettingLocaleKeys.emailInfoAudio.tr();
                                                    } else if (ref.infoAudioUrl.value ==
                                                        SettingLocaleKeys.emailInfoAudio.tr()) {
                                                      ref.infoAudioUrl.value = null;
                                                    }
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    visible: isEmailInfoVisibleV,
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w8,
                                                      right: AppSize.w8,
                                                    ),
                                                    onCloseTap: () {
                                                      ref.isEmailInfoVisible.value = false;
                                                      if (ref.infoAudioUrl.value == SettingLocaleKeys.emailInfoAudio.tr()) {
                                                        ref.infoAudioUrl.value = null;
                                                      }
                                                    },
                                                    bodyText: SettingLocaleKeys.emailInfoText.tr(),
                                                  ),
                                                  textFields: [
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.currentEmailError,
                                                      builder: (context,currentEmailError,child) {
                                                        return Column(
                                                          children: [
                                                            CustomOutlinedTextfield(
                                                               autovalidateMode: AutovalidateMode.onUserInteraction,
                                                              controller: ref.currentEmailController,
                                                              textAction: TextInputAction.next,
                                                              labelText: SettingLocaleKeys.emailCurrentEmailLabel.tr(),
                                                              isError: currentEmailError != '' ,
                                                              onChanged: (_){
                                                                 ref.currentEmailValid();
                                                              },
                                                              // validator: (value) => SettingValidations.emailValidator(
                                                              //   invalidEmailError: SettingLocaleKeys.errorsCurrentEmailInvalid.tr(),
                                                              // ).call(value?.trim()),
                                                            ),
                                                            if(currentEmailError != '')
                                                              CustomErrorWidget(errorMessgaeText: ref.currentEmailError.value)
                                                            else if(state is EmailIncorrectState && ref.validateEmailFields())
                                                              CustomErrorWidget(errorMessgaeText:state.incorrectMessage)
                                                            else
                                                              const SizedBox()
                                  
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.newEmailError,
                                                      builder: (context,newEmailError,child) {
                                                        return Column(
                                                          children: [
                                                            CustomOutlinedTextfield(
                                                              autovalidateMode: AutovalidateMode.onUserInteraction,
                                                              controller: ref.newEmailController,
                                                              textAction: TextInputAction.next,
                                                              labelText: SettingLocaleKeys.emailNewEmailLabel.tr(),
                                                              isError: newEmailError != '',
                                                              onChanged: (_){
                                                                 ref.newEmailValid();
                                                              },
                                                              // validator: (value) => SettingValidations.emailValidator(
                                                              //   invalidEmailError: SettingLocaleKeys.errorsNewEmailInvalid.tr(),
                                                              // ).call(value?.trim()),
                                                            ),
                                                            if(newEmailError != '')
                                                              CustomErrorWidget(errorMessgaeText: ref.newEmailError.value)
                                                            else if(state is EmailExistState && ref.validateEmailFields())
                                                              CustomErrorWidget(errorMessgaeText:state.emailExistMessage)
                                                            else
                                                              const SizedBox()
                                  
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.confirmEmailError,
                                                      builder: (context,confirmEmailError,child) {
                                                        return Column(
                                                          children: [
                                                            CustomOutlinedTextfield(
                                                              controller: ref.confirmEmailController,
                                                              textAction: TextInputAction.done,
                                                              labelText: SettingLocaleKeys.emailConfirmNewEmailLabel.tr(),
                                                              isError:confirmEmailError != '' ,
                                                              onChanged: (_){
                                                                ref.confirmEmailValid();
                                                              },
                                                              // validator: (value) => SettingValidations.confirmEmailValidator(
                                                              //   notMatchError: SettingLocaleKeys.errorsEmailMatch.tr(),
                                                              //   email: ref.newEmailController.text.trim(),
                                                              // ).call(value?.trim()),
                                                            ),
                                                            if(confirmEmailError != '')
                                                              CustomErrorWidget(errorMessgaeText: ref.confirmEmailError.value)
                                  
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                   /* ValueListenableBuilder(
                                                        valueListenable: ref.confirmEmailError,
                                                        builder: (context,confirmEmailError,child){
                                                          if(state is EmailIncorrectState && ref.validateEmailFields()) {
                                                            return CustomErrorWidget(errorMessgaeText:state.incorrectMessage);
                                                          }else if(state is EmailExistState && ref.validateEmailFields()){
                                                            return CustomErrorWidget(errorMessgaeText:state.emailExistMessage);
                                                          }else{
                                                            return const SizedBox();
                                                          }
                                                        })*/
                                  
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                          SpaceV(AppSize.h14),
                                          const CustomDivider(color: Color(0x1F000000)),
                                          SpaceV(AppSize.h10),
                                          ValueListenableBuilder(
                                            valueListenable: ref.isPasswordInfoVisible,
                                            builder: (context, isPasswordInfoVisibleV, _) {
                                              return Form(
                                                key: ref.passwordKey,
                                                child: SettingPannelWidget(
                                                  isError: ref.isPasswordValid.value,
                                                  name: 'password',
                                                  isLoading: state is LoadingPasswordSettingState,
                                                  heading: SettingLocaleKeys.passwordTitle.tr(),
                                                  onUpdateTap: () {
                                                    // if (ref.passwordKey.currentState!.validate()) {
                                                    if(ref.validatePasswordFields()) {
                                                      ref.currentPasswordController.text.logD;
                                                      ref.newPasswordlController.text.logD;
                                                      ref.changeUserPasswordApi(
                                                        currentPassword: ref.currentPasswordController.text,
                                                        newPassword: ref.newPasswordlController.text,
                                                      );
                                                    }
                                                  },
                                                  onInfoTap: () {
                                                    ref.isPasswordInfoVisible.value = !ref.isPasswordInfoVisible.value;
                                                    if (ref.isPasswordInfoVisible.value) {
                                                      ref.infoAudioUrl.value = SettingLocaleKeys.passwordInfoAudio.tr();
                                                    } else if (ref.infoAudioUrl.value ==
                                                        SettingLocaleKeys.passwordInfoAudio.tr()) {
                                                      ref.infoAudioUrl.value = null;
                                                    }
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    visible: isPasswordInfoVisibleV,
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w8,
                                                      right: AppSize.w8,
                                                    ),
                                                    onCloseTap: () {
                                                      ref.isPasswordInfoVisible.value = false;
                                                      if (ref.infoAudioUrl.value ==
                                                          SettingLocaleKeys.passwordInfoAudio.tr()) {
                                                        ref.infoAudioUrl.value = null;
                                                      }
                                                    },
                                                    bodyText: SettingLocaleKeys.passwordInfoText.tr(),
                                                  ),
                                                  textFields: [
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.currentPasswordError,
                                                      builder: (context,currentPasswordError,child) {
                                                        return Column(
                                                          children: [
                                                            ValueListenableBuilder(
                                                              valueListenable: ref.showPassword,
                                                              builder: (context, value, child) {
                                                                return CustomOutlinedTextfield(
                                                                  //obscureText: true,
                                                                  obscureText: !ref.showPassword.value,
                                                                  autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                  suffixIcon: GestureDetector(
                                                                    onTap: ref.togglevisibility,
                                                                    child: Icon(
                                                                      ref.showPassword.value
                                                                          ? Icons.visibility
                                                                          : Icons.visibility_off,
                                                                      color: context.themeColors.textfieldTextColor,
                                                                      size: AppSize.sp18,
                                                                    ),
                                                                  ),
                                                                  controller: ref.currentPasswordController,
                                                                  textAction: TextInputAction.next,
                                                                  isError:currentPasswordError != '' ,
                                                                  labelText: SettingLocaleKeys.passwordCurrentPasswordLabel.tr(),
                                                                  onChanged: (_){
                                                                    ref.currentPasswordValid();
                                                                  },
                                                                  // validator: (value) =>
                                                                  //     SettingValidations.passwordValidator().call(value?.trim()),
                                                                );
                                                              },
                                                            ),
                                                            if(currentPasswordError != '')
                                                              CustomErrorWidget(errorMessgaeText: currentPasswordError)
                                                            else if(state is IncorrectPasswordState && ref.validatePasswordFields())
                                                              CustomErrorWidget(errorMessgaeText: state.passwordMessage)
                                                            else
                                                              const SizedBox()
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                        valueListenable: ref.newPasswordError,
                                                      builder: (context,newPasswordError,child) {
                                                        return Column(
                                                          children: [
                                                            ValueListenableBuilder(
                                                              valueListenable: ref.showNewPassword,
                                                              builder: (context, value, child) {
                                                                return CustomOutlinedTextfield(
                                                                  //obscureText: true,
                                                                  controller: ref.newPasswordlController,
                                                                  textAction: TextInputAction.next,
                                                                  isError: newPasswordError != '',
                                                                  obscureText: !ref.showNewPassword.value,
                                                                  autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                  suffixIcon: GestureDetector(
                                                                    onTap: ref.toggleNewPasswordvisibility,
                                                                    child: Icon(
                                                                      ref.showNewPassword.value
                                                                          ? Icons.visibility
                                                                          : Icons.visibility_off,
                                                                      color: context.themeColors.textfieldTextColor,
                                                                      size: AppSize.sp18,
                                                                    ),
                                                                  ),
                                                                  labelText: SettingLocaleKeys.passwordNewPasswordLabel.tr(),
                                                                  onChanged: (_){
                                                                    ref.newPasswordValid();
                                                                  },
                                                                  // validator: (value) =>
                                                                  //     SettingValidations.passwordValidator().call(value),
                                                                );
                                                              },
                                                            ),
                                                            if(newPasswordError != '')
                                                              CustomErrorWidget(errorMessgaeText: newPasswordError)
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.confirmPasswordError,
                                                      builder: (context,confirmPasswordError,child) {
                                                        return Column(
                                                          children: [
                                                            ValueListenableBuilder(
                                                              valueListenable: ref.showConfirmNewPassword,
                                                              builder: (context, value, child) {
                                                                return CustomOutlinedTextfield(
                                                                  //obscureText: true,
                                                                  obscureText: !ref.showConfirmNewPassword.value,
                                                                  controller: ref.confirmPasswordController,
                                                                  isError: confirmPasswordError != '' ,
                                                                  autovalidateMode: AutovalidateMode.onUserInteraction,
                                                                  textAction: TextInputAction.done,
                                                                  suffixIcon: GestureDetector(
                                                                    onTap: ref.toggleConfirmNewPasswordvisibility,
                                                                    child: Icon(
                                                                      ref.showConfirmNewPassword.value
                                                                          ? Icons.visibility
                                                                          : Icons.visibility_off,
                                                                      color: context.themeColors.textfieldTextColor,
                                                                      size: AppSize.sp18,
                                                                    ),
                                                                  ),
                                                                  labelText: SettingLocaleKeys.passwordConfirmNewPasswordLabel.tr(),
                                                                  onChanged: (_){
                                                                    ref.confirmPasswordValid();
                                                                  },
                                                                  // validator: (value) => SettingValidations.confirmPasswordValidator(
                                                                  //   notMatchError: SettingLocaleKeys.errorsPasswordMatch.tr(),
                                                                  //   password: ref.newPasswordlController.text,
                                                                  // ).call(value),
                                                                );
                                                              },
                                                            ),
                                                            if(confirmPasswordError != '')
                                                              CustomErrorWidget(errorMessgaeText: confirmPasswordError)
                                                          ],
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                          SpaceV(AppSize.h14),
                                          const CustomDivider(color: Color(0x1F000000)),
                                          SpaceV(AppSize.h10),
                                          ValueListenableBuilder(
                                            valueListenable: ref.isSupporterInfoVisible,
                                            builder: (context, isSupporterInfoVisibleV, _) {
                                              return Form(
                                                key: supporterKey,
                                                child: SettingPannelWidget(
                                                  isError: ref.isMySupporterValid.value,
                                                  name: 'supporters',
                                                  isLoading: state is LoadingSuppoterSettingState,
                                                  heading: SettingLocaleKeys.supportersTitle.tr(),
                                                  onUpdateTap: () {
                                                    if(ref.supporter1FieldState.value == SupporterFieldState.addBtn
                                                        && ref.supporter2FieldState.value == SupporterFieldState.addBtn
                                                        && ref.supporter3FieldState.value == SupporterFieldState.addBtn ){
                                                      ref.changeUserSupporterAPI(
                                                        supporterList: [
                                                          ref.firstSupporterEmailController.text,
                                                          ref.secondSupporterEmailController.text,
                                                          ref.thirdSupporterEmailController.text,
                                                        ],
                                                      );
                                                    }else if(ref.validateSupporterEmailFields()){
                                                      ref.changeUserSupporterAPI(
                                                        supporterList: [
                                                          ref.firstSupporterEmailController.text,
                                                          ref.secondSupporterEmailController.text,
                                                          ref.thirdSupporterEmailController.text,
                                                        ],
                                                      );
                                                    }
                                                    // if (supporterKey.currentState!.validate()) {
                                                    //   ref.firstSupporterEmailController.text.logD;
                                                    //   ref.secondSupporterEmailController.text.logD;
                                                    //   ref.thirdSupporterEmailController.text.logD;
                                                    //   ref.changeUserSupporterAPI(
                                                    //     supporterList: [
                                                    //       ref.firstSupporterEmailController.text,
                                                    //       ref.secondSupporterEmailController.text,
                                                    //       ref.thirdSupporterEmailController.text,
                                                    //     ],
                                                    //   );
                                                    // }
                                                  },
                                                  onInfoTap: () {
                                                    ref.isSupporterInfoVisible.value = !ref.isSupporterInfoVisible.value;
                                  
                                                    if (ref.isSupporterInfoVisible.value) {
                                                      ref.infoAudioUrl.value = SettingLocaleKeys.supportersInfoAudio.tr();
                                                    } else if (ref.infoAudioUrl.value ==
                                                        SettingLocaleKeys.supportersInfoAudio.tr()) {
                                                      ref.infoAudioUrl.value = null;
                                                    }
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    visible: isSupporterInfoVisibleV,
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w8,
                                                      right: AppSize.w8,
                                                    ),
                                                    onCloseTap: () {
                                                      ref.isSupporterInfoVisible.value = false;
                                                      if (ref.infoAudioUrl.value ==
                                                          SettingLocaleKeys.supportersInfoAudio.tr()) {
                                                        ref.infoAudioUrl.value = null;
                                                      }
                                                    },
                                                    bodyText: (DynamicAssetLoader.getNestedValue(
                                                      SettingLocaleKeys.supportersInfoText,
                                                      context,
                                                    ) as List)
                                                        .join('\n\n'),
                                                  ),
                                                  textFields: [
                                                    ValueListenableBuilder(
                                                    valueListenable: ref.supporter1FieldState,
                                                    builder: (context, supporter1FieldStateV, _) {
                                                      return SupporterWidget(
                                                        onDeleteYesTap: () {
                                                          ref.firstSupporterEmailController.clear();
                                                          ref.supporter1FieldState.value = SupporterFieldState.addBtn;
                                                        },
                                                        // emailValidator: SettingValidations.supporterEmailValidator(
                                                        //   otherSupporterMails: [
                                                        //     ref.secondSupporterEmailController.text,
                                                        //     ref.thirdSupporterEmailController.text,
                                                        //   ],
                                                        // ).call,
                                                        // confirmValidator: SettingValidations.confirmSupporterEmailValidator(
                                                        //   supporterEmail: ref.firstSupporterEmailController.text,
                                                        // ).call,
                                                        onChanged: (val){
                                                          ref.firstSupportersEmailValid();
                                                        },
                                                        onConfirmChanged: (val){
                                                          ref.firstSupportersConfirmEmailValid();
                                                        },
                                                        emailController: ref.firstSupporterEmailController,
                                                        confirmEmailController: ref.firstSupporterConfirmEmailController,
                                                        onSuffixDeleteTap: () {
                                                          ref.supporter1FieldState.value = SupporterFieldState.confirmDelete;
                                                        },
                                                        confirmEmailLabelText: removeCurlyBraces(
                                                          SettingLocaleKeys.supportersLabelsConfirmEmail.tr(
                                                            namedArgs: {'number': 'first'},
                                                          ),
                                                        ),
                                                        onCloseDialogueTap: () {
                                                          ref.supporter1FieldState.value = SupporterFieldState.savedEmail;
                                                        },
                                                        emailLabelText: removeCurlyBraces(
                                                          SettingLocaleKeys.supportersLabelsEmail.tr(
                                                            namedArgs: {'number': 'first'},
                                                          ),
                                                        ),
                                                        supporterFieldState: supporter1FieldStateV,
                                                        addBtnName: removeCurlyBraces(
                                                          SettingLocaleKeys.supportersLabelsAdd.tr(
                                                            namedArgs: {'number': 'first'},
                                                          ),
                                                        ),
                                                        onAddTap: () {
                                                          ref.supporter1FieldState.value = SupporterFieldState.enterEmail;
                                                        },
                                                        state: state,
                                                        ref: ref,
                                                        emailError: ref.firstSupporterEmailError,
                                                        confirmEmailError: ref.firstSupporterConfirmEmailError,
                                                      );
                                                    },
                                                  ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.supporter2FieldState,
                                                      builder: (context, supporter2FieldStateV, _) {
                                                        return SupporterWidget(
                                                          emailValidator: (value) =>
                                                              SettingValidations.supporterEmailValidator(
                                                            otherSupporterMails: [
                                                              ref.firstSupporterEmailController.text,
                                                              ref.thirdSupporterEmailController.text,
                                                            ],
                                                          ).call(value?.trim()),
                                                          onDeleteYesTap: () {
                                                            ref.secondSupporterEmailController.clear();
                                                            ref.supporter2FieldState.value = SupporterFieldState.addBtn;
                                                          },
                                                          confirmValidator: (value) =>
                                                              SettingValidations.confirmSupporterEmailValidator(
                                                            supporterEmail: ref.secondSupporterEmailController.text,
                                                          ).call(value?.trim()),
                                                          onChanged: (val){
                                                            ref.secondSupportersEmailValid();
                                                          },
                                                          onConfirmChanged: (val){
                                                            ref.secondSupportersConfirmEmailValid();
                                                          },
                                                          onSuffixDeleteTap: () {
                                                            ref.supporter2FieldState.value =
                                                                SupporterFieldState.confirmDelete;
                                                          },
                                                          confirmEmailLabelText: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsConfirmEmail.tr(
                                                              namedArgs: {'number': 'second'},
                                                            ),
                                                          ),
                                                          onCloseDialogueTap: () {
                                                            ref.supporter2FieldState.value = SupporterFieldState.savedEmail;
                                                          },
                                                          emailController: ref.secondSupporterEmailController,
                                                          confirmEmailController: ref.secondSupporterConfirmEmailController,
                                                          emailLabelText: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsEmail.tr(
                                                              namedArgs: {'number': 'second'},
                                                            ),
                                                          ),
                                                          supporterFieldState: supporter2FieldStateV,
                                                          addBtnName: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsAdd.tr(
                                                              namedArgs: {'number': 'second'},
                                                            ),
                                                          ),
                                                          onAddTap: () {
                                                            ref.supporter2FieldState.value = SupporterFieldState.enterEmail;
                                                          },
                                                          state: state,
                                                          ref: ref,
                                                          emailError: ref.secondSupporterEmailError,
                                                          confirmEmailError: ref.secondSupporterConfirmEmailError,
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h16),
                                                    ValueListenableBuilder(
                                                      valueListenable: ref.supporter3FieldState,
                                                      builder: (context, supporter3FieldStateV, _) {
                                                        return SupporterWidget(
                                                          emailValidator: (value) =>
                                                              SettingValidations.supporterEmailValidator(
                                                            otherSupporterMails: [
                                                              ref.firstSupporterEmailController.text,
                                                              ref.secondSupporterEmailController.text,
                                                            ],
                                                          ).call(value?.trim()),
                                                          onDeleteYesTap: () {
                                                            ref.thirdSupporterEmailController.clear();
                                                            ref.supporter3FieldState.value = SupporterFieldState.addBtn;
                                                          },
                                                          confirmValidator: (value) =>
                                                              SettingValidations.confirmSupporterEmailValidator(
                                                            supporterEmail: ref.thirdSupporterEmailController.text,
                                                          ).call(value?.trim()),
                                                          onChanged: (val){
                                                            ref.thirdSupportersEmailValid();
                                                          },
                                                          onConfirmChanged: (val){
                                                            ref.thirdSupportersConfirmEmailValid();
                                                          },
                                                          emailController: ref.thirdSupporterEmailController,
                                                          confirmEmailController: ref.thirdSupporterConfirmEmailController,
                                                          onSuffixDeleteTap: () {
                                                            ref.supporter3FieldState.value =
                                                                SupporterFieldState.confirmDelete;
                                                          },
                                                          confirmEmailLabelText: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsConfirmEmail.tr(
                                                              namedArgs: {
                                                                'number': 'third',
                                                              },
                                                            ),
                                                          ),
                                                          onCloseDialogueTap: () {
                                                            ref.supporter3FieldState.value = SupporterFieldState.savedEmail;
                                                          },
                                                          emailLabelText: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsEmail.tr(
                                                              namedArgs: {
                                                                'number': 'third',
                                                              },
                                                            ),
                                                          ),
                                                          supporterFieldState: supporter3FieldStateV,
                                                          addBtnName: removeCurlyBraces(
                                                            SettingLocaleKeys.supportersLabelsAdd.tr(
                                                              namedArgs: {
                                                                'number': 'third',
                                                              },
                                                            ),
                                                          ),
                                                          onAddTap: () {
                                                            ref.supporter3FieldState.value = SupporterFieldState.enterEmail;
                                                          },
                                                          state: state,
                                                          ref: ref,
                                                          emailError: ref.thirdSupporterEmailError,
                                                          confirmEmailError: ref.thirdSupporterConfirmEmailError,
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  String removeCurlyBraces(String input) {
    return input.replaceAll(RegExp('[{}]'), '');
  }
}
