
class SettingLocaleKeys {
  // name keys
  static const nameTitle = 'settings.name.title';
  static const nameLabel = 'settings.name.label';
  static const nameSuccess = 'settings.name.success';
  static const nameInfoText = 'settings.name.info.text';
  static const nameInfoAudio = 'settings.name.info.audio';
  static const chooseLanguage = 'app.appLocaleSetup.chooseLanguage';

  // email keys
  static const emailTitle = 'settings.email.title';
  static const emailCurrentEmailLabel = 'settings.email.currentEmailLabel';
  static const emailNewEmailLabel = 'settings.email.newEmailLabel';
  static const emailConfirmNewEmailLabel = 'settings.email.confirmNewEmailLabel';
  static const emailSuccess = 'settings.email.success';
  static const emailInfoText = 'settings.email.info.text';
  static const emailInfoAudio = 'settings.email.info.audio';

  // password keys
  static const passwordTitle = 'settings.password.title';
  static const passwordCurrentPasswordLabel = 'settings.password.currentPasswordLabel';
  static const passwordNewPasswordLabel = 'settings.password.newPasswordLabel';
  static const passwordConfirmNewPasswordLabel = 'settings.password.confirmNewPasswordLabel';
  static const passwordSuccess = 'settings.password.success';
  static const passwordInfoText = 'settings.password.info.text';
  static const passwordInfoAudio = 'settings.password.info.audio';

  // supporters keys
  static const supportersTitle = 'settings.supporters.title';
  static const supportersNumbersFirst = 'settings.supporters.numbers.first';
  static const supportersNumbersSecond = 'settings.supporters.numbers.second';
  static const supportersNumbersThird = 'settings.supporters.numbers.third';
  static const supportersLabelsCurrent = 'settings.supporters.labels.current';
  static const supportersLabelsAdd = 'settings.supporters.labels.add';
  static const supportersLabelsEmail = 'settings.supporters.labels.email';
  static const supportersLabelsConfirmEmail = 'settings.supporters.labels.confirmEmail';
  static const supportersLabelsRemove = 'settings.supporters.labels.remove';
  static const supportersRemoveTitle = 'settings.supporters.remove.title';
  static const supportersRemoveText = 'settings.supporters.remove.text';
  static const supportersSuccess = 'settings.supporters.success';
  static const supportersInfoText = 'settings.supporters.info.text';
  static const supportersInfoAudio = 'settings.supporters.info.audio';

  // buttons keys
  static const buttonsAddNew = 'settings.buttons.addNew';
  static const buttonsUpdate = 'settings.buttons.update';
  static const buttonsSave = 'settings.buttons.save';
  static const buttonsYes = 'settings.buttons.yes';
  static const buttonsNo = 'settings.buttons.no';

  // errors keys
  static const errorsNameMaxLength = 'settings.errors.nameMaxLength';
  static const errorsRequired = 'settings.errors.required';
  static const errorsCurrentEmailInvalid = 'settings.errors.currentEmailInvalid';
  static const errorsNewEmailInvalid = 'settings.errors.newEmailInvalid';
  static const errorsEmailMatch = 'settings.errors.emailMatch';
  static const errorsCurrentEmailIncorrect = 'settings.errors.currentEmailIncorrect';
  static const errorsEmailExists = 'settings.errors.emailExists';
  static const errorsPasswordMatch = 'settings.errors.passwordMatch';
  static const errorsPasswordMinLength = 'settings.errors.passwordMinLength';
  static const errorsPasswordMaxLength = 'settings.errors.passwordMaxLength';
  static const errorsCurrentPasswordIncorrect = 'settings.errors.currentPasswordIncorrect';
  static const errorsPasswordInsecure = 'settings.errors.passwordInsecure';
  static const errorsSupporterMatch = 'settings.errors.supporterMatch';
  static const errorsSupporterInvalid = 'settings.errors.supporterInvalid';
  static const errorsSupporterDuplicate = 'settings.errors.supporterDuplicate';
  static const errorsSupporterUnsubscribed = 'settings.errors.supporterUnsubscribed';
  static const errorsSupporterYourself = 'settings.errors.supporterYourself';
  static const errorsInternal = 'settings.errors.internal';
}
