import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/pages/situation_action_map_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_impact_action_page/emotional_impact_action_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/pages/my_lifestyle_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/pages/unhelpful_behaviour_calendar_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/information_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/keys/toolkit_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/container_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/difficautl_Situtation_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/emotional_impact_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/lifestyle_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/negative_thoughts_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/physical_sensation_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/unhelpful_behaviour_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';

class MyRecoveryToolkitPage extends StatefulWidget {
  const MyRecoveryToolkitPage({super.key});

  @override
  State<MyRecoveryToolkitPage> createState() => _MyRecoveryToolkitPageState();
}

class _MyRecoveryToolkitPageState extends State<MyRecoveryToolkitPage> {
  @override
  Widget build(BuildContext context) {
    Injector.instance<AppDB>().userModel?.user.strategies?.dsAs?.length.logD;
    return BlocProvider(
      create: (context) => MyRecoveryToolKitCubit()
        ..getDiagramNodeColor()
        ..getSituattionUserData(context)
        ..getNegativeThoughtUserData(context)
        ..getPhysicalSensationUserData(context)
        ..getEmotionalUserData(context)
        ..getPhysicalSensationUserData(context)
        ..getLifeStyleUserData(context)
        ..getUnhelpfulBehaviourUserData(context),
      child: BlocBuilder<MyRecoveryToolKitCubit, MyRecoveryToolKitState>(
        builder: (ctx, state) {
          //Todo: Added static text in whole screen
          final ref = ctx.read<MyRecoveryToolKitCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                resizeToAvoidBottomInset: false,
                scaffoldKey: ref.scaffoldKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                infoAudioUrl: ref.infoAudioUrl,
                drawer: AppDrawer(scaffoldKey: ref.scaffoldKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    ref.scaffoldKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: ColoredBox(
                  color: context.themeColors.whiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: AppSize.w4),
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              '/// ref.infoAudioUrl = ${ref.infoAudioUrl.value}'.logD;
                              return CustomRawScrollbar(
                                child: SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20,
                                        top: AppSize.h24,
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              AppTextWidget(
                                                ToolKitLocaleKeys.titleResources.tr(),
                                                style: context.textTheme.titleSmall?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              SpaceV(AppSize.h6),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  CustomIconButton(
                                                    assetIcon: Assets.icons.infoIcon,
                                                    size: AppSize.h28,
                                                    onTap: () {
                                                      ref.infoAudioUrl.value =
                                                          ToolKitLocaleKeys.infoPanelInformationAudio.tr();
                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        ToolKitLocaleKeys.infoPanelInformationText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');
                                                      ref.headerInfoText.value =
                                                          (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                  ref.headerInfoText.value == info)
                                                              ? null
                                                              : info;
                                                    },
                                                  ),
                                                  CustomIconButton(
                                                    iconPath: Assets.icons.idea,
                                                    size: AppSize.h28,
                                                    onTap: () {
                                                      ref.infoAudioUrl.value =
                                                          ToolKitLocaleKeys.infoPanelLearnAudio.tr();
                                                      final info = (DynamicAssetLoader.getNestedValue(
                                                        ToolKitLocaleKeys.infoPanelLearnText,
                                                        context,
                                                      ) as List)
                                                          .join('<br/><br/>');
                                                      ref.headerInfoText.value =
                                                          (ref.headerInfoText.value.isNotEmptyAndNotNull &&
                                                                  ref.headerInfoText.value == info)
                                                              ? null
                                                              : info;
                                                    },
                                                  ),
                                                ],
                                              ),
                                              SpaceV(AppSize.h4),
                                              ValueListenableBuilder(
                                                valueListenable: ref.headerInfoText,
                                                builder: (context, headerPlanInfoTextV, _) {
                                                  if (headerPlanInfoTextV != null) {
                                                    return CustomInfoWidget(
                                                      customWidget: Column(
                                                        children: [
                                                          Html(
                                                            data: ref.headerInfoText.value ?? '',
                                                            style: {
                                                              'strong': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontWeight: FontWeight.bold,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                              'body': Style(
                                                                fontSize: FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      onCloseTap: () {
                                                        ref.headerInfoText.value = null;
                                                        ref.infoAudioUrl.value = null;
                                                        ref.isAudioPanelVisible.value = false;
                                                      },
                                                      visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerPlanInfoTextV,
                                                    );
                                                  } else {
                                                    return const SizedBox.shrink();
                                                  }
                                                },
                                              ),
                                              SpaceV(AppSize.h18),
                                              Divider(
                                                height: 1,
                                                color: context.themeColors.greyColor.withOpacity(.6),
                                              ),
                                              SpaceV(AppSize.h16),
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  // AppTextWidget(
                                                  //   'This is your recovery toolkit',
                                                  //   style: context.textTheme.titleSmall?.copyWith(),
                                                  // ),
                                                  // SpaceV(AppSize.h10),
                                                  // AppTextWidget(
                                                  //   'Here, you can see the six areas of your personal diagram, along with your most recent information summaries and action plans for each one.',
                                                  //   style: context.textTheme.titleSmall?.copyWith(),
                                                  // ),
                                                  SpaceV(AppSize.h10),
                                                  CustomContainer(
                                                    score: ref.dsScore,
                                                    visible: ref.showDSontainer,
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(context, const SituationActionMapPage());
                                                    },
                                                    onTap: () {
                                                      ref.showDSontainer.value = !ref.showDSontainer.value;
                                                    },
                                                    title: ToolKitLocaleKeys.areaDs.tr(),
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  DifficultSitutation(
                                                    ref: ref,
                                                  ),
                                                  SpaceV(AppSize.h24),

                                                  CustomContainer(
                                                    score: ref.ntScore,
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(
                                                          diagramState: MyDiagramStates.negativeThoughts,
                                                        ),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(context, const MyThoughtsPage());
                                                    },
                                                    visible: ref.showNTontainer,
                                                    onTap: () {
                                                      ref.showNTontainer.value = !ref.showNTontainer.value;
                                                    },
                                                    title: ToolKitLocaleKeys.areaNt.tr(),
                                                    color: context.themeColors.orangeColor,
                                                  ),

                                                  SpaceV(AppSize.h14),
                                                  NegativeThoughts(
                                                    ref: ref,
                                                    iconPath: Assets.icons.actionIcons.negativeThoughts,
                                                    iconInfoPath: Assets.icons.infoPage.negativeThoughts,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CustomContainer(
                                                    score: ref.eiScore,
                                                    visible: ref.showETcontainer,
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(
                                                          diagramState: MyDiagramStates.emotionalImpact,
                                                        ),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        EmotionalImpactActionPage(),
                                                      );
                                                    },
                                                    title: ToolKitLocaleKeys.areaEi.tr(),
                                                    onTap: () {
                                                      ref.showETcontainer.value = !ref.showETcontainer.value;
                                                    },
                                                    color: context.themeColors.orangeColor,
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  EmotionalImpactWidget(ref: ref),
                                                  SpaceV(AppSize.h24),
                                                  CustomContainer(
                                                    score: ref.psScore,
                                                    visible: ref.showpscontainer,
                                                    title: ToolKitLocaleKeys.areaPs.tr(),
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(
                                                          diagramState: MyDiagramStates.physicalSensations,
                                                        ),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        EmotionalImpactActionPage(
                                                          isPhysicalSensation: true,
                                                        ),
                                                      );
                                                    },
                                                    onTap: () {
                                                      ref.showpscontainer.value = !ref.showpscontainer.value;
                                                    },
                                                    color: context.themeColors.orangeColor,
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  PhysicalSensationWidget(ref: ref),
                                                  SpaceV(AppSize.h24),
                                                  CustomContainer(
                                                    score: ref.ubScore,
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(
                                                          diagramState: MyDiagramStates.unhelpfulBehaviours,
                                                        ),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        const UnhelpfulBehaviourCalendarPage(),
                                                      );
                                                    },
                                                    visible: ref.showUBcontainer,
                                                    title: ToolKitLocaleKeys.areaUb.tr(),
                                                    onTap: () {
                                                      ref.showUBcontainer.value = !ref.showUBcontainer.value;
                                                    },
                                                    color: context.themeColors.redColor,
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  UnhelpfulBehaviourWidget(ref: ref),
                                                  SpaceV(AppSize.h24),
                                                  CustomContainer(
                                                    score: ref.lsScore,
                                                    visible: ref.showLifeStylecontainer,
                                                    title: ToolKitLocaleKeys.areaLs.tr(),
                                                    onInfoButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        InformationPage(
                                                          diagramState: MyDiagramStates.lifestyle,
                                                        ),
                                                      );
                                                    },
                                                    onActionButtonTap: () {
                                                      AppNavigation.nextScreen(
                                                        context,
                                                        const MyLifestylePage(),
                                                      );
                                                    },
                                                    onTap: () {
                                                      ref.showLifeStylecontainer.value =
                                                          !ref.showLifeStylecontainer.value;
                                                    },
                                                    color: context.themeColors.redColor,
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  LifestyleWidget(ref: ref),
                                                  SpaceV(AppSize.h24),
                                                  SpaceV(AppSize.h10),
                                                ],
                                              ),
                                            ],
                                          ),

                                          SpaceV(AppSize.h10),
                                          // CustomButton(
                                          //   padding: EdgeInsets.zero,
                                          //   title: CoreLocaleKeys.buttonsNext.tr(),
                                          //   onTap: () async {},
                                          //   isBottom: true,
                                          //   color: context.themeColors.blueColor,
                                          // ),
                                          //  SpaceV(AppSize.h20),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
