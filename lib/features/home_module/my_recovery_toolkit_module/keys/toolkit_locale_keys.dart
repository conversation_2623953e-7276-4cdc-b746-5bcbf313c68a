class ToolKitLocaleKeys {
  // Titles
  static const titleAreas = 'toolkit.titles.areas';
  static const titleResources = 'toolkit.titles.resources';

  // Info Panels
  static const infoPanelAreasText = 'toolkit.infoPanels.areas.text';
  static const infoPanelAreasAudio = 'toolkit.infoPanels.areas.audio';

  static const infoPanelInformationText = 'toolkit.infoPanels.information.text';
  static const infoPanelInformationAudio = 'toolkit.infoPanels.information.audio';

  static const infoPanelLearnText = 'toolkit.infoPanels.learn.text';
  static const infoPanelLearnAudio = 'toolkit.infoPanels.learn.audio';

  static const infoPanelResourcesText = 'toolkit.infoPanels.resources.text';
  static const infoPanelResourcesAudio = 'toolkit.infoPanels.resources.audio';

  // Areas
  static const areaDs = 'toolkit.areas.ds';
  static const areaNt = 'toolkit.areas.nt';
  static const areaEi = 'toolkit.areas.ei';
  static const areaLs = 'toolkit.areas.ls';
  static const areaUb = 'toolkit.areas.ub';
  static const areaPs = 'toolkit.areas.ps';
  static const total = 'toolkit.total';
  static const showEarlier = 'toolkit.showEarlier';
  static const hideEarlier = 'toolkit.hideEarlier';
  static const informationSummary = 'toolkit.informationSummary';
  static const actionPlan = 'toolkit.actionPlan';

  // Month Names
  static const monthJan = 'toolkit.monthNames.0';
  static const monthFeb = 'toolkit.monthNames.1';
  static const monthMar = 'toolkit.monthNames.2';
  static const monthApr = 'toolkit.monthNames.3';
  static const monthMay = 'toolkit.monthNames.4';
  static const monthJun = 'toolkit.monthNames.5';
  static const monthJul = 'toolkit.monthNames.6';
  static const monthAug = 'toolkit.monthNames.7';
  static const monthSep = 'toolkit.monthNames.8';
  static const monthOct = 'toolkit.monthNames.9';
  static const monthNov = 'toolkit.monthNames.10';
  static const monthDec = 'toolkit.monthNames.11';

  // Labels
  static const labelFilter = 'toolkit.labels.filter';
  static const labelActionCount = 'toolkit.labels.actionCount';
  static const labelInformationCount = 'toolkit.labels.informationCount';
  static const labelActionCompletedOn = 'toolkit.labels.actionCompletedOn';
  static const labelInformationCompletedOn = 'toolkit.labels.informationCompletedOn';

  // Buttons
  static const buttonDiagram = 'toolkit.buttons.diagram';
  static const buttonDashboard = 'toolkit.buttons.dashboard';
}
