import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/difficult_situtation_repository/difficult_situtation_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/file_download.dart/files_download.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/lifestyle_repository/lifestyle_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/negative_thoughts_repository/negative_thoughts_repository.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/unhelpful_behaviour_repository/unhelpful_behaviour_repository.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/dashboard_page/widgets/checkin_processer_widget.dart';
import 'package:breakingfree_v2/features/home_module/dashboard_module/keys/dashboard_locale_keys.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'my_recovery_tool_kit_cubit.freezed.dart';
part 'my_recovery_tool_kit_state.dart';

class MyRecoveryToolKitCubit extends Cubit<MyRecoveryToolKitState> {
  MyRecoveryToolKitCubit() : super(MyRecoveryToolKitState());
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<bool> showDSontainer = ValueNotifier(false);
  ValueNotifier<bool> showNTontainer = ValueNotifier(false);
  ValueNotifier<bool> showETcontainer = ValueNotifier(false);
  ValueNotifier<bool> showLifeStylecontainer = ValueNotifier(false);
  ValueNotifier<bool> showUBcontainer = ValueNotifier(false);
  ValueNotifier<bool> showpscontainer = ValueNotifier(false);
  ValueNotifier<bool> showAllResources = ValueNotifier(false);
  ValueNotifier<bool> showNegativeThoughtsResources = ValueNotifier(false);
  ValueNotifier<bool> showEmoionalImpactResources = ValueNotifier(false);
  ValueNotifier<bool> showPhysicalSensationResources = ValueNotifier(false);
  ValueNotifier<bool> showLifeStyleResources = ValueNotifier(false);
  ValueNotifier<bool> showUnhelpFulResources = ValueNotifier(false);
  final DifficultSitutationRepository repository = DifficultSitutationRepository();
  NegativeThoughtsRepository negativeThoughtsRepository = NegativeThoughtsRepository();
  final UnhelpfulBehaviourRepository unhelpfulBehaviourRepository = UnhelpfulBehaviourRepository();
  LifestyleRepository lifestyleRepository = LifestyleRepository();

  final scaffoldKey = GlobalKey<ScaffoldState>();

  int dsScore = 0;
  int ntScore = 0;
  int eiScore = 0;
  int lsScore = 0;
  int ubScore = 0;
  int psScore = 0;
  // bool showAllResources = false;
  List<DsI> displayDsIs = [];
  List<DsA> displayDsAs = [];

  List<DsI> displayNtIs = [];
  List<NtA> displayNtAs = [];

  List<DsI> displayEiIs = [];
  List<EiA> displayEiAs = [];

  List<DsI> displayPsIs = [];
  List<PsA> displayPsAs = [];

  List<DsI> displayLsIs = [];
  List<LsA> displayLsAs = [];

  List<DsI> displayUbIs = [];
  List<UbA> displayUbAs = [];

  String formatDate(DateTime date, {bool isDay = false}) {
    final monthNames = (DynamicAssetLoader.getNestedValue(
      DashboardLocaleKeys.recoveryProgressLabelsMonthNames,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();


    final month = monthNames[date.month - 1];
    final formattedDate = '${date.day} $month';

    return formattedDate;

  }

  String formatDate2(DateTime date, {bool isDay = false}) {
    final localizedDays = (DynamicAssetLoader.getNestedValue(
      AsLocaleKeys.lsDays,
      navigatorKey.currentContext!,
    ) as List)
        .cast<String>();

    final dayIndex = date.weekday % 7;

    final translatedDay = localizedDays[dayIndex];
    if (isDay) {
      return translatedDay;
    } else {
      return '$translatedDay ${date.day}';
    }
  }

  int getHour(String dateTimeString) {
  DateTime dateTime = DateTime.parse(dateTimeString);
  return dateTime.hour;
}

int getMinute(String dateTimeString) {
  DateTime dateTime = DateTime.parse(dateTimeString);
  return dateTime.minute;
}


  void getDiagramNodeColor() {
    final checkins = Injector.instance<AppDB>().userModel?.user.checkins;
    final entries1 = <MapEntry<DateTime, Checkin>>[];
    'checkins $checkins'.logD;
    if (checkins != null && checkins.isNotEmpty) {
// Sort the check-ins by date (assuming the Checkin has a 'date' field)
      final data1 = CheckinProcessor.processCheckins1<Checkin>(
        entries1,
        checkins,
      );

// Sort the data by the DateTime in descending order (most recent first)
      data1.sort((a, b) => b.key.compareTo(a.key)); // Sort by DateTime (b is more recent)

      if (data1.isNotEmpty) {
        final mostRecentCheckin = data1.first.value; // Get the Checkin from the most recent entry

        final checkin = mostRecentCheckin.toJson();
        final checkinData = Checkin.fromJson(checkin);

        'b ===>${checkinData.rate?.toJson().values}'.logD;

        // Assign values to scores
        dsScore = checkinData.rate?.ds ?? 0;
        ntScore = checkinData.rate?.nt ?? 0;
        eiScore = checkinData.rate?.ei ?? 0;
        lsScore = checkinData.rate?.ls ?? 0;
        psScore = checkinData.rate?.ps ?? 0;
        ubScore = checkinData.rate?.ub ?? 0;

        'dsScore $dsScore'.logD;
        'eiScore $eiScore'.logD;
        'lsScore $lsScore'.logD;
        'psScore $psScore'.logD;
        'ubScore $ubScore'.logD;
      }
    } else {
      'checkins ++ $checkins'.logD;

      final data = Injector.instance<AppDB>().userModel?.user.assessment;
      dsScore = data?.ds?.rate ?? 0;
      ntScore = data?.nt?.rate ?? 0;
      eiScore = data?.ei?.rate ?? 0;
      lsScore = data?.ls?.rate ?? 0;
      psScore = data?.ps?.rate ?? 0;
      ubScore = data?.ub?.rate ?? 0;
      'dsScore $dsScore'.logD;
      'eiScore $eiScore'.logD;
      'lsScore $lsScore'.logD;
      'psScore $psScore'.logD;
      'ubScore $ubScore'.logD;
    }
    'Scores ===> ${[dsScore, ntScore, eiScore, lsScore, psScore, ubScore].any(
      (element) => element <= 2,
    )}'
        .logD;
  }

  void getUnhelpfulBehaviourUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final ubIs = userStrategies?.ubIs ?? [];
    final ubAs = userStrategies?.ubAs ?? [];

    // Sort lists by time (descending)
    final sortedUbIs = [...ubIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedUbAs = [...ubAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentUbIs = sortedUbIs.isNotEmpty ? sortedUbIs.first : null;
    final recentUbAs = sortedUbAs.isNotEmpty ? sortedUbAs.first : null;

    // Update display lists based on the toggle state
    displayUbIs = showUnhelpFulResources.value ? sortedUbIs : [if (recentUbIs != null) recentUbIs];
    displayUbAs = showUnhelpFulResources.value ? sortedUbAs : [if (recentUbAs != null) recentUbAs];
  }

  void getLifeStyleUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final lsIs = userStrategies?.lsIs ?? [];
    final lsAs = userStrategies?.lsAs ?? [];

    // Sort lists by time (descending)
    final sortedEiIs = [...lsIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedEiAs = [...lsAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentLsIs = sortedEiIs.isNotEmpty ? sortedEiIs.first : null;
    final recentLsAs = sortedEiAs.isNotEmpty ? sortedEiAs.first : null;

    // Update display lists based on the toggle state
    displayLsIs = showLifeStyleResources.value ? sortedEiIs : [if (recentLsIs != null) recentLsIs];
    displayLsAs = showLifeStyleResources.value ? sortedEiAs : [if (recentLsAs != null) recentLsAs];
  }

  void getEmotionalUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final eiIs = userStrategies?.eiIs ?? [];
    final eiAs = userStrategies?.eiAs ?? [];

    // Sort lists by time (descending)
    final sortedEiIs = [...eiIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedEiAs = [...eiAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentEiIs = sortedEiIs.isNotEmpty ? sortedEiIs.first : null;
    final recentEiAs = sortedEiAs.isNotEmpty ? sortedEiAs.first : null;

    // Update display lists based on the toggle state
    displayEiIs = showEmoionalImpactResources.value ? sortedEiIs : [if (recentEiIs != null) recentEiIs];
    displayEiAs = showEmoionalImpactResources.value ? sortedEiAs : [if (recentEiAs != null) recentEiAs];
  }

  void getPhysicalSensationUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final psIs = userStrategies?.psIs ?? [];
    final psAs = userStrategies?.psAs ?? [];

    // Sort lists by time (descending)
    final sortedEiIs = [...psIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedEiAs = [...psAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentEiIs = sortedEiIs.isNotEmpty ? sortedEiIs.first : null;
    final recentEiAs = sortedEiAs.isNotEmpty ? sortedEiAs.first : null;

    // Update display lists based on the toggle state
    displayPsIs = showPhysicalSensationResources.value ? sortedEiIs : [if (recentEiIs != null) recentEiIs];
    displayPsAs = showPhysicalSensationResources.value ? sortedEiAs : [if (recentEiAs != null) recentEiAs];
  }

  void getSituattionUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final dsIs = userStrategies?.dsIs ?? [];
    final dsAs = userStrategies?.dsAs ?? [];

    // Sort lists by time (descending)
    final sortedDsIs = [...dsIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedDsAs = [...dsAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentDsIs = sortedDsIs.isNotEmpty ? sortedDsIs.first : null;
    final recentDsAs = sortedDsAs.isNotEmpty ? sortedDsAs.first : null;

    // Update display lists based on the toggle state
    displayDsIs = showAllResources.value ? sortedDsIs : [if (recentDsIs != null) recentDsIs];
    displayDsAs = showAllResources.value ? sortedDsAs : [if (recentDsAs != null) recentDsAs];
  }

  void getNegativeThoughtUserData(BuildContext context) {
    authRepository.getUserData(context: context);

    final userStrategies = Injector.instance<AppDB>().userModel?.user.strategies;
    final ntIs = userStrategies?.ntIs ?? [];
    final ntAs = userStrategies?.ntAs ?? [];

    // Sort lists by time (descending)
    final sortedNtIs = [...ntIs]..sort((a, b) => b.time!.compareTo(a.time!));
    final sortedNtAs = [...ntAs]..sort((a, b) => b.time!.compareTo(a.time!));

    // Get most recent items
    final recentNtIs = sortedNtIs.isNotEmpty ? sortedNtIs.first : null;
    final recentNtAs = sortedNtAs.isNotEmpty ? sortedNtAs.first : null;

    // Update display lists based on the toggle state
    displayNtIs = showNegativeThoughtsResources.value ? sortedNtIs : [if (recentNtIs != null) recentNtIs];
    displayNtAs = showNegativeThoughtsResources.value ? sortedNtAs : [if (recentNtAs != null) recentNtAs];
  }

  Future<void> dsActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    ValueNotifier<bool>? downloadPdfAPILoading,
    ValueNotifier<bool>? emailPdfAPILoading,
  }) async {
    isEmail ? emailPdfAPILoading?.value = true : downloadPdfAPILoading?.value = true;
    try {
      final response = await repository.situtationStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Managing your risky situations.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? emailPdfAPILoading?.value = false : downloadPdfAPILoading?.value = false;
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? emailPdfAPILoading?.value = false : downloadPdfAPILoading?.value = false;
    }
  }

  Future<void> ntActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required ValueNotifier<bool> isEmailPdfAPILoading,
    required ValueNotifier<bool> isDownloadPdfAPILoading,
  }) async {
    isEmail ? isEmailPdfAPILoading.value = true : isDownloadPdfAPILoading.value = true;
    try {
      final response = await negativeThoughtsRepository.ntActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Escaping your mind trap.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    }
  }

  Future<void> unhelpfulStrategforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required ValueNotifier<bool> isEmailPdfAPILoading,
    required ValueNotifier<bool> isDownloadPdfAPILoading,
  }) async {
    isEmail ? isEmailPdfAPILoading.value = true : isDownloadPdfAPILoading.value = true;
    try {
      final response = await unhelpfulBehaviourRepository.ubActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Planning your time positively.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    }
  }

  Future<void> lsActionStrategyforDownloadPdfApi({
    required BuildContext context,
    required bool isEmail,
    required ValueNotifier<bool> isEmailPdfAPILoading,
    required ValueNotifier<bool> isDownloadPdfAPILoading,
  }) async {
    isEmail ? isEmailPdfAPILoading.value = true : isDownloadPdfAPILoading.value = true;
    try {
      final response = await lifestyleRepository.lsActionStrategyforDownload(
        context: context,
        isEmail: isEmail,
      );
      if (response != null && response.data!['success'] == true) {
        if (!isEmail) {
          if (response.data!['pdf'] != null) {
            final encodedStr = response.data?['pdf'];

            await FilesDownload.downloadAndOpenPdf(
              encodedStr as String,
              'Achieving your life goals.pdf',
            );
          }
        } else {
          CustomSnackbar.showSucessSnackBar(
            message: 'Email sent successfully',
          );
        }
      }
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    } catch (e) {
      CustomSnackbar.showErrorSnackBar(
        message: e.toString(),
      );
      isEmail ? isEmailPdfAPILoading.value = false : isDownloadPdfAPILoading.value = false;
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPanelVisible.dispose();
    headerInfoText.dispose();
    showDSontainer.dispose();
    showNTontainer.dispose();
    showETcontainer.dispose();
    showLifeStylecontainer.dispose();
    showUBcontainer.dispose();
    showpscontainer.dispose();
    showAllResources.dispose();
    showNegativeThoughtsResources.dispose();
    showEmoionalImpactResources.dispose();
    showPhysicalSensationResources.dispose();
    showLifeStyleResources.dispose();
    showUnhelpFulResources.dispose();
    displayDsIs.clear();
    displayDsAs.clear();
    displayNtIs.clear();
    displayNtAs.clear();
    displayEiIs.clear();
    displayEiAs.clear();
    displayPsIs.clear();
    displayPsAs.clear();
    displayLsIs.clear();
    displayLsAs.clear();
    displayUbIs.clear();
    displayUbAs.clear();
    return super.close();
  }
}
