import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/emotional_action_module/emotional_action_plan_page/emtional_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/summary_page/summary_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/keys/toolkit_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/custom_information_summary.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class EmotionalImpactWidget extends StatelessWidget {
  const EmotionalImpactWidget({required this.ref, super.key});
  final MyRecoveryToolKitCubit ref;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ref.showEmoionalImpactResources,
      builder: (context, value, child) {
        return Column(
          children: [
            Padding(
              padding:  EdgeInsets.only(bottom: AppSize.h8),
              child: AppTextWidget(
                '${ToolKitLocaleKeys.total.tr()}: ${(Injector.instance<AppDB>().userModel?.user.strategies?.eiAs?.length ?? 0) + (Injector.instance<AppDB>().userModel?.user.strategies?.eiIs?.length ?? 0)}',
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.eiIs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: Assets.icons.infoIcons.emtionalImpactInfoIcon,
              )
            else
              ...ref.displayEiIs.map(
                (item) => GestureDetector(
                  onTap: () {
                    AppNavigation.nextScreen(
                      context,
                      SummaryPage(
                        diagramState: MyDiagramStates.emotionalImpact,
                      ),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: Assets.icons.infoIcons.emtionalImpactInfoIcon,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                  ),
                ),
              ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.eiAs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: Assets.icons.actionIcons.emotionalImpact,
                title: ToolKitLocaleKeys.actionPlan.tr(),
              )
            else
              ...ref.displayEiAs.map(
                (item) => GestureDetector(
                  onTap: () {
                    // final ref = BlocProvider.of<DifficultSitutationCubit>(context);
                    AppNavigation.nextScreen(
                      context,
                      const EmtionalActionPlanPage(),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: Assets.icons.actionIcons.emotionalImpact,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                    title: ToolKitLocaleKeys.actionPlan.tr(),
                  ),
                ),
              ),
            SpaceV(AppSize.h2),
            GestureDetector(
              onTap: () {
                ref.showEmoionalImpactResources.value = !ref.showEmoionalImpactResources.value;

                ref.getEmotionalUserData(context);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppTextWidget(
                    ref.showEmoionalImpactResources.value
                        ? ToolKitLocaleKeys.hideEarlier.tr()
                        : ToolKitLocaleKeys.showEarlier.tr(),
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: AppSize.sp12,
                      color: context.themeColors.greenColor,
                    ),
                  ),
                  Icon(
                    ref.showEmoionalImpactResources.value ? Icons.arrow_upward_outlined : Icons.arrow_downward_outlined,
                    size: AppSize.sp12,
                    color: context.themeColors.greenColor,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
