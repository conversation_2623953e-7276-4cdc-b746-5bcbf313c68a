import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/achievement_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/unhelpful_behaviour_module/widgets/enjoyment_activity_list.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class UnhelpfulToolkitPage extends StatefulWidget {
  const UnhelpfulToolkitPage({required this.date, super.key, this.ref});
  final int date;
  final MyRecoveryToolKitCubit? ref;
  @override
  State<UnhelpfulToolkitPage> createState() => _UnhelpfulToolkitPageState();
}

class _UnhelpfulToolkitPageState extends State<UnhelpfulToolkitPage> {
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);

  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  final scaffoldActionPlanDone = GlobalKey<ScaffoldState>();
  DateTime today = DateTime.now();
  List<Fri> enjoymentActivities = [];
  List<Fri> achievementActivites = [];

  Map<String, List<Fri>>? mostRecentEntry;

  final ValueNotifier<bool> downloadPdfAPILoading = ValueNotifier(false);
  final ValueNotifier<bool> emailPdfAPILoading = ValueNotifier(false);

  String formatDate(DateTime date,{bool isDay = false}) {
    final mon = AsLocaleKeys.lsUbDaysMon.tr();
    final tue = AsLocaleKeys.lsUbDaysTue.tr();
    final wed = AsLocaleKeys.lsUbDaysWed.tr();
    final thu = AsLocaleKeys.lsUbDaysThur.tr();
    final fri = AsLocaleKeys.lsUbDaysFri.tr();
    final sat = AsLocaleKeys.lsUbDaysSat.tr();
    final sun = AsLocaleKeys.lsUbDaysSun.tr();

    final days = [sun,mon,tue,wed,thu,fri,sat];

    final dayIndex = date.weekday % 7;

    final translatedDay = days[dayIndex];
    if(isDay) {
      return translatedDay;
    } else {
      return '$translatedDay ${date.day}';
    }
  }


  @override
  void initState() {
    infoAudioUrl.value = AsLocaleKeys.lsUbActionPlanAudio.tr();

    isAudioPanelVisible.value = false;
    'date ==${widget.date}'.logD;
    final entries = <MapEntry<int, UbAData>>[]; // List of entries to hold time-data pairs
    Injector.instance<AppDB>().userModel?.user.strategies?.ubAs?.firstWhere(
      (element) {
        final isMatch = element.time == widget.date; // Check if the time matches the widget date
        if (isMatch) {
          // Add the matching entry to the entries list
          entries.add(MapEntry(element.time!, element.data!));
        }
        return isMatch; // Return the match condition
      },
    );
    final mostRecentEntry1 = entries.isNotEmpty ? entries.first : null;

    mostRecentEntry = mostRecentEntry1?.value.toJson().map<String, List<Fri>>((key, value) {
      return MapEntry(key, (value as List).map((e) => Fri.fromJson(e as Map<String, dynamic>)).toList());
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: infoAudioUrl,
      builder: (context, value, child) {
        return AppScaffold(
          resizeToAvoidBottomInset: false,
          scaffoldKey: scaffoldActionPlanDone,
          isAudioPanelVisible: isAudioPanelVisible,
          infoAudioUrl: infoAudioUrl,
          drawer: AppDrawer(scaffoldKey: scaffoldActionPlanDone),
          appBar: CommonAppBar(
            onPrefixTap: () {
              scaffoldActionPlanDone.currentState?.openDrawer();
            },
            onSuffixTap: () {
              if (infoAudioUrl.value.isNotEmptyAndNotNull) {
                isAudioPanelVisible.value = !isAudioPanelVisible.value;
              }
            },
          ),
          body: ColoredBox(
            color: context.themeColors.whiteColor,
            child: Column(
              children: [
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: constraints.maxHeight,
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: AppSize.w24,
                              right: AppSize.w24,
                              bottom: AppSize.h20,
                              top: AppSize.h24,
                            ),
                            child: Column(
                              children: [
                                Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    InformationPageHeadingWidget(
                                      onBackArrowTap: () {
                                        infoAudioUrl.value = AsLocaleKeys.lsUbSummaryAudioApp.tr();
                                        headerInfoText.value = null;
                                        Navigator.pop(context);
                                      },
                                      title: CoreLocaleKeys.titlesInformationStrategiesUnhelpfulBehaviours.tr(),
                                      subtitle: AsLocaleKeys.lsUbTitle.tr(),
                                      icon: Assets.icons.infoPage.difficultSituation,
                                      onInfoTap: () {
                                        infoAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsInformationAudio.tr();

                                        final info = (DynamicAssetLoader.getNestedValue(
                                          AsLocaleKeys.lsUbInfoPanelsInformationText,
                                          context,
                                        ) as List)
                                            .join('<br/><br/>');
                                        headerInfoText.value =
                                            (headerInfoText.value.isNotEmptyAndNotNull && headerInfoText.value == info)
                                                ? null
                                                : info;
                                        // headerVideoUrl = ValueNotifier(true);
                                      },
                                      onLearnTap: () {
                                        final info = (DynamicAssetLoader.getNestedValue(
                                          AsLocaleKeys.lsUbInfoPanelsLearnText,
                                          context,
                                        ) as List)
                                            .join('<br/><br/>');
                                        headerInfoText.value =
                                            (headerInfoText.value.isNotEmptyAndNotNull && headerInfoText.value == info)
                                                ? null
                                                : info;
                                        // headerVideoUrl = ValueNotifier(false);
                                        infoAudioUrl.value = AsLocaleKeys.lsUbInfoPanelsLearnAudio.tr();
                                      },
                                      infoWidget: ValueListenableBuilder(
                                        valueListenable: headerInfoText,
                                        builder: (context, headerInfoTextV, _) {
                                          return CustomInfoWidget(
                                            customWidget: Column(
                                              children: [
                                                Html(
                                                  data: headerInfoText.value ?? '',
                                                  style: {
                                                    'strong': Style(
                                                      fontSize: FontSize(AppSize.sp13),
                                                      color: context.themeColors.darkOrangeColor,
                                                      fontWeight: FontWeight.bold,
                                                      fontFamily: 'Poppins',
                                                    ),
                                                    'body': Style(
                                                      fontSize: FontSize(AppSize.sp13),
                                                      color: context.themeColors.darkOrangeColor,
                                                      fontFamily: 'Poppins',
                                                    ),
                                                  },
                                                ),
                                              ],
                                            ),
                                            onCloseTap: () {
                                              headerInfoText.value = null;
                                              infoAudioUrl.value = null;
                                            },
                                            visible: headerInfoTextV.isNotEmptyAndNotNull,
                                            margin: EdgeInsets.symmetric(
                                              vertical: AppSize.h8,
                                            ),
                                            bodyText: headerInfoTextV,
                                          );
                                        },
                                      ),
                                    ),
                                    SpaceV(AppSize.h10),
                                    AppTextWidget(
                                      AsLocaleKeys.lsUbActionPlanTitle.tr(),
                                      style: context.textTheme.titleSmall?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    SpaceV(AppSize.h20),
                                    AppTextWidget(
                                      (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsUbActionPlanText0,
                                        context,
                                      ) as List)
                                          .join('\n\n'),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h20),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                                      children: [
                                        AppTextWidget(
                                          AsLocaleKeys.lsUbEnjoyment.tr(),
                                          style: context.textTheme.titleSmall?.copyWith(
                                            fontSize: AppSize.sp14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        AppTextWidget(
                                          AsLocaleKeys.lsUbAchievement.tr(),
                                          style: context.textTheme.titleSmall?.copyWith(
                                            fontSize: AppSize.sp14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    SpaceV(AppSize.h10),
                                    Column(
                                      children: List.generate(7, (index) {
                                        final date = today.add(Duration(days: index));

                                        final dayNumber = DateFormat('d').format(date); // Day number (1, 2, 3, etc.)
                                        final weekday = DateFormat('EEE')
                                            .format(date)
                                            .toUpperCase(); // Day name (e.g., MON, TUE, etc.)
                                        final formattedWeekday = weekday == 'THU' ? 'THUR' : weekday;

                                        enjoymentActivities = (mostRecentEntry?[formattedWeekday] ?? [])
                                            .where((activity) => activity.type == AsLocaleKeys.lsUbEnjoyment.tr())
                                            .toList();
                                        achievementActivites = (mostRecentEntry?[formattedWeekday] ?? [])
                                            .where((activity) => activity.type == AsLocaleKeys.lsUbAchievement.tr())
                                            .toList();

                                        return Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            // Day Number and Weekday Display
                                            if (enjoymentActivities.isNotEmpty || achievementActivites.isNotEmpty)
                                              AppTextWidget(
                                                formatDate(date),
                                                style: context.textTheme.titleSmall?.copyWith(
                                                  fontSize: AppSize.sp12,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.activityTextColor,
                                                ),
                                              )
                                            else
                                              const SizedBox(),
                                            SizedBox(height: AppSize.h4),
                                            Row(
                                              //mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      ...enjoymentActivities.map((activity) {
                                                        return GestureDetector(
                                                          onTap: () {
                                                            // log('is tabbed${selectedForActivityIndex.value}');
                                                            // log('is tabbed${activity.index}');
                                                            // selectedForActivityIndex.value = activity;
                                                          },
                                                          child: Container(
                                                            height: AppSize.h60,
                                                            decoration: BoxDecoration(
                                                              color: Colors.white,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Colors.black.withOpacity(0.2), // Soft shadow
                                                                  offset: const Offset(
                                                                    4,
                                                                    4,
                                                                  ),
                                                                  blurRadius: 6,
                                                                ),
                                                                BoxShadow(
                                                                  color: Colors.black.withOpacity(0.2),
                                                                  offset: const Offset(
                                                                    -4,
                                                                    -4,
                                                                  ), // Shadow towards top-left
                                                                  blurRadius: 6,
                                                                ),
                                                              ],
                                                            ),
                                                            width: MediaQuery.of(context).size.width,
                                                            margin: EdgeInsets.only(
                                                              top: AppSize.h6,
                                                              bottom: AppSize.h6,
                                                            ),
                                                            child: Padding(
                                                              padding: EdgeInsets.all(AppSize.sp6),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                children: [
                                                                  Row(
                                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                                    children: [
                                                                      Center(
                                                                        child: ColoredBox(
                                                                          color: activity.color != null &&
                                                                                  activity.color != null
                                                                              ? Color(
                                                                                  int.parse(
                                                                                    '0xFF${activity.color?.colorDefault?.replaceAll('#', '')}',
                                                                                  ),
                                                                                )
                                                                              : Colors
                                                                                  .transparent, // Default transparent color
                                                                          child: activity.color != null &&
                                                                                  activity.color != null
                                                                              ? AppCachedNetworkImage(
                                                                                  imageUrl:
                                                                                      AsLocaleKeys.lsUbCustomLogo.tr(),
                                                                                  width: AppSize.w44,
                                                                                  height: AppSize.w44,
                                                                                  fit: BoxFit.cover,
                                                                                )
                                                                              : AppCachedNetworkImage(
                                                                                  imageUrl: activity.type == AsLocaleKeys.lsUbEnjoyment.tr()
                                                                                      ? EnjoymentActivictyList
                                                                                                  .activityImages[
                                                                                              activity.name] ??
                                                                                          ''
                                                                                      : AchievementActivictyList
                                                                                                  .activityImages[
                                                                                              activity.name] ??
                                                                                          '',
                                                                                  width: AppSize.w44,
                                                                                  height: AppSize.w44,
                                                                                  fit: BoxFit.cover,
                                                                                ),
                                                                        ),
                                                                      ),
                                                                      SpaceH(AppSize.w10),
                                                                      Expanded(
                                                                        child: Column(
                                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                                          children: [
                                                                            AppTextWidget(
                                                                              activity.name ?? '',
                                                                              style: context.textTheme.titleSmall
                                                                                  ?.copyWith(
                                                                                fontSize: AppSize.sp12,
                                                                              ),
                                                                            ),
                                                                            AppTextWidget(
                                                                              activity.time ?? '',
                                                                              style: context.textTheme.titleSmall
                                                                                  ?.copyWith(
                                                                                fontSize: AppSize.sp12,
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }),
                                                    ],
                                                  ),
                                                ),
                                                SpaceH(AppSize.w10),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      ...achievementActivites.map((activity) {
                                                        '===${AchievementActivictyList.activityImages[activity.name] ?? ''}'
                                                            .logD;
                                                        return GestureDetector(
                                                          onTap: () {
                                                            // log('is tabbed${selectedForActivityIndex.value}');
                                                            // log('is tabbed${activity.index}');
                                                            // selectedForActivityIndex.value = activity;
                                                          },
                                                          child: Container(
                                                            height: AppSize.h60,
                                                            decoration: BoxDecoration(
                                                              color: Colors.white,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Colors.black.withOpacity(0.2), // Soft shadow
                                                                  offset: const Offset(
                                                                    4,
                                                                    4,
                                                                  ),
                                                                  blurRadius: 6,
                                                                ),
                                                                BoxShadow(
                                                                  color: Colors.black.withOpacity(0.2),
                                                                  offset: const Offset(
                                                                    -4,
                                                                    -4,
                                                                  ), // Shadow towards top-left
                                                                  blurRadius: 6,
                                                                ),
                                                              ],
                                                            ),
                                                            width: MediaQuery.of(context).size.width,
                                                            margin: EdgeInsets.only(
                                                              top: AppSize.h6,
                                                              bottom: AppSize.h6,
                                                            ),
                                                            child: Padding(
                                                              padding: EdgeInsets.all(AppSize.sp6),
                                                              child: Column(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                children: [
                                                                  Row(
                                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                                    children: [
                                                                      Center(
                                                                        child: ColoredBox(
                                                                          color: activity.color != null &&
                                                                                  activity.color != null
                                                                              ? Color(
                                                                                  int.parse(
                                                                                    '0xFF${activity.color?.colorDefault?.replaceAll('#', '')}',
                                                                                  ),
                                                                                )
                                                                              : Colors
                                                                                  .transparent, // Default transparent color
                                                                          child: activity.color != null &&
                                                                                  activity.color != null
                                                                              ? AppCachedNetworkImage(
                                                                                  imageUrl:
                                                                                      AsLocaleKeys.lsUbCustomLogo.tr(),
                                                                                  width: AppSize.w44,
                                                                                  height: AppSize.w44,
                                                                                  fit: BoxFit.cover,
                                                                                ) // Empty container with the color as background
                                                                              : AppCachedNetworkImage(
                                                                                  imageUrl: activity.type == AsLocaleKeys.lsUbEnjoyment.tr()
                                                                                      ? EnjoymentActivictyList
                                                                                                  .activityImages[
                                                                                              activity.name] ??
                                                                                          ''
                                                                                      : AchievementActivictyList
                                                                                                  .activityImages[
                                                                                              activity.name] ??
                                                                                          '',
                                                                                  width: AppSize.w44,
                                                                                  height: AppSize.w44,
                                                                                  fit: BoxFit.cover,
                                                                                ),
                                                                        ),
                                                                      ),
                                                                      SpaceH(AppSize.w10),
                                                                      Expanded(
                                                                        child: Column(
                                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                                          children: [
                                                                            AppTextWidget(
                                                                              activity.name ?? '',
                                                                              style: context.textTheme.titleSmall
                                                                                  ?.copyWith(
                                                                                fontSize: AppSize.sp12,
                                                                              ),
                                                                            ),
                                                                            AppTextWidget(
                                                                              activity.time ?? '',
                                                                              style: context.textTheme.titleSmall
                                                                                  ?.copyWith(
                                                                                fontSize: AppSize.sp12,
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        );
                                      }),
                                    ),
                                    SpaceV(AppSize.h20),
                                  ],
                                ),
                                MultiValueListenableBuilder(
                                  valueListenables: [downloadPdfAPILoading, emailPdfAPILoading],
                                  builder: (context, List<dynamic> values, child) {
                                    return CustomYesNoButton(
                                      exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                      agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                      isDownLoad: true,
                                      inNoProgress: ValueNotifier<bool>(
                                        (downloadPdfAPILoading.value) || (emailPdfAPILoading.value),
                                      ),
                                      onDownloadTap: () {
                                        CustomDownloadPopup.buildPopupMenu(
                                          context: context,
                                          onDownLoadPdf: () async {
                                            await widget.ref?.unhelpfulStrategforDownloadPdfApi(
                                              context: context,
                                              isEmail: false,
                                              isDownloadPdfAPILoading: downloadPdfAPILoading,
                                              isEmailPdfAPILoading: emailPdfAPILoading,
                                            );
                                          },
                                          onEmailDownload: () async {
                                            await widget.ref?.unhelpfulStrategforDownloadPdfApi(
                                              context: context,
                                              isEmail: true,
                                              isDownloadPdfAPILoading: downloadPdfAPILoading,
                                              isEmailPdfAPILoading: emailPdfAPILoading,
                                            );
                                          },
                                          // onDownLoadPdf: () async {
                                          //   '======>'.logE;
                                          //   await widget.ref?.myBehaviourDownloadPdfApi(
                                          //     context: context,
                                          //     isEmail: true,
                                          //     isDownloadPdfAPILoading: downloadPdfAPILoading,
                                          //     isEmailPdfAPILoading: emailPdfAPILoading,
                                          //   );
                                          // },
                                          // onEmailDownload: () async {
                                          //   await widget.ref?.myBehaviourDownloadPdfApi(
                                          //     context: context,
                                          //     isEmail: false,
                                          //     isDownloadPdfAPILoading: downloadPdfAPILoading,
                                          //     isEmailPdfAPILoading: emailPdfAPILoading,
                                          //   );
                                          // },
                                        );
                                      },
                                      padding: EdgeInsets.zero,
                                      onTapYes: () async {
                                        infoAudioUrl.value = null;
                                        await AppNavigation.pushAndRemoveAllScreen(
                                          context,
                                          const MyDiagramPage(),
                                        );
                                      },
                                      onTapNo: () async {
                                        // Request permissions
                                      },
                                      noButtonColor: context.themeColors.orangeColor,
                                      isYesNoButton: true,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
