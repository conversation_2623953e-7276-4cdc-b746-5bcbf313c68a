import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/summary_page/summary_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/keys/toolkit_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/custom_information_summary.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/my_lifestyle_action_plan_toolkit_page.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LifestyleWidget extends StatelessWidget {
  const LifestyleWidget({required this.ref, super.key});
  final MyRecoveryToolKitCubit ref;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ref.showLifeStyleResources,
      builder: (context, value, child) {
        return Column(
          children: [
            Padding(
              padding:  EdgeInsets.only(bottom: AppSize.h8),
              child: AppTextWidget(
                '${ToolKitLocaleKeys.total.tr()}: ${(Injector.instance<AppDB>().userModel?.user.strategies?.lsAs?.length ?? 0) + (Injector.instance<AppDB>().userModel?.user.strategies?.lsIs?.length ?? 0)}',
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.lsIs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: Assets.icons.infoIcons.lifestyleInfoIcon,
              )
            else
              ...ref.displayLsIs.map(
                (item) => GestureDetector(
                  onTap: () {
                    AppNavigation.nextScreen(
                      context,
                      SummaryPage(
                        diagramState: MyDiagramStates.lifestyle,
                      ),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: Assets.icons.infoIcons.lifestyleInfoIcon,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                  ),
                ),
              ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.lsAs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: Assets.icons.actionIcons.lifestyle,
                title: ToolKitLocaleKeys.actionPlan.tr(),
              )
            else
              ...ref.displayLsAs.map(
                (item) => GestureDetector(
                  onTap: () {
                    //  final ref = BlocProvider.of<NegativeThoughtsCubit>(context);
                    'mountain ${Injector.instance<AppDB>().userModel?.user.strategies?.lsAs?.firstWhere(
                              (element) => element.time == item.time!,
                            ).data?.mountain?.name ?? ''}'
                        .logD;
                    AppNavigation.nextScreen(
                      context,
                      MyLifestyleActionPlanToolkitPage(
                        ref: ref,
                        otherdata: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepFour
                                ?.otherBenefit ??
                            '',
                        mountainName: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.mountain
                                ?.name ??
                            '',
                        question1: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepOne
                                ?.lifeGoal ??
                            '',
                        question2: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepOne
                                ?.nextStep ??
                            '',
                        selectBarrierValue: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepTwo
                                ?.barrier ??
                            0,
                        date: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepThree
                                ?.date
                                ?.toString() ??
                            '',
                        selectedBenefitIndices: Injector.instance<AppDB>()
                                .userModel
                                ?.user
                                .strategies
                                ?.lsAs
                                ?.firstWhere(
                                  (element) => element.time == item.time!,
                                )
                                .data
                                ?.stepFour
                                ?.benefits ??
                            [],
                      ),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: Assets.icons.actionIcons.lifestyle,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                    title: ToolKitLocaleKeys.actionPlan.tr(),
                  ),
                ),
              ),
            SpaceV(AppSize.h2),
            GestureDetector(
              onTap: () {
                ref.showLifeStyleResources.value = !ref.showLifeStyleResources.value;

                ref.getLifeStyleUserData(context);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppTextWidget(
                    ref.showLifeStyleResources.value
                        ? ToolKitLocaleKeys.hideEarlier.tr()
                        : ToolKitLocaleKeys.showEarlier.tr(),
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: AppSize.sp12,
                      color: context.themeColors.greenColor,
                    ),
                  ),
                  Icon(
                    ref.showLifeStyleResources.value ? Icons.arrow_upward_outlined : Icons.arrow_downward_outlined,
                    size: AppSize.sp12,
                    color: context.themeColors.greenColor,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
