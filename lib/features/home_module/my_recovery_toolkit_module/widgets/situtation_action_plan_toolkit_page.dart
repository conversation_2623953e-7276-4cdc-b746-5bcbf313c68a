import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_html/flutter_html.dart' as html;
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/cubit/difficult_situtation_cubit.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/difficult_situtation_module/google_map/google_map_page.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/strategies_model/strategies_model.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class SitutationActionPlanToolkitPage extends StatefulWidget {
  const SitutationActionPlanToolkitPage({required this.date, required this.ref, super.key});
  final int date;
  final MyRecoveryToolKitCubit ref;

  @override
  State<SitutationActionPlanToolkitPage> createState() => _SitutationActionPlanToolkitPageState();
}

class _SitutationActionPlanToolkitPageState extends State<SitutationActionPlanToolkitPage> {

  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);

  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  final ValueNotifier<bool> downloadPdfAPILoading = ValueNotifier(false);
  final ValueNotifier<bool> emailPdfAPILoading = ValueNotifier(false);

  final scaffoldActionPlanKey = GlobalKey<ScaffoldState>();

  Set<Marker> allMarkers = {};
  final updatedMapList = <Datum>[];

  List<String> situtationWhyList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsDsQuestionsWhyOptions,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  List<String> situtationHowList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsDsQuestionsHowOptions,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  LatLngBounds calculateBounds(Set<Marker> markers) {
    double? minLat;
    double? maxLat;
    double? minLng;
    double? maxLng;

    for (final marker in markers) {
      final position = marker.position;
      if (minLat == null || position.latitude < minLat) minLat = position.latitude;
      if (maxLat == null || position.latitude > maxLat) maxLat = position.latitude;
      if (minLng == null || position.longitude < minLng) minLng = position.longitude;
      if (maxLng == null || position.longitude > maxLng) maxLng = position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }



  Future<void> mapListUpdate() async {
    final entries = <MapEntry<int, List<Map<String, dynamic>>>>[];

    Injector.instance<AppDB>().userModel?.user.strategies?.dsAs?.firstWhere(
      (element) {
        final isMatch = element.time == widget.date; // Check if the time matches the widget date
        if (isMatch) {
          // Add the matching entry to the entries list
          entries.add(
            MapEntry(
              element.time!,
              element.data!.map((datum) => datum.toJson()).toList(),
            ),
          );
        }
        return isMatch; // Return the match condition
      },
    );

    final mostRecentEntry = entries.isNotEmpty ? entries.first : null;

    if (mostRecentEntry != null) {
      final mostRecentDataList = mostRecentEntry.value;

      for (final mostRecentData in mostRecentDataList) {
        final updatedDatum = Datum(
          what: mostRecentData['what'] as String,
          why: mostRecentData['why'] as int,
          how: mostRecentData['how'] as int,
          lat: mostRecentData['lat'] as double,
          lng: mostRecentData['lng'] as double,
        );
        updatedMapList.add(updatedDatum);
      }
      for (final mostRecentData in mostRecentDataList) {
        final lat = mostRecentData['lat'] as double;
        final lng = mostRecentData['lng'] as double;
        // Create a marker for each data entry
        final marker = Marker(
          icon: await MarkerIconHelper.loadCustomIcon(
            'assets/icons/red_marker_icon.png',
            140,
          ),
          markerId: MarkerId('$lat-$lng'),
          position: LatLng(lat, lng),
          infoWindow: InfoWindow(
            snippet: 'Date: ${mostRecentEntry.key}',
          ),
        );
        allMarkers.add(marker); // Add marker to the list
      }

      setState(() {});
    } else {}
  }


  @override
  void initState() {
    infoAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
    isAudioPanelVisible.value = false;
    'allMarkers ==$allMarkers'.logD;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context)=>DifficultSitutationCubit()..mapListUpdate(),
      child: BlocBuilder<DifficultSitutationCubit, DifficultSitutationState>(
        builder: (ctx, state) {
          final ref = ctx.read<DifficultSitutationCubit>();
          return ValueListenableBuilder(
            valueListenable: ref.infoPlanAudioUrl,
            builder: (context,infoPlanAudioUrl,child) {
              return AppScaffold(
                resizeToAvoidBottomInset: false,
                scaffoldKey: scaffoldActionPlanKey,
                isAudioPanelVisible: ref.isAudioPanelVisible,
                isManuallyPaused: ref.isManuallyPaused,
                infoAudioUrl: ref.infoPlanAudioUrl,
                drawer: AppDrawer(scaffoldKey: scaffoldActionPlanKey),
                appBar: CommonAppBar(
                  onPrefixTap: () {
                    scaffoldActionPlanKey.currentState?.openDrawer();
                  },
                  onSuffixTap: () {
                    if (ref.infoPlanAudioUrl.value.isNotEmptyAndNotNull) {
                      ref.isAudioPanelVisible.value = !ref.isAudioPanelVisible.value;
                    }
                  },
                ),
                body: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                    minHeight: constraints.maxHeight,
                                  ),
                                  child: ColoredBox(
                                    color: context.themeColors.whiteColor,
                                    child: Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20,
                                        top: AppSize.h24,
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(
                                            children: [
                                              InformationPageHeadingWidget(
                                                onBackArrowTap: () {
                                                  ref.clearPlanAudioData();
                                                  ref.infoWellDoneAudioUrl.value = AsLocaleKeys.lsDsSummaryAudio.tr();
                                                  Navigator.pop(context);
                                                },
                                                title:
                                                CoreLocaleKeys.titlesInformationStrategiesDifficultSituations.tr(),
                                                subtitle: AsLocaleKeys.lsDsTitle.tr(),//IsLocaleKeys.dsTitle.tr(),
                                                icon: Assets.icons.actionIcons.situtation,
                                                onInfoTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsDsInfoPanelsInformationText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (ref.headerPlanInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerPlanInfoText.value == info) {
                                                    ref.isManuallyPaused.value = true;
                                                    ref.headerPlanInfoText.value = null;
                                                    ref.infoPlanAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                  } else {
                                                    ref.isManuallyPaused.value = false;
                                                    ref.infoPlanAudioUrl.value =
                                                        AsLocaleKeys.lsDsInfoPanelsInformationAudio.tr();
                                                    ref.headerPlanInfoText.value = info;
                                                  }
                                                },
                                                onLearnTap: () {
                                                  final info = (DynamicAssetLoader.getNestedValue(
                                                    AsLocaleKeys.lsDsInfoPanelsLearnText,
                                                    context,
                                                  ) as List)
                                                      .join('<br/><br/>');
                                                  if (ref.headerPlanInfoText.value.isNotEmptyAndNotNull &&
                                                      ref.headerPlanInfoText.value == info) {
                                                    ref.isManuallyPaused.value = true;
                                                    ref.headerPlanInfoText.value = null;
                                                    ref.infoPlanAudioUrl.value = AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                  } else {
                                                    ref.isManuallyPaused.value = false;
                                                    ref.infoPlanAudioUrl.value = AsLocaleKeys.lsDsInfoPanelsLearnAudio.tr();
                                                    ref.headerPlanInfoText.value = info;
                                                  }
                                                },
                                                infoWidget: ValueListenableBuilder(
                                                  valueListenable: ref.headerPlanInfoText,
                                                  builder: (context, headerPlanInfoTextV, _) {
                                                    return CustomInfoWidget(
                                                      customWidget: Column(
                                                        children: [
                                                          html.Html(
                                                            data: ref.headerPlanInfoText.value ?? '',
                                                            style: {
                                                              'strong': html.Style(
                                                                fontSize: html.FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontWeight: FontWeight.bold,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                              'body': html.Style(
                                                                fontSize: html.FontSize(AppSize.sp13),
                                                                color: context.themeColors.darkOrangeColor,
                                                                fontFamily: 'Poppins',
                                                              ),
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      onCloseTap: () {
                                                        ref.isManuallyPaused.value = true;
                                                        ref.headerPlanInfoText.value = null;
                                                        ref.infoPlanAudioUrl.value =
                                                            AsLocaleKeys.lsDsActionPlanAudio.tr();
                                                      },
                                                      visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                                      margin: EdgeInsets.symmetric(
                                                        vertical: AppSize.h8,
                                                      ),
                                                      bodyText: headerPlanInfoTextV,
                                                    );
                                                  },
                                                ),
                                              ),
                                              AppTextWidget(
                                                AsLocaleKeys.lsDsActionPlanTitle.tr(),
                                                style: context.textTheme.titleSmall?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  fontWeight: FontWeight.w700,
                                                ),
                                              ),
                                              SpaceV(AppSize.h10),
                                              AppTextWidget(
                                                (DynamicAssetLoader.getNestedValue(
                                                  AsLocaleKeys.lsDsActionPlanText,
                                                  context,
                                                ) as List)
                                                    .join('\n\n'),
                                                style: context.textTheme.titleSmall,
                                              ),
                                              SpaceV(AppSize.h10),
                                              SizedBox(
                                                height: context.height * 0.5,
                                                child: GoogleMap(
                                                  onMapCreated: (GoogleMapController controller) async{
                                                    'marker : ${ref.getAllMarkers()}'.logD;
                                                    ref.controller = controller;
                                                      final bounds = ref.calculateBounds(state.marker);
                                                      try {
                                                        await controller.animateCamera(
                                                          CameraUpdate.newLatLngBounds(bounds, 100),
                                                        );
                                                      } catch (e) {
                                                        print('Error setting camera bounds: $e');
                                                      }

                                                  },
                                                  zoomControlsEnabled: false,
                                                  markers: ref.getAllMarkers(),
                                                  gestureRecognizers:  const <Factory<OneSequenceGestureRecognizer>>{
                                                    Factory<ScaleGestureRecognizer>(ScaleGestureRecognizer.new),
                                                    Factory<TapGestureRecognizer>(TapGestureRecognizer.new),
                                                    Factory<PanGestureRecognizer>(PanGestureRecognizer.new),
                                                  },
                                                  initialCameraPosition: state.marker.isNotEmpty
                                                      ? CameraPosition(target:state.marker.first.position, zoom: 14)
                                                      : const CameraPosition(
                                                    target: LatLng(-72.44564054136866, -14.852879270911215),
                                                    zoom: 5,
                                                  ),
                                                ),
                                              ),
                                              if (state.mapDetailList.isNotEmpty) ...{
                                                ListView.builder(
                                                  shrinkWrap: true,
                                                  physics: const NeverScrollableScrollPhysics(),
                                                  itemCount: state.mapDetailList.length,
                                                  itemBuilder: (context, index) {
                                                    return Padding(
                                                      padding: EdgeInsets.symmetric(
                                                        vertical: AppSize.h10,
                                                      ),
                                                      child: Container(
                                                        decoration: BoxDecoration(
                                                          color: const Color.fromRGBO(235, 235, 235, 1),
                                                          //color: context.themeColors.whiteColor,
                                                          border: Border.all(
                                                                color: const Color(0xFFBDBDBD),
                                                              ),
                                                          borderRadius: BorderRadius.circular(AppSize.r4),
                                                          // boxShadow: [
                                                          //   BoxShadow(
                                                          //     color: Colors.black.withOpacity(0.10),
                                                          //     // Very light shadow color
                                                          //     offset: const Offset(
                                                          //       0,
                                                          //       3,
                                                          //     ),
                                                          //     // Slight vertical shadow (bottom only)
                                                          //     blurRadius: 6,
                                                          //     // Subtle blur effect
                                                          //     spreadRadius: 1, // Light spreading of the shadow
                                                          //   ),
                                                          // ],
                                                        ),
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w8,
                                                            right: AppSize.w12,
                                                            top: AppSize.h10,
                                                            bottom: AppSize.h10,
                                                          ),
                                                          child: Row(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Stack(
                                                                alignment: Alignment.center,
                                                                children: [
                                                                  Assets.icons.redMarkerIcon.image(
                                                                    height: AppSize.h40,
                                                                    width: AppSize.w40,
                                                                  ),
                                                                  Positioned(
                                                                    top: 9,
                                                                    child: Text(
                                                                      '${index + 1}',
                                                                      style: context.textTheme.titleSmall?.copyWith(
                                                                        fontWeight: FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              SpaceH(AppSize.w10),
                                                              Expanded(
                                                                child: Column(
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  children: [
                                                                    AppTextWidget(
                                                                      state.mapDetailList[index].what ?? '',
                                                                      style: context.textTheme.titleSmall,
                                                                    ),
                                                                    SpaceV(AppSize.h10),
                                                                    AppTextWidget(
                                                                      (state.mapDetailList[index].why ?? 0) == (situtationWhyList.length -  1)? state.mapDetailList[index].customWhy ??''
                                                                          : situtationWhyList[state.mapDetailList[index].why ?? 0],
                                                                      style: context.textTheme.titleSmall?.copyWith(color: context.themeColors.redColor,
                                                                      ),
                                                                    ),
                                                                    // AppTextWidget(
                                                                    //   situtationWhyList[state.mapDetailList[index].why ?? 0],
                                                                    //   style: context.textTheme.titleSmall?.copyWith(
                                                                    //     color: context.themeColors.redColor,
                                                                    //   ),
                                                                    // ),
                                                                    SpaceV(AppSize.h10),
                                                                    AppTextWidget(
                                                                      (state.mapDetailList[index].how ??0) == (situtationHowList.length - 1)?(state.mapDetailList[index].customHow ??'')
                                                                          : situtationHowList[
                                                                      state.mapDetailList[index].how ?? 0],
                                                                      style: context
                                                                          .textTheme.titleSmall
                                                                          ?.copyWith(
                                                                        color: context
                                                                            .themeColors
                                                                            .greenBtnColor,
                                                                      ),
                                                                    ),
                                                                    // AppTextWidget(
                                                                    //   situtationHowList[state.mapDetailList[index].how ?? 0],
                                                                    //   style: context.textTheme.titleSmall?.copyWith(
                                                                    //     color: context.themeColors.greenBtnColor,
                                                                    //   ),
                                                                    // ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              },
                                            ],
                                          ),
                                          MultiValueListenableBuilder(
                                            valueListenables: [downloadPdfAPILoading, emailPdfAPILoading],
                                            builder: (context, List<dynamic> values, child) {
                                              return Column(
                                                children: [
                                                  SpaceV(AppSize.h10),
                                                  CustomYesNoButton(
                                                    padding: EdgeInsets.zero,
                                                    isDownLoad: true,
                                                    isYesNoButton: true,
                                                    onDownloadTap: () {
                                                      CustomDownloadPopup.buildPopupMenu(
                                                        context: context,
                                                        onDownLoadPdf: () async {
                                                          await widget.ref.dsActionStrategyforDownloadPdfApi(
                                                            context: context,
                                                            isEmail: false,
                                                            downloadPdfAPILoading: downloadPdfAPILoading,
                                                            emailPdfAPILoading: emailPdfAPILoading,
                                                          );
                                                        },
                                                        onEmailDownload: () async {
                                                          await widget.ref.dsActionStrategyforDownloadPdfApi(
                                                            context: context,
                                                            isEmail: true,
                                                            downloadPdfAPILoading: downloadPdfAPILoading,
                                                            emailPdfAPILoading: emailPdfAPILoading,
                                                          );
                                                        },
                                                      );
                                                    },
                                                    exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                                    agreeText: AsLocaleKeys.lsUbButtonsFinish.tr(),
                                                    onTapYes: () {
                                                      AppNavigation.pushAndRemoveAllScreen(
                                                        context,
                                                        const MyDiagramPage(),
                                                      );
                                                    },
                                                    onTapNo: () {},
                                                    noButtonColor: context.themeColors.orangeColor,
                                                  ),
                                                  // CustomButton(
                                                  //   title: CoreLocaleKeys.buttonsNext.tr(),
                                                  //   onTap: () {
                                                  //     AppNavigation.nextScreen(context, const MyDiagramPage());
                                                  //   },
                                                  //   isBottom: true,
                                                  //   color: context.themeColors.blueColor,
                                                  // ),
                                                ],
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

}

Map<String, dynamic> countryMap = {
  'UK': {'lat': 55.3781, 'lng': -3.4360, 'zoom': 5.0}, // United Kingdom
  'US': {'lat': 37.0902, 'lng': -95.7129, 'zoom': 4.0}, // USA
  'AU': {'lat': -25.2744, 'lng': 133.7751, 'zoom': 0.0}, // Australia
  'CA': {'lat': 56.1304, 'lng': -106.3468, 'zoom': 4.0}, // Canada
};

String countryCode = navigatorKey.currentContext!.locale.countryCode?.toUpperCase() ??
    'UK'; // Replace with actual method to get the country code
Map<String, dynamic> countryData = (countryMap[countryCode] ?? countryMap['UK']) as Map<String, dynamic>;

CameraPosition _initialCameraPosition() {
  print('countryData ${countryMap[countryCode]}');
  print('countryData ${navigatorKey.currentContext!.locale.countryCode?.toUpperCase()}');
  return CameraPosition(
    target: LatLng(countryData['lat'] as double, countryData['lng'] as double),
    zoom: countryData['zoom'] as double,
  );
}
