import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/negative_thoughts_module/pages/my_thoughts_action_plan_page.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/summary_page/summary_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/keys/toolkit_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/widgets/custom_information_summary.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NegativeThoughts extends StatelessWidget {
  const NegativeThoughts({
    required this.ref,
    super.key,
    this.iconPath,
    this.iconInfoPath,
  });

  final MyRecoveryToolKitCubit ref;
  final String? iconInfoPath;
  final String? iconPath;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: ref.showNegativeThoughtsResources,
      builder: (context, value, child) {
        return Column(
          children: [
            Padding(
              padding:  EdgeInsets.only(bottom: AppSize.h8),
              child: AppTextWidget(
                '${ToolKitLocaleKeys.total.tr()}: ${(Injector.instance<AppDB>().userModel?.user.strategies?.ntAs?.length ?? 0) + (Injector.instance<AppDB>().userModel?.user.strategies?.ntIs?.length ?? 0)}',
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.ntIs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: iconInfoPath ?? Assets.icons.infoIcons.physicalSenstationInfoIcon,
              )
            else
              ...ref.displayNtIs.map(
                (item) => GestureDetector(
                  onTap: () {
                    AppNavigation.nextScreen(
                      context,
                      SummaryPage(
                        diagramState: MyDiagramStates.negativeThoughts,
                      ),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: iconInfoPath ?? Assets.icons.infoIcons.physicalSenstationInfoIcon,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                  ),
                ),
              ),
            if ((Injector.instance<AppDB>().userModel?.user.strategies?.ntAs?.length ?? 0) == 0)
              CustomDottedSummary(
                iconPath: iconPath ?? Assets.icons.actionIcons.physicalSansations,
                title: ToolKitLocaleKeys.actionPlan.tr(),
              )
            else
              ...ref.displayNtAs.map(
                (item) => GestureDetector(
                  onTap: () {
                    //  final ref = BlocProvider.of<NegativeThoughtsCubit>(context);
                    AppNavigation.nextScreen(
                      context,
                      BlocProvider.value(
                        value: ref, // Pass the existing Cubit instance
                        child: MyThoughtsActionPlanPage(
                          recoveryCubit: ref,
                          time: item.time,
                          selectedMindTrapValue: ValueNotifier(
                            Injector.instance<AppDB>()
                                    .userModel
                                    ?.user
                                    .strategies
                                    ?.ntAs
                                    ?.firstWhere(
                                      (element) =>
                                          element.time == item.time!, // Replace 'desiredTime' with your condition
                                    )
                                    .data
                                    ?.mindTrap ??
                                '',
                          ),
                          isFromToolKit: true,
                        ), // The new screen that will use the Cubit
                      ),
                    );
                  },
                  child: CustomInformationSummary(
                    iconPath: iconPath ?? Assets.icons.actionIcons.physicalSansations,
                    date:ref.formatDate(DateTime.fromMillisecondsSinceEpoch(item.time!)) ,
                    title: ToolKitLocaleKeys.actionPlan.tr(),
                  ),
                ),
              ),
            SpaceV(AppSize.h2),
            GestureDetector(
              onTap: () {
                ref.showNegativeThoughtsResources.value = !ref.showNegativeThoughtsResources.value;

                ref.getNegativeThoughtUserData(context);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppTextWidget(
                    ref.showNegativeThoughtsResources.value
                        ? ToolKitLocaleKeys.hideEarlier.tr()
                        : ToolKitLocaleKeys.showEarlier.tr(),
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: AppSize.sp12,
                      color: context.themeColors.greenColor,
                    ),
                  ),
                  Icon(
                    ref.showNegativeThoughtsResources.value
                        ? Icons.arrow_upward_outlined
                        : Icons.arrow_downward_outlined,
                    size: AppSize.sp12,
                    color: context.themeColors.greenColor,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
