import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/keys/toolkit_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomInformationSummary extends StatelessWidget {
  const CustomInformationSummary({
    required this.iconPath,
    required this.date,
    super.key,
    this.title,
  });
  final String iconPath;
  final String date;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSize.w30, vertical: AppSize.h6),
      child: Container(
        decoration: BoxDecoration(
          color: context.themeColors.greyColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppSize.r12),
          border: Border.all(
            color: const Color(0xFFBDBDBD),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: AppSize.h10, horizontal: AppSize.w10),
          child: Row(
            children: [
              SpaceH(AppSize.w8),
              CustomIconButton(
                iconPath: iconPath,
                size: AppSize.h38,
              ),
              SpaceH(AppSize.w10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppTextWidget(
                      maxLines: 1,
                      overFlow: TextOverflow.ellipsis,
                      title ?? ToolKitLocaleKeys.informationSummary.tr(),
                      style: context.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    AppTextWidget(
                      date,
                      style: context.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: AppSize.sp12,
                        color: context.themeColors.greyColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomDottedSummary extends StatelessWidget {
  const CustomDottedSummary({
    required this.iconPath,
    super.key,
    this.title,
  });
  final String iconPath;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSize.w30, vertical: AppSize.h6),
      child: DottedBorder(
        borderType: BorderType.RRect,
        color: context.themeColors.greyColor,
        radius: Radius.circular(AppSize.r12),
        strokeWidth: 1.5,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: AppSize.h8, horizontal: AppSize.w10),
          child: Row(
            children: [
              SpaceH(AppSize.w8),
              CustomIconButton(
                iconPath: iconPath,
                size: AppSize.h38,
              ),
              SpaceH(AppSize.w10),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(right: AppSize.h4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FittedBox(
                        child: AppTextWidget(
                          title ?? ToolKitLocaleKeys.informationSummary.tr(),
                          style: context.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      AppTextWidget(
                        'None yet',
                        style: context.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: AppSize.sp12,
                          color: context.themeColors.greyColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
