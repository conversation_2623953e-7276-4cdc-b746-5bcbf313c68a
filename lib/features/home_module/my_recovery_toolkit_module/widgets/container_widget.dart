import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/keys/daigram_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/widgets/action_btn.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomContainer extends StatelessWidget {
  const CustomContainer({
    required this.title,
    required this.visible,
    super.key,
    this.color,
    this.onTap,
    this.onInfoButtonTap,
    this.onActionButtonTap,
    this.score = 0,
  });
  final String title;
  final Color? color;
  final int score;
  final void Function()? onTap;
  final void Function()? onInfoButtonTap;
  final void Function()? onActionButtonTap;
  final ValueNotifier<bool> visible;

  DiagramBtnColor _getSelectedColor(int score) {
    'score $score'.logD;
    if (score >= 0 && score <= 2) {
      return DiagramBtnColor.green;
    } else if (score >= 3 && score <= 6) {
      return DiagramBtnColor.orange;
    } else if (score >= 7 && score <= 10) {
      return DiagramBtnColor.red;
    } else {
      // Handle out-of-range scores if necessary
      return DiagramBtnColor.orange;
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedColor = _getSelectedColor(score);

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
        child: Container(
          decoration: BoxDecoration(
            color: selectedColor.color,
            borderRadius: BorderRadius.circular(AppSize.r12),
          ),
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: AppSize.h16,
            ),
            child: Column(
              children: [
                AppTextWidget(
                  title,
                  style: context.textTheme.titleSmall?.copyWith(
                    fontSize: AppSize.sp14,
                    color: context.themeColors.whiteColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: visible,
                  builder: (context, value, child) {
                    return Visibility(
                      visible: value,
                      child: Column(
                        children: [
                          SpaceV(AppSize.h16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ActionBtn(
                                name: DiagramLocaleKeys.buttonsInformation.tr(),
                                onInformationTap: onInfoButtonTap,
                              ),
                              SpaceH(AppSize.w8),
                              ActionBtn(
                                width: AppSize.w100,
                                name: DiagramLocaleKeys.buttonsAction.tr(),
                                onInformationTap: onActionButtonTap,
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
