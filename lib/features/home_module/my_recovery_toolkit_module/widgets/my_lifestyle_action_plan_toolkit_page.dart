// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_download_bottom_sheet.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/extensions/ext_string_null.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/common_action_widgets/custom_action_plan_text_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/keys/as_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/common_lifestyle.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_offset.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/flag_painter.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/keys/is_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/information_page/widgets/information_page_heading_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/my_recovery_toolkit_module/cubit/my_recovery_tool_kit_cubit.dart';
import 'package:breakingfree_v2/features/home_module/widgets/app_drawer.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/assets_path.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class MyLifestyleActionPlanToolkitPage extends StatefulWidget {
  const MyLifestyleActionPlanToolkitPage({
    required this.question1,
    required this.question2,
    required this.selectBarrierValue,
    required this.selectedBenefitIndices,
    required this.date,
    required this.otherdata,
    required this.mountainName,
    this.ref,
    super.key,
  });
  final String question1;
  final String question2;
  final int selectBarrierValue;
  final List<int> selectedBenefitIndices;
  final String date;
  final String otherdata;
  final String mountainName;
  final MyRecoveryToolKitCubit? ref;

  @override
  State<MyLifestyleActionPlanToolkitPage> createState() => _MyLifestyleActionPlanToolkitPageState();
}

class _MyLifestyleActionPlanToolkitPageState extends State<MyLifestyleActionPlanToolkitPage> {
  late Future<ui.Image> lightGreenFlag;
  late Future<ui.Image> darkGreenFlag;
  ValueNotifier<String?> headerInfoText = ValueNotifier(null);

  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  final ValueNotifier<bool> downloadPdfAPILoading = ValueNotifier(false);
  final ValueNotifier<bool> emailPdfAPILoading = ValueNotifier(false);

  ValueNotifier<bool> isAudioPanelVisible = ValueNotifier(false);
  final scaffoldActionPlanKey = GlobalKey<ScaffoldState>();

  List<String> barrierList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsIdentifyBarrierOptions,
    navigatorKey.currentContext!,
  ) as List<dynamic>)
      .cast<String>();

  final List<String> beenfitList = (DynamicAssetLoader.getNestedValue(
    AsLocaleKeys.lsMotivateBenefits,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  List<String> getBenefitsFromIndices(List<int> indices) {
    final selectedBenefits = <String>[];
    for (final index in indices) {
      if (index >= 0 && index < beenfitList.length) {
        selectedBenefits.add(beenfitList[index]);
      } else {
        selectedBenefits.add('Invalid index');
      }
    }
    return selectedBenefits;
  }

  Future<ui.Image> _loadImage(String imagePath) async {
    final bd = await rootBundle.load(imagePath);
    final bytes = Uint8List.view(bd.buffer);
    final codec = await ui.instantiateImageCodec(bytes, targetHeight: 30, targetWidth: 25, allowUpscaling: false);
    final image = (await codec.getNextFrame()).image;
    return image;
  }

  int getMountainName(String value) {
    switch (value) {
      case 'Everest':
        return 0;
      case 'Kilimanjaro':
        return 1;
      case 'Machu Picchu':
        return 2;
      case 'Matterhorn':
        return 3;
      default:
        return -1;
    }
  }

  @override
  void initState() {
    'widget.mountainName ${widget.mountainName}'.logD;
    infoAudioUrl.value = AsLocaleKeys.lsActionPlanAudio.tr();

    isAudioPanelVisible.value = false;
    lightGreenFlag = _loadImage('assets/icons/anim_asset/flag_light_green.png');
    darkGreenFlag = _loadImage('assets/icons/anim_asset/flag_dark_green.png');

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    '???? my date = ${widget.date}'.logD;
    widget.mountainName.logD;
    return ValueListenableBuilder(
      valueListenable: infoAudioUrl,
      builder: (context, value, child) {
        return AppScaffold(
          // resizeToAvoidBottomInset: false,
          scaffoldKey: scaffoldActionPlanKey,
          isAudioPanelVisible: isAudioPanelVisible,
          infoAudioUrl: infoAudioUrl,
          drawer: AppDrawer(scaffoldKey: scaffoldActionPlanKey),
          appBar: CommonAppBar(
            onPrefixTap: () {
              scaffoldActionPlanKey.currentState?.openDrawer();
            },
            onSuffixTap: () {
              if (infoAudioUrl.value.isNotEmptyAndNotNull) {
                isAudioPanelVisible.value = !isAudioPanelVisible.value;
              }
            },
          ),
          body: ColoredBox(
            color: context.themeColors.whiteColor,
            child: Column(
              children: [
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(minHeight: constraints.maxHeight),
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: AppSize.w24,
                              right: AppSize.w24,
                              bottom: AppSize.h20,
                              top: AppSize.h20,
                            ),
                            child: Column(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    InformationPageHeadingWidget(
                                      onBackArrowTap: () {
                                        headerInfoText.value = null;

                                        infoAudioUrl.value = AsLocaleKeys.lsSummaryAudio.tr();
                                        Navigator.pop(context);
                                      },
                                      title: CoreLocaleKeys.titlesInformationStrategiesLifestyle.tr(),
                                      subtitle: AsLocaleKeys.lsTitle.tr(),
                                      icon: Assets.icons.infoPage.difficultSituation,
                                      onInfoTap: () {
                                        if (infoAudioUrl.value == AsLocaleKeys.lsInfoPanelsInformationAudio.tr()) {
                                          infoAudioUrl.value = AsLocaleKeys.lsActionPlanAudio.tr();
                                        } else {
                                          infoAudioUrl.value = AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                        }
                                        final info = (DynamicAssetLoader.getNestedValue(
                                          AsLocaleKeys.lsInfoPanelsInformationText,
                                          context,
                                        ) as List)
                                            .join('<br/><br/>');
                                        headerInfoText.value =
                                            (headerInfoText.value.isNotEmptyAndNotNull && headerInfoText.value == info)
                                                ? null
                                                : info;
                                        final audio = AsLocaleKeys.lsInfoPanelsInformationAudio.tr();
                                        log('audio $audio');
                                        infoAudioUrl
                                          ..value = null
                                          ..value = infoAudioUrl.value == audio ? null : audio;
                                      },
                                      onLearnTap: () {
                                        if (infoAudioUrl.value == AsLocaleKeys.lsInfoPanelsLearnAudio.tr()) {
                                          infoAudioUrl.value = AsLocaleKeys.lsActionPlanAudio.tr();
                                        } else {
                                          infoAudioUrl.value = AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                        }

                                        final info = (DynamicAssetLoader.getNestedValue(
                                          AsLocaleKeys.lsInfoPanelsLearnText,
                                          context,
                                        ) as List)
                                            .join('<br/><br/>');
                                        headerInfoText.value =
                                            (headerInfoText.value.isNotEmptyAndNotNull && headerInfoText.value == info)
                                                ? null
                                                : info;
                                        // final audio = AsLocaleKeys.lsInfoPanelsLearnAudio.tr();
                                        // infoAudioUrl.value = infoAudioUrl.value == audio ? null : audio;
                                      },
                                      infoWidget: ValueListenableBuilder(
                                        valueListenable: headerInfoText,
                                        builder: (context, headerPlanInfoTextV, _) {
                                          return CustomInfoWidget(
                                            customWidget: Column(
                                              children: [
                                                Html(
                                                  data: headerInfoText.value ?? '',
                                                  style: {
                                                    'strong': Style(
                                                      fontSize: FontSize(AppSize.sp13),
                                                      color: context.themeColors.darkOrangeColor,
                                                      fontWeight: FontWeight.bold,
                                                      fontFamily: 'Poppins',
                                                    ),
                                                    'body': Style(
                                                      fontSize: FontSize(AppSize.sp13),
                                                      color: context.themeColors.darkOrangeColor,
                                                      fontFamily: 'Poppins',
                                                    ),
                                                  },
                                                ),
                                              ],
                                            ),
                                            onCloseTap: () {
                                              headerInfoText.value = null;
                                              infoAudioUrl.value = null;
                                            },
                                            visible: headerPlanInfoTextV.isNotEmptyAndNotNull,
                                            margin: EdgeInsets.symmetric(
                                              vertical: AppSize.h8,
                                            ),
                                            bodyText: headerPlanInfoTextV,
                                          );
                                        },
                                      ),
                                    ),
                                    SpaceV(AppSize.h6),
                                    Center(
                                      child: AppTextWidget(
                                        AsLocaleKeys.lsActionPlanTitle.tr(),
                                        style: context.textTheme.titleSmall?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    SpaceV(AppSize.h14),
                                    AppTextWidget(
                                      (DynamicAssetLoader.getNestedValue(
                                        AsLocaleKeys.lsActionPlanText,
                                        context,
                                      ) as List)
                                          .join('\n\n'),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h16),
                                    Container(
                                      clipBehavior: Clip.hardEdge,
                                      decoration: BoxDecoration(
                                        color: const Color.fromRGBO(200, 208, 232, 1),
                                        borderRadius: BorderRadius.circular(AppSize.r14),
                                      ),
                                      child: Stack(
                                        children: [
                                          AppCachedNetworkImage(
                                            imageUrl:
                                                MyLifeStyle.getMountainImage(getMountainName(widget.mountainName)),
                                          ),
                                          FutureBuilder(
                                            future: Future.wait([lightGreenFlag, darkGreenFlag]),
                                            builder: (context, snapshot) {
                                              if (snapshot.hasData) {
                                                return SizedBox(
                                                  height: AppSize.h130,
                                                  width: double.maxFinite,
                                                  child: CustomPaint(
                                                    painter: FlagPainter(
                                                      darkGreenFlag: snapshot.data!.last,
                                                      lightGreenFlag: snapshot.data!.first,
                                                      flagOffsets: FlagOffset.getEverestFlagsByIndex(
                                                        3,
                                                        getMountainName(widget.mountainName),
                                                      ),
                                                      lineOffsets: FlagOffset.getEverestLineByIndex(
                                                        3,
                                                        getMountainName(widget.mountainName),
                                                      ),
                                                      animation: 1,
                                                    ),
                                                  ),
                                                );
                                              } else {
                                                return SizedBox(
                                                  height: AppSize.h100,
                                                );
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    SpaceV(AppSize.h16),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanLifeGoal.tr(),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h8),
                                    CustomActionPlanTextWidget(
                                      question: widget.question1,
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                    ),
                                    SpaceV(AppSize.h16),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanNextStep.tr(),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h8),
                                    CustomActionPlanTextWidget(
                                      question: widget.question2,
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                    ),
                                    SpaceV(AppSize.h16),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanBarrier.tr(),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h8),
                                    CustomActionPlanTextWidget(
                                      question: barrierList[widget.selectBarrierValue],
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                    ),
                                    SpaceV(AppSize.h16),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanPlanned.tr(),
                                      style: context.textTheme.titleSmall?.copyWith(
                                        fontSize: AppSize.sp13,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    SpaceV(AppSize.h8),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanCommit.tr(),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h8),
                                    CustomActionPlanTextWidget(
                                      //question: DateFormat('EEE HH:mm').format(DateTime.parse(widget.date)),
                                      question: 
                                          '${widget.ref!.formatDate2(DateTime.parse(widget.date),isDay: true)} ${widget.ref!.getHour(widget.date)}:${widget.ref!.getMinute(widget.date)}',
                                      padding: EdgeInsets.only(top: AppSize.h8),
                                    ),
                                    SpaceV(AppSize.h16),
                                    AppTextWidget(
                                      AsLocaleKeys.lsActionPlanBenefits.tr(),
                                      style: context.textTheme.titleSmall,
                                    ),
                                    SpaceV(AppSize.h8),
                                    ListView(
                                      shrinkWrap: true, // Prevent ListView from taking up all space
                                      children: getBenefitsFromIndices(widget.selectedBenefitIndices).map((benefit) {
                                        return Row(
                                          children: [
                                            Padding(
                                                                padding: EdgeInsets.only(
                                                                  top:  AppSize.h10,
                                                                ), // Adjust for vertical alignment
                                                                child: const AppCachedNetworkImage(
                                                                  imageUrl: AssetsPath.greenBulletPoint,
                                                                ),
                                                              ),
                                            Padding(
                                              padding:  EdgeInsets.only(top: AppSize.h5, left: AppSize.w10,bottom: AppSize.h5),
                                              child: AppTextWidget(
                                                                                        benefit,
                                                                                        style: context.textTheme.titleSmall?.copyWith(
                                              fontWeight: FontWeight.w600,
                                                                                        ),
                                                                                      ),
                                            ) 
                                          ],
                                        );// Display each benefit as a text widget
                                      }).toList(),
                                    ),
                                    if (widget.otherdata.isNotEmpty) ...{
                                      SpaceV(AppSize.h8),
                                      //Text('ff'),
                                      CustomActionPlanTextWidget(
                                        question: widget.otherdata, // Join the filtered values with new line
                                        padding: EdgeInsets.only(top: AppSize.h8),
                                      ),
                                    },
                                    SpaceV(AppSize.h20),
                                  ],
                                ),
                                MultiValueListenableBuilder(
                                  valueListenables: [downloadPdfAPILoading, emailPdfAPILoading],
                                  builder: (context, List<dynamic> values, child) {
                                    return CustomYesNoButton(
                                      padding: EdgeInsets.zero,
                                      isDownLoad: true,
                                      isYesNoButton: true,
                                      exitText: CoreLocaleKeys.buttonsDownloadPdf.tr(),
                                      agreeText: IsLocaleKeys.buttonsFinish.tr(),
                                      inNoProgress: ValueNotifier<bool>(
                                        (downloadPdfAPILoading.value) || (emailPdfAPILoading.value),
                                      ),
                                      onDownloadTap: () {
                                        CustomDownloadPopup.buildPopupMenu(
                                          context: context,
                                          onDownLoadPdf: () async {
                                            await widget.ref?.lsActionStrategyforDownloadPdfApi(
                                              context: context,
                                              isEmail: false,
                                              isDownloadPdfAPILoading: downloadPdfAPILoading,
                                              isEmailPdfAPILoading: emailPdfAPILoading,
                                            );
                                          },
                                          onEmailDownload: () async {
                                            await widget.ref?.lsActionStrategyforDownloadPdfApi(
                                              context: context,
                                              isEmail: true,
                                              isDownloadPdfAPILoading: downloadPdfAPILoading,
                                              isEmailPdfAPILoading: emailPdfAPILoading,
                                            );
                                          },
                                        );
                                      },
                                      onTapYes: () async {
                                        // log('ref.selectedBenefitValue.value ${ref.selectedDateController.text}');
                                        // ref.infoActionAudioUrl.value = null;
                                        await AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                      },
                                      onTapNo: () async {},
                                      noButtonColor: context.themeColors.orangeColor,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // CustomButton(
                //   title: CoreLocaleKeys.surveyThankYouFinishButton.tr(),
                //   onTap: () async {
                //     // log('selectedBenefitValue.value ${selectedDateController.text}');
                //     await AppNavigation.nextScreen(context, const MyDiagramPage());
                //   },
                //   isBottom: true,
                //   color: context.themeColors.blueColor,
                // ),
              ],
            ),
          ),
        );
      },
    );
  }
}
