import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drugs_page.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_drinking_goal_cubit.freezed.dart';
part 'assessment_drinking_goal_state.dart';

class AssessmentDrinkingGoalCubit extends Cubit<AssessmentDrinkingGoalState> {
  AssessmentDrinkingGoalCubit() : super(AssessmentDrinkingGoalState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

//Drinking Goal
  final ValueNotifier<TextEditingController> drinkingGoalUnitsController = ValueNotifier(TextEditingController(text: '0'));
  final ValueNotifier<TextEditingController> drinkingGoalFreeDayController = ValueNotifier(TextEditingController(text: '0'));

  void userDrinkingGoalData() {
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.drinkingGoal;

    // Prepopulate data if available
    if (userModel != null) {
      drinkingGoalUnitsController.value.text = userModel.units?.toString() ?? '0';
      drinkingGoalFreeDayController.value.text = userModel.freeDays?.toString() ?? '0';
      emit(
        state.copyWith(
          currentDrinkingUnit: userModel.units ?? 0,
          currentDrinkingfreeDays: userModel.freeDays ?? 0,
        ),
      );
    }
  }
  
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);
  void assignValue() {
    isManuallyPaused.value = true;
    emit(state.copyWith(
      currentDrinkingUnit: int.tryParse(drinkingGoalUnitsController.value.text) ?? 0,
      ));
      emit(
        state.copyWith(
          currentDrinkingfreeDays: int.tryParse(drinkingGoalFreeDayController.value.text) ?? 0,
        ),
      );
  }

  // void increaseDrinkingGoalUnitsValue() {
  //   if (state.currentDrinkingUnit < 10000) {
  //     final newValue = state.currentDrinkingUnit + 1;
  //     drinkingGoalUnitsController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingUnit: newValue,
  //       ),
  //     );
  //   }
  // }

  void increaseDrinkingGoalUnitsValue() {
  final textValue = int.tryParse(drinkingGoalUnitsController.value.text) ?? state.currentDrinkingUnit;

  if (textValue < 10000) {
    final newValue = textValue + 1;
    drinkingGoalUnitsController.value.text = newValue.toString();
    // emit(state.copyWith(currentDrinkingUnit: newValue));
  }
}

void decreaseDrinkingUnitValue() {
  final textValue = int.tryParse(drinkingGoalUnitsController.value.text) ?? state.currentDrinkingUnit;

    if (textValue > 0) {
      final newValue = textValue - 1;
      drinkingGoalUnitsController.value.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDrinkingUnit: newValue,
      //   ),
      // );
    }
  }

  // void decreaseDrinkingUnitValue() {
  //   if (state.currentDrinkingUnit > 0) {
  //     final newValue = state.currentDrinkingUnit - 1;
  //     drinkingGoalUnitsController.value.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingUnit: newValue,
  //       ),
  //     );
  //   }
  // }

  // void increaseDrinkingGoalFreeDayValue() {
  //   if (state.currentDrinkingfreeDays < 7) {
  //     final newValue = state.currentDrinkingfreeDays + 1;
  //     drinkingGoalFreeDayController.value.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingfreeDays: newValue,
  //       ),
  //     );
  //   }
  // }

  void increaseDrinkingGoalFreeDayValue() {
    final textValue = int.tryParse(drinkingGoalFreeDayController.value.text) ?? state.currentDrinkingfreeDays;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drinkingGoalFreeDayController.value.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDrinkingfreeDays: newValue,
      //   ),
      // );
    }
  }

void decreaseDrinkingFreeDayValue() {
  final textValue = int.tryParse(drinkingGoalFreeDayController.value.text) ?? state.currentDrinkingfreeDays;
    if (textValue > 0) {
      final newValue = textValue - 1;
      drinkingGoalFreeDayController.value.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDrinkingfreeDays: newValue,
      //   ),
      // );
    }
  }

  // void decreaseDrinkingFreeDayValue() {
  //   if (state.currentDrinkingfreeDays > 0) {
  //     final newValue = state.currentDrinkingfreeDays - 1;
  //     drinkingGoalFreeDayController.value.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingfreeDays: newValue,
  //       ),
  //     );
  //   }
  // }

  Future<void> putdrinkingGoalAPI({
    required int units,
    required int freeDays,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putdrinkingGoal(
        context: context,
        units: units,
        freeDays: freeDays,
      );
      if (response != null && (response.success ?? false) == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);

        if (Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 0) {
          context.read<AssessmentCubit>().gotoNextWidget(increment: 4);
        } else {
          context.read<AssessmentDrugCubit>().isManuallyPaused.value = false;
          context.read<AssessmentCubit>().gotoNextWidget();
        }
        //  context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close
    drinkingGoalUnitsController.dispose();
    drinkingGoalFreeDayController.dispose();

    return super.close();
  }
}
