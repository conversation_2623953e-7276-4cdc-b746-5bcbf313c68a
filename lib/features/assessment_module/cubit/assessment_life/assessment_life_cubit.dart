import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_situtation/assessment_situtation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_life_cubit.freezed.dart';
part 'assessment_life_state.dart';

class AssessmentLifeCubit extends Cubit<AssessmentLifeState> {
  AssessmentLifeCubit() : super(AssessmentLifeState());
  AssessmentRepository assessmentRepository = AssessmentRepository();
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isLifeButtonClicked = ValueNotifier(false);

//Life page
  ValueNotifier<String> qualityValue = ValueNotifier('');
  ValueNotifier<String> healthValue = ValueNotifier('');
  ValueNotifier<String> activitiesValue = ValueNotifier('');
  ValueNotifier<String> relationshipsValue = ValueNotifier('');
  ValueNotifier<String> workValue = ValueNotifier('');
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.lifeAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<bool> islifeFirstInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> islifeSecondInfoVisible = ValueNotifier(false);

  ValueNotifier<int> lifeFirstSliderValue = ValueNotifier(-1);
  ValueNotifier<int> lifeSecondSliderValue = ValueNotifier(-1);

  final qualityKey = GlobalKey();
  final healthKey = GlobalKey();
  final activitiesKey = GlobalKey();
  final relationshipKey = GlobalKey();
  final workKey = GlobalKey();

  List<String> qualityList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.lifeQuestionsQualityItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> healthList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.lifeQuestionsHealthItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> activitiesList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.lifeQuestionsActivitiesItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> relationshipsList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.lifeQuestionsRelationshipsItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> workList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.lifeQuestionsWorkItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  void userLiftData() {
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.life;

    // Prepopulate data if available
    if (userModel != null) {
      qualityValue.value =
          (userModel.quality != null && userModel.quality! >= 0 && userModel.quality! < qualityList.length)
              ? qualityList[userModel.quality!]
              : qualityValue.value;

      healthValue.value = (userModel.health != null && userModel.health! >= 0 && userModel.health! < healthList.length)
          ? healthList[userModel.health!]
          : healthValue.value;

      activitiesValue.value =
          (userModel.activities != null && userModel.activities! >= 0 && userModel.activities! < activitiesList.length)
              ? activitiesList[userModel.activities!]
              : activitiesValue.value;

      relationshipsValue.value = (userModel.relationships != null &&
              userModel.relationships! >= 0 &&
              userModel.relationships! < relationshipsList.length)
          ? relationshipsList[userModel.relationships!]
          : relationshipsValue.value;

      workValue.value = (userModel.work != null && userModel.work! >= 0 && userModel.work! < workList.length)
          ? workList[userModel.work!]
          : workValue.value;

      // qualityValue.value = qualityList[userModel.assessment?.life?.quality ?? -1];
      // healthValue.value = healthList[userModel.assessment?.life?.health ?? -1];
      // activitiesValue.value = activitiesList[userModel.assessment?.life?.activities ?? -1];
      //  relationshipsValue.value = relationshipsList[userModel.assessment?.life?.relationships ?? -1];
      //  workValue.value = workList[userModel.assessment?.life?.work ?? -1];
      lifeFirstSliderValue.value = userModel.difficulties ?? -1;
      lifeSecondSliderValue.value = userModel.rate ?? -1;
    }
  }

  /// Auth repository for calling get user data api.
  Future<void> putAssessmentLifeAPI({
    required int quality,
    required int health,
    required int activities,
    required int relationships,
    required int work,
    required int difficulties,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      //   // isLoading.value = true;
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putAssessmentLife(
        context: context,
        quality: quality,
        health: health,
        activities: activities,
        difficulties: difficulties,
        rate: rate,
        relationships: relationships,
        work: work,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (context.read<AssessmentLifeCubit>().islifeSecondInfoVisible.value == true) {
          context.read<AssessmentLifeCubit>().infoAudioUrl.value = AssessmentLocaleKeys.lifeAudio.tr();
          context.read<AssessmentLifeCubit>().islifeSecondInfoVisible.value = false;
        }
              context.read<AssessmentSitutationCubit>().isManuallyPaused.value = false;

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close
    isLifeButtonClicked.dispose();
    islifeFirstInfoVisible.dispose();
    islifeSecondInfoVisible.dispose();
    qualityValue.dispose();
    healthValue.dispose();
    activitiesValue.dispose();
    relationshipsValue.dispose();
    workValue.dispose();
    lifeFirstSliderValue.dispose();
    lifeSecondSliderValue.dispose();
    return super.close();
  }
}
