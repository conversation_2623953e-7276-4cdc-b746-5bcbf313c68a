import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_life_style_cubit.freezed.dart';
part 'assessment_life_style_state.dart';

class AssessmentLifeStyleCubit extends Cubit<AssessmentLifeStyleState> {
  AssessmentLifeStyleCubit() : super(AssessmentLifeStyleState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.lifestyleAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isLifeStyleClicked = ValueNotifier(false);

  ValueNotifier<bool> rateLifeStyleInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> rateLifeStyleInfoVisibleLabel = ValueNotifier(false);
  ValueNotifier<int> rateLifeStyleSliderVlaue = ValueNotifier(-1);

  //Lifestyle
  ValueNotifier<ButtonState> healthLifeStyleState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> workLifeStyleState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> leisureState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> relationshipsState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> housingState = ValueNotifier(ButtonState.bothDisabled);

  final healthLifeStyleKey = GlobalKey();
  final workLifeStyleKey = GlobalKey();
  final leisureKey = GlobalKey();
  final relationshipsKey = GlobalKey();
  final housingKey = GlobalKey();

  void userLifeStyleData() {
    final userData = Injector.instance<AppDB>().userModel?.user.assessment?.ls;
    if (userData != null) {
      healthLifeStyleState.value = userData.health == null
          ? ButtonState.bothDisabled
          : userData.health == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      workLifeStyleState.value = userData.work == null
          ? ButtonState.bothDisabled
          : userData.work == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      leisureState.value = userData.leisure == null
          ? ButtonState.bothDisabled
          : userData.leisure == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      relationshipsState.value = userData.relationships == null
          ? ButtonState.bothDisabled
          : userData.relationships == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      housingState.value = userData.housing == null
          ? ButtonState.bothDisabled
          : userData.housing == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      rateLifeStyleSliderVlaue.value = userData.rate ?? -1;
    }
  }

  Future<void> putLifeStyleAPI({
    required int health,
    required int work,
    required int leisure,
    required int relationships,
    required int housing,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      isManuallyPaused.value = true;
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putLifeStyle(
        context: context,
        health: health,
        work: work,
        leisure: leisure,
        relationships: relationships,
        housing: housing,
        rate: rate,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (rateLifeStyleInfoVisible.value == true) {
          infoAudioUrl.value = AssessmentLocaleKeys.lifestyleAudio.tr();
          rateLifeStyleInfoVisible.value = false;
        }
        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close
    rateLifeStyleInfoVisible.dispose();
    isLifeStyleClicked.dispose();
    healthLifeStyleState.dispose();
    workLifeStyleState.dispose();
    leisureState.dispose();
    relationshipsState.dispose();
    housingState.dispose();
    rateLifeStyleSliderVlaue.dispose();

    return super.close();
  }
}
