import 'dart:developer';
import 'dart:io';
import 'dart:math' as Math;

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drugs_detail.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_drug_cubit.freezed.dart';
part 'assessment_drug_state.dart';

class AssessmentDrugCubit extends Cubit<AssessmentDrugState> {
  AssessmentDrugCubit() : super(AssessmentDrugState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.drugsAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  //Drug Goal
  final TextEditingController drugfrequencyController = TextEditingController(text: '0');
  final TextEditingController drugamountController = TextEditingController(text: '0');
  ValueNotifier<bool> isDrugFeelingButtonClicked = ValueNotifier(false);

  ValueNotifier<bool> drugSelectionoVisible = ValueNotifier(true);
  ValueNotifier<bool> drugSelectionDialogVisible = ValueNotifier(true);
  ValueNotifier<bool> rateDrugInfoVisible = ValueNotifier(false);

  ValueNotifier<bool> isDrugButtonClick = ValueNotifier(false);
  ValueNotifier<bool> isUnitAndDrugsButton = ValueNotifier(false);

  ValueNotifier<int> rateDrugsSliderVlaue = ValueNotifier(-1);
  Map<String, Map<String, String?>>? updatedDrugMap;

//drug Feeling
  ValueNotifier<String> drugControl = ValueNotifier('');
  ValueNotifier<String> drugAnxious = ValueNotifier('');
  ValueNotifier<String> drugWorry = ValueNotifier('');
  ValueNotifier<String> drugWill = ValueNotifier('');
  ValueNotifier<String> drugDifficulty = ValueNotifier('');
  ValueNotifier<String> drugRate = ValueNotifier('');

  void assignValue() {
    isManuallyPaused.value = true;
    emit(
        state.copyWith(
          drugGoalUnitValue: int.tryParse(drugGoalUnitsController.value.text) ?? 0,
        ),
      );
      emit(
        state.copyWith(
          currentdrugfreeDaysValue: int.tryParse(drugGoalFreeDayController.text) ?? 0,
        ),
      );
  }

  ValueNotifier<bool> isManuallyPausedForDrug = ValueNotifier(false);
  void assignValueForDrugDays() {
    isManuallyPausedForDrug.value = true;
    emit(
        state.copyWith(
          currentfrequencyValue: int.tryParse(drugfrequencyController.text) ?? 0,
        ),
      );
  }

 String formatName(String name) {
  if (name.isEmpty) return name;

  // If the name is all uppercase (acronym), keep as is
  if (name == name.toUpperCase()) return name;

  // Else, capitalize first letter only
  return name[0].toUpperCase() + name.substring(1).toLowerCase();
}

String normalize(String input) {
  return input
      .toLowerCase()
      .replaceAll(RegExp(r'[éèêë]'), 'e')
      .replaceAll(RegExp(r'[àâä]'), 'a')
      .replaceAll(RegExp(r'[îï]'), 'i')
      .replaceAll(RegExp(r'[ôö]'), 'o')
      .replaceAll(RegExp(r'[ûü]'), 'u');
}

//  String formatString(String str) {
//   final knownAcronyms = {'GHB', 'PVP', 'MDPV', 'MSJ', 'HGH', 'AH-7921', 'GBL', 'O-PCE'}; // Add all known acronyms
//   final wordRegExp = RegExp(r'\b[A-Za-z0-9-]+\b');

//   return str.replaceAllMapped(wordRegExp, (match) {
//     final word = match.group(0)!;

//     final parts = word.split('-');
//     final formattedParts = parts.map((part) {
//       final upperPart = part.toUpperCase();
//       if (knownAcronyms.contains(upperPart)) {
//         return upperPart;
//       } else {
//         return part.toLowerCase();
//       }
//     }).toList();

//     return formattedParts.join('-');
//   });
// }

String formatString(String str) {
  final regex = RegExp(r'\b[A-Za-z0-9-]+\b');

  return str.replaceAllMapped(regex, (match) {
    final word = match.group(0)!;

    // If the entire word is uppercase (with numbers or dashes), keep it as is
    if (RegExp(r'^[A-Z0-9-]+$').hasMatch(word)) {
      return word;
    }

    // Otherwise, convert parts of the word to lowercase unless they are acronyms
    return word.replaceAllMapped(RegExp(r'[A-Za-z0-9]+'), (subMatch) {
      final sub = subMatch.group(0)!;
      return RegExp(r'^[A-Z0-9]+$').hasMatch(sub) ? sub : sub.toLowerCase();
    });
  });
}

String? getFormattedDrugName(String inputDrug, List<String> drugList) {
  final inputLower = inputDrug.toLowerCase();

  return drugList.firstWhere(
    (drug) {
      // Remove anything in parentheses for comparison, and trim
      final cleanedDrug = drug.split('(')[0].trim().toLowerCase();
      return cleanedDrug == inputLower;
    },
    orElse: () => '',
  );
}

String matchDrugText(String text1, String text2) {
  /// Normalizes input by removing spaces, parentheses and converting to lowercase
  String normalize(String input) {
    return input
        .toLowerCase()
        .replaceAll(RegExp(r'\s+'), '')
        .replaceAll('(', '')
        .replaceAll(')', '');
  }

  final normalized1 = normalize(text1);
  final normalized2 = normalize(text2);

  if (normalized1.contains(normalized2) || normalized2.contains(normalized1)) {
    return text1; // Return the original first value
  }

  return ''; // No match
}

String getLocalizedDrugNameFromKey(String key) {
  final normalizedKey = key.toLowerCase().replaceAll(' ', '-').replaceAll('_', '-');
  final localeKey = drugLocaleKeyMap[normalizedKey];
  return localeKey?.tr() ?? key; // fallback to raw key if not found
}


final Map<String, String> drugLocaleKeyMap = {
  'ah-7921': DrugsLocaleKeys.aH7921Drug,
  'gbl': DrugsLocaleKeys.gbl,
  'ghb': DrugsLocaleKeys.ghb,
  'hgh': DrugsLocaleKeys.hgh,
  'mdpv': DrugsLocaleKeys.mdpv,
  'msj': DrugsLocaleKeys.msj,
  'o-pce': DrugsLocaleKeys.oPce,
  'sustanon': DrugsLocaleKeys.sustanon,
  'testosterone': DrugsLocaleKeys.testosterone,
  'acamprosate': DrugsLocaleKeys.acamprosate,
  'adderall': DrugsLocaleKeys.adderall,
  'alpha-pvp': DrugsLocaleKeys.alphaPvp,
  'ambien': DrugsLocaleKeys.ambien,
  'amitriptyline': DrugsLocaleKeys.amitriptyline,
  'amphetamines': DrugsLocaleKeys.amphetamines,
  'buprenorphine': DrugsLocaleKeys.buprenorphine,
  'butane': DrugsLocaleKeys.butane,
  'clonazepam': DrugsLocaleKeys.clonazepam,
  'co-codamol': DrugsLocaleKeys.coCodamol,
  'cocaine': DrugsLocaleKeys.cocaine,
  'codeine': DrugsLocaleKeys.codeine,
  'crack': DrugsLocaleKeys.crack,
  'demerol': DrugsLocaleKeys.demerol,
  'dexedrine': DrugsLocaleKeys.dexedrine,
  'diazepam': DrugsLocaleKeys.diazepam,
  'disulfiram': DrugsLocaleKeys.disulfiram,
  'ecstasy': DrugsLocaleKeys.ecstasy,
  'ephedrine': DrugsLocaleKeys.ephedrine,
  'etizolam': DrugsLocaleKeys.etizolam,
  'fentanyl': DrugsLocaleKeys.fentanyl,
  'gabapentin': DrugsLocaleKeys.gabapentin,
  'heroin': DrugsLocaleKeys.heroin,
  'hydromorphone': DrugsLocaleKeys.hydromorphone,
  'ketamine': DrugsLocaleKeys.ketamine,
  'khat': DrugsLocaleKeys.khat,
  'lorezepam': DrugsLocaleKeys.lorezepam,
  'lunesta': DrugsLocaleKeys.lunesta,
  'marijuana': DrugsLocaleKeys.marijuana,
  'mephedrone': DrugsLocaleKeys.mephedrone,
  'mephobarbital': DrugsLocaleKeys.mephobarbital,
  'methadone': DrugsLocaleKeys.methadone,
  'methamphetamine': DrugsLocaleKeys.methamphetamine,
  'methoxatamine': DrugsLocaleKeys.methoxatamine,
  'modafinil': DrugsLocaleKeys.modafinil,
  'morphine': DrugsLocaleKeys.morphine,
  'nalmefene': DrugsLocaleKeys.nalmefene,
  'naltrexone': DrugsLocaleKeys.naltrexone,
  'nitrazepam': DrugsLocaleKeys.nitrazepam,
  'nitrous-oxide': DrugsLocaleKeys.nitrousOxide,
  'oxandrolone': DrugsLocaleKeys.oxandrolone,
  'oxycontin': DrugsLocaleKeys.oxycontin,
  'oxymorphone': DrugsLocaleKeys.oxymorphone,
  'pcp': DrugsLocaleKeys.pcp,
  'phenobarbital': DrugsLocaleKeys.phenobarbital,
  'pholcodeine': DrugsLocaleKeys.pholcodeine,
  'pregabalin': DrugsLocaleKeys.pregabalin,
  'ritalin': DrugsLocaleKeys.ritalin,
  'sonata': DrugsLocaleKeys.sonata,
  'suboxone': DrugsLocaleKeys.suboxone,
  'synthetic-cannabis': DrugsLocaleKeys.syntheticCannabis,
  'temazepam': DrugsLocaleKeys.temazepam,
  'tobacco': DrugsLocaleKeys.tobacco,
  'tramadol': DrugsLocaleKeys.tramadol,
  'trenbolone': DrugsLocaleKeys.trenbolone,
  'triazolam': DrugsLocaleKeys.triazolam,
  'vicodin': DrugsLocaleKeys.vicodin,
  'xanax': DrugsLocaleKeys.xanax,
  'zopiclon': DrugsLocaleKeys.zopiclon,
  'alfentanil': DrugsLocaleKeys.alfentanil,
  'alpha-d2pv': DrugsLocaleKeys.alphaD2pv,
  'bromadoline': DrugsLocaleKeys.bromadoline,
  'bromazolam': DrugsLocaleKeys.bromazolam,
  'buvidal': DrugsLocaleKeys.buvidal,
  'caffeine': DrugsLocaleKeys.caffeine,
  'carfentanil': DrugsLocaleKeys.carfentanil,
  'desomorphine': DrugsLocaleKeys.desomorphine,
  'diclazepam': DrugsLocaleKeys.diclazepam,
  'flephedrone': DrugsLocaleKeys.flephedrone,
  'flubromazolam': DrugsLocaleKeys.flubromazolam,
  'hysingla': DrugsLocaleKeys.hysingla,
  'ketazolam': DrugsLocaleKeys.ketazolam,
  'kratom': DrugsLocaleKeys.kratom,
  'meprobamate': DrugsLocaleKeys.meprobamate,
  'metandienone': DrugsLocaleKeys.metandienone,
  'methylone': DrugsLocaleKeys.methylone,
  'midazolam': DrugsLocaleKeys.midazolam,
  'pentazocine': DrugsLocaleKeys.pentazocine,
  'phenibut': DrugsLocaleKeys.phenibut,
  'sufentanil': DrugsLocaleKeys.sufentanil,
  'tapentadol': DrugsLocaleKeys.tapentadol,
  'topiramate': DrugsLocaleKeys.topiramate,
  'u-47700': DrugsLocaleKeys.u47700,
  'vape-marijuana': DrugsLocaleKeys.vapeMarijuana,
  'vape-nicotine': DrugsLocaleKeys.vapeNicotine,
  'vyvanse': DrugsLocaleKeys.vyvanse,
  'xelstrym': DrugsLocaleKeys.xelstrym,
  'xylazine': DrugsLocaleKeys.xylazine,
  'zohydro': DrugsLocaleKeys.zohydro,
};


  final drugControlKey = GlobalKey();
  final drugAnxiousKey = GlobalKey();
  final drugWorryKey = GlobalKey();
  final drugWillKey = GlobalKey();
  final drugDifficultyKey = GlobalKey();

  List<String> drugControlList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drugsFeelingControlItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugAnxiousList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drugsFeelingAnxiousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugWorryList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drugsFeelingWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugWillList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drugsFeelingWillItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> drugDifficultyList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drugsFeelingDifficultyItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  final TextEditingController drugGoalUnitsController = TextEditingController(text: '0');
  final TextEditingController drugGoalFreeDayController = TextEditingController(text: '0');

  final TextEditingController drugFirstGoalUnitsController = TextEditingController(text: '0');
  final TextEditingController drugFirstGoalFreeDayController = TextEditingController(text: '0');
  final TextEditingController drugSecondGoalUnitsController = TextEditingController(text: '0');
  final TextEditingController drugSecondGoalFreeDayController = TextEditingController(text: '0');

  final unitsKey = GlobalKey();
  final freeDayKey = GlobalKey();

  final TextEditingController drugThirdGoalUnitsController = TextEditingController(text: '0');
  final TextEditingController drugThirdGoalFreeDayController = TextEditingController(text: '0');
  void userDrugData() {
    '>?>?>? userDrugData'.logV;
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.drugs;
    if (userModel != null && userModel.list != null) {
      rateDrugsSliderVlaue.value = userModel.rate ?? -1;
      final drugDetails = <DrugDetail>[];
      userModel.list!.forEach((drugName, drugInfo) {
        final drugDetail = DrugDetail(
          drug: drugInfo.drug,
          unit: drugInfo.unit,
          amount: drugInfo.amount.toString(),
          frequency: drugInfo.frequency.toString(),
        );
        drugDetails.add(drugDetail);
        'drugDetails${userModel.list?.length}'.logD;
      });
      updatedDrugMap = {
        for (final item in drugDetails)
          item.drug.toString(): {
            'drug': item.drug.toString(),
            'unit': item.unit,
            'amount': item.amount,
            'frequency': item.frequency,
          },
      };
      emit(state.copyWith(drugDetailList: drugDetails));
      if (state.drugDetailList.isNotEmpty && state.drugDetailList.length < 3) {
        drugSelectionoVisible.value = false;
      } else {}
    }
  }

  // Utility method to get the relevant measurement types based on drug
  List<String> getMeasurementUnitsForDrug(String? drugName) {
    'drugName $drugName'.logV;
    if (drugName == null) return [];

    final dynamic entry = drugData[AppCommonFunctions.formatListString(getDrugNameKeyForValue(drugName))];
    if (entry is Map<String, dynamic>) {
      return entry.keys.toList(); 
    }
    return [];
  }

  void userDrugFeelingData() {
    '>?>?>? userDrugFeelingData'.logV;
    final drugName = Injector.instance<AppDB>().userModel?.user.assessment?.drugs?.list?.keys.first ?? '';
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling;
    '>?>?>? userModel = ${userModel != null}'.logV;
    if (userModel != null) {
    //   '>?>?>? userModel.control = ${userModel.control}'.logV;
    // '>?>?>? userModel.anxious = ${userModel.anxious}'.logV;
    // '>?>?>? userModel.worry = ${userModel.worry}'.logV;
    // '>?>?>? userModel.difficulty = ${userModel.difficulty}'.logV;
    // '>?>?>? userModel.will = ${userModel.will}'.logV;
    '>?>?>? userDrugFeelingData'.logV;
    '>?>?>? drugControl = ${drugControl.value}'.logV;
    '>?>?>? drugAnxious = ${drugAnxious.value}'.logV;
    '>?>?>? drugWorry = ${drugWorry.value}'.logV;
    '>?>?>? drugDifficulty = ${drugDifficulty.value}'.logV;
    '>?>?>? drugWill = ${drugWill.value}'.logV;
    //'>?>?>? drugControlList[userModel[drugName]?.control! = ${drugControlList[userModel[drugName]?.control! ?? 0]}'.logV;
      drugControl.value = 
          (userModel.control != null && userModel.control! >= 0 && userModel.control! < drugControlList.length)
              ? drugControlList[userModel.control!]
              : drugControl.value;
      drugAnxious.value =
          (userModel.anxious != null && userModel.anxious! >= 0 && userModel.anxious! < drugAnxiousList.length)
              ? drugAnxiousList[userModel.anxious!]
              : drugAnxious.value;
      drugWorry.value = (userModel.worry != null && userModel.worry! >= 0 && userModel.worry! < drugWorryList.length)
          ? drugWorryList[userModel.worry!]
          : drugWorry.value;
      drugDifficulty.value = (userModel.difficulty != null &&
              userModel.difficulty! >= 0 &&
              userModel.difficulty! < drugDifficultyList.length)
          ? drugDifficultyList[userModel.difficulty!]
          : drugDifficulty.value;
      drugWill.value = (userModel.difficulty != null && userModel.will! >= 0 && userModel.will! < drugWillList.length)
          ? drugWillList[userModel.difficulty!]
          : drugWill.value;
    }
    

  }

  int unitCalculator = 0;

  void userDrugGoalData() {
    '>?>?>? userDrugGoalData'.logV; 
    
    final userdrugGoal = Injector.instance<AppDB>().userModel?.user.assessment?.drugsGoal;

    if (userdrugGoal != null) {
      
      // Iterate through all drug entries in the list
      if (userdrugGoal.drugDetails.isNotEmpty) {
        
        userdrugGoal.drugDetails.forEach((drugName, drugDetail) {
          '>?>?>?>?>? drug name = ${drugName}'.logV;
          '>?>?>?>?>? freedays = ${drugDetail.freeDays}'.logV;
          unitCalculator = drugDetail.units ?? 0;
          '>?>?>?>?>? units = ${drugDetail.units}'.logV;
          'Drug Name: $drugName'.logD;
          'Units: ${drugDetail.units}'.logD;
          'Free Days: ${drugDetail.freeDays}'.logD;

          // Example: Use the data for your controllers or UI
          drugGoalUnitsController.text = drugDetail.units?.toString() ?? ''; //drugGoalUnitsController.text = '0'; //drugDetail.units?.toString() ?? '';
          drugGoalFreeDayController.text = drugDetail.freeDays?.toString() ?? ''; //drugGoalFreeDayController.text = '0'; //drugDetail.freeDays?.toString() ?? '';

          // Emit or process the data
          emit(
            state.copyWith(
              currentdrugfreeDaysValue: drugDetail.freeDays ?? -1,
              currentdrugunitsValue: drugDetail.units ?? -1,
            ),
          );
        });
      } else {
        'No drug data available.'.logD;
      }
    }
  }

  void updateSelecteUnit(String? newUnitValue) {
    drugSelectionDialogVisible.value = false;
    emit(state.copyWith(selecteUnitValue: newUnitValue ?? ''));
  }

  void updateSelecteDrugs(String? newDrugValue) {
    drugamountController.text = '0';
    drugfrequencyController.text = '0';
    emit(state.copyWith(selecteDrugsValue: newDrugValue ?? '',currentamountValue: 0,currentfrequencyValue: 0));
  }

  void clearData() {
    emit(state.copyWith(selecteDrugsValue: '', selecteUnitValue: ''));
  }

  String getDrugValueForKey(String key) {
    'state.drugDetailList.first.drug $key'.logV;
    final map = DrugAndUnitList.createMap();
    'state.drugDetailList.first.drug cap ${map[key]}'.logV;
    
    // Log the entire map for debugging
    'Drug map contents: $map'.logV;
    
    // Get the value from the map
    final value = map[key];
    'Retrieved value for key $key: $value'.logV;
    
    if (value == null) {
      'No value found for key: $key'.logV;
      return '';
    }
    
    // Check if the drug name is an acronym (all uppercase)
    if (key.toUpperCase() == key && key.length > 1) {
      'Converting to uppercase for acronym: $value'.logV;
      return value.toUpperCase();
    }
    
    'Converting to lowercase: $value'.logV;
    return value.toLowerCase();
  }

  String getDrugNameKeyForValue(String value) {
    // Call the function to retrieve the map
    final map = DrugAndUnitList.createMap();

    return map.entries
        .firstWhere(
          (entry) => entry.value == value,
          orElse: () => const MapEntry('', ''), // Return empty if not found
        )
        .key;
  }

  String getDrugUnitValueForKey(String key) {
    final map = DrugAndUnitList.units;
    return map[key] ?? '';
  }

  String getDrugUnitNameKeyForValue(String value) {
    // Call the function to retrieve the map
    final map = DrugAndUnitList.units;

    return map.entries
        .firstWhere(
          (entry) => entry.value == value,
          orElse: () => const MapEntry('', ''), // Return empty if not found
        )
        .key;
  }

  void addDrygData() {
    '/// drugamountController.text ${drugamountController.text}'.logD;  
    '/// drugfrequencyController.text = ${drugfrequencyController.text}'.logD;
    if (drugfrequencyController.text == null || drugfrequencyController.text.isEmpty) {
                                                              // Scroll or show error as needed
                                                              //await AppCommonFunctions.scrollToKey(assessmentDrinkingCubit.unitKey);
                                                              return;
                                                            }
    final drugList1 = DrugDetail(
      amount: drugamountController.text,
      drug: AppCommonFunctions.formatListString(getDrugNameKeyForValue(state.selecteDrugsValue ?? '')),
      frequency: drugfrequencyController.text,
      unit: AppCommonFunctions.formatListString(getDrugUnitNameKeyForValue(state.selecteUnitValue ?? '')),
    );
    final updatedDrugList = List<DrugDetail>.from(state.drugDetailList)..add(drugList1);
    'state.selecteDrugsValue ${state.selecteDrugsValue}'.logD;
    'state.selecteDrugsValue ${DrugAndUnitList.drugList.toList()}'.logD;
    //final drugName = ;
    // 'value ++ $drugName'.logD;

    updatedDrugMap = {
      for (final item in updatedDrugList)
        item.drug.toString(): {
          'drug': item.drug.toString(),
          'unit': item.unit,
          'amount': item.amount,
          'frequency': item.frequency,
        },
    };
    log('updatedDrugMap $updatedDrugMap');
    // Log the updated map for debugging
    updatedDrugMap?.forEach((key, value) {
      log('Drug Key: $key, Details: $value');
    });
    for (final element in updatedDrugList) {
      'updatedDrugList${element.amount}'.logV;
      'updatedDrugList${element.frequency}'.logV;
      'updatedDrugList${element.drug}'.logV;
      'updatedDrugList${element.unit}'.logV;
    }
    emit(state.copyWith(drugDetailList: updatedDrugList));
    drugSelectionoVisible.value = false;
    drugSelectionDialogVisible.value = false;

    emit(
      state.copyWith(
        currentfrequencyValue: 0,
        currentamountValue: 0,
      ),
    );
    // cleaseDrugData();
  }

  void cleaseDrugData() {
    'clear'.logD;
    drugamountController.text = '0';
    drugfrequencyController.text = '0';
    emit(state.copyWith(selecteDrugsValue: null, selecteUnitValue: null));
  }

  void removeDrugData(int index) {
    final updatedRemovedDrugList = List<DrugDetail>.from(state.drugDetailList)..removeAt(index);

    updatedDrugMap = {
      for (final item in updatedRemovedDrugList)
        item.drug ?? '': {
          'drug': item.drug,
          'unit': item.unit,
          'amount': item.amount,
          'frequency': item.frequency,
        },
    };
    log('updatedDrugMap $updatedDrugMap');
    updatedDrugMap?.forEach((key, value) {
      log('Drug Key: $key, Details: $value');
    });
    if (index == 0) {
      drugSelectionoVisible.value = true;
      cleaseDrugData();
    }
    // userDrugData();
    emit(state.copyWith(drugDetailList: updatedRemovedDrugList));
    emit(
      state.copyWith(
        currentfrequencyValue: 0,
        currentamountValue: 0,
      ),
    );
  }

  Map<String, double> getLimitsForSelectedDrugUnit(String? drugName, String? unit) {
    'drug ===$drugName'.logV;
    drugName = drugName?.replaceAll(RegExp(r'\s*\(.*?\)'), '').trim();
    'drug ===$drugName'.logV;

    final drug = drugData[AppCommonFunctions.formatListString1(drugName ?? '')];
// final drug = 'HGH';

    
    if (drug != null && drug is Map<String, dynamic>) {
      'drug === ++++++ $drug'.logV;
      '///drug ===$drug'.logV;
    '///drug unit ===$unit'.logV;
      '///unitInfo ===${drug['milligrams']}'.logV;
      unit = unit?.toLowerCase();
      final unitInfo = drug[unit];
      'unitInfo ===$unitInfo'.logV;
      if (unitInfo is Map<String, dynamic>) {
        '///success'.logV;
        return {
          'incr': (unitInfo['incr'] as num).toDouble(),
          'min': (unitInfo['min'] as num).toDouble(),
          'max': (unitInfo['max'] as num).toDouble(),
        };
      }
    }
    return {
      'incr': 1.0,
      'min': 0.0,
      'max': 10.0,
    }; // Default fallback
  }

  // void increaseDrugAmountValue() {
  //   final selectedDrug = state.selecteDrugsValue;
  //   final selectedUnit = state.selecteUnitValue;

  //   final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  //   final incr = limits['incr'] ?? 1;
  //   final min = limits['min'] ?? 0;
  //   final max = limits['max'] ?? 100;

  //   'incr $incr'.logV;
  //   'max $max'.logV;

  //   if (state.currentamountValue < max) {
  //     final newValue = (state.currentamountValue + incr).clamp(min, max);
  //     drugamountController.text = newValue.toStringAsFixed(
  //       incr % 1 == 0 ? 0 : 1, // If increment is whole number, show int; otherwise, 1 decimal
  //     );
  //     emit(state.copyWith(currentamountValue: newValue));
  //   }
  // }
  void increaseDrugAmountValue() {
  final selectedDrug = state.selecteDrugsValue;
  final selectedUnit = state.selecteUnitValue;

  final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  final incr = limits['incr'] ?? 1.0;
  final min = limits['min'] ?? 0.0;
  final max = limits['max'] ?? 100.0;

  // ✅ Parse current value from controller
  final textValue = double.tryParse(drugamountController.text.trim()) ?? 0.0;

  if (textValue < max) {
    final newValue = (textValue + incr).clamp(min, max);
    // ✅ Update controller directly
    drugamountController.text = newValue.toStringAsFixed(
      incr % 1 == 0 ? 0 : 1, // Show decimal only if needed
    );
    // ✅ Optional: move cursor to end
    drugamountController.selection = TextSelection.fromPosition(
      TextPosition(offset: drugamountController.text.length),
    );
  }
}

  // void increaseDrugAmountValue() {
  //   final selectedDrug = state.selecteDrugsValue;
  //   final selectedUnit = state.selecteUnitValue;

  //   final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  //   final incr = limits['incr'] ?? 1;
  //   final min = limits['min'] ?? 0;
  //   final max = limits['max'] ?? 100;

  //   'incr $incr'.logV;
  //   'max $max'.logV;
  //   final textValue = int.tryParse(drugamountController.text) ?? state.currentamountValue;
  //   if (textValue < max) {
  //     final newValue = (textValue + incr).clamp(min, max);
  //     drugamountController.text = newValue.toStringAsFixed(
  //       incr % 1 == 0 ? 0 : 1, // If increment is whole number, show int; otherwise, 1 decimal
  //     );
  //     emit(state.copyWith(currentamountValue: newValue));
  //   }
  // }

  // void decreaseDrugAmountValue() {
  //   final selectedDrug = state.selecteDrugsValue;
  //   final selectedUnit = state.selecteUnitValue;

  //   final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  //   final incr = limits['incr'] ?? 1;
  //   final min = limits['min'] ?? 0;
  //   final max = limits['max'] ?? 100;

  //   final textValue = int.tryParse(drugamountController.text) ?? state.currentamountValue;
  //   if (textValue > min) {
  //     final newValue = (textValue - incr).clamp(min, max);
  //     drugamountController.text = newValue.toStringAsFixed(
  //       incr % 1 == 0 ? 0 : 1,
  //     );
  //     emit(state.copyWith(currentamountValue: newValue));
  //   }
  // }

  void decreaseDrugAmountValue() {
  final selectedDrug = state.selecteDrugsValue;
  final selectedUnit = state.selecteUnitValue;

  final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  final incr = limits['incr'] ?? 1.0;
  final min = limits['min'] ?? 0.0;
  final max = limits['max'] ?? 100.0;

  // ✅ Parse current value directly from the controller
  final textValue = double.tryParse(drugamountController.text.trim()) ?? 0.0;

  if (textValue > min) {
    final newValue = (textValue - incr).clamp(min, max);
    // ✅ Update controller directly
    drugamountController.text = newValue.toStringAsFixed(
      incr % 1 == 0 ? 0 : 1, // Show decimals only if needed
    );
    // ✅ Optional: move cursor to end
    drugamountController.selection = TextSelection.fromPosition(
      TextPosition(offset: drugamountController.text.length),
    );
  }
}


  // void decreaseDrugAmountValue() {
  //   final selectedDrug = state.selecteDrugsValue;
  //   final selectedUnit = state.selecteUnitValue;

  //   final limits = getLimitsForSelectedDrugUnit(selectedDrug, selectedUnit);
  //   final incr = limits['incr'] ?? 1;
  //   final min = limits['min'] ?? 0;
  //   final max = limits['max'] ?? 100;

  //   if (state.currentamountValue > min) {
  //     final newValue = (state.currentamountValue - incr).clamp(min, max);
  //     drugamountController.text = newValue.toStringAsFixed(
  //       incr % 1 == 0 ? 0 : 1,
  //     );
  //     emit(state.copyWith(currentamountValue: newValue));
  //   }
  // }

  // void increaseDrugFrequencyValue() {
  //   if (state.currentfrequencyValue < 7) {
  //     final newValue = state.currentfrequencyValue + 1;
  //     drugfrequencyController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentfrequencyValue: newValue,
  //       ),
  //     );
  //   }
  // }

   void increaseDrugFrequencyValue() {
    final textValue = int.tryParse(drugfrequencyController.text) ?? state.currentfrequencyValue;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drugfrequencyController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentfrequencyValue: newValue,
      //   ),
      // );
    }
  }

  void decreaseDrugFrequencyValue() {
    final textValue = int.tryParse(drugfrequencyController.text) ?? state.currentfrequencyValue;
    if (textValue > 0) {
      final newValue = textValue - 1;
      drugfrequencyController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentfrequencyValue: newValue,
      //   ),
      // );
    }
  }


  // void decreaseDrugFrequencyValue() {
  //   if (state.currentfrequencyValue > 0) {
  //     final newValue = state.currentfrequencyValue - 1;
  //     drugfrequencyController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentfrequencyValue: newValue,
  //       ),
  //     );
  //   }
  // }

//  void increaseDrugGoal() {
//     isUnitAndDrugsButton.value = false;
//     if (state.drugGoalUnitValue  < 10000) {
      
//       final newValue = unitCalculator += 1;//state.drugGoalUnitValue + 1;
//       drugGoalUnitsController.text = newValue.toString();
//       emit(
//         state.copyWith(
//           drugGoalUnitValue: newValue,
//         ),
//       );
//     }
//     '/// ${state.currentdrugunitsValue}'.logV;
//   }

  void increaseDrugGoal() {
  final textValue = int.tryParse(drugGoalUnitsController.value.text) ?? state.drugGoalUnitValue;
    isUnitAndDrugsButton.value = false;
    if (textValue < 10000) {
      
      final newValue = textValue + 1; //unitCalculator += 1;//state.drugGoalUnitValue + 1;
      drugGoalUnitsController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     drugGoalUnitValue: newValue,
      //   ),
      // );
    }
    '/// ${state.currentdrugunitsValue}'.logV;
  }

  void decreaseDrugGoal() {
  final textValue = int.tryParse(drugGoalUnitsController.value.text) ?? state.drugGoalUnitValue;
    isUnitAndDrugsButton.value = false;
    if (textValue > 0) {  //state.drugGoalUnitValue  > 0
      final newValue = textValue - 1;//unitCalculator -= 1;//state.drugGoalUnitValue - 1;
      drugGoalUnitsController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     drugGoalUnitValue: newValue,
      //   ),
      // );
    }
  }


// void decreaseDrugGoal() {
//     isUnitAndDrugsButton.value = false;
//     if (unitCalculator > 0) {  //state.drugGoalUnitValue  > 0
//       final newValue = unitCalculator -= 1;//state.drugGoalUnitValue - 1;
//       drugGoalUnitsController.text = newValue.toString();
//       emit(
//         state.copyWith(
//           drugGoalUnitValue: newValue,
//         ),
//       );
//     }
//   }

double roundToDecimal(double value, int places) {
  final mod = Math.pow(10.0, places);
  return (value * mod).round() / mod;
}


//  void increaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   isUnitAndDrugsButton.value = false;
  
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.firstDrugunitsValue is num)
//       ? (state.firstDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue < max) {
//     var newValue = (currentValue + incr).clamp(min, max);
//     newValue = roundToDecimal(newValue, 1);
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     // Update the text field
//     //drugGoalUnitsController.text = formattedValue;
//     drugFirstGoalUnitsController.text = formattedValue;
    
//     'newValue === ++ $newValue, formattedValue === ++ $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         firstDrugunitsValue : (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }

// void increaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   isUnitAndDrugsButton.value = false;
  
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.firstDrugunitsValue is num)
//       ? (state.firstDrugunitsValue as num).toDouble()
//       : 0.0;

//   final textValue = int.tryParse(drugFirstGoalUnitsController.text) ?? currentValue;
//   if (textValue < max) {
//     var newValue = (textValue + incr).clamp(min, max);
//     newValue = roundToDecimal(newValue, 1);
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     // Update the text field
//     //drugGoalUnitsController.text = formattedValue;
//     drugFirstGoalUnitsController.text = formattedValue;
    
//     'newValue === ++ $newValue, formattedValue === ++ $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         firstDrugunitsValue : (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }

void increaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
  isUnitAndDrugsButton.value = false;

  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1.0;
  final min = limits['min'] ?? 0.0;
  final max = limits['max'] ?? 10000.0;

  // ✅ Safely get current value from controller
  final currentValue = double.tryParse(drugFirstGoalUnitsController.text.trim()) ?? 0.0;

  if (currentValue < max) {
    var newValue = (currentValue + incr).clamp(min, max);
    newValue = roundToDecimal(newValue, 1); // ✅ Optional rounding

    // ✅ Format value based on whether it's integer or decimal
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    // ✅ Update controller directly
    drugFirstGoalUnitsController.text = formattedValue;

    // ✅ Optional: Move cursor to end
    drugFirstGoalUnitsController.selection = TextSelection.fromPosition(
      TextPosition(offset: drugFirstGoalUnitsController.text.length),
    );

    'newValue === ++ $newValue, formattedValue === ++ $formattedValue'.logV;
  }
}


// void decreaseDrugUnitValue(String? selectedDrug, String? unit) {
//   isUnitAndDrugsButton.value = false;
  
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.firstDrugunitsValue is num)
//       ? (state.firstDrugunitsValue as num).toDouble()
//       : 0.0;

//   final textValue = int.tryParse(drugFirstGoalUnitsController.text) ?? currentValue;
//   if (textValue > min) {
//     var newValue = (textValue - incr).clamp(min, max);
//     newValue = roundToDecimal(newValue, 1);
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     // Update the text field
//     //drugGoalUnitsController.text = formattedValue;
//     drugFirstGoalUnitsController.text = formattedValue;
    
//     '====>+++ Raw: $newValue, Formatted: $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         firstDrugunitsValue : (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }

void decreaseDrugUnitValue(String? selectedDrug, String? unit) {
  isUnitAndDrugsButton.value = false;

  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1.0;
  final min = limits['min'] ?? 0.0;
  final max = limits['max'] ?? 10000.0;

  // ✅ Safely get current value from controller
  final currentValue = double.tryParse(drugFirstGoalUnitsController.text.trim()) ?? 0.0;

  if (currentValue > min) {
    var newValue = (currentValue - incr).clamp(min, max);
    newValue = roundToDecimal(newValue, 1); // ✅ Optional rounding

    // ✅ Format value (remove .0 for integers)
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    // ✅ Update controller directly
    drugFirstGoalUnitsController.text = formattedValue;

    // ✅ Optional: Move cursor to end
    drugFirstGoalUnitsController.selection = TextSelection.fromPosition(
      TextPosition(offset: drugFirstGoalUnitsController.text.length),
    );

    '====>+++ Raw: $newValue, Formatted: $formattedValue'.logV;
  }
}



//   void decreaseDrugUnitValue(String? selectedDrug, String? unit) {
//   isUnitAndDrugsButton.value = false;
  
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.firstDrugunitsValue is num)
//       ? (state.firstDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue > min) {
//     var newValue = (currentValue - incr).clamp(min, max);
//     newValue = roundToDecimal(newValue, 1);
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     // Update the text field
//     //drugGoalUnitsController.text = formattedValue;
//     drugFirstGoalUnitsController.text = formattedValue;
    
//     '====>+++ Raw: $newValue, Formatted: $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         firstDrugunitsValue : (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }


//   void secondIncreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.secondDrugunitsValue is num)
//       ? (state.secondDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue < max) {
//     final newValue = (currentValue + incr).clamp(min, max);
    
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     // Update controllers
//     drugSecondGoalUnitsController.text = formattedValue;
//     //drugGoalUnitsController.text = formattedValue;
    
//     '====>+++ Raw: $newValue, Formatted: $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         secondDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }

void secondIncreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1;
  final min = limits['min'] ?? 0;
  final max = limits['max'] ?? 10000;

  // Safely get the current value
  final currentValue = (state.secondDrugunitsValue is num)
      ? (state.secondDrugunitsValue as num).toDouble()
      : 0.0;

  final textValue = int.tryParse(drugSecondGoalUnitsController.value.text) ?? currentValue;
  if (textValue < max) {
    final newValue = (textValue + incr).clamp(min, max);
    
    // Format based on type
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    // Update controllers
    drugSecondGoalUnitsController.text = formattedValue;
    //drugGoalUnitsController.text = formattedValue;
    
    '====>+++ Raw: $newValue, Formatted: $formattedValue'.logV;
    
    emit(
      state.copyWith(
        secondDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
      ),
    );
  }
}

void secondDecreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1;
  final min = limits['min'] ?? 0;
  final max = limits['max'] ?? 10000;

  // Safely get the current value
  final currentValue = (state.secondDrugunitsValue is num)
      ? (state.secondDrugunitsValue as num).toDouble()
      : 0.0;

  final textValue = int.tryParse(drugSecondGoalUnitsController.value.text) ?? currentValue;
  if (textValue > min) {
    final newValue = (textValue - incr).clamp(min, max);
    
    // Format based on type
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    drugSecondGoalUnitsController.text = formattedValue;
    '====>+++ $newValue'.logV;
    '====>+++ formatted $formattedValue'.logV;
    
    emit(
      state.copyWith(
        secondDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
      ),
    );
  }
}

//   void secondDecreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.secondDrugunitsValue is num)
//       ? (state.secondDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue > min) {
//     final newValue = (currentValue - incr).clamp(min, max);
    
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     drugSecondGoalUnitsController.text = formattedValue;
//     '====>+++ $newValue'.logV;
//     '====>+++ formatted $formattedValue'.logV;
    
//     emit(
//       state.copyWith(
//         secondDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }


//   void thirdIncreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   '====>+++ $selectedDrug $unit'.logV;
  
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Ensure we have a number
//   final currentValue = (state.thirdDrugunitsValue is num)
//       ? (state.thirdDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue < max) {
//     final newValue = (currentValue + incr).clamp(min, max);
    
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     drugThirdGoalUnitsController.text = formattedValue;
    
//     emit(
//       state.copyWith(
//         thirdDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }

void thirdIncreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
  '====>+++ $selectedDrug $unit'.logV;
  
  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1;
  final min = limits['min'] ?? 0;
  final max = limits['max'] ?? 10000;

  // Ensure we have a number
  final currentValue = (state.thirdDrugunitsValue is num)
      ? (state.thirdDrugunitsValue as num).toDouble()
      : 0.0;

  final textValue = int.tryParse(drugThirdGoalUnitsController.text) ?? currentValue;
  if (textValue < max) {
    final newValue = (textValue + incr).clamp(min, max);
    
    // Format based on type
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    drugThirdGoalUnitsController.text = formattedValue;
    
    emit(
      state.copyWith(
        thirdDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
      ),
    );
  }
}


void thirdDecreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
  final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
  final incr = limits['incr'] ?? 1;
  final min = limits['min'] ?? 0;
  final max = limits['max'] ?? 10000;

  // Safely get the current value
  final currentValue = (state.thirdDrugunitsValue is num)
      ? (state.thirdDrugunitsValue as num).toDouble()
      : 0.0;

  final textValue = int.tryParse(drugThirdGoalUnitsController.text) ?? currentValue;
  if (textValue > min) {
    final newValue = (textValue - incr).clamp(min, max);
    
    // Format based on type
    final formattedValue = (newValue % 1 == 0)
        ? newValue.toInt().toString()
        : newValue.toString();

    drugThirdGoalUnitsController.text = formattedValue;
    
    emit(
      state.copyWith(
        thirdDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
      ),
    );
  }
}


//   void thirdDecreaseDrugGoalUnitsValue(String? selectedDrug, String? unit) {
//   final limits = getLimitsForSelectedDrugUnit(selectedDrug, unit);
//   final incr = limits['incr'] ?? 1;
//   final min = limits['min'] ?? 0;
//   final max = limits['max'] ?? 10000;

//   // Safely get the current value
//   final currentValue = (state.thirdDrugunitsValue is num)
//       ? (state.thirdDrugunitsValue as num).toDouble()
//       : 0.0;

//   if (currentValue > min) {
//     final newValue = (currentValue - incr).clamp(min, max);
    
//     // Format based on type
//     final formattedValue = (newValue % 1 == 0)
//         ? newValue.toInt().toString()
//         : newValue.toString();

//     drugThirdGoalUnitsController.text = formattedValue;
    
//     emit(
//       state.copyWith(
//         thirdDrugunitsValue: (newValue % 1 == 0) ? newValue.toInt() : newValue,
//       ),
//     );
//   }
// }


  // void secondIncreaseDrugGoalFreeDayValue() {
  //   if (state.secondDrugfreeDaysValue < 7) {
  //     final newValue = state.secondDrugfreeDaysValue + 1;
  //     drugSecondGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         secondDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void secondIncreaseDrugGoalFreeDayValue() {
    final textValue = int.tryParse(drugSecondGoalFreeDayController.text) ?? state.secondDrugfreeDaysValue;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drugSecondGoalFreeDayController.text = newValue.toString();
      emit(
        state.copyWith(
          secondDrugfreeDaysValue: newValue,
        ),
      );
    }
  }

  void secondDecreaseDrugFreeDayValue() {
    final textValue = int.tryParse(drugSecondGoalFreeDayController.text) ?? state.secondDrugfreeDaysValue;
    if (textValue > 0) {
      final newValue = textValue   - 1;
      drugSecondGoalFreeDayController.text = newValue.toString();
      emit(
        state.copyWith(
          secondDrugfreeDaysValue: newValue,
        ),
      );
    }
  }

  // void secondDecreaseDrugFreeDayValue() {
  //   if (state.secondDrugfreeDaysValue > 0) {
  //     final newValue = state.secondDrugfreeDaysValue - 1;
  //     drugSecondGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         secondDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  // void thirdIncreaseDrugGoalFreeDayValue() {
  //   if (state.thirdDrugfreeDaysValue < 7) {
  //     final newValue = state.thirdDrugfreeDaysValue + 1;
  //     drugThirdGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         thirdDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

   void thirdIncreaseDrugGoalFreeDayValue() {
    final textValue = int.tryParse(drugThirdGoalFreeDayController.text) ?? state.thirdDrugfreeDaysValue;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drugThirdGoalFreeDayController.text = newValue.toString();
      emit(
        state.copyWith(
          thirdDrugfreeDaysValue: newValue,
        ),
      );
    }
  }


  //  void firstIncreaseDrugGoalFreeDayValue() {
  //   '>?>?>?>? increase'.logV;
  //   if (state.firstDrugfreeDaysValue < 7) {
  //     final newValue = state.firstDrugfreeDaysValue + 1;
  //     drugFirstGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         firstDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void firstIncreaseDrugGoalFreeDayValue() {
    final textValue = int.tryParse(drugFirstGoalFreeDayController.text) ?? state.firstDrugfreeDaysValue;
    '>?>?>?>? increase'.logV;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drugFirstGoalFreeDayController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     firstDrugfreeDaysValue: newValue,
      //   ),
      // );
    }
  }

  void firstDecreaseDrugFreeDayValue() {
    final textValue = int.tryParse(drugFirstGoalFreeDayController.text) ?? state.firstDrugfreeDaysValue;
    '>?>?>?>? decrease'.logV;
    if (textValue > 0) {
      final newValue = textValue - 1;
      drugFirstGoalFreeDayController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     firstDrugfreeDaysValue: newValue,
      //   ),
      // );
    }
  }

  // void firstDecreaseDrugFreeDayValue() {
  //   '>?>?>?>? decrease'.logV;
  //   if (state.firstDrugfreeDaysValue > 0) {
  //     final newValue = state.firstDrugfreeDaysValue - 1;
  //     drugFirstGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         firstDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  // void increaseFirstDrugGoalFreeDayValue() {
  //   //isUnitAndDrugsButton.value = false;
  //   if (state.currentdrugfreeDaysValue < 7) {
  //     final newValue = state.currentdrugfreeDaysValue + 1;
  //     //drugGoalFreeDayController.text = newValue.toString();
  //     drugFirstGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentdrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  // void decreaseFirstDrugFreeDayValue() {
  //   //isUnitAndDrugsButton.value = false;
  //   if (state.currentdrugfreeDaysValue > 0) {
  //     final newValue = state.currentdrugfreeDaysValue - 1;
  //     //drugGoalFreeDayController.text = newValue.toString();
  //     drugFirstGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentdrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  // void thirdDecreaseDrugFreeDayValue() {
  //   if (state.thirdDrugfreeDaysValue > 0) {
  //     final newValue = state.thirdDrugfreeDaysValue - 1;
  //     drugThirdGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         thirdDrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void thirdDecreaseDrugFreeDayValue() {
    final textValue = int.tryParse(drugThirdGoalFreeDayController.text) ?? state.thirdDrugfreeDaysValue;
    if (textValue > 0) {
      final newValue = textValue - 1;
      drugThirdGoalFreeDayController.text = newValue.toString();
      emit(
        state.copyWith(
          thirdDrugfreeDaysValue: newValue,
        ),
      );
    }
  }

  // void increaseDrugGoalFreeDayValue() {
  //   isUnitAndDrugsButton.value = false;
  //   if (state.currentdrugfreeDaysValue < 7) {
  //     final newValue = state.currentdrugfreeDaysValue + 1;
  //     drugGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentdrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void increaseDrugGoalFreeDayValue() {
  final textValue = int.tryParse(drugGoalFreeDayController.text) ?? state.currentdrugfreeDaysValue;
    isUnitAndDrugsButton.value = false;
    if (textValue < 7) {
      final newValue = textValue + 1;
      drugGoalFreeDayController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentdrugfreeDaysValue: newValue,
      //   ),
      // );
    }
  }

  void decreaseDrugFreeDayValue() {
  final textValue = int.tryParse(drugGoalFreeDayController.text) ?? state.currentdrugfreeDaysValue;
    isUnitAndDrugsButton.value = false;
    if (textValue > 0) {
      final newValue = textValue - 1;
      drugGoalFreeDayController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentdrugfreeDaysValue: newValue,
      //   ),
      // );
    }
  }

  // void decreaseDrugFreeDayValue() {
  //   isUnitAndDrugsButton.value = false;
  //   if (state.currentdrugfreeDaysValue > 0) {
  //     final newValue = state.currentdrugfreeDaysValue - 1;
  //     drugGoalFreeDayController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentdrugfreeDaysValue: newValue,
  //       ),
  //     );
  //   }
  // }

  Future<void> putdrugAPI({
    required Map<String, dynamic> list,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putdrug(context: context, list: list, rate: rate);
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        //Injector.instance<AppDB>().userModel!.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (rateDrugInfoVisible.value == true) {
          infoAudioUrl.value = AssessmentLocaleKeys.drugsAudio.tr();
          rateDrugInfoVisible.value = false;
        }
        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  Future<void> putdrugFeelingAPI({
    required int control,
    required int anxious,
    required int worry,
    required int will,
    required int difficulty,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isFeelingApiLoading: true));

      '>?>?>?>?>? anxious1 = $anxious'.logD;
      '>?>?>?>?>? control1 = $control'.logD;
      '>?>?>?>?>? difficulty1 = $difficulty'.logD;
      '>?>?>?>?>? will1 = $will'.logD;
      '>?>?>?>?>? worry1 = $worry'.logD;

      final response = await assessmentRepository.putdrugFeeling(
        context: context,
        anxious: anxious,
        will: will,
        difficulty: difficulty,
        control: control,
        worry: worry,
      );
      

     response?.toJson().logI;

      if (response != null && (response.success ?? false)) {
          final drugName = Injector.instance<AppDB>().userModel?.user.assessment?.drugs?.list?.keys.first ?? '';
      //   '=== drugname === $drugName'.logD;
      //   ' >?>?>?>?>? anxious = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeelingWrapper?[drugName]?.anxious}'.logD;
      // ' >?>?>?>?>? control = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.control}'.logD;
      // ' >?>?>?>?>? difficulty = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.difficulty}'.logD;
      // ' >?>?>?>?>? will = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.will}'.logD;
      // ' >?>?>?>?>? worry = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.worry}'.logD;

        '>?>?>?>?>? final response.assessment = ${response.assessment?.toJson()}'.logD;
        '>?>?>?>?>? before original response = ${Injector.instance<AppDB>().userModel?.user.assessment?.toJson()}'.logV;
       final userModel = Injector.instance<AppDB>().userModel;
       userModel?.user.assessment = response.assessment ?? Assessment();
       Injector.instance<AppDB>().userModel = userModel;

      //   '>?>?>?>?>? original response = ${Injector.instance<AppDB>().userModel?.user.assessment?.toJson()}'.logV;

      //    ' >?>?>?>?>? anxious = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.toJson()}'.logD;
      // ' >?>?>?>?>? control = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.control}'.logD;
      // ' >?>?>?>?>? difficulty = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.difficulty}'.logD;
      // ' >?>?>?>?>? will = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.will}'.logD;
      // ' >?>?>?>?>? worry = ${Injector.instance<AppDB>().userModel?.user.assessment?.drugsFeeling?.worry}'.logD;
        //Injector.instance<AppDB>().userModel!.user.assessment.logD;
        await authRepository.getUserData(context: context);

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isFeelingApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isFeelingApiLoading: false));
    } finally {
      emit(state.copyWith(isFeelingApiLoading: false));
    }
  }

  Future<void> putdrugGoalAPI({
    required double units,
    required int freeDays,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isGoalApiLoading: true));
      final response = await assessmentRepository.putdrugGoal(
        context: context,
        units: units,
        freeDays: freeDays,
      );
      'api response === goal = ${response}'.logV;
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        //Injector.instance<AppDB>().userModel!.user.assessment.logD;
        await authRepository.getUserData(context: context);

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isGoalApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isGoalApiLoading: false));
    } finally {
      emit(state.copyWith(isGoalApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close
    drugfrequencyController.dispose();
    drugamountController.dispose();
    drugControl.dispose();
    drugAnxious.dispose();
    drugWorry.dispose();
    drugWill.dispose();
    drugDifficulty.dispose();
    drugRate.dispose();
    rateDrugsSliderVlaue.dispose();
    rateDrugInfoVisible.dispose();
    drugSelectionoVisible.dispose();
    isDrugFeelingButtonClicked.dispose();
    drugGoalUnitsController.dispose();
    drugGoalFreeDayController.dispose();
    return super.close();
  }
}
