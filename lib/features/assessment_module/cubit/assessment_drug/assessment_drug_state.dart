part of 'assessment_drug_cubit.dart';

@freezed
class AssessmentDrugState with _$AssessmentDrugState {
  factory AssessmentDrugState({
    @Default(null) String? selecteDrugsValue,
    @Default(null) String? selecteUnitValue,
    @Default(false) bool isApiLoading,
    @Default(false) bool isGoalApiLoading,
    @Default(false) bool isFeelingApiLoading,
    @Default([]) List<DrugDetail> drugDetailList,
    @Default(0.0) double currentamountValue,
    @Default(0) int currentfrequencyValue,
    @Default(0) int currentdrugfreeDaysValue,
    @Default(0) int drugGoalUnitValue,
    @Default(0.0) dynamic currentdrugunitsValue,
    @Default(0.0) dynamic secondDrugunitsValue,
    @Default(0) int secondDrugfreeDaysValue,
    @Default(0.0) dynamic firstDrugunitsValue,
    @Default(0) int firstDrugfreeDaysValue,
    @Default(0.0) dynamic thirdDrugunitsValue,
    @Default(0) int thirdDrugfreeDaysValue,
  }) = _AssessmentDrugState;
}
