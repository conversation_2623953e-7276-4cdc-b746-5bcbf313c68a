import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_physical_senstion/assessment_physical_senstation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_physical_sensation_page.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_thoughts_cubit.freezed.dart';
part 'assessment_thoughts_state.dart';

class AssessmentThoughtsCubit extends Cubit<AssessmentThoughtsState> {
  AssessmentThoughtsCubit() : super(AssessmentThoughtsState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.negativeThoughtsAudio.tr());

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isThoughtButtonClicked = ValueNotifier(false);

  //thoughts
  ValueNotifier<ButtonState> goodState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> controlState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> healthState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> copeState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> trustState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<bool> rateThoughtsInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> rateThoughtsInfoVisibleLable = ValueNotifier(false);
  ValueNotifier<int> rateThoughtSliderVlaue = ValueNotifier(-1);
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  final goodKey = GlobalKey();
  final controlKey = GlobalKey();
  final healthKey = GlobalKey();
  final copeKey = GlobalKey();
  final trustKey = GlobalKey();

  void userThoughtData() {
    final userData = Injector.instance<AppDB>().userModel?.user.assessment?.nt;
    if (userData != null) {
      'userData.good${userData.good}'.logD;

      goodState.value = userData.good == null
          ? ButtonState.bothDisabled
          : userData.good == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      controlState.value = userData.control == null
          ? ButtonState.bothDisabled
          : userData.control == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      healthState.value = userData.health == null
          ? ButtonState.bothDisabled
          : userData.health == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      copeState.value = userData.cope == null
          ? ButtonState.bothDisabled
          : userData.cope == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      controlState.value = userData.control == null
          ? ButtonState.bothDisabled
          : userData.control == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;
      trustState.value = userData.trust == null
          ? ButtonState.bothDisabled
          : userData.trust == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      rateThoughtSliderVlaue.value = userData.rate ?? -1;
    }
  }

  Future<void> putAssessmentNegativeThoughtsAPI({
    required int control,
    required int cope,
    required int good,
    required int health,
    required int trust,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putAssessmentNegativeThoughts(
        context: context,
        control: control,
        cope: cope,
        good: good,
        health: health,
        trust: trust,
        rate: rate,
      );
      if (response != null && (response.success ?? false) == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (context.read<AssessmentThoughtsCubit>().rateThoughtsInfoVisible.value == true) {
          context.read<AssessmentThoughtsCubit>().infoAudioUrl.value = AssessmentLocaleKeys.negativeThoughtsAudio.tr();

          context.read<AssessmentThoughtsCubit>().rateThoughtsInfoVisible.value = false;
        }
        context.read<AssessmentPhysicalSenstationCubit>().isManuallyPaused.value = false;

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    goodState.dispose();
    controlState.dispose();
    healthState.dispose();
    copeState.dispose();
    trustState.dispose();
    isThoughtButtonClicked.dispose();
    rateThoughtSliderVlaue.dispose();
    rateThoughtsInfoVisible.dispose();
    return super.close();
  }
}
