import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_emotional_impact_cubit.freezed.dart';
part 'assessment_emotional_impact_state.dart';

class AssessmentEmotionalImpactCubit extends Cubit<AssessmentEmotionalImpactState> {
  AssessmentEmotionalImpactCubit() : super(AssessmentEmotionalImpactState());
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.emotionalImpactAudio.tr());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();
  ValueNotifier<bool> isEmotionButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> rateEmotionalImpactInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> rateEmotionalImpactInfoVisibleLabel = ValueNotifier(false);
  ValueNotifier<int> emtionSliderValue = ValueNotifier(-1);
  ValueNotifier<int> selectedBreakingFreeIndex = ValueNotifier(-1);

  //emotional imapact
  ValueNotifier<String> nervousValue = ValueNotifier('');
  ValueNotifier<String> worryValue = ValueNotifier('');
  ValueNotifier<String> downValue = ValueNotifier('');
  ValueNotifier<String> badValue = ValueNotifier('');
  ValueNotifier<String> interestValue = ValueNotifier('');

  final nervousKey = GlobalKey();
  final worryKey = GlobalKey();
  final downKey = GlobalKey();
  final badKey = GlobalKey();
  final interestKey = GlobalKey();

  List<String> nervousList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.emotionalImpactQuestionsNervousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> worryList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.emotionalImpactQuestionsWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> downList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.emotionalImpactQuestionsDownItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> badList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.emotionalImpactQuestionsBadItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> interestList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.emotionalImpactQuestionsInterestItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  void userEmotionalImpactData() {
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.ei;
    userModel.logD;
    // Prepopulate data if available
    if (userModel != null) {
      nervousValue.value =
          (userModel.nervous != null && userModel.nervous! >= 0 && userModel.nervous! < nervousList.length)
              ? nervousList[userModel.nervous!]
              : nervousValue.value;

      worryValue.value = (userModel.worry != null && userModel.worry! >= 0 && userModel.worry! < worryList.length)
          ? worryList[userModel.worry!]
          : worryValue.value;

      downValue.value = (userModel.down != null && userModel.down! >= 0 && userModel.down! < downList.length)
          ? downList[userModel.down!]
          : downValue.value;

      badValue.value = (userModel.bad != null && userModel.bad! >= 0 && userModel.bad! < badList.length)
          ? badList[userModel.bad!]
          : badValue.value;

      interestValue.value =
          (userModel.interest != null && userModel.interest! >= 0 && userModel.interest! < interestList.length)
              ? interestList[userModel.interest!]
              : interestValue.value;
      // nervousValue.value = nervousList[userModel.nervous ?? ];
      //   worryValue.value = worryList[userModel.worry ?? -1];
      //  downValue.value = downList[userModel.down ?? -1];
      // badValue.value = badList[userModel.bad ?? -1];
      // interestValue.value = interestList[userModel.interest ?? -1];

      emtionSliderValue.value = userModel.rate ?? -1;
    }
  }

  Future<void> putEmotionalImpactAPI({
    required int nervous,
    required int worry,
    required int down,
    required int bad,
    required int interest,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putEmotionalImpact(
        context: context,
        bad: bad,
        down: down,
        interest: interest,
        nervous: nervous,
        worry: worry,
        rate: rate,
      );
      '>?>?>? before if'.logV;
      if (response != null && response.success == true) {
        '>?>?>? in if'.logV;
        '>?>?>? response not null =${response.success} ${response.toJson()}'.logV; 
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);

        Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase.logD;
        if (rateEmotionalImpactInfoVisible.value == true) {
          infoAudioUrl.value = AssessmentLocaleKeys.emotionalImpactAudio.tr();
          rateEmotionalImpactInfoVisible.value = false;
        }

        if (Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 1) {
          '>?>?>? addiction case 1 '.logV;
          context.read<AssessmentCubit>().gotoNextWidget(increment: 4);
        } else {
          '>?>?>? addiction case not 1 '.logV;
          context.read<AssessmentDrinkingCubit>().isManuallyPaused.value = false;

          context.read<AssessmentCubit>().gotoNextWidget();
        }
      }
      '>?>?>? after if'.logV;
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    // TODO: implement close
    selectedBreakingFreeIndex.dispose();
    isEmotionButtonClicked.dispose();
    emtionSliderValue.dispose();
    rateEmotionalImpactInfoVisible.dispose();
    nervousValue.dispose();
    worryValue.dispose();
    downValue.dispose();
    badValue.dispose();
    interestValue.dispose();

    return super.close();
  }
}
