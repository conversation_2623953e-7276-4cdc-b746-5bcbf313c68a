part of 'assessment_drinking_cubit.dart';

@freezed
class AssessmentDrinkingState with _$AssessmentDrinkingState {
  factory AssessmentDrinkingState({
    @Default(0) int customDrinkingValue,
    @Default(0) int currentDrinkingValue,
    @Default(0) int currentDayValue,
    @Default('') String? drinkHelpMeCalculateTitleText,
    @Default('') String? selecteddrinkHelpMeCalculateSubTitleText,
    @Default('') String? selecteddrinkHelpMeSizeText,
    @Default('') String? selecteddrinkHelpMeCalculateTitleText,
    @Default('') String? selecteddrinkHelpMeCalculatePercentageText,
    @Default([]) List<DrinkDetail> drinkDetails,
    @Default(false) bool showBackArrownQuestion,
    @Default(false) bool isApiLoading,
  }) = _AssessmentDrinkingState;
}
