import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drinking_gridview_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drinking_help_me_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_drinking_cubit.freezed.dart';
part 'assessment_drinking_state.dart';

class AssessmentDrinkingCubit extends Cubit<AssessmentDrinkingState> {
  AssessmentDrinkingCubit() : super(AssessmentDrinkingState());
  AssessmentRepository assessmentRepository = AssessmentRepository();
  AuthRepository authRepository = AuthRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.drinkingAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<List<DrinkItem>> drinkItemsNotifier = ValueNotifier([]);
  ValueNotifier<List<DrinkItem>> drinkItemsAuNotifier = ValueNotifier([]);

  ValueNotifier<bool> drinkHelpMeCalculate = ValueNotifier(false);
  ValueNotifier<bool> audrinkHelpMeCalculate = ValueNotifier(false);
  ValueNotifier<bool> usdrinkHelpMeCalculate = ValueNotifier(false);
  ValueNotifier<bool> cadrinkHelpMeCalculate = ValueNotifier(false);
  ValueNotifier<bool> isShowAlltheDetail = ValueNotifier(false);
  ValueNotifier<bool> showBackArrow = ValueNotifier(false);
  ValueNotifier<bool> isPdfLoading = ValueNotifier(false);
  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isUnitAndDrinkingButton= ValueNotifier(false);



  final TextEditingController unitsController = TextEditingController(text: '0');
  final TextEditingController unitshelpmeController = TextEditingController(text: '0');
  final TextEditingController daysController = TextEditingController(text: '0');

  final unitKey = GlobalKey();
  final dayKey = GlobalKey();

  ValueNotifier<bool> rateDrinkingInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> rateDrinkingInfoVisibleLabel = ValueNotifier(false);
  ValueNotifier<bool> isDrinkingButtonClick = ValueNotifier(false);

  ValueNotifier<int> rateDrinkingSliderValue = ValueNotifier(-1);

  List<Map<String, dynamic>> drinkHistory = [];
  List<Map<String, dynamic>> drinkAuHistory = [];

  final List<DrinkItem> drinkItems = []; // List to store all the drink items
  List<DrinkDetail> drinkDetails = [];

  String drinkHistoryTitleText = '';
  bool drinkHistoryBackButton = false;
  
  void assignValue() {
    emit(
        state.copyWith(
          currentDrinkingValue: int.tryParse(unitsController.text) ?? 0,
        ),
      );
      emit(
        state.copyWith(
          currentDayValue: int.tryParse(daysController.text) ?? 0,
        ),
      );
  }

  int unitvalue = 0;
  int dayvalue = 0;

  int matchvalue(){
    unitvalue = int.parse(unitsController.text);
    dayvalue = int.parse(daysController.text);
    return unitvalue * dayvalue;
  }

  void userDrinkingData() {
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.drinking;

    // Prepopulate data if available and valid
    if (userModel != null) {
      unitsController.text = userModel.units?.toString() ?? '0';
      daysController.text = userModel.days?.toString() ?? '0';
      rateDrinkingSliderValue.value = userModel.rate ?? rateDrinkingSliderValue.value; // Ensure a valid fallback
    } else {
      // Handle case where userModel is null
      unitsController.text = '0';
      daysController.text = '0';
      rateDrinkingSliderValue.value = rateDrinkingSliderValue.value; // Fallback to a safe default
    }
  }

  void updateDrinkItems(String name) {
    List<DrinkItem> newItems;
    String newTitle; // Declare new title here
    // Switch case based on drink name
    switch (name) {
      case 'Beer or cider':
        newItems = [
          createDrinkItem(
            name: 'Small bottle',
            volume: '(275 ml)',
            icon: Assets.icons.drinkingIcons.beer.smallBottle275ml,
            onTap: () {
              log("drinkHelpMeCalculateTitleText ${state.drinkHelpMeCalculateTitleText ?? ''}");
              updateDrinkSubItems('Small bottle');
              emitDrinkState('And what strength?', 'Beer or cider', 'Small bottle', '0', '(275 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(330 ml)',
            icon: Assets.icons.drinkingIcons.beer.bottle330ml,
            onTap: () {
              updateDrinkSubItems('Bottle');
              emitDrinkState('And what strength?', 'Beer or cider', 'Bottle', '0', '(330 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(440 ml)',
            icon: Assets.icons.drinkingIcons.beer.can440ml,
            onTap: () {
              updateDrinkSubItems('Can');
              emitDrinkState('And what strength?', 'Beer or cider', 'Can', '0', '(440 ml)');
            },
          ),
          createDrinkItem(
            name: 'Pint',
            volume: '(568 ml)',
            icon: Assets.icons.drinkingIcons.beer.pint568ml,
            onTap: () {
              updateDrinkSubItems('Pint');
              emitDrinkState('And what strength?', 'Beer or cider', 'Pint', '0', '(568 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?'; // Update title here

      case 'Wine or champagne':
        newItems = [
          createDrinkItem(
            name: 'Small glass',
            volume: '(125 ml)',
            icon: Assets.icons.drinkingIcons.wine.small125ml,
            onTap: () {
              updateDrinkSubItems('Small glass');
              emitDrinkState('And what strength?', 'Wine or champagne', 'Small glass', '0', '(125 ml)');
            },
          ),
          createDrinkItem(
            name: 'Standard glass',
            volume: '(175 ml)',
            icon: Assets.icons.drinkingIcons.wine.standard175ml,
            onTap: () {
              updateDrinkSubItems('Standard glass');
              emitDrinkState('And what strength?', 'Wine or champagne', 'Standard glass', '0', '(175 ml)');
            },
          ),
          createDrinkItem(
            name: 'Large glass',
            volume: '(250 ml)',
            icon: Assets.icons.drinkingIcons.wine.large250ml,
            onTap: () {
              updateDrinkSubItems('Large glass');
              emitDrinkState('And what strength?', 'Wine or champagne', 'Large glass', '0', '(250 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '1 l',
            icon: Assets.icons.drinkingIcons.wine.bottle1L,
            onTap: () {
              updateDrinkSubItems('Bottle1');
              emitDrinkState('And what strength?', 'Wine or champagne', 'Bottle', '0', '1 l');
            },
          ),
        ];
        newTitle = 'What do you normally go for?'; // Update title here

      case 'Spirits or shots':
        newItems = [
          createDrinkItem(
            name: 'Single',
            volume: '(25 ml)',
            icon: Assets.icons.drinkingIcons.spirits.single25ml,
            onTap: () {
              updateDrinkSubItems('Single');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Single', '0', '(25 ml)');
            },
          ),
          createDrinkItem(
            name: 'Large single',
            volume: '(35 ml)',
            icon: Assets.icons.drinkingIcons.spirits.largeSingle35ml,
            onTap: () {
              updateDrinkSubItems('Large single');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Large single', '0', '(35 ml)');
            },
          ),
          createDrinkItem(
            name: 'Double',
            volume: '(50 ml)',
            icon: Assets.icons.drinkingIcons.spirits.double50ml,
            onTap: () {
              updateDrinkSubItems('Double');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Double', '0', '(50 ml)');
            },
          ),
          createDrinkItem(
            name: 'Large double',
            volume: '(70 ml)',
            icon: Assets.icons.drinkingIcons.spirits.largeDouble70ml,
            onTap: () {
              updateDrinkSubItems('Large double');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Large double', '0', '(70 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(700 ml)',
            icon: Assets.icons.drinkingIcons.spirits.bottle700ml,
            onTap: () {
              updateDrinkSubItems('Bottle2');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Bottle', '0', '(700 ml)');
            },
          ),
          createDrinkItem(
            name: 'Large bottle',
            volume: '(1 l)',
            icon: Assets.icons.drinkingIcons.spirits.largeBottle1L,
            onTap: () {
              updateDrinkSubItems('Large bottle');
              emitDrinkState('And what strength?', 'Spirits or shots', 'Large bottle', '0', '(1 l)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?'; // Update title here

      case 'Alcopop':
        newItems = [
          createDrinkItem(
            name: 'Small bottle',
            volume: '(275 ml)',
            icon: Assets.icons.drinkingIcons.alcopops.small275ml,
            onTap: () {
              updateDrinkSubItems('Alcopop Small bottle');
              emitDrinkState('And what strength?', 'Alcopop', 'Small bottle', '0', '(275 ml)');
            },
          ),
          createDrinkItem(
            name: 'Standard bottle',
            volume: '(330 ml)',
            icon: Assets.icons.drinkingIcons.alcopops.standard330ml,
            onTap: () {
              updateDrinkSubItems('Alcopop Standard bottle');
              emitDrinkState('And what strength?', 'Alcopop', 'Standard bottle', '0', '(330 ml)');
            },
          ),
          createDrinkItem(
            name: 'Large bottle',
            volume: '(750 ml)',
            icon: Assets.icons.drinkingIcons.alcopops.large750ml,
            onTap: () {
              updateDrinkSubItems('Alcopop Large bottle');
              emitDrinkState('And what strength?', 'Alcopop', 'Large bottle', '0', '(750 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?'; // Update title here

      default:
        newItems = [];
        newTitle = 'On a typical day what do you normally drink?'; // Set a default title
        break;
    }

    // Store current drink items and title in history
    drinkHistory.add({
      'items': drinkItemsNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': name,
    });
    unitshelpmeController.text = '0';
    emit(state.copyWith(customDrinkingValue: 0));

    // Update the drinkItemsNotifier value
    drinkItemsNotifier.value = newItems;
    log('state.se${state.selecteddrinkHelpMeCalculatePercentageText}');
    log('state.se${state.selecteddrinkHelpMeCalculateSubTitleText}');
    // Update the title to the new title
    drinkHistoryTitleText = newTitle;
  }

  void updateAuDrinkItems(String name) {
    List<DrinkItem> newItems;
    String newTitle; // Declare new title here
    // Switch case based on drink name
    switch (name) {
      case 'Beer':
        newItems = [
          createDrinkItem(
            name: 'Middy glass',
            volume: '(285 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.middy285ml,
            onTap: () {
              log("drinkHelpMeCalculateTitleText ${state.drinkHelpMeCalculateTitleText ?? ''}");
              updateDrinkAuSubItems('Middy glass');
              emitDrinkState('And what strength?', 'Beer', 'Middy glass', '0', '(285 ml)');
            },
          ),
          createDrinkItem(
            name: 'Schooner glass',
            volume: '(425 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.schooner425ml,
            onTap: () {
              updateDrinkAuSubItems('Schooner glass');
              emitDrinkState('And what strength?', 'Beer', 'Schooner glass', '0', '(425 ml)');
            },
          ),
          createDrinkItem(
            name: 'Pint glass',
            volume: '(570 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.pint570ml,
            onTap: () {
              updateDrinkAuSubItems('Pint glass');
              emitDrinkState('And what strength?', 'Beer', 'Pint glass', '0', '(570 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(375 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.bottle375ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle');
              emitDrinkState('And what strength?', 'Beer', 'Bottle', '0', '(375 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(375 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.can375ml,
            onTap: () {
              updateDrinkAuSubItems('Can');
              emitDrinkState('And what strength?', 'Beer', 'Can', '0', '(375 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?'; // Update title here

      case 'Cider':
        newItems = [
          createDrinkItem(
            name: 'Middy glass',
            volume: '(285 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.middy285ml,
            onTap: () {
              log("drinkHelpMeCalculateTitleText ${state.drinkHelpMeCalculateTitleText ?? ''}");
              updateDrinkAuSubItems('Middy glass');
              emitDrinkState('And what strength?', 'Cider', 'Middy glass', '0', '(285 ml)');
            },
          ),
          createDrinkItem(
            name: 'Schooner glass',
            volume: '(425 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.schooner425ml,
            onTap: () {
              updateDrinkAuSubItems('Schooner glass');
              emitDrinkState('And what strength?', 'Cider', 'Schooner glass', '0', '(425 ml)');
            },
          ),
          createDrinkItem(
            name: 'Pint glass',
            volume: '(570 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.pint570ml,
            onTap: () {
              updateDrinkAuSubItems('Pint glass');
              emitDrinkState('And what strength?', 'Cider', 'Pint glass', '0', '(570 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(375 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.bottle375ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle');
              emitDrinkState('And what strength?', 'Cider', 'Bottle', '0', '(375 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(375 ml)',
            icon: Assets.icons.drinkingAusIcons.ausBeer.can375ml,
            onTap: () {
              updateDrinkAuSubItems('Can');
              emitDrinkState('And what strength?', 'Cider', 'Can', '0', '(375 ml)');
            },
          ),
        ];

        newTitle = 'What do you normally go for?'; // Update title here

      case 'Alcopops':
        newItems = [
          createDrinkItem(
            name: 'Bottle',
            volume: '(275 ml)',
            icon: Assets.icons.drinkingAusIcons.ausAlcopops.small275ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(275)');
              emitDrinkState('And what strength?', 'Alcopops', 'Bottle', '0', '(275 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(330 ml)',
            icon: Assets.icons.drinkingAusIcons.ausAlcopops.standard330ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(330)');
              emitDrinkState('And what strength?', 'Alcopops', 'Bottle', '0', '(330 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(660 ml)',
            icon:Assets.icons.drinkingAusIcons.ausAlcopops.large660ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(660)');
              emitDrinkState('And what strength?', 'Alcopops', 'Bottle', '0', '(660 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

      case 'Pre-mixed spirits':
        newItems = [
          createDrinkItem(
            name: 'Can',
            volume: '(250 ml)',
            icon: Assets.icons.drinkingAusIcons.ausPremixedSpirits.can250ml,
            onTap: () {
              updateDrinkAuSubItems('Can(250)');
              emitDrinkState('And what strength?', 'Pre-mixed spirits', 'Can', '0', '(250 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(300 ml)',
            icon: Assets.icons.drinkingAusIcons.ausPremixedSpirits.can300ml,
            onTap: () {
              updateDrinkAuSubItems('Can(300)');
              emitDrinkState('And what strength?', 'Pre-mixed spirits', 'Can', '0', '(300 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(375 ml)',
            icon: Assets.icons.drinkingAusIcons.ausPremixedSpirits.can375ml,
            onTap: () {
              updateDrinkAuSubItems('Can(375)');
              emitDrinkState('And what strength?', 'Pre-mixed spirits', 'Can', '0', '(375 ml)');
            },
          ),
          createDrinkItem(
            name: 'Can',
            volume: '(440 ml)',
            icon: Assets.icons.drinkingAusIcons.ausPremixedSpirits.can440ml,
            onTap: () {
              updateDrinkAuSubItems('Can(440)');
              emitDrinkState('And what strength?', 'Pre-mixed spirits', 'Can', '0', '(440 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

      case 'Wine':
        newItems = [
          createDrinkItem(
            name: 'Small glass',
            volume: '(100 ml)',
            icon: Assets.icons.drinkingAusIcons.ausWine.small100ml,
            onTap: () {
              updateDrinkAuSubItems('Small glass');
              emitDrinkState('And what strength?', 'Wine', 'Small glass', '0', '(100 ml)');
            },
          ),
          createDrinkItem(
            name: 'Medium glass',
            volume: '(150 ml)',
            icon: Assets.icons.drinkingAusIcons.ausWine.medium150ml,
            onTap: () {
              updateDrinkAuSubItems('Medium glass');
              emitDrinkState('And what strength?', 'Wine', 'Medium glass', '0', '(150 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(750 ml)',
            icon: Assets.icons.drinkingAusIcons.ausWine.bottle750ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(750)');
              emitDrinkState('And what strength?', 'Wine', 'Bottle', '0', '(750 ml)');
            },
          ),
          createDrinkItem(
            name: 'Cask',
            volume: '(2 l)',
            icon: Assets.icons.drinkingAusIcons.ausWine.cask2000ml,
            onTap: () {
              updateDrinkAuSubItems('Cask(2 l)');
              emitDrinkState('And what strength?', 'Wine', 'Cask', '0', '(2 l)');
            },
          ),
          createDrinkItem(
            name: 'Large cask',
            volume: '(4 l)',
            icon: Assets.icons.drinkingAusIcons.ausWine.cask4000ml,
            onTap: () {
              updateDrinkAuSubItems('Large cask');
              emitDrinkState('And what strength?', 'Wine', 'Large cask', '0', '(4 l)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

      case 'Sparkling wine':
        newItems = [
          createDrinkItem(
            name: 'Glass',
            volume: '(150 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSparklingWine.glass150ml,
            onTap: () {
              updateDrinkAuSubItems('Glass(150)');
              emitDrinkState('And what strength?', 'Sparkling wine', 'Glass', '0', '(150 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(750 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSparklingWine.bottle750ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(75.0)');
              emitDrinkState('And what strength?', 'Sparkling wine', 'Bottle', '0', '(750 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

      case 'Sherry/port':
        newItems = [
          createDrinkItem(
            name: 'Glass',
            volume: '(60 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSherryPort.glass60ml,
            onTap: () {
              updateDrinkAuSubItems('Glass(60)');
              emitDrinkState('And what strength?', 'Sherry/port', 'Glass', '0', '(60 ml)');
            },
          ),
          createDrinkItem(
            name: 'Cask',
            volume: '(2 l)',
            icon: Assets.icons.drinkingAusIcons.ausSherryPort.cask2000ml,
            onTap: () {
              updateDrinkAuSubItems('Cask');
              emitDrinkState('And what strength?', 'Sherry/port', 'Cask', '0', '(2 l)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

      case 'Spirits':
        newItems = [
          createDrinkItem(
            name: 'Single',
            volume: '(30 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSpirits.single30ml,
            onTap: () {
              updateDrinkAuSubItems('Single');
              emitDrinkState('And what strength?', 'Spirits', 'Single', '0', '(30 ml)');
            },
          ),
          createDrinkItem(
            name: 'Double',
            volume: '(60 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSpirits.double60ml,
            onTap: () {
              updateDrinkAuSubItems('Double');
              emitDrinkState('And what strength?', 'Spirits', 'Double', '0', '(60 ml)');
            },
          ),
          createDrinkItem(
            name: 'Bottle',
            volume: '(700 ml)',
            icon: Assets.icons.drinkingAusIcons.ausSpirits.bottle700ml,
            onTap: () {
              updateDrinkAuSubItems('Bottle(700)');
              emitDrinkState('And what strength?', 'Spirits', 'Bottle', '0', '(700 ml)');
            },
          ),
        ];
        newTitle = 'What do you normally go for?';

        // Update title here

      default:
        newItems = [];
        newTitle = 'On a typical day what do you normally drink?'; // Set a default title
        break;
    }

    // Store current drink items and title in history
    drinkAuHistory.add({
      'items': drinkItemsAuNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': name,
    });
    unitshelpmeController.text = '0';
    emit(state.copyWith(customDrinkingValue: 0));

    // Update the drinkItemsNotifier value
    drinkItemsAuNotifier.value = newItems;
    log('state.se${state.selecteddrinkHelpMeCalculatePercentageText}');
    log('state.se${state.selecteddrinkHelpMeCalculateSubTitleText}');
    // Update the title to the new title
    drinkHistoryTitleText = newTitle;
  }

  void updateDrinkSubItems(String name) {
    List<DrinkItem> newItems;
    const newTitle = 'And what strength?'; // Fixed title for sub-items

    switch (name) {
      case 'Small bottle':
      case 'Bottle':
      case 'Can':
      case 'Pint':
        newItems = [
          createDrinkItem(
            name: '4%',
            onTap: () {
              subDrinkTapFunction('4%');
            },
          ),
          createDrinkItem(
            name: '4.5%',
            onTap: () {
              subDrinkTapFunction('4.5%');
            },
          ),
          createDrinkItem(
            name: '5%',
            onTap: () {
              subDrinkTapFunction('5%');
            },
          ),
          createDrinkItem(
            name: '6%',
            onTap: () {
              subDrinkTapFunction('6%');
            },
          ),
          createDrinkItem(
            name: '7%',
            onTap: () {
              subDrinkTapFunction('7%');
            },
          ),
          createDrinkItem(
            name: '8%',
            onTap: () {
              subDrinkTapFunction('8%');
            },
          ),
          createDrinkItem(
            name: '9%',
            onTap: () {
              subDrinkTapFunction('9%');
            },
          ),
        ];
      case 'Small glass':
      case 'Standard glass':
      case 'Large glass':
      case 'Bottle1':
        newItems = [
          createDrinkItem(
            name: '9%',
            onTap: () {
              subDrinkTapFunction('9%');
            },
          ),
          createDrinkItem(
            name: '10%',
            onTap: () {
              subDrinkTapFunction('10%');
            },
          ),
          createDrinkItem(
            name: '10.5%',
            onTap: () {
              subDrinkTapFunction('10.5%');
            },
          ),
          createDrinkItem(
            name: '11%',
            onTap: () {
              subDrinkTapFunction('11%');
            },
          ),
          createDrinkItem(
            name: '11.5%',
            onTap: () {
              subDrinkTapFunction('11.5');
            },
          ),
          createDrinkItem(
            name: '12%',
            onTap: () {
              subDrinkTapFunction('12%');
            },
          ),
          createDrinkItem(
            name: '13%',
            onTap: () {
              subDrinkTapFunction('13%');
            },
          ),
          createDrinkItem(
            name: '14%',
            onTap: () {
              subDrinkTapFunction('14%');
            },
          ),
          createDrinkItem(
            name: '15%',
            onTap: () {
              subDrinkTapFunction('15%');
            },
          ),
          createDrinkItem(
            name: '16%',
            onTap: () {
              subDrinkTapFunction('16%');
            },
          ),
          createDrinkItem(
            name: '17%',
            onTap: () {
              subDrinkTapFunction('17%');
            },
          ),
        ];
      case 'Single':
      case 'Large single':
      case 'Double':
      case 'Large double':
      case 'Bottle2':
      case 'Large bottle':
        newItems = [
          createDrinkItem(
            name: '37%',
            onTap: () {
              subDrinkTapFunction('37%');
            },
          ),
          createDrinkItem(
            name: '40%',
            onTap: () {
              subDrinkTapFunction('40%');
            },
          ),
        ];
      case 'Alcopop Small bottle':
      case 'Alcopop Standard bottle':
      case 'Alcopop Large bottle':
        newItems = [
          createDrinkItem(
            name: '5%',
            onTap: () {
              subDrinkTapFunction('5%');
            },
          ),
          createDrinkItem(
            name: '5.5%',
            onTap: () {
              subDrinkTapFunction('5.5%');
            },
          ),
        ];
      default:
        newItems = [];
        break;
    }

    drinkHistory.add({
      'items': drinkItemsNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
      'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    });
    log('state.se${state.selecteddrinkHelpMeCalculatePercentageText}');
    log('state.se${state.selecteddrinkHelpMeCalculateSubTitleText}');
    drinkItemsNotifier.value = newItems;
    drinkHistoryTitleText = newTitle;
  }
  void updateDrinkAuSubItems(String name) {
    List<DrinkItem> newItems;
    const newTitle = 'And what strength?'; // Fixed title for sub-items

    switch (name) {
      case 'Middy glass':
      case 'Schooner glass':
      case 'Pint glass':
      case 'Bottle':
      case 'Can':
        newItems = [
          createDrinkItem(
            name: '2.7%',
            onTap: () {
              subDrinkAuTapFunction('2.7%');
            },
          ),
          createDrinkItem(
            name: '3.5%',
            onTap: () {
              subDrinkAuTapFunction('3.5%');
            },
          ),
          createDrinkItem(
            name: '4.8%',
            onTap: () {
              subDrinkAuTapFunction('4.8%');
            },
          ),

        ];
      case 'Bottle(275)':
      case 'Bottle(330)':
      case 'Bottle(660)':
        newItems = [
          createDrinkItem(
            name: '5%',
            onTap: () {
              subDrinkAuTapFunction('5%');
            },
          ),
          createDrinkItem(
            name: '7%',
            onTap: () {
              subDrinkAuTapFunction('7%');
            },
          ),

        ];
      case 'Can(250)':
      case 'Can(300)':
      case 'Can(375)':
      case 'Can(440)':
        newItems = [
          createDrinkItem(
            name: '5%',
            onTap: () {
              subDrinkAuTapFunction('5%');
            },
          ),
          createDrinkItem(
            name: '7%',
            onTap: () {
              subDrinkAuTapFunction('7%');
            },
          ),
        ];
      case 'Small glass':
      case 'Medium glass':
      case 'Bottle(750)':
      case 'Cask(2 l)':
      case 'Large cask':
        newItems = [
          createDrinkItem(
            name: '11.5%',
            onTap: () {
              subDrinkAuTapFunction('11.5%');
            },
          ),
          createDrinkItem(
            name: '13.5%',
            onTap: () {
              subDrinkAuTapFunction('13.5%');
            },
          ),
        ];
      case 'Glass(150)':
      case 'Bottle(75.0)':
        newItems = [
          createDrinkItem(
            name: '12%',
            onTap: () {
              subDrinkAuTapFunction('12%');
            },
          ),
        ];
      case 'Glass(60)':
      case 'Cask':
        newItems = [
          createDrinkItem(
            name: '17.5%',
            onTap: () {
              subDrinkAuTapFunction('17.5%');
            },
          ),
        ];
      case 'Single':
      case 'Double':
      case 'Bottle(700)':
      newItems = [
        createDrinkItem(
          name: '40%',
          onTap: () {
            subDrinkAuTapFunction('40%');
          },
        ),
      ];
      default:
        newItems = [];
        break;
    }

    drinkAuHistory.add({
      'items': drinkItemsAuNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
      'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    });
    log('state.se${state.selecteddrinkHelpMeCalculatePercentageText}');
    log('state.se${state.selecteddrinkHelpMeCalculateSubTitleText}');
    drinkItemsAuNotifier.value = newItems;
    drinkHistoryTitleText = newTitle;
  }

  void subDrinkTapFunction(String percentage) {
    drinkHistory.add({
      'items': drinkItemsNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
      'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    });
    emitDrinkState(
      'How many of these do you drink?',
      state.selecteddrinkHelpMeCalculateTitleText ?? '',
      state.selecteddrinkHelpMeCalculateSubTitleText ?? '',
      percentage,
      state.selecteddrinkHelpMeSizeText ?? '',
    );
  }
  void subDrinkAuTapFunction(String percentage) {
    drinkAuHistory.add({
      'items': drinkItemsAuNotifier.value,
      'title': drinkHistoryTitleText,
      'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
      'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    });
    emitDrinkState(
      'How many of these do you drink?',
      state.selecteddrinkHelpMeCalculateTitleText ?? '',
      state.selecteddrinkHelpMeCalculateSubTitleText ?? '',
      percentage,
      state.selecteddrinkHelpMeSizeText ?? '',
    );
  }

  void goBack() {
    if (drinkHistory.isNotEmpty) {
      // Restore the last state from history
      final previousState = drinkHistory.removeLast();
      drinkItemsNotifier.value = previousState['items'] as List<DrinkItem>;
      drinkHistoryTitleText = previousState['title'] as String;
      final previousSubTitle = previousState['selectedSubTitle'] as String?;
      final previousPercentage = previousState['selectedPercentage'] as String?;
      final showBackArrow = drinkHistory.isNotEmpty;
      emit(
        state.copyWith(
          drinkHelpMeCalculateTitleText: drinkHistoryTitleText,
          showBackArrownQuestion: showBackArrow,
          selecteddrinkHelpMeCalculateSubTitleText: previousSubTitle,
          selecteddrinkHelpMeCalculatePercentageText: previousPercentage,
        ),
      );
      if (drinkHistory.isEmpty) {
        initializeDrinkItems();
      }
    } else {
      initializeDrinkItems();
    }
  }
  void goBackAu() {
    if (drinkAuHistory.isNotEmpty) {
      // Restore the last state from history
      final previousState = drinkAuHistory.removeLast();
      drinkItemsAuNotifier.value = previousState['items'] as List<DrinkItem>;
      drinkHistoryTitleText = previousState['title'] as String;
      final previousSubTitle = previousState['selectedSubTitle'] as String?;
      final previousPercentage = previousState['selectedPercentage'] as String?;
      final showBackArrow = drinkAuHistory.isNotEmpty;
      emit(
        state.copyWith(
          drinkHelpMeCalculateTitleText: drinkHistoryTitleText,
          showBackArrownQuestion: showBackArrow,
          selecteddrinkHelpMeCalculateSubTitleText: previousSubTitle,
          selecteddrinkHelpMeCalculatePercentageText: previousPercentage,
        ),
      );
      if (drinkAuHistory.isEmpty) {
        initializeAuDrinkItems();
      }
    } else {
      initializeAuDrinkItems();
    }
  }

  void clearDrinkHistory() {
    drinkHistory = [];
    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: '',
        selecteddrinkHelpMeCalculatePercentageText: '',
        selecteddrinkHelpMeCalculateSubTitleText: '',
        showBackArrownQuestion: false,
        drinkDetails: [],
      ),
    );
    isShowAlltheDetail.value = false;
  }

  void clearAuDrinkHistory() {
    drinkAuHistory = [];
    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: '',
        selecteddrinkHelpMeCalculatePercentageText: '',
        selecteddrinkHelpMeCalculateSubTitleText: '',
        showBackArrownQuestion: false,
        drinkDetails: [],
      ),
    );
    isShowAlltheDetail.value = false;
  }

  double total = 0;
  double grandTotal = 0;


  void calculateDrinks(List<DrinkDetail> drinkDetails) {
    final regExp = RegExp(r'[\d.]+'); // Updated to also match decimals
    double grandTotal = 0;

    for (final drink in drinkDetails) {
      try {
        final strength = double.parse(drink.strength.toString().replaceAll('%', ''));
        var size = double.parse(regExp.firstMatch(drink.size.toString())!.group(0)!);
        final qty = int.parse(drink.quantity.toString());
        '/// size = $size'.logV;
        size =  size == 1.0 ?  size*1000 : size;
        double drinkUnit;
        if (navigatorKey.currentContext!.locale == const Locale('en', 'GB')) {
          drinkUnit = strength * size * qty * 0.001;
        } else {
          drinkUnit = strength * size * qty * 0.0008;
        }

        grandTotal += drinkUnit;
      } catch (e) {
        print('Error parsing drink detail: $e');
      }
    }

    print('grandTotal : $grandTotal');

    // Update the controller and emit state
    unitsController.text = grandTotal.round().toString();
    emit(state.copyWith(currentDrinkingValue: grandTotal.round()));
  }



  void initializeDrinkItems() {
    drinkItemsNotifier.value = [
      createDrinkItem(
        name: 'Beer or cider',
        icon: Assets.icons.drinkingIcons.normallyDrink.beerCider,
        onTap: () {
          updateDrinkItems('Beer or cider');
          emitDrinkState('What do you normally go for?', 'Beer or cider', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Wine or champagne',
        icon: Assets.icons.drinkingIcons.normallyDrink.wineChampagne,
        onTap: () {
          updateDrinkItems('Wine or champagne');
          emitDrinkState('What do you normally go for?', 'Wine or champagne', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Spirits or shots',
        icon: Assets.icons.drinkingIcons.normallyDrink.spiritsShots,
        onTap: () {
          updateDrinkItems('Spirits or shots');
          emitDrinkState('What do you normally go for?', 'Spirits or shots', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Alcopop',
        icon: Assets.icons.drinkingIcons.normallyDrink.alcopop,
        onTap: () {
          updateDrinkItems('Alcopop');
          emitDrinkState('What do you normally go for?', 'Alcopop', '', '0', '0');
        },
      ),
    ];

    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: 'On a typical day what do you normally drink?',
      ),
    );
    // drinkHistory.add({
    //   'items': drinkItemsNotifier.value,
    //   'title': state.drinkHelpMeCalculateTitleText,
    //   'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
    //   'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    // });
    drinkHistoryTitleText = state.drinkHelpMeCalculateTitleText ?? '';
    drinkHistoryBackButton = state.showBackArrownQuestion;
  }

  void initializeAuDrinkItems(){
    drinkItemsAuNotifier.value = [
      createDrinkItem(
        name: 'Beer',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.beer,
        onTap: () {
          updateAuDrinkItems('Beer');
          emitDrinkState('What do you normally go for?', 'Beer', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Cider',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.cider,
        onTap: () {
          updateAuDrinkItems('Cider');
          emitDrinkState('What do you normally go for?', 'Cider', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Alcopops',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.alcopop,
        onTap: () {
          updateAuDrinkItems('Alcopops');
          emitDrinkState('What do you normally go for?', 'Alcopop', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Pre-mixed spirits',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.preMixedSpirits,
        onTap: () {
          updateAuDrinkItems('Pre-mixed spirits');
          emitDrinkState('What do you normally go for?', 'Pre-mixed spirits', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Wine ',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.wine,
        onTap: () {
          updateAuDrinkItems('Wine');
          emitDrinkState('What do you normally go for?', 'Wine', '', '0', '0');
        },
      ),
      createDrinkItem(
        name: 'Sparkling wine',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.sparklingWine,
        onTap: () {
          updateAuDrinkItems('Sparkling wine');
          emitDrinkState('What do you normally go for?', 'Sparkling wine', '', '0', '0');
        },
      ),

      createDrinkItem(
        name: 'Sherry/port',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.sheeryPort,
        onTap: () {
          updateAuDrinkItems('Sherry/port');
          emitDrinkState('What do you normally go for?', 'Sherry/port', '', '0', '0');
        },
      ),

      createDrinkItem(
        name: 'Spirits',
        icon: Assets.icons.drinkingAusIcons.ausNormallyDrink.spiritsShots,
        onTap: () {
          updateAuDrinkItems('Spirits');
          emitDrinkState('What do you normally go for?', 'Spirits', '', '0', '0');
        },
      ),

    ];

    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: 'On a typical day what do you normally drink?',
      ),
    );
    // drinkHistory.add({
    //   'items': drinkItemsNotifier.value,
    //   'title': state.drinkHelpMeCalculateTitleText,
    //   'selectedSubTitle': state.selecteddrinkHelpMeCalculateSubTitleText,
    //   'selectedPercentage': state.selecteddrinkHelpMeCalculatePercentageText,
    // });
    drinkHistoryTitleText = state.drinkHelpMeCalculateTitleText ?? '';
    drinkHistoryBackButton = state.showBackArrownQuestion;
  }


// Helper method to create DrinkItem
  DrinkItem createDrinkItem({required String name, String? volume, String? icon, void Function()? onTap}) {
    return DrinkItem(
      name: name,
      volume: volume,
      ismil: true,
      icon: icon,
      onTap: onTap ?? () {},
    );
  }

// Helper method to emit state with title and back arrow visibility
  void emitDrinkState(
    String title,
    String selectedTitle,
    String selectedSubTitle,
    String percentage,
    String size,
  ) {
    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: title,
        showBackArrownQuestion: true,
        selecteddrinkHelpMeCalculateTitleText: selectedTitle,
        selecteddrinkHelpMeCalculateSubTitleText: selectedSubTitle,
        selecteddrinkHelpMeCalculatePercentageText: percentage,
        selecteddrinkHelpMeSizeText: size,
      ),
    );
  }

  void emitAddDrinkState() {
    final currentQuantity = int.tryParse(unitshelpmeController.text) ?? 0;

    // Prevent adding if quantity is zero or less
    if (currentQuantity <= 0) return;

    emit(
      state.copyWith(
        drinkHelpMeCalculateTitleText: 'Your drinks on a typical day',
        showBackArrownQuestion: false,
        selecteddrinkHelpMeCalculateTitleText: state.selecteddrinkHelpMeCalculateTitleText,
        selecteddrinkHelpMeCalculateSubTitleText: state.selecteddrinkHelpMeCalculateSubTitleText,
        selecteddrinkHelpMeCalculatePercentageText: state.selecteddrinkHelpMeCalculatePercentageText,
      ),
    );

    final drinkDetail = DrinkDetail(
      drinkType: state.selecteddrinkHelpMeCalculateTitleText ?? '',
      strength: state.selecteddrinkHelpMeCalculatePercentageText ?? '',
      quantity: currentQuantity.toString(),
      size: state.selecteddrinkHelpMeSizeText ?? '',
    );

    addDrinkDetail(drinkDetail);

    unitshelpmeController.text = '0';
    isShowAlltheDetail.value = true;
  }

  void emitAddAthoerDrinkState() {
    if (state.selecteddrinkHelpMeCalculateTitleText != null &&
        state.selecteddrinkHelpMeSizeText != null &&
        state.selecteddrinkHelpMeCalculatePercentageText != null &&
        unitshelpmeController.text.isNotEmpty) {
      drinkItems.add(
        DrinkItem(
          name: state.selecteddrinkHelpMeCalculateTitleText!,
          ismil: true,
          volume: state.selecteddrinkHelpMeSizeText,
        ),
      );
      clearDrinkHistory();
    }
  }

  // Method to add a new drink detail to the list
  void addDrinkDetail(DrinkDetail detail) {
    // Check if the drink type already exists in the list
    final existingIndex = drinkDetails.indexWhere((d) => d.drinkType == detail.drinkType);

    if (existingIndex != -1) {
      drinkDetails[existingIndex] = detail;
    } else {
      // Add the new drink detail to the list
      drinkDetails.add(detail);
    }

    calculateDrinks(drinkDetails);
    emit(state.copyWith(drinkDetails: List<DrinkDetail>.from(drinkDetails)));

  }

/*
  void removeDrinkDetail(DrinkDetail drinkDetail) {
    final existingIndex = drinkDetails.indexWhere((element) => element.drinkType == drinkDetail.drinkType);

    if (existingIndex != -1) {
      final removedDrink = drinkDetails[existingIndex];
      final removedQuantity = int.tryParse(removedDrink.quantity ?? '0') ?? 0;

      var currentQuantity = int.tryParse(unitsController.text) ?? 0;

      currentQuantity -= removedQuantity;
      if (currentQuantity < 0) currentQuantity = 0;

      unitsController.text = currentQuantity.toString();

      drinkDetails.removeAt(existingIndex);

      emit(
        state.copyWith(
          drinkDetails: List<DrinkDetail>.from(drinkDetails), // Emit the new list
          currentDrinkingValue: currentQuantity,
        ),
      );
    } else {
      log('Drink detail not found for removal: ${drinkDetail.drinkType}');
    }
  }
*/

  void removeDrinkDetail(DrinkDetail drinkDetail) {
    final existingIndex = drinkDetails.indexWhere(
          (element) => element.drinkType == drinkDetail.drinkType &&
          element.strength == drinkDetail.strength &&
          element.size == drinkDetail.size &&
          element.quantity == drinkDetail.quantity,
    );

    if (existingIndex != -1) {
      drinkDetails.removeAt(existingIndex);

      // Recalculate the total from the updated list
      double recalculatedTotal = 0.0;
      final regExp = RegExp(r'\d+');

      for (final item in drinkDetails) {
        final strength = int.tryParse(item.strength?.replaceAll('%', '') ?? '0') ?? 0;
        final sizeMatch = regExp.firstMatch(item.size ?? '');
        final size = int.tryParse(sizeMatch?.group(0) ?? '0') ?? 0;
        final qty = int.tryParse(item.quantity ?? '0') ?? 0;

        double unit;
        if (navigatorKey.currentContext!.locale == const Locale('en', 'GB')) {
          unit = strength * size * qty * 0.001;
        } else {
          unit = strength * size * qty * 0.0008;
        }

        recalculatedTotal += unit;
      }

      unitsController.text = recalculatedTotal.round().toString();

      emit(
        state.copyWith(
          drinkDetails: List<DrinkDetail>.from(drinkDetails),
          currentDrinkingValue: recalculatedTotal.round(),
        ),
      );

      print('Recalculated Total: $recalculatedTotal');
    } else {
      log('Drink detail not found for removal: ${drinkDetail.drinkType}');
    }
  }


  // void increaseDrinkingValue() {
  //   isUnitAndDrinkingButton.value = false;
  //   if (state.currentDrinkingValue < 10000) {
  //     final newValue = state.currentDrinkingValue + 1;
  //     unitsController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void increaseDrinkingValue() {
    final textValue = int.tryParse(unitsController.text) ?? state.currentDrinkingValue;
    isUnitAndDrinkingButton.value = false;
    if (textValue < 10000) {
      final newValue = textValue + 1;
      unitsController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDrinkingValue: newValue,
      //   ),
      // );
    }
  }

  void decreaseDrinkingValue() {
    final textValue = int.tryParse(unitsController.text) ?? state.currentDrinkingValue;
    isUnitAndDrinkingButton.value = false;
    if (textValue > 0) {
      final newValue = textValue - 1;
      unitsController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDrinkingValue: newValue,
      //   ),
      // );
    }

  }

  // void decreaseDrinkingValue() {
  //   isUnitAndDrinkingButton.value = false;
  //   if (state.currentDrinkingValue > 0) {
  //     final newValue = state.currentDrinkingValue - 1;
  //     unitsController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDrinkingValue: newValue,
  //       ),
  //     );
  //   }

  // }


  void increaseCustomDrinkingValue() {
    if (state.customDrinkingValue < 10000) {
      final newValue = state.customDrinkingValue + 1;

      // Update controller and emit custom value
      unitshelpmeController.text = newValue.toString();
      emit(state.copyWith(customDrinkingValue: newValue));

    }
  }


  void decreaseCustomDrinkingValue() {
    if (state.customDrinkingValue > 0) {
      final newValue = state.customDrinkingValue - 1;

      // Update controller and emit custom value
      unitshelpmeController.text = newValue.toString();
      emit(state.copyWith(customDrinkingValue: newValue));

    }
  }

  // void increaseDaysValue() {
  //   isUnitAndDrinkingButton.value = false;
  //   if (state.currentDayValue < 7) {
  //     final newValue = state.currentDayValue + 1;
  //     daysController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDayValue: newValue,
  //       ),
  //     );
  //   }
  // }

  void increaseDaysValue() {
    final textValue = int.tryParse(daysController.value.text) ?? state.currentDayValue;
    isUnitAndDrinkingButton.value = false;
    if (textValue < 7) {
      final newValue = textValue + 1;
      daysController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDayValue: newValue,
      //   ),
      // );
    }
  }

  void decreaseDayValue() {
    final textValue = int.tryParse(daysController.value.text) ?? state.currentDayValue;
    isUnitAndDrinkingButton.value = false;
    if (textValue > 0) {
      final newValue = textValue - 1;
      daysController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentDayValue: newValue,
      //   ),
      // );
    }
  }

  // void decreaseDayValue() {
  //   isUnitAndDrinkingButton.value = false;
  //   if (state.currentDayValue > 0) {
  //     final newValue = state.currentDayValue - 1;
  //     daysController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentDayValue: newValue,
  //       ),
  //     );
  //   }
  // }

  Future<void> putDrinkingAPI({
    required int units,
    required int days,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putDrinking(
        context: context,
        days: days,
        units: units,
        rate: rate,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (rateDrinkingInfoVisible.value == true) {
          infoAudioUrl.value = AssessmentLocaleKeys.drinkingAudio.tr();
          rateDrinkingInfoVisible.value = false;
        }
        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }
}
