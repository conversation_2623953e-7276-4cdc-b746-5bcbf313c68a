import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life_style/assessment_life_style_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_unhelpful_behaviour_cubit.freezed.dart';
part 'assessment_unhelpful_behaviour_state.dart';

class AssessmentUnhelpfulBehaviourCubit extends Cubit<AssessmentUnhelpfulBehaviourState> {
  AssessmentUnhelpfulBehaviourCubit() : super(AssessmentUnhelpfulBehaviourState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.unhelpfulBehavioursAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isUnHelpBehaviourClicked = ValueNotifier(false);
  ValueNotifier<int> rateBehaviourSliderVlaue = ValueNotifier(-1);
  ValueNotifier<bool> rateBehaviourInfoVisible = ValueNotifier(false);
  ValueNotifier<bool> rateBehaviourInfoVisibleLabel = ValueNotifier(false);

  //Unhelpful Behaviours
  ValueNotifier<ButtonState> aggressiveState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> avoidState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> activeState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> careState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> policeState = ValueNotifier(ButtonState.bothDisabled);

  final aggressiveKey = GlobalKey();
  final avoidKey = GlobalKey();
  final activeKey = GlobalKey();
  final careKey = GlobalKey();
  final policeKey = GlobalKey();

  void userUnhelpfulBehaviourData() {
    final userData = Injector.instance<AppDB>().userModel?.user.assessment?.ub;
    if (userData != null) {
      aggressiveState.value = userData.aggressive == null
          ? ButtonState.bothDisabled
          : userData.aggressive == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      avoidState.value = userData.avoid == null
          ? ButtonState.bothDisabled
          : userData.avoid == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      activeState.value = userData.active == null
          ? ButtonState.bothDisabled
          : userData.active == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      careState.value = userData.care == null
          ? ButtonState.bothDisabled
          : userData.care == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      policeState.value = userData.police == null
          ? ButtonState.bothDisabled
          : userData.police == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      rateBehaviourSliderVlaue.value = userData.rate ?? -1;
    }
  }

  Future<void> putUnhelpfulBehaviourAPI({
    required int aggressive,
    required int avoid,
    required int active,
    required int care,
    required int police,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putUnhelpfulBehaviour(
        context: context,
        active: active,
        aggressive: aggressive,
        avoid: avoid,
        care: care,
        police: police,
        rate: rate,
      );
      if (response != null && (response.success ?? false) == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        context.read<AssessmentLifeStyleCubit>().isManuallyPaused.value = false;

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    isUnHelpBehaviourClicked.dispose();
    rateBehaviourSliderVlaue.dispose();
    aggressiveState.dispose();
    avoidState.dispose();
    activeState.dispose();
    careState.dispose();
    policeState.dispose();
    rateBehaviourInfoVisible.dispose();

    return super.close();
  }
}
