import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_unhelpful_behaviour/assessment_unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_physical_senstation_cubit.freezed.dart';
part 'assessment_physical_senstation_state.dart';

class AssessmentPhysicalSenstationCubit extends Cubit<AssessmentPhysicalSenstationState> {
  AssessmentPhysicalSenstationCubit() : super(AssessmentPhysicalSenstationState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.physicalSensationsAudio.tr());

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<bool> isPhysicalSensationClicked = ValueNotifier(false);
  ValueNotifier<int> ratePhysicalSensationSliderVlaue = ValueNotifier(-1);

  //physical sensations
  ValueNotifier<ButtonState> cravingsState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> shakesState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> crampsState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> nauseaState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> tirednessState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<bool> ratePhysicalSensationsInfoVisible = ValueNotifier(false);

  ValueNotifier<bool> ratePhysicalSensationsInfoVisibleLabel = ValueNotifier(false);

  ScrollController scrollController = ScrollController();

  final cravingsKey = GlobalKey();
  final shakesKey = GlobalKey();
  final crampsKey = GlobalKey();
  final nauseaKey = GlobalKey();
  final tirednessKey = GlobalKey();

  void userPhysicalSenstiontData() {
    final userData = Injector.instance<AppDB>().userModel?.user.assessment?.ps;
    if (userData != null) {
      cravingsState.value = userData.craving == null
          ? ButtonState.bothDisabled
          : userData.craving == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      shakesState.value = userData.shakes == null
          ? ButtonState.bothDisabled
          : userData.shakes == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      crampsState.value = userData.cramps == null
          ? ButtonState.bothDisabled
          : userData.cramps == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      nauseaState.value = userData.nausea == null
          ? ButtonState.bothDisabled
          : userData.nausea == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      tirednessState.value = userData.tiredness == null
          ? ButtonState.bothDisabled
          : userData.tiredness == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      ratePhysicalSensationSliderVlaue.value = userData.rate ?? -1;
    }
  }

  Future<void> putPhysicalSensationAPI({
    required int cravings,
    required int shakes,
    required int cramps,
    required int nausea,
    required int tiredness,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      isManuallyPaused.value = true;
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putPhysicalSensation(
        context: context,
        cramps: cramps,
        cravings: cravings,
        nausea: nausea,
        rate: rate,
        shakes: shakes,
        tiredness: tiredness,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (context.read<AssessmentPhysicalSenstationCubit>().ratePhysicalSensationsInfoVisible.value == true) {
          context.read<AssessmentPhysicalSenstationCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.physicalSensationsAudio.tr();
          context.read<AssessmentPhysicalSenstationCubit>().ratePhysicalSensationsInfoVisible.value = false;
        }
        context.read<AssessmentUnhelpfulBehaviourCubit>().isManuallyPaused.value = false;

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    isPhysicalSensationClicked.dispose();
    cravingsState.dispose();
    shakesState.dispose();
    crampsState.dispose();
    nauseaState.dispose();
    tirednessState.dispose();
    ratePhysicalSensationsInfoVisible.dispose();
    ratePhysicalSensationSliderVlaue.dispose();
    return super.close();
  }
}
