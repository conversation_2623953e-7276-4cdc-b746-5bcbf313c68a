import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_thoughts/assessment_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_situtation_cubit.freezed.dart';
part 'assessment_situtation_state.dart';

class AssessmentSitutationCubit extends Cubit<AssessmentSitutationState> {
  AssessmentSitutationCubit() : super(AssessmentSitutationState());

  ValueNotifier<bool> isSitutationButtonClicked = ValueNotifier(false);

  //difficult situations
  ValueNotifier<ButtonState> conflictState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> workState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> moneyState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> risksState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<ButtonState> pressureState = ValueNotifier(ButtonState.bothDisabled);
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(AssessmentLocaleKeys.difficultSituationsAudio.tr());
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  ValueNotifier<bool> rateSitutationInfoVisible = ValueNotifier(false);
  ValueNotifier<int> rateStateSliderVlaue = ValueNotifier(-1);
  AssessmentRepository assessmentRepository = AssessmentRepository();
  AuthRepository authRepository = AuthRepository();

  final conflictKey = GlobalKey();
  final workKey = GlobalKey();
  final moneyKey = GlobalKey();
  final riskKey = GlobalKey();
  final pressureKey = GlobalKey();

  void userSitutationData() {
    final userData = Injector.instance<AppDB>().userModel?.user.assessment?.ds;
    if (userData != null) {
      'userData.conflict${userData.conflict}'.logD;
      conflictState.value = userData.conflict == null
          ? ButtonState.bothDisabled
          : userData.conflict == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      moneyState.value = userData.conflict == null
          ? ButtonState.bothDisabled
          : userData.money == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      pressureState.value = userData.pressure == null
          ? ButtonState.bothDisabled
          : userData.pressure == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      risksState.value = userData.risks == null
          ? ButtonState.bothDisabled
          : userData.risks == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      workState.value = userData.work == null
          ? ButtonState.bothDisabled
          : userData.work == 0
              ? ButtonState.noEnabled
              : ButtonState.yesEnabled;

      rateStateSliderVlaue.value = userData.rate ?? -1;
    }
  }

  Future<void> putAssessmentLifeAPI({
    required int conflict,
    required int money,
    required int pressure,
    required int risks,
    required int work,
    required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putAssessmentDifficultSituations(
        context: context,
        conflict: conflict,
        money: money,
        pressure: pressure,
        risks: risks,
        rate: rate,
        work: work,
      );
      if (response != null && (response.success ?? false) == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);
        if (context.read<AssessmentSitutationCubit>().rateSitutationInfoVisible.value == true) {
          context.read<AssessmentSitutationCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.difficultSituationsAudio.tr();
          context.read<AssessmentSitutationCubit>().rateSitutationInfoVisible.value = false;
        }
        context.read<AssessmentThoughtsCubit>().isManuallyPaused.value = false;
        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    rateSitutationInfoVisible.dispose();
    conflictState.dispose();
    workState.dispose();
    moneyState.dispose();
    risksState.dispose();
    pressureState.dispose();
    isSitutationButtonClicked.dispose();
    rateStateSliderVlaue.dispose();

    return super.close();
  }
}
