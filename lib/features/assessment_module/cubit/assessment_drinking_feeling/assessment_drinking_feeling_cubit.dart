import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_drinking_feeling_cubit.freezed.dart';
part 'assessment_drinking_feeling_state.dart';

class AssessmentDrinkingFeelingCubit extends Cubit<AssessmentDrinkingFeelingState> {
  AssessmentDrinkingFeelingCubit() : super(AssessmentDrinkingFeelingState());

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();
  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isDrinkingFeelingButtonClicked = ValueNotifier(false);

//Drinking Feeling
  ValueNotifier<String> control = ValueNotifier('');
  ValueNotifier<String> anxious = ValueNotifier('');
  ValueNotifier<String> worry = ValueNotifier('');
  ValueNotifier<String> will = ValueNotifier('');
  ValueNotifier<String> difficulty = ValueNotifier('');
  ValueNotifier<String> rate = ValueNotifier('');

  final controlKey = GlobalKey();
  final anxiousKey = GlobalKey();
  final worryKey = GlobalKey();
  final willKey = GlobalKey();
  final difficultyKey = GlobalKey();

  List<String> controlList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drinkingFeelingQuestionsControlItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> anxiousList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drinkingFeelingQuestionsAnxiousItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> worryList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drinkingFeelingQuestionsWorryItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> willList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drinkingFeelingQuestionsWillItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();
  List<String> difficultyList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.drinkingFeelingQuestionsDifficultyItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  void userDrinkingFeelingData() {
    final userModel = Injector.instance<AppDB>().userModel?.user.assessment?.drinkingFeeling;

    // Prepopulate data if available
    if (userModel != null) {
      control.value = (userModel.control != null && userModel.control! >= 0 && userModel.control! < controlList.length)
          ? controlList[userModel.control!]
          : control.value;

      anxious.value = (userModel.anxious != null && userModel.anxious! >= 0 && userModel.anxious! < anxiousList.length)
          ? anxiousList[userModel.anxious!]
          : anxious.value;

      worry.value = (userModel.worry != null && userModel.worry! >= 0 && userModel.worry! < worryList.length)
          ? worryList[userModel.worry!]
          : worry.value;

      will.value = (userModel.will != null && userModel.will! >= 0 && userModel.will! < willList.length)
          ? willList[userModel.will!]
          : will.value;

      difficulty.value =
          (userModel.difficulty != null && userModel.difficulty! >= 0 && userModel.difficulty! < difficultyList.length)
              ? difficultyList[userModel.difficulty!]
              : difficulty.value;

      //  will.value = willList[userModel.will ?? -1];
      //  difficulty.value = difficultyList[userModel.difficulty ?? -1];
    }
  }

  Future<void> putDrinkingFeelingAPI({
    required int control,
    required int anxious,
    required int worry,
    required int will,
    required int difficulty,
    // required int rate,
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isApiLoading: true));
      final response = await assessmentRepository.putDrinkingFeeling(
        context: context,
        anxious: anxious,
        control: control,
        difficulty: difficulty,
        will: will,
        worry: worry,
        // rate: rate,
      );
      if (response != null && (response.success ?? false) == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment ?? Assessment();
        Injector.instance<AppDB>().userModel?.user.assessment.logD;
        await authRepository.getUserData(context: context);

        context.read<AssessmentCubit>().gotoNextWidget();
      }
      emit(state.copyWith(isApiLoading: false));
    } catch (e) {
      emit(state.copyWith(isApiLoading: false));
    } finally {
      emit(state.copyWith(isApiLoading: false));
    }
  }

  @override
  Future<void> close() {
    control.dispose();
    anxious.dispose();
    worry.dispose();
    will.dispose();
    difficulty.dispose();
    rate.dispose();
    isDrinkingFeelingButtonClicked.dispose();
    return super.close();
  }
}
