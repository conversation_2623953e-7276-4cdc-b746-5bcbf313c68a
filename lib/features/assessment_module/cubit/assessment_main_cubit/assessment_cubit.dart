import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_notification/assessment_notification_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_notification_page.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_selection_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentCubit extends Cubit<AssessmentState> {
  AssessmentCubit() : super(AssessmentState()) {
    infoAudioUrl.value = getAudiourl(state.index);
  }

  /// Repository for assessment related api calls.
  AssessmentRepository assessmentRepository = AssessmentRepository();


  ValueNotifier<int> count = ValueNotifier(0);
  /// Auth repository for calling get user data api.
  AuthRepository authRepository = AuthRepository();

  ValueNotifier<bool> isManuallyPaused = ValueNotifier(false);

  //for audio control
  ValueNotifier<String?> infoAudioUrl = ValueNotifier(null);
  ValueNotifier<bool> isAudioPannelVisible = ValueNotifier(false);

  ValueNotifier<bool> isVideoEnded = ValueNotifier(false);
  ValueNotifier<bool> isAssessmentVideoEnded = ValueNotifier(false);
  ValueNotifier<bool> isBridgingAssessmentVideoEnded = ValueNotifier(false);

  //Thumb selection
  ValueNotifier<bool> isRecoveryButtonClicked = ValueNotifier(false);
  ValueNotifier<bool> isMaxTextLength = ValueNotifier(false);
  ValueNotifier<bool> isConsentBoxCheck = ValueNotifier(false);
  ValueNotifier<bool> isSelectedButton = ValueNotifier(false);

  //recovery program
  ValueNotifier<bool> isRecoveryButtonDisable = ValueNotifier(false);

  //check box variables
  ValueNotifier<bool> ageCheckValue = ValueNotifier(false);
  ValueNotifier<bool> confirmAgeValue = ValueNotifier(false);
  ValueNotifier<bool> isGenderCheckValue = ValueNotifier(false);
  ValueNotifier<bool> isEthnicCheckValue = ValueNotifier(false);

  ValueNotifier<bool> isButtonClicked = ValueNotifier(false);

  // dropdown variables
  ValueNotifier<int> selectedBreakingFreeIndex = ValueNotifier(-1);
  ValueNotifier<int> selectedrecoveryIndex = ValueNotifier(-1);

  ValueNotifier<String> firstDrug = ValueNotifier('');

  //slider life page variables
  ValueNotifier<int> rateEmotionalImpactSliderVlaue = ValueNotifier(-1);

  final genderKey = GlobalKey();
  final ethiniCityKey = GlobalKey();
  final ageKey = GlobalKey();
  final nameLength = GlobalKey();

  final TextEditingController ageController = TextEditingController(text: '18');
  final TextEditingController nameController = TextEditingController();

  void resetVideoEnded() {
    '///hello'.logV;
  }

  void assignValue() {
    isManuallyPaused.value = true;
    emit(
        state.copyWith(
          currentAgeValue: int.parse(ageController.text),
        ),
      ); 
  }

String formatString2(String str) {
  final Map<String, String> knownAcronymsWithDescription = {
    'GHB': '',
    'PVP': '',
    'MDPV': '',
    'MSJ': '',
    'HGH': '(human growth hormone)',
    'AH-7921': '',
    'GBL': '',
    'O-PCE': '',
  };

  final wordRegex = RegExp(r'\b[A-Za-z0-9-]+\b');

  return str.replaceAllMapped(wordRegex, (match) {
    final word = match.group(0)!;

    // If the word is all uppercase with possible dashes/numbers, leave as-is
    if (RegExp(r'^[A-Z0-9-]+$').hasMatch(word)) {
      return knownAcronymsWithDescription.containsKey(word)
          ? word + (knownAcronymsWithDescription[word]!.isNotEmpty ? ' ${knownAcronymsWithDescription[word]}' : '')
          : word;
    }

    // Otherwise, split by dashes and handle each part
    final parts = word.split('-');
    final formattedParts = parts.map((part) {
      final upperPart = part.toUpperCase();
      if (knownAcronymsWithDescription.containsKey(upperPart)) {
        return upperPart;
      } else {
        return part.toLowerCase();
      }
    }).toList();

    final result = formattedParts.join('-');
    // If the full result matches a known acronym with description, append it
    final upperResult = result.toUpperCase();
    if (knownAcronymsWithDescription.containsKey(upperResult) &&
        knownAcronymsWithDescription[upperResult]!.isNotEmpty) {
      return upperResult + ' ${knownAcronymsWithDescription[upperResult]}';
    }

    return result;
  });
}

String? getFormattedDrugName(String inputDrug, List<String> drugList) {
  final inputLower = inputDrug.toLowerCase();

  return drugList.firstWhere(
    (drug) {
      // Remove anything in parentheses for comparison, and trim
      final cleanedDrug = drug.split('(')[0].trim().toLowerCase();
      return cleanedDrug == inputLower;
    },
    orElse: () => '',
  );
}




String formatString(String str) {
  final regex = RegExp(r'\b[A-Za-z0-9-]+\b');

  return str.replaceAllMapped(regex, (match) {
    final word = match.group(0)!;

    // If the entire word is uppercase (with numbers or dashes), keep it as is
    if (RegExp(r'^[A-Z0-9-]+$').hasMatch(word)) {
      return word;
    }

    // Otherwise, convert parts of the word to lowercase unless they are acronyms
    return word.replaceAllMapped(RegExp(r'[A-Za-z0-9]+'), (subMatch) {
      final sub = subMatch.group(0)!;
      return RegExp(r'^[A-Z0-9]+$').hasMatch(sub) ? sub : sub.toLowerCase();
    });
  });
}

String matchDrugText(String text1, String text2) {
  /// Normalizes input by removing spaces, parentheses and converting to lowercase
  String normalize(String input) {
    return input
        .toLowerCase()
        .replaceAll(RegExp(r'\s+'), '')
        .replaceAll('(', '')
        .replaceAll(')', '');
  }

  final normalized1 = normalize(text1);
  final normalized2 = normalize(text2);

  if (normalized1.contains(normalized2) || normalized2.contains(normalized1)) {
    return text1; // Return the original first value
  }

  return ''; // No match
}


final Map<String, String> drugLocaleKeyMap = {
  'ah-7921': DrugsLocaleKeys.aH7921Drug,
  'gbl': DrugsLocaleKeys.gbl,
  'ghb': DrugsLocaleKeys.ghb,
  'hgh': DrugsLocaleKeys.hgh,
  'mdpv': DrugsLocaleKeys.mdpv,
  'msj': DrugsLocaleKeys.msj,
  'o-pce': DrugsLocaleKeys.oPce,
  'sustanon': DrugsLocaleKeys.sustanon,
  'testosterone': DrugsLocaleKeys.testosterone,
  'acamprosate': DrugsLocaleKeys.acamprosate,
  'adderall': DrugsLocaleKeys.adderall,
  'alpha-pvp': DrugsLocaleKeys.alphaPvp,
  'ambien': DrugsLocaleKeys.ambien,
  'amitriptyline': DrugsLocaleKeys.amitriptyline,
  'amphetamines': DrugsLocaleKeys.amphetamines,
  'buprenorphine': DrugsLocaleKeys.buprenorphine,
  'butane': DrugsLocaleKeys.butane,
  'clonazepam': DrugsLocaleKeys.clonazepam,
  'co-codamol': DrugsLocaleKeys.coCodamol,
  'cocaine': DrugsLocaleKeys.cocaine,
  'codeine': DrugsLocaleKeys.codeine,
  'crack': DrugsLocaleKeys.crack,
  'demerol': DrugsLocaleKeys.demerol,
  'dexedrine': DrugsLocaleKeys.dexedrine,
  'diazepam': DrugsLocaleKeys.diazepam,
  'disulfiram': DrugsLocaleKeys.disulfiram,
  'ecstasy': DrugsLocaleKeys.ecstasy,
  'ephedrine': DrugsLocaleKeys.ephedrine,
  'etizolam': DrugsLocaleKeys.etizolam,
  'fentanyl': DrugsLocaleKeys.fentanyl,
  'gabapentin': DrugsLocaleKeys.gabapentin,
  'heroin': DrugsLocaleKeys.heroin,
  'hydromorphone': DrugsLocaleKeys.hydromorphone,
  'ketamine': DrugsLocaleKeys.ketamine,
  'khat': DrugsLocaleKeys.khat,
  'lorezepam': DrugsLocaleKeys.lorezepam,
  'lunesta': DrugsLocaleKeys.lunesta,
  'marijuana': DrugsLocaleKeys.marijuana,
  'mephedrone': DrugsLocaleKeys.mephedrone,
  'mephobarbital': DrugsLocaleKeys.mephobarbital,
  'methadone': DrugsLocaleKeys.methadone,
  'methamphetamine': DrugsLocaleKeys.methamphetamine,
  'methoxatamine': DrugsLocaleKeys.methoxatamine,
  'modafinil': DrugsLocaleKeys.modafinil,
  'morphine': DrugsLocaleKeys.morphine,
  'nalmefene': DrugsLocaleKeys.nalmefene,
  'naltrexone': DrugsLocaleKeys.naltrexone,
  'nitrazepam': DrugsLocaleKeys.nitrazepam,
  'nitrous-oxide': DrugsLocaleKeys.nitrousOxide,
  'oxandrolone': DrugsLocaleKeys.oxandrolone,
  'oxycontin': DrugsLocaleKeys.oxycontin,
  'oxymorphone': DrugsLocaleKeys.oxymorphone,
  'pcp': DrugsLocaleKeys.pcp,
  'phenobarbital': DrugsLocaleKeys.phenobarbital,
  'pholcodeine': DrugsLocaleKeys.pholcodeine,
  'pregabalin': DrugsLocaleKeys.pregabalin,
  'ritalin': DrugsLocaleKeys.ritalin,
  'sonata': DrugsLocaleKeys.sonata,
  'suboxone': DrugsLocaleKeys.suboxone,
  'synthetic-cannabis': DrugsLocaleKeys.syntheticCannabis,
  'temazepam': DrugsLocaleKeys.temazepam,
  'tobacco': DrugsLocaleKeys.tobacco,
  'tramadol': DrugsLocaleKeys.tramadol,
  'trenbolone': DrugsLocaleKeys.trenbolone,
  'triazolam': DrugsLocaleKeys.triazolam,
  'vicodin': DrugsLocaleKeys.vicodin,
  'xanax': DrugsLocaleKeys.xanax,
  'zopiclon': DrugsLocaleKeys.zopiclon,
  'alfentanil': DrugsLocaleKeys.alfentanil,
  'alpha-d2pv': DrugsLocaleKeys.alphaD2pv,
  'bromadoline': DrugsLocaleKeys.bromadoline,
  'bromazolam': DrugsLocaleKeys.bromazolam,
  'buvidal': DrugsLocaleKeys.buvidal,
  'caffeine': DrugsLocaleKeys.caffeine,
  'carfentanil': DrugsLocaleKeys.carfentanil,
  'desomorphine': DrugsLocaleKeys.desomorphine,
  'diclazepam': DrugsLocaleKeys.diclazepam,
  'flephedrone': DrugsLocaleKeys.flephedrone,
  'flubromazolam': DrugsLocaleKeys.flubromazolam,
  'hysingla': DrugsLocaleKeys.hysingla,
  'ketazolam': DrugsLocaleKeys.ketazolam,
  'kratom': DrugsLocaleKeys.kratom,
  'meprobamate': DrugsLocaleKeys.meprobamate,
  'metandienone': DrugsLocaleKeys.metandienone,
  'methylone': DrugsLocaleKeys.methylone,
  'midazolam': DrugsLocaleKeys.midazolam,
  'pentazocine': DrugsLocaleKeys.pentazocine,
  'phenibut': DrugsLocaleKeys.phenibut,
  'sufentanil': DrugsLocaleKeys.sufentanil,
  'tapentadol': DrugsLocaleKeys.tapentadol,
  'topiramate': DrugsLocaleKeys.topiramate,
  'u-47700': DrugsLocaleKeys.u47700,
  'vape-marijuana': DrugsLocaleKeys.vapeMarijuana,
  'vape-nicotine': DrugsLocaleKeys.vapeNicotine,
  'vyvanse': DrugsLocaleKeys.vyvanse,
  'xelstrym': DrugsLocaleKeys.xelstrym,
  'xylazine': DrugsLocaleKeys.xylazine,
  'zohydro': DrugsLocaleKeys.zohydro,
};


  List<DrugDetail> drugList = [];
  List<String> ethnicList = (DynamicAssetLoader.getNestedValue(
        AssessmentLocaleKeys.recoveryProgramQuestionsEthnicityItems,
        navigatorKey.currentContext!,
      ) as List<dynamic>?)
          ?.map(
            (e) => e['label'] as String, // Extract the 'label' value as String
          )
          .toList() ??
      [];

  List<String> genderList = (DynamicAssetLoader.getNestedValue(
        AssessmentLocaleKeys.recoveryProgramQuestionsGenderItems,
        navigatorKey.currentContext!,
      ) as List<dynamic>?)
          ?.map(
            (e) => e['label'] as String, // Extract the 'label' value as String
          )
          .toList() ??
      []; // Provide an empty list as a fallback if null

  List<String> specialAddictionList = (DynamicAssetLoader.getNestedValue(
    AssessmentLocaleKeys.recoveryProgramQuestionsSpecialAddictionItems,
    navigatorKey.currentContext!,
  ) as List)
      .cast<String>();

  void onButtonTapped(int index, ValueNotifier<int> value) {
    value.value = index;
  }

  bool validateAndShowError({bool shouldShowError = false}) {
    if ((ageCheckValue.value || int.parse(ageController.text) <= 15) && !confirmAgeValue.value) {
      if (shouldShowError) {
        // CustomSnackbar.showErrorSnackBar(
        //   message: AssessmentLocaleKeys.errorsConsentRequiredMessage.tr(),
        // );
      }
    } else if (!isGenderCheckValue.value && state.selectedGenderValue == null) {
      if (shouldShowError) {
        // CustomSnackbar.showErrorSnackBar(message: AssessmentLocaleKeys.errorsRequiredMessage.tr());
      }
    } else if (!isEthnicCheckValue.value && state.selectedEthnicValue == null) {
      if (shouldShowError) {
        // CustomSnackbar.showErrorSnackBar(message: AssessmentLocaleKeys.errorsRequiredMessage.tr());
      }
    } else if (selectedBreakingFreeIndex.value == -1) {
      if (shouldShowError) {
        // CustomSnackbar.showErrorSnackBar(message: AssessmentLocaleKeys.errorsRequiredMessage.tr());
      }
    } else if (selectedBreakingFreeIndex.value == 2 && selectedrecoveryIndex.value == -1) {
      if (shouldShowError) {
        // CustomSnackbar.showErrorSnackBar(message: AssessmentLocaleKeys.errorsRequiredMessage.tr());
      }
      recoveryButtonDisable(false);
    } else {
      recoveryButtonDisable(true);
    }
    return isRecoveryButtonDisable.value;
  }

  void recoveryButtonDisable(bool value) {
    isRecoveryButtonDisable.value = value;
  }

  void gotoNextWidget({int increment = 1}) {
    final currentIndex = state.index;
    int newIndex = currentIndex + increment;
    print('????? gotoNextWidget: currentIndex=$currentIndex, newIndex=$newIndex');
    final userModel = Injector.instance<AppDB>().userModel;
    final addictionCase = userModel?.user.assessment?.rp?.addictionCase;
    // For addiction case 0, skip drugs pages (13, 14, 15)
    if (addictionCase == 0) {
      if (newIndex == 13) newIndex = 16; // skip to Thank You
      if (newIndex == 14) newIndex = 16;
      if (newIndex == 15) newIndex = 16;
    }
    if (newIndex <= 19) {
      emit(
        state.copyWith(
          index: newIndex,
          lastIndex: currentIndex,
        ),
      );
    }
  }

  void gotoPreviousWidget({int decrement = 1}) {
    final currentIndex = state.index;
    final newIndex = currentIndex - decrement;

    if (newIndex > 0) {
      emit(
        state.copyWith(
          index: newIndex,
          lastIndex: currentIndex,
        ),
      );
      // if (newIndex > 1) {
      //   updateAudioUrl(newIndex); // Update audio URL
      // } else {
      //   infoAudioUrl.value = null;
      // }
    }
  }

  void updateAudioUrl(int index) {
    infoAudioUrl.value = getAudiourl(index);
  }

  // void increaseAgeValue() {
  //   if (state.currentAgeValue < 99) {
  //     final newValue = state.currentAgeValue + 1;
  //     ageController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentAgeValue: newValue,
  //       ),
  //     );
  //   }
  //   validateAndShowError();
  // }

  void increaseAgeValue() {
    final textValue = int.tryParse(ageController.text) ?? state.currentAgeValue;
    if (textValue < 99) {
      final newValue = textValue + 1;
      ageController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentAgeValue: newValue,
      //   ),
      // );
    }
    validateAndShowError();
  }

  void decreaseAgeValue() {
    final textValue = int.tryParse(ageController.text) ?? state.currentAgeValue;
    if (textValue > 14) {
      final newValue = textValue - 1;
      ageController.text = newValue.toString();
      // emit(
      //   state.copyWith(
      //     currentAgeValue: newValue,
      //   ),
      // );
    }
    validateAndShowError();
  }

  // void decreaseAgeValue() {
  //   if (state.currentAgeValue > 14) {
  //     final newValue = state.currentAgeValue - 1;
  //     ageController.text = newValue.toString();
  //     emit(
  //       state.copyWith(
  //         currentAgeValue: newValue,
  //       ),
  //     );
  //   }
  //   validateAndShowError();
  // }

  void toggleCheckbox(ValueNotifier<bool> value) {
    value.value = !value.value; // Update the value inside the ValueNotifier
  }

  void updateSelectedGender(String? newGender) {
    emit(state.copyWith(selectedGenderValue: newGender ?? ''));
    validateAndShowError();
  }

  void updateIndex(int? newGender) {
    emit(state.copyWith(index: newGender ?? -1));
  }

  void userRecoveryData() {
    if (Injector.instance<AppDB>().userModel?.user != null) {
      final userModel = Injector.instance<AppDB>().userModel?.user;
      nameController.text = userModel?.nameOnCertificate ?? '';
      selectedBreakingFreeIndex.value = userModel?.assessment?.rp?.addictionCase ?? -1;
      selectedrecoveryIndex.value =
          userModel?.assessment?.rp?.addictionCase == 2 ? userModel?.assessment?.rp?.specialAddiction ?? -1 : -1;
      // Set ageController text
      if (userModel?.assessment?.rp?.age != null && userModel?.assessment?.rp?.age is int) {
        ageController.text = '${userModel?.assessment?.rp?.age}';
      }
      if (userModel?.assessment?.rp?.gender != null && userModel?.assessment?.rp?.gender is int) {
        final genderIndex = userModel!.assessment!.rp!.gender as int;
        if (genderIndex >= 0 && genderIndex < genderList.length) {
          emit(state.copyWith(selectedGenderValue: genderList[genderIndex]));
        } else {
          emit(state.copyWith(selectedGenderValue: null));
        }
      } else {
        emit(state.copyWith(selectedGenderValue: null));
      }

      if (userModel?.assessment?.rp?.ethnicity != null && userModel?.assessment?.rp?.ethnicity is int) {
        final ethnicityIndex = userModel!.assessment!.rp!.ethnicity as int;
        if (ethnicityIndex >= 0 && ethnicityIndex < ethnicList.length) {
          emit(state.copyWith(selectedEthnicValue: ethnicList[ethnicityIndex]));
        } else {
          emit(state.copyWith(selectedEthnicValue: null)); // Set to null if index is out of bounds
        }
      } else {
        emit(state.copyWith(selectedEthnicValue: null)); // Default to null if gender is null or not an int
      }
      // Set isGenderCheckValue
      isGenderCheckValue.value = userModel?.assessment?.rp?.gender == false;

      // Set isEthnicCheckValue
      isEthnicCheckValue.value = userModel?.assessment?.rp?.ethnicity == false;

      // Set confirmAgeValue
      confirmAgeValue.value = userModel?.assessment?.rp?.age == false;
      ageCheckValue.value = userModel?.assessment?.rp?.age == false;
    }
  }

  void initialData() {
    print('????? initialData called, addictionCase=${Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase}');
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        final userModel = Injector.instance<AppDB>().userModel;
        final completedAssessments = userModel?.user.assessment?.completed ?? [];
        final addictionCase = userModel?.user.assessment?.rp?.addictionCase;
        'completedAssessments $completedAssessments'.logD;
        List<Map<String, dynamic>> conditions = [];
        if (completedAssessments.isEmpty) {
          updateIndex(1); // Show welcome video for fresh user
          print('????? Fresh user: showing welcome video (index 1)');
          return;
        } else if (addictionCase == 0) {
          print('????? ENTERED addictionCase==0 block');
          try {
            print('????? TOP OF TRY');
            print('????? completedAssessments=$completedAssessments');
            List<String> flow = [
              'recovery-program', // 2
              'life', // 3
              'difficult-situations', // 4
              'negative-thoughts', // 5
              'physical-sensations', // 6
              'unhelpful-behaviours', // 7
              'lifestyle', // 8
              'emotional-impact', // 9
              'drinking', // 10
              'drinking-feeling', // 11
              'drinking-goal', // 12
              'thank-you', // 16
              'assessment-video', // 17
              'bridging-video', // 18
            ];
            print('????? after flow');
            const mapping = [2,3,4,5,6,7,8,9,10,11,12,16,17,18];
            print('????? after mapping');
            int firstIncomplete = flow.indexWhere((step) => !completedAssessments.contains(step));
            print('????? after firstIncomplete: $firstIncomplete');
            int widgetIndex = (firstIncomplete == -1) ? 17 : mapping[firstIncomplete];
            print('????? after widgetIndex: $widgetIndex');
            print('????? addictionCase=0, completed=$completedAssessments, firstIncomplete=$firstIncomplete, widgetIndex=$widgetIndex');
            updateIndex(widgetIndex);
          } catch (e, stack) {
            print('????? EXCEPTION: $e');
            print(stack);
          }
          print('????? END OF addictionCase==0 block');
          return;
        } else if (addictionCase == 1) {
          // Drugs only: skip drinking pages
          conditions = [
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
                'assessment-video',
                'bridging-video',
                'notifications',
              ],
              'index': 19,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
                'assessment-video',
              ],
              'index': 18,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
              ],
              'index': 17,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
              ],
              'index': 16,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
                'drugs-feeling',
              ],
              'index': 15,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drugs',
              ],
              'index': 14,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
              ],
              'index': 13,
            },
            // ... continue for earlier steps as needed ...
          ];
        } else {
          // Both: show all pages (current flow)
          conditions = [
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
                'assessment-video',
                'bridging-video',
                'notifications',
              ],
              'index': 19,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
                'assessment-video',
              ],
              'index': 18,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
                'thank-you',
              ],
              'index': 17,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
                'drugs-goal',
              ],
              'index': 16,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 15,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 14,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 13,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 12,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 11,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 10,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 9,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 8,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 7,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 6,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 5,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 4,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 3,
            },
            {
              'conditions': [
                'recovery-program',
                'life',
                'difficult-situations',
                'negative-thoughts',
                'physical-sensations',
                'unhelpful-behaviours',
                'lifestyle',
                'emotional-impact',
                'drinking',
                'drinking-feeling',
                'drinking-goal',
                'drugs',
                'drugs-feeling',
              ],
              'index': 2,
            },
          ];
        }

        // Determine the highest index that matches the conditions
        for (final condition in conditions) {
          if (condition['conditions'] != null &&
              (condition['conditions']! as List<String>).every(completedAssessments.contains)) {
            updateIndex(condition['index']! as int);
            return;
          }
        }

        // If no condition matches, find the first incomplete step
        // Use the static index mapping as in getWidget
        List<String> flow;
        '????? addiction case  = $addictionCase'.logV;
        if (addictionCase == 0) {
          print('????? ENTERED addictionCase==0 block');
          try {
            print('????? TOP OF TRY');
            print('????? completedAssessments=$completedAssessments');
            flow = [
              'recovery-program', // 2
              'life', // 3
              'difficult-situations', // 4
              'negative-thoughts', // 5
              'physical-sensations', // 6
              'unhelpful-behaviours', // 7
              'lifestyle', // 8
              'emotional-impact', // 9
              'drinking', // 10
              'drinking-feeling', // 11
              'drinking-goal', // 12
              'thank-you', // 16
              'assessment-video', // 17
              'bridging-video', // 18
            ];
            print('????? after flow');
            const mapping = [2,3,4,5,6,7,8,9,10,11,12,16,17,18];
            print('????? after mapping');
            int firstIncomplete = flow.indexWhere((step) => !completedAssessments.contains(step));
            print('????? after firstIncomplete: $firstIncomplete');
            int widgetIndex = (firstIncomplete == -1) ? 17 : mapping[firstIncomplete];
            print('????? after widgetIndex: $widgetIndex');
            print('????? addictionCase=0, completed=$completedAssessments, firstIncomplete=$firstIncomplete, widgetIndex=$widgetIndex');
            updateIndex(widgetIndex);
          } catch (e, stack) {
            print('????? EXCEPTION: $e');
            print(stack);
          }
          print('????? END OF addictionCase==0 block');
          return;
        } else if (addictionCase == 1) {
          flow = [
            'recovery-program', // 2
            'life', // 3
            'difficult-situations', // 4
            'negative-thoughts', // 5
            'physical-sensations', // 6
            'unhelpful-behaviours', // 7
            'lifestyle', // 8
            'emotional-impact', // 9
            'drugs', // 13
            'drugs-feeling', // 14
            'drugs-goal', // 15
            'thank-you', // 16
            'assessment-video', // 17
            'bridging-video', // 18
          ];
          const mapping = [2,3,4,5,6,7,8,9,13,14,15,16,17,18];
          int firstIncomplete = flow.indexWhere((step) => !completedAssessments.contains(step));
          int widgetIndex = (firstIncomplete == -1) ? 17 : mapping[firstIncomplete];
          updateIndex(widgetIndex);
          return;
        } else {
          flow = [
            'recovery-program', // 2
            'life', // 3
            'difficult-situations', // 4
            'negative-thoughts', // 5
            'physical-sensations', // 6
            'unhelpful-behaviours', // 7
            'lifestyle', // 8
            'emotional-impact', // 9
            'drinking', // 10
            'drinking-feeling', // 11
            'drinking-goal', // 12
            'drugs', // 13
            'drugs-feeling', // 14
            'drugs-goal', // 15
            'thank-you', // 16
            'assessment-video', // 17
            'bridging-video', // 18
          ];
          const mapping = [2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18];
          int firstIncomplete = flow.indexWhere((step) => !completedAssessments.contains(step));
          int widgetIndex = (firstIncomplete == -1) ? 17 : mapping[firstIncomplete];
          updateIndex(widgetIndex);
          return;
        }
      },
    );
    print('????? initialData END');
  }

  void updateSelectedEthnic(String? newValue) {
    emit(state.copyWith(selectedEthnicValue: newValue ?? ''));
    validateAndShowError();
  }

  void updateQualityLife(AssessmentState state, String newValue, int questionIndex) {
    final updatedList = List<String?>.from(state.aboutYourLifeQuestionList);
    if (questionIndex < updatedList.length) {
      updatedList[questionIndex] = newValue;
    } else {
      updatedList.add(newValue);
    }
    final newState = state.copyWith(aboutYourLifeQuestionList: updatedList);
    emit(newState);
  }

  String getAudiourl(int url) {
    log('url$url');
    switch (url) {
      case 2:
        return AssessmentLocaleKeys.recoveryProgramAudio.tr();
      case 3:
        return AssessmentLocaleKeys.lifeAudio.tr();
      case 4:
        return AssessmentLocaleKeys.difficultSituationsAudio.tr();
      case 5:
        return AssessmentLocaleKeys.negativeThoughtsAudio.tr();
      case 6:
        return AssessmentLocaleKeys.physicalSensationsAudio.tr();
      case 7:
        return AssessmentLocaleKeys.unhelpfulBehavioursAudio.tr();
      case 8:
        return AssessmentLocaleKeys.lifestyleAudio.tr();
      case 9:
        return AssessmentLocaleKeys.emotionalImpactAudio.tr();
      case 10:
        return AssessmentLocaleKeys.drinkingAudio.tr();
      case 11:
        return AssessmentLocaleKeys.drinkingFeelingAudio.tr();
      case 12:
        return AssessmentLocaleKeys.drinkingGoalAudioLight.tr();
      case 13:
        return AssessmentLocaleKeys.drugsAudio.tr();
      case 14:
        return AssessmentLocaleKeys.drugsFeelingAudio.tr();
      case 15:
        return AssessmentLocaleKeys.drugsGoalAbstinentAudio.tr();
      case 16:
        return AssessmentLocaleKeys.thankYouAudio.tr();

      default:
        return '';
    }
  }

  Future<void> putAssessmentRecoveryProgramAPI({
    required dynamic age,
    required dynamic gender,
    required dynamic ethnicity,
    required int addictionCase,
    required int? specialAddiction,
    required String? name,
    required BuildContext context,
  }) async {
    // try {
    // isLoading.value = true;
    emit(state.copyWith(isRecoveryProgramAPILoading: true));
    final response = await assessmentRepository.putAssessmentRecoveryProgram(
      age: age,
      name: name,
      gender: gender,
      ethnicity: ethnicity,
      addictionCase: addictionCase,
      specialAddiction: specialAddiction,
      context: context,
    );
    if (response != null && (response.success ?? false) == true) {
      Injector.instance<AppDB>().userModel?.user.assessment?.rp = response.assessment?.rp;
      Injector.instance<AppDB>().userModel?.user.assessment?.rp.logD;
      await authRepository.getUserData(context: context);
      context.read<AssessmentLifeCubit>().isManuallyPaused.value = false;
      context.read<AssessmentLifeCubit>().infoAudioUrl.value = AssessmentLocaleKeys.lifeAudio.tr();
      gotoNextWidget();
    }
    emit(state.copyWith(isRecoveryProgramAPILoading: false));
    // } catch (e) {
    //   emit(state.copyWith(isRecoveryProgramAPILoading: false));
    // } finally {
    //   emit(state.copyWith(isRecoveryProgramAPILoading: false));
    // }
  }

  Future<void> thankYouAPI({
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isThankYouAPILoading: true));
      final response = await assessmentRepository.thankYou(
        context: context,
      );
      'api response === ${response}'.logV;
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment;
        Injector.instance<AppDB>().userModel?.user.assessment?.rp.logD;
        await authRepository.getUserData(context: context);
        gotoNextWidget();
      }
      emit(state.copyWith(isThankYouAPILoading: false));
    } catch (e) {
      emit(state.copyWith(isThankYouAPILoading: false));
    } finally {
      emit(state.copyWith(isThankYouAPILoading: false));
    }
  }

  Future<void> assessmentVideoAPI({
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isAssessmentVideoAPILoading: true));
      final response = await assessmentRepository.assessmentVideo(
        context: context,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment;
        await authRepository.getUserData(context: context);
        gotoNextWidget();
      }
      emit(state.copyWith(isAssessmentVideoAPILoading: false));
    } catch (e) {
      emit(state.copyWith(isAssessmentVideoAPILoading: false));
    } finally {
      emit(state.copyWith(isAssessmentVideoAPILoading: false));
    }
  }

  Future<void> bridgingVideoAPI({
    required BuildContext context,
  }) async {
    try {
      emit(state.copyWith(isbridgingVideoAPILoading: true));
      final response = await assessmentRepository.bridgingVideo(
        context: context,
      );
      if (response != null && response.success == true) {
        Injector.instance<AppDB>().userModel?.user.assessment = response.assessment;
        await authRepository.getUserData(context: context);

        await AppNavigation.nextScreen(navigatorKey.currentContext!, BlocProvider(
      create: (_) => AssessmentNotificationCubit(),
      child: const AssessmentNotificationPage(),
    ),);
      } else if (response != null && response.success == false) {
        updateIndex(9);
      }
      emit(state.copyWith(isbridgingVideoAPILoading: false));
    } catch (e) {
      emit(state.copyWith(isbridgingVideoAPILoading: false));
    } finally {
      emit(state.copyWith(isbridgingVideoAPILoading: false));
    }
  }

  @override
  Future<void> close() {
    infoAudioUrl.dispose();
    isAudioPannelVisible.dispose();
    isVideoEnded.dispose();
    isAssessmentVideoEnded.dispose();
    isBridgingAssessmentVideoEnded.dispose();
    isRecoveryButtonClicked.dispose();
    isRecoveryButtonDisable.dispose();
    ageCheckValue.dispose();
    confirmAgeValue.dispose();
    isGenderCheckValue.dispose();
    isEthnicCheckValue.dispose();
    selectedBreakingFreeIndex.dispose();
    selectedrecoveryIndex.dispose();
    rateEmotionalImpactSliderVlaue.dispose();
    ageController.dispose();
    nameController.dispose();
    return super.close();
  }
}
