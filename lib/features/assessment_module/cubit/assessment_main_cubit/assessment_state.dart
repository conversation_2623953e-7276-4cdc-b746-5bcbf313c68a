import 'package:freezed_annotation/freezed_annotation.dart';

part 'assessment_state.freezed.dart';

@freezed
class AssessmentState with _$AssessmentState {
  factory AssessmentState({
    @Default(1) int index,
    @Default(1) int lastIndex,
    @Default(18) int currentAgeValue,
    @Default(0) int currentDrinkingUnit,
    @Default(0) int currentDrinkingfreeDays,
    @Default(null) String? selectedGenderValue,
    @Default(null) String? selectedEthnicValue,
    @Default('') String? drinkHelpMeCalculateTitleText,
    @Default('') String? selecteddrinkHelpMeCalculateSubTitleText,
    @Default('') String? selecteddrinkHelpMeSizeText,
    @Default('') String? selecteddrinkHelpMeCalculateTitleText,
    @Default('') String? selecteddrinkHelpMeCalculatePercentageText,
    @Default([]) List<String?> aboutYourLifeQuestionList,
    @Default(false) bool showBackArrownQuestion,
    @Default(false) bool isRecoveryProgramAPILoading,
    @Default(false) bool isThankYouAPILoading,
    @Default(false) bool isAssessmentVideoAPILoading,
    @Default(false) bool isbridgingVideoAPILoading,
  }) = _AssessmentState;
}
