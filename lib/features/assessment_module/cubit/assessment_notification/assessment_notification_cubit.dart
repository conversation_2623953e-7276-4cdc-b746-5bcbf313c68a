import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:permission_handler/permission_handler.dart';

part 'assessment_notification_cubit.freezed.dart';
part 'assessment_notification_state.dart';

class AssessmentNotificationCubit extends Cubit<AssessmentNotificationState>{
  AssessmentNotificationCubit() : super(AssessmentNotificationState());

  ValueNotifier<bool> isGrantedLocation = ValueNotifier(false);
  ValueNotifier<bool> isGrantedNotification = ValueNotifier(false);
  ValueNotifier<bool> isGrantedLocationLabel = ValueNotifier(false);
  ValueNotifier<bool> isGrantedNotificationLabel = ValueNotifier(false);
  ValueNotifier<bool> isButtomClicked = ValueNotifier(false);

  ValueNotifier<bool> isLoading = ValueNotifier(false);

   Future<void> handlePermissions() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 30) {
        // Android 11+ (API 30+) requires manageExternalStorage permission
        final status = await Permission.manageExternalStorage.request();

        if (status.isGranted) {
          log('Permission granted. Proceeding with file operations.');
        } else if (status.isDenied) {
          log('Permission denied. Cannot proceed.');
        } else if (status.isPermanentlyDenied) {
          log('Permission permanently denied. Redirecting to app settings.');
          await openAppSettings();
        }
      } else {
        // For Android 10 and below, request storage permissions
        final status = await Permission.storage.request();

        if (status.isGranted) {
          log('Storage permission granted.');
        } else if (status.isDenied) {
          log('Storage permission denied.');
        } else if (status.isPermanentlyDenied) {
          log('Storage permission permanently denied. Redirecting to app settings.');
          await openAppSettings();
        }
      }
    } else {
      log('This is not an Android device.');
    }
  }
}
