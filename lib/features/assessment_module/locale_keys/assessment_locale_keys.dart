class AssessmentLocaleKeys {
  static const welcomeVideoTitle = 'assessment.welcome-video.title';
  static const welcomeVideoSrc = 'assessment.welcome-video.video.src';
  static const welcomeVideoPoster = 'assessment.welcome-video.video.poster';

  //recovery program
  static const recoveryProgramTitle = 'assessment.recovery-program.title';
  static const recoveryProgramAudio = 'assessment.recovery-program.audio';

  static const recoveryProgramQuestionsNameLabel = 'assessment.recovery-program.questions.name.label';
  static const recoveryProgramQuestionsNameHint = 'assessment.recovery-program.questions.name.hint';

  static const recoveryProgramQuestionsAgeLabel = 'assessment.recovery-program.questions.age.label';
  static const recoveryProgramQuestionsAgePlusLabel = 'assessment.recovery-program.questions.age.plusLabel';
  static const recoveryProgramQuestionsAgeMinusLabel = 'assessment.recovery-program.questions.age.minusLabel';
  static const recoveryProgramQuestionsAgeWithheld = 'assessment.recovery-program.questions.age.withheld';
  static const recoveryProgramQuestionsAgeConsent = 'assessment.recovery-program.questions.age.consent';

  static const recoveryProgramQuestionsGenderLabel = 'assessment.recovery-program.questions.gender.label';
  static const recoveryProgramQuestionsGenderItems = 'assessment.recovery-program.questions.gender.items';
  static const recoveryProgramQuestionsGenderWithheld = 'assessment.recovery-program.questions.gender.withheld';

  static const recoveryProgramQuestionsEthnicityLabel = 'assessment.recovery-program.questions.ethnicity.label';
  static const recoveryProgramQuestionsEthnicityItems = 'assessment.recovery-program.questions.ethnicity.items';
  static const recoveryProgramQuestionsEthnicityWithheld = 'assessment.recovery-program.questions.ethnicity.withheld';

  static const recoveryProgramQuestionsAddictionCaseLabel = 'assessment.recovery-program.questions.addictionCase.label';
  static const recoveryProgramQuestionsAddictionCaseItems = 'assessment.recovery-program.questions.addictionCase.items';

  static const recoveryProgramQuestionsSpecialAddictionLabel =
      'assessment.recovery-program.questions.specialAddiction.label';
  static const recoveryProgramQuestionsSpecialAddictionItems =
      'assessment.recovery-program.questions.specialAddiction.items';

  //Lifes
  static const lifeTitle = 'assessment.life.title';
  static const lifeAudio = 'assessment.life.audio';

  static const lifeQuestionsQualityLabel = 'assessment.life.questions.quality.label';
  static const lifeQuestionsQualityItems = 'assessment.life.questions.quality.items';

  static const lifeQuestionsHealthLabel = 'assessment.life.questions.health.label';
  static const lifeQuestionsHealthItems = 'assessment.life.questions.health.items';

  static const lifeQuestionsActivitiesLabel = 'assessment.life.questions.activities.label';
  static const lifeQuestionsActivitiesItems = 'assessment.life.questions.activities.items';

  static const lifeQuestionsRelationshipsLabel = 'assessment.life.questions.relationships.label';
  static const lifeQuestionsRelationshipsItems = 'assessment.life.questions.relationships.items';

  static const lifeQuestionsWorkLabel = 'assessment.life.questions.work.label';
  static const lifeQuestionsWorkItems = 'assessment.life.questions.work.items';

  static const lifeQuestionsDifficultiesLabel = 'assessment.life.questions.difficulties.label';
  static const lifeQuestionsDifficultiesSliderLabels = 'assessment.life.questions.difficulties.sliderLabels';
  static const lifeQuestionsDifficultiesInfoText = 'assessment.life.questions.difficulties.info.text';
  static const lifeQuestionsDifficultiesInfoAudio = 'assessment.life.questions.difficulties.info.audio';

  static const lifeQuestionsRateLabel = 'assessment.life.questions.rate.label';
  static const lifeQuestionsRateSliderLabels = 'assessment.life.questions.rate.sliderLabels';
  static const lifeQuestionsRateInfoText = 'assessment.life.questions.rate.info.text';
  static const lifeQuestionsRateInfoAudio = 'assessment.life.questions.rate.info.audio';

  //Difficult Situations
  static const difficultSituationsTitle = 'assessment.difficult-situations.title';
  static const difficultSituationsSubtitle = 'assessment.difficult-situations.subtitle';
  static const difficultSituationsAudio = 'assessment.difficult-situations.audio';

  static const difficultSituationsQuestionsConflictLabel = 'assessment.difficult-situations.questions.conflict.label';
  static const difficultSituationsQuestionsConflictItems = 'assessment.difficult-situations.questions.conflict.items';

  static const difficultSituationsQuestionsWorkLabel = 'assessment.difficult-situations.questions.work.label';
  static const difficultSituationsQuestionsWorkItems0 = 'assessment.difficult-situations.questions.work.items.0';
  static const difficultSituationsQuestionsWorkItems1 = 'assessment.difficult-situations.questions.work.items.1';

  static const difficultSituationsQuestionsMoneyLabel = 'assessment.difficult-situations.questions.money.label';
  static const difficultSituationsQuestionsMoneyItems0 = 'assessment.difficult-situations.questions.money.items.0';
  static const difficultSituationsQuestionsMoneyItems1 = 'assessment.difficult-situations.questions.money.items.1';

  static const difficultSituationsQuestionsRisksLabel = 'assessment.difficult-situations.questions.risks.label';
  static const difficultSituationsQuestionsRisksItems0 = 'assessment.difficult-situations.questions.risks.items.0';
  static const difficultSituationsQuestionsRisksItems1 = 'assessment.difficult-situations.questions.risks.items.1';

  static const difficultSituationsQuestionsPressureLabel = 'assessment.difficult-situations.questions.pressure.label';
  static const difficultSituationsQuestionsPressureItems0 =
      'assessment.difficult-situations.questions.pressure.items.0';
  static const difficultSituationsQuestionsPressureItems1 =
      'assessment.difficult-situations.questions.pressure.items.1';

  static const difficultSituationsQuestionsRateLabel = 'assessment.difficult-situations.questions.rate.label';
  static const difficultSituationsQuestionsRateSliderLabels =
      'assessment.difficult-situations.questions.rate.sliderLabels';

  static const difficultSituationsQuestionsRateInfoText = 'assessment.difficult-situations.questions.rate.info.text';
  static const difficultSituationsQuestionsRateInfoAudio = 'assessment.difficult-situations.questions.rate.info.audio';

  // Negative Thoughts
  static const negativeThoughtsTitle = 'assessment.negative-thoughts.title';
  static const negativeThoughtsAudio = 'assessment.negative-thoughts.audio';
  static const negativeThoughtsSubtitle = 'assessment.negative-thoughts.subtitle';

  static const negativeThoughtsQuestionsGoodLabel = 'assessment.negative-thoughts.questions.good.label';
  static const negativeThoughtsQuestionsGoodItems0 = 'assessment.negative-thoughts.questions.good.items.0';
  static const negativeThoughtsQuestionsGoodItems1 = 'assessment.negative-thoughts.questions.good.items.1';

  static const negativeThoughtsQuestionsControlLabel = 'assessment.negative-thoughts.questions.control.label';
  static const negativeThoughtsQuestionsControlItems0 = 'assessment.negative-thoughts.questions.control.items.0';
  static const negativeThoughtsQuestionsControlItems1 = 'assessment.negative-thoughts.questions.control.items.1';

  static const negativeThoughtsQuestionsHealthLabel = 'assessment.negative-thoughts.questions.health.label';
  static const negativeThoughtsQuestionsHealthItems0 = 'assessment.negative-thoughts.questions.health.items.0';
  static const negativeThoughtsQuestionsHealthItems1 = 'assessment.negative-thoughts.questions.health.items.1';

  static const negativeThoughtsQuestionsCopeLabel = 'assessment.negative-thoughts.questions.cope.label';
  static const negativeThoughtsQuestionsCopeItems0 = 'assessment.negative-thoughts.questions.cope.items.0';
  static const negativeThoughtsQuestionsCopeItems1 = 'assessment.negative-thoughts.questions.cope.items.1';

  static const negativeThoughtsQuestionsTrustLabel = 'assessment.negative-thoughts.questions.trust.label';
  static const negativeThoughtsQuestionsTrustItems0 = 'assessment.negative-thoughts.questions.trust.items.0';
  static const negativeThoughtsQuestionsTrustItems1 = 'assessment.negative-thoughts.questions.trust.items.1';

  static const negativeThoughtsQuestionsRateLabel = 'assessment.negative-thoughts.questions.rate.label';
  static const negativeThoughtsQuestionsRateSliderLabels = 'assessment.negative-thoughts.questions.rate.sliderLabels';

  static const negativeThoughtsQuestionsRateInfoText = 'assessment.negative-thoughts.questions.rate.info.text';
  static const negativeThoughtsQuestionsRateInfoAudio = 'assessment.negative-thoughts.questions.rate.info.audio';

  // Physical Sensations
  static const physicalSensationsTitle = 'assessment.physical-sensations.title';
  static const physicalSensationsAudio = 'assessment.physical-sensations.audio';
  static const physicalSensationsSubtitle = 'assessment.physical-sensations.subtitle';

  static const physicalSensationsQuestionsCravingLabel = 'assessment.physical-sensations.questions.craving.label';
  static const physicalSensationsQuestionsCravingItems0 = 'assessment.physical-sensations.questions.craving.items.0';
  static const physicalSensationsQuestionsCravingItems1 = 'assessment.physical-sensations.questions.craving.items.1';

  static const physicalSensationsQuestionsShakesLabel = 'assessment.physical-sensations.questions.shakes.label';
  static const physicalSensationsQuestionsShakesItems0 = 'assessment.physical-sensations.questions.shakes.items.0';
  static const physicalSensationsQuestionsShakesItems1 = 'assessment.physical-sensations.questions.shakes.items.1';

  static const physicalSensationsQuestionsCrampsLabel = 'assessment.physical-sensations.questions.cramps.label';
  static const physicalSensationsQuestionsCrampsItems0 = 'assessment.physical-sensations.questions.cramps.items.0';
  static const physicalSensationsQuestionsCrampsItems1 = 'assessment.physical-sensations.questions.cramps.items.1';

  static const physicalSensationsQuestionsNauseaLabel = 'assessment.physical-sensations.questions.nausea.label';
  static const physicalSensationsQuestionsNauseaItems0 = 'assessment.physical-sensations.questions.nausea.items.0';
  static const physicalSensationsQuestionsNauseaItems1 = 'assessment.physical-sensations.questions.nausea.items.1';

  static const physicalSensationsQuestionsTirednessLabel = 'assessment.physical-sensations.questions.tiredness.label';
  static const physicalSensationsQuestionsTirednessItems0 =
      'assessment.physical-sensations.questions.tiredness.items.0';
  static const physicalSensationsQuestionsTirednessItems1 =
      'assessment.physical-sensations.questions.tiredness.items.1';

  static const physicalSensationsQuestionsRateLabel = 'assessment.physical-sensations.questions.rate.label';
  static const physicalSensationsQuestionsRateSliderLabels =
      'assessment.physical-sensations.questions.rate.sliderLabels';

  static const physicalSensationsQuestionsRateInfoText = 'assessment.physical-sensations.questions.rate.info.text';
  static const physicalSensationsQuestionsRateInfoAudio = 'assessment.physical-sensations.questions.rate.info.audio';

  // Unhelpful Behaviours
  static const unhelpfulBehavioursTitle = 'assessment.unhelpful-behaviours.title';
  static const unhelpfulBehavioursAudio = 'assessment.unhelpful-behaviours.audio';
  static const unhelpfulBehavioursSubtitle = 'assessment.unhelpful-behaviours.subtitle';

  static const unhelpfulBehavioursQuestionsAggressiveLabel =
      'assessment.unhelpful-behaviours.questions.aggressive.label';
  static const unhelpfulBehavioursQuestionsAggressiveItems0 =
      'assessment.unhelpful-behaviours.questions.aggressive.items.0';
  static const unhelpfulBehavioursQuestionsAggressiveItems1 =
      'assessment.unhelpful-behaviours.questions.aggressive.items.1';

  static const unhelpfulBehavioursQuestionsAvoidLabel = 'assessment.unhelpful-behaviours.questions.avoid.label';
  static const unhelpfulBehavioursQuestionsAvoidItems0 = 'assessment.unhelpful-behaviours.questions.avoid.items.0';
  static const unhelpfulBehavioursQuestionsAvoidItems1 = 'assessment.unhelpful-behaviours.questions.avoid.items.1';

  static const unhelpfulBehavioursQuestionsActiveLabel = 'assessment.unhelpful-behaviours.questions.active.label';
  static const unhelpfulBehavioursQuestionsActiveItems0 = 'assessment.unhelpful-behaviours.questions.active.items.0';
  static const unhelpfulBehavioursQuestionsActiveItems1 = 'assessment.unhelpful-behaviours.questions.active.items.1';

  static const unhelpfulBehavioursQuestionsCareLabel = 'assessment.unhelpful-behaviours.questions.care.label';
  static const unhelpfulBehavioursQuestionsCareItems0 = 'assessment.unhelpful-behaviours.questions.care.items.0';
  static const unhelpfulBehavioursQuestionsCareItems1 = 'assessment.unhelpful-behaviours.questions.care.items.1';

  static const unhelpfulBehavioursQuestionsPoliceLabel = 'assessment.unhelpful-behaviours.questions.police.label';
  static const unhelpfulBehavioursQuestionsPoliceItems0 = 'assessment.unhelpful-behaviours.questions.police.items.0';
  static const unhelpfulBehavioursQuestionsPoliceItems1 = 'assessment.unhelpful-behaviours.questions.police.items.1';

  static const unhelpfulBehavioursQuestionsRateLabel = 'assessment.unhelpful-behaviours.questions.rate.label';
  static const unhelpfulBehavioursQuestionsRateSliderLabels =
      'assessment.unhelpful-behaviours.questions.rate.sliderLabels';

  static const unhelpfulBehavioursQuestionsRateInfoText = 'assessment.unhelpful-behaviours.questions.rate.info.text';
  static const unhelpfulBehavioursQuestionsRateInfoAudio = 'assessment.unhelpful-behaviours.questions.rate.info.audio';

  // Lifestyle
  static const lifestyleTitle = 'assessment.lifestyle.title';
  static const lifestyleSubtitle = 'assessment.lifestyle.subtitle';
  static const lifestyleAudio = 'assessment.lifestyle.audio';

  static const lifestyleQuestionsHealthLabel = 'assessment.lifestyle.questions.health.label';
  static const lifestyleQuestionsHealthItems0 = 'assessment.lifestyle.questions.health.items.0';
  static const lifestyleQuestionsHealthItems1 = 'assessment.lifestyle.questions.health.items.1';

  static const lifestyleQuestionsWorkLabel = 'assessment.lifestyle.questions.work.label';
  static const lifestyleQuestionsWorkItems0 = 'assessment.lifestyle.questions.work.items.0';
  static const lifestyleQuestionsWorkItems1 = 'assessment.lifestyle.questions.work.items.1';

  static const lifestyleQuestionsLeisureLabel = 'assessment.lifestyle.questions.leisure.label';
  static const lifestyleQuestionsLeisureItems0 = 'assessment.lifestyle.questions.leisure.items.0';
  static const lifestyleQuestionsLeisureItems1 = 'assessment.lifestyle.questions.leisure.items.1';

  static const lifestyleQuestionsRelationshipsLabel = 'assessment.lifestyle.questions.relationships.label';
  static const lifestyleQuestionsRelationshipsItems0 = 'assessment.lifestyle.questions.relationships.items.0';
  static const lifestyleQuestionsRelationshipsItems1 = 'assessment.lifestyle.questions.relationships.items.1';

  static const lifestyleQuestionsHousingLabel = 'assessment.lifestyle.questions.housing.label';
  static const lifestyleQuestionsHousingItems0 = 'assessment.lifestyle.questions.housing.items.0';
  static const lifestyleQuestionsHousingItems1 = 'assessment.lifestyle.questions.housing.items.1';

  static const lifestyleQuestionsRateLabel = 'assessment.lifestyle.questions.rate.label';
  static const lifestyleQuestionsRateSliderLabels = 'assessment.lifestyle.questions.rate.sliderLabels';
  static const lifestyleQuestionsRateInfoText = 'assessment.lifestyle.questions.rate.info.text';
  static const lifestyleQuestionsRateInfoAudio = 'assessment.lifestyle.questions.rate.info.audio';

  // Emotional Impact
  static const emotionalImpactTitle = 'assessment.emotional-impact.title';
  static const emotionalImpactAudio = 'assessment.emotional-impact.audio';
  static const emotionalImpactSubtitle = 'assessment.emotional-impact.subtitle';

  static const emotionalImpactQuestionsNervousLabel = 'assessment.emotional-impact.questions.nervous.label';
  static const emotionalImpactQuestionsNervousItems = 'assessment.emotional-impact.questions.nervous.items';

  static const emotionalImpactQuestionsWorryLabel = 'assessment.emotional-impact.questions.worry.label';
  static const emotionalImpactQuestionsWorryItems = 'assessment.emotional-impact.questions.worry.items';

  static const emotionalImpactQuestionsDownLabel = 'assessment.emotional-impact.questions.down.label';
  static const emotionalImpactQuestionsDownItems = 'assessment.emotional-impact.questions.down.items';

  static const emotionalImpactQuestionsBadLabel = 'assessment.emotional-impact.questions.bad.label';
  static const emotionalImpactQuestionsBadItems = 'assessment.emotional-impact.questions.bad.items';

  static const emotionalImpactQuestionsInterestLabel = 'assessment.emotional-impact.questions.interest.label';
  static const emotionalImpactQuestionsInterestItems = 'assessment.emotional-impact.questions.interest.items';

  static const emotionalImpactQuestionsRateLabel = 'assessment.emotional-impact.questions.rate.label';
  static const emotionalImpactQuestionsRateSliderLabels = 'assessment.emotional-impact.questions.rate.sliderLabels';
  static const emotionalImpactQuestionsRateInfoText = 'assessment.emotional-impact.questions.rate.info.text';
  static const emotionalImpactQuestionsRateInfoAudio = 'assessment.emotional-impact.questions.rate.info.audio';

  // Drinking
  static const drinkingTitle = 'assessment.drinking.title';
  static const drinkingAudio = 'assessment.drinking.audio';
  static const drinkingSubtitle0 = 'assessment.drinking.subtitle';
  static const drinkingButton = 'assessment.drinking.button';

  static const drinkingQuestionsUnitsLabel = 'assessment.drinking.questions.units.label';

  static const drinkinginfographic = 'assessment.drinking.img.src';
  static const drinkinginfographicCAPdf = 'assessment.drinking.pdf';
  static const drinkingQuestionsUnitsPlusLabel = 'assessment.drinking.questions.units.plusLabel';
  static const drinkingQuestionsUnitsMinusLabel = 'assessment.drinking.questions.units.minusLabel';

  static const drinkingQuestionsDaysLabel = 'assessment.drinking.questions.days.label';
  static const drinkingQuestionsDaysPlusLabel = 'assessment.drinking.questions.days.plusLabel';
  static const drinkingQuestionsDaysMinusLabel = 'assessment.drinking.questions.days.minusLabel';

  static const drinkingQuestionsRateLabel = 'assessment.drinking.questions.rate.label';
  static const drinkingQuestionsRateSliderLabels = 'assessment.drinking.questions.rate.sliderLabels';
  static const drinkingQuestionsRateInfoText = 'assessment.drinking.questions.rate.info.text';
  static const drinkingQuestionsRateInfoAudio = 'assessment.drinking.questions.rate.info.audio';

  // Drinking Feeling
  static const drinkingFeelingTitle = 'assessment.drinking-feeling.title';
  static const drinkingFeelingAudio = 'assessment.drinking-feeling.audio';
  static const drinkingFeelingSubtitle = 'assessment.drinking-feeling.subtitle';

  static const drinkingFeelingQuestionsControlLabel = 'assessment.drinking-feeling.questions.control.label';
  static const drinkingFeelingQuestionsControlItems = 'assessment.drinking-feeling.questions.control.items';

  static const drinkingFeelingQuestionsAnxiousLabel = 'assessment.drinking-feeling.questions.anxious.label';
  static const drinkingFeelingQuestionsAnxiousItems = 'assessment.drinking-feeling.questions.anxious.items';

  static const drinkingFeelingQuestionsWorryLabel = 'assessment.drinking-feeling.questions.worry.label';
  static const drinkingFeelingQuestionsWorryItems = 'assessment.drinking-feeling.questions.worry.items';

  static const drinkingFeelingQuestionsWillLabel = 'assessment.drinking-feeling.questions.will.label';
  static const drinkingFeelingQuestionsWillItems = 'assessment.drinking-feeling.questions.will.items';

  static const drinkingFeelingQuestionsDifficultyLabel = 'assessment.drinking-feeling.questions.difficulty.label';
  static const drinkingFeelingQuestionsDifficultyItems = 'assessment.drinking-feeling.questions.difficulty.items';

  // Drinking Goal Section
  static const drinkingGoalTitle = 'assessment.drinking-goal.title';
  static const drinkingGoalAudioAbstinent = 'assessment.drinking-goal.audio.abstinent';
  static const drinkingGoalAudioLight = 'assessment.drinking-goal.audio.light';
  static const drinkingGoalAudioHeavy = 'assessment.drinking-goal.audio.heavy';
  static const drinkingGoalSubtitlesAbstinent = 'assessment.drinking-goal.subtitles.abstinent';
  static const drinkingGoalSubtitlesLight = 'assessment.drinking-goal.subtitles.light';
  static const drinkingGoalSubtitlesHeavy = 'assessment.drinking-goal.subtitles.heavy';
  static const drinkingGoalQuestionsUnitsLabel = 'assessment.drinking-goal.questions.units.label';
  static const drinkingGoalQuestionsUnitsPlusLabel = 'assessment.drinking-goal.questions.units.plusLabel';
  static const drinkingGoalQuestionsUnitsMinusLabel = 'assessment.drinking-goal.questions.units.minusLabel';
  static const drinkingGoalQuestionsFreeDaysLabel = 'assessment.drinking-goal.questions.freeDays.label';
  static const drinkingGoalQuestionsFreeDaysPlusLabel = 'assessment.drinking-goal.questions.freeDays.plusLabel';
  static const drinkingGoalQuestionsFreeDaysMinusLabel = 'assessment.drinking-goal.questions.freeDays.minusLabel';

// Drugs Section
  static const drugsTitle = 'assessment.drugs.title';
  static const drugsAudio = 'assessment.drugs.audio';
  static const drugsSubtitle = 'assessment.drugs.subtitle';
  static const drugsQuestionsListHeadersDrug = 'assessment.drugs.questions.list.headers.drug';
  static const drugsQuestionsListHeadersAmount = 'assessment.drugs.questions.list.headers.amount';
  static const drugsQuestionsListHeadersUnit = 'assessment.drugs.questions.list.headers.unit';
  static const drugsQuestionsListHeadersFrequency = 'assessment.drugs.questions.list.headers.frequency';
  static const drugsQuestionsListButtonsAdd = 'assessment.drugs.questions.list.buttons.add';
  static const drugsQuestionsListButtonsRemove = 'assessment.drugs.questions.list.buttons.remove';
  static const drugsQuestionsListButtonsCancel = 'assessment.drugs.questions.list.buttons.cancel';
  static const drugsQuestionsListButtonsSave = 'assessment.drugs.questions.list.buttons.save';
  static const drugsQuestionsListLabelsDrugFirst = 'assessment.drugs.questions.list.labels.drugFirst';
  static const drugsQuestionsListLabelsDrug = 'assessment.drugs.questions.list.labels.drug';
  static const drugsQuestionsListLabelsUnit = 'assessment.drugs.questions.list.labels.unit';
  static const drugsQuestionsListLabelsAmount = 'assessment.drugs.questions.list.labels.amount';
  static const drugsQuestionsListLabelsAddUnit = 'assessment.drugs.questions.list.labels.addUnit';
  static const drugsQuestionsListLabelsRemoveUnit = 'assessment.drugs.questions.list.labels.removeUnit';
  static const drugsQuestionsListLabelsFrequency = 'assessment.drugs.questions.list.labels.frequency';
  static const drugsQuestionsListLabelsAddDay = 'assessment.drugs.questions.list.labels.addDay';
  static const drugsQuestionsListLabelsRemoveDay = 'assessment.drugs.questions.list.labels.removeDay';
  static const drugsQuestionsListLabelsDelete = 'assessment.drugs.questions.list.labels.delete';
  static const drugsQuestionsListLabelsDaysPerWeek = 'assessment.drugs.questions.list.labels.daysPerWeek';
  static const drugsQuestionsListLabelsDayPerWeek = 'assessment.drugs.questions.list.labels.dayPerWeek';
  static const drugsQuestionsListLabelsUnitsPerDay = 'assessment.drugs.questions.list.labels.unitsPerDay';
  static const drugsQuestionsRateLabel = 'assessment.drugs.questions.rate.label';
  static const drugsQuestionsRateSliderLabels = 'assessment.drugs.questions.rate.sliderLabels';
  static const drugsQuestionsRateInfoText = 'assessment.drugs.questions.rate.info.text';
  static const drugsQuestionsRateInfoAudio = 'assessment.drugs.questions.rate.info.audio';

// Drugs Feelings
  static const drugsFeelingTitle = 'assessment.drugs-feeling.title';
  static const drugsFeelingAudio = 'assessment.drugs-feeling.audio';
  static const drugsFeelingSubtitle = 'assessment.drugs-feeling.subtitle';

  static const drugsFeelingControlLabel = 'assessment.drugs-feeling.questions.control.label';
  static const drugsFeelingControlItems = 'assessment.drugs-feeling.questions.control.items';

  static const drugsFeelingAnxiousLabel = 'assessment.drugs-feeling.questions.anxious.label';
  static const drugsFeelingAnxiousItems = 'assessment.drugs-feeling.questions.anxious.items';

  static const drugsFeelingWorryLabel = 'assessment.drugs-feeling.questions.worry.label';
  static const drugsFeelingWorryItems = 'assessment.drugs-feeling.questions.worry.items';

  static const drugsFeelingWillLabel = 'assessment.drugs-feeling.questions.will.label';
  static const drugsFeelingWillItems = 'assessment.drugs-feeling.questions.will.items';

  static const drugsFeelingDifficultyLabel = 'assessment.drugs-feeling.questions.difficulty.label';
  static const drugsFeelingDifficultyItems = 'assessment.drugs-feeling.questions.difficulty.items';

//Drugs goal
  static const drugsGoalTitle = 'assessment.drugs-goal.title';

  static const drugsGoalAbstinentAudio = 'assessment.drugs-goal.audio.abstinent';
  static const drugsGoalTakingAudio = 'assessment.drugs-goal.audio.taking';

  static const drugsGoalAbstinentSubtitle = 'assessment.drugs-goal.subtitles.abstinent';
  static const drugsGoalTakingSubtitle = 'assessment.drugs-goal.subtitles.taking';

  static const drugsGoalUnitsLabel = 'assessment.drugs-goal.questions.units.label';
  static const drugsGoalUnitsPlusLabel = 'assessment.drugs-goal.questions.units.plusLabel';
  static const drugsGoalUnitsMinusLabel = 'assessment.drugs-goal.questions.units.minusLabel';

  static const drugsGoalFreeDaysLabel = 'assessment.drugs-goal.questions.freeDays.label';
  static const drugsGoalFreeDaysPlusLabel = 'assessment.drugs-goal.questions.freeDays.plusLabel';
  static const drugsGoalFreeDaysMinusLabel = 'assessment.drugs-goal.questions.freeDays.minusLabel';

//thankYou

  static const thankYouTitle = 'assessment.thank-you.title';
  static const thankYouAudio = 'assessment.thank-you.audio';
  static const thankYouText = 'assessment.thank-you.text';

  static const assessmentVideoTitle = 'assessment.assessment-video.title';
  static const assessmentVideoSrc = 'assessment.assessment-video.video.src';
  static const assessmentVideoPoster = 'assessment.assessment-video.video.poster';

  static const bridgingVideoTitle = 'assessment.bridging-video.title';
  static const bridgingVideoSrc = 'assessment.bridging-video.video.src';
  static const bridgingVideoPoster = 'assessment.bridging-video.video.poster';

  static const errorsVideoMessage = 'assessment.errors.video';
  static const errorsRequiredMessage = 'assessment.errors.required';
  static const errorsConsentRequiredMessage = 'assessment.errors.consentRequired';
  static const errorsNameMaxLengthMessage = 'assessment.errors.nameMaxLength';
  static const errorsInvalidUnitsMessage = 'assessment.errors.invalidUnits';
  static const errorsInvalidDaysMessage = 'assessment.errors.invalidDays';
  static const errorsMinimumOneDrug = 'assessment.errors.minimumOneDrug';
}
