class DrugsLocaleKeys {
  // Drug names
  static const aH7921Drug = 'drugs.names.AH-7921';
  static const gbl = 'drugs.names.GBL';
  static const ghb = 'drugs.names.GHB';
  static const hgh = 'drugs.names.HGH';
  static const mdpv = 'drugs.names.MDPV';
  static const msj = 'drugs.names.MSJ';
  static const oPce = 'drugs.names.O-PCE';
  static const sustanon = 'drugs.names.Sustanon';
  static const testosterone = 'drugs.names.Testosterone';
  static const acamprosate = 'drugs.names.acamprosate';
  static const adderall = 'drugs.names.adderall';
  static const alphaPvp = 'drugs.names.alpha-pvp';
  static const ambien = 'drugs.names.ambien';
  static const amitriptyline = 'drugs.names.amitriptyline';
  static const amphetamines = 'drugs.names.amphetamines';
  static const buprenorphine = 'drugs.names.buprenorphine';
  static const butane = 'drugs.names.butane';
  static const clonazepam = 'drugs.names.clonazepam';
  static const coCodamol = 'drugs.names.co-codamol';
  static const cocaine = 'drugs.names.cocaine';
  static const codeine = 'drugs.names.codeine';
  static const crack = 'drugs.names.crack';
  static const demerol = 'drugs.names.demerol';
  static const dexedrine = 'drugs.names.dexedrine';
  static const diazepam = 'drugs.names.diazepam';
  static const disulfiram = 'drugs.names.disulfiram';
  static const ecstasy = 'drugs.names.ecstasy';
  static const ephedrine = 'drugs.names.ephedrine';
  static const etizolam = 'drugs.names.etizolam';
  static const fentanyl = 'drugs.names.fentanyl';
  static const gabapentin = 'drugs.names.gabapentin';
  static const heroin = 'drugs.names.heroin';
  static const hydromorphone = 'drugs.names.hydromorphone';
  static const ketamine = 'drugs.names.ketamine';
  static const khat = 'drugs.names.khat';
  static const lorezepam = 'drugs.names.lorezepam';
  static const lunesta = 'drugs.names.lunesta';
  static const marijuana = 'drugs.names.marijuana';
  static const mephedrone = 'drugs.names.mephedrone';
  static const mephobarbital = 'drugs.names.mephobarbital';
  static const methadone = 'drugs.names.methadone';
  static const methamphetamine = 'drugs.names.methamphetamine';
  static const methoxatamine = 'drugs.names.methoxatamine';
  static const modafinil = 'drugs.names.modafinil';
  static const morphine = 'drugs.names.morphine';
  static const nalmefene = 'drugs.names.nalmefene';
  static const naltrexone = 'drugs.names.naltrexone';
  static const nitrazepam = 'drugs.names.nitrazepam';
  static const nitrousOxide = 'drugs.names.nitrous-oxide';
  static const oxandrolone = 'drugs.names.oxandrolone';
  static const oxycontin = 'drugs.names.oxycontin';
  static const oxymorphone = 'drugs.names.oxymorphone';
  static const pcp = 'drugs.names.pcp';
  static const phenobarbital = 'drugs.names.phenobarbital';
  static const pholcodeine = 'drugs.names.pholcodeine';
  static const pregabalin = 'drugs.names.pregabalin';
  static const ritalin = 'drugs.names.ritalin';
  static const sonata = 'drugs.names.sonata';
  static const suboxone = 'drugs.names.suboxone';
  static const syntheticCannabis = 'drugs.names.synthetic-cannabis';
  static const temazepam = 'drugs.names.temazepam';
  static const tobacco = 'drugs.names.tobacco';
  static const tramadol = 'drugs.names.tramadol';
  static const trenbolone = 'drugs.names.trenbolone';
  static const triazolam = 'drugs.names.triazolam';
  static const vicodin = 'drugs.names.vicodin';
  static const xanax = 'drugs.names.xanax';
  static const zopiclon = 'drugs.names.zopiclon';
  static const alfentanil = 'drugs.names.alfentanil';
  static const alphaD2pv = 'drugs.names.alpha-d2pv';
  static const bromadoline = 'drugs.names.bromadoline';
  static const bromazolam = 'drugs.names.bromazolam';
  static const buvidal = 'drugs.names.buvidal';
  static const caffeine = 'drugs.names.caffeine';
  static const carfentanil = 'drugs.names.carfentanil';
  static const desomorphine = 'drugs.names.desomorphine';
  static const diclazepam = 'drugs.names.diclazepam';
  static const flephedrone = 'drugs.names.flephedrone';
  static const flubromazolam = 'drugs.names.flubromazolam';
  static const hysingla = 'drugs.names.hysingla';
  static const ketazolam = 'drugs.names.ketazolam';
  static const kratom = 'drugs.names.kratom';
  static const meprobamate = 'drugs.names.meprobamate';
  static const metandienone = 'drugs.names.metandienone';
  static const methylone = 'drugs.names.methylone';
  static const midazolam = 'drugs.names.midazolam';
  static const pentazocine = 'drugs.names.pentazocine';
  static const phenibut = 'drugs.names.phenibut';
  static const sufentanil = 'drugs.names.sufentanil';
  static const tapentadol = 'drugs.names.tapentadol';
  static const topiramate = 'drugs.names.topiramate';
  static const u47700 = 'drugs.names.u-47700';
  static const vapeMarijuana = 'drugs.names.vape-marijuana';
  static const vapeNicotine = 'drugs.names.vape-nicotine';
  static const vyvanse = 'drugs.names.vyvanse';
  static const xelstrym = 'drugs.names.xelstrym';
  static const xylazine = 'drugs.names.xylazine';
  static const zohydro = 'drugs.names.zohydro';
  static const drugElisions = 'drugs.elisions';
  


  // Units
  static const String units = 'drugs.units';
  static const String milligrams = '$units.milligrams';
  static const String tablets = '$units.tablets';
  static const String lines = '$units.lines';
  static const String hits = '$units.hits';
  static const String grams = '$units.grams';
  static const String wraps = '$units.wraps';
  static const String cans = '$units.cans';
  static const String huffs = '$units.huffs';
  static const String rocks = '$units.rocks';
  static const String microgram = '$units.microgram';
  static const String millilitres = '$units.millilitres';
  static const String bags = '$units.bags';
  static const String leaves = '$units.leaves';
  static const String joints = '$units.joints';
  static const String balloons = '$units.balloons';
  static const String cigarettes = '$units.cigarettes';
  static const String injection = '$units.injection';
  static const String iu = '$units.IU';
  static const String gummies = '$units.gummies';
  static const String ampules = '$units.ampules';
  static const String pens = '$units.pens';
  static const String cartridges = '$units.cartridges';
  static const String patches = '$units.patches';
  static const String drinks = '$units.drinks';

  // Groupings
  static const String groupings = 'drugs.groupings';
  static const String alcohol = '$groupings.alcohol';
  static const String methamphetamineGrouping = '$groupings.methamphetamine';
  static const String barbiturates = '$groupings.barbiturates';
  static const String cannabis = '$groupings.cannabis';
  static const String clubDrugs = '$groupings.clubDrugs';
  static const String cocaineGrouping = '$groupings.cocaine';
  static const String crackGrouping = '$groupings.crack';
  static const String heroinGrouping = '$groupings.heroin';
  static const String newPsychoactiveSubstances = '$groupings.newPsychoactiveSubstances';
  static const String steroids = '$groupings.steroids';
  static const String prescribedMedications = '$groupings.prescribedMedications';
  static const String prescriptionDrugs = '$groupings.prescriptionDrugs';
  static const String substituteMedications = '$groupings.substituteMedications';
  static const String tobaccoGrouping = '$groupings.tobacco';
  static const String stimulants = '$groupings.stimulants';
  static const String opioids = '$groupings.opioids';
  static const String solvents = '$groupings.solvents';
}
