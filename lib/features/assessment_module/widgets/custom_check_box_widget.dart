import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomCheckbox extends StatelessWidget {
  const CustomCheckbox({
    this.text,
    this.onTap,
    super.key,
    this.initialValue = false,
    this.isChecked, this.containerColor, this.containerRadius, this.containerPadding, this.borderColor,
  });
  final String? text;
  final bool initialValue;
  final ValueNotifier<bool>? isChecked;
  final void Function()? onTap;
  final Color? containerColor;
  final BorderRadius? containerRadius;
  final EdgeInsetsGeometry? containerPadding;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isChecked ?? ValueNotifier(false),
      builder: (context, value, child) {
        return GestureDetector(
          onTap: onTap ?? () {},
          child: Container(
            padding:containerPadding,

           decoration: BoxDecoration(
               color: containerColor,
               borderRadius: containerRadius),
            child: Row(
              children: [
                Container(
                  width: AppSize.w16,
                  height: AppSize.w16,
                  decoration: BoxDecoration(
                    color: value ? context.themeColors.greenColor : Colors.transparent,
                    border: Border.all(
                      color: borderColor ?? context.themeColors.greenColor,
                      width: AppSize.w2,
                    ),
                    borderRadius: BorderRadius.circular(AppSize.r2),
                  ),
                  child: value
                      ? Center(
                          child: Icon(
                            Icons.check,
                            size: AppSize.sp12,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
                SpaceH(AppSize.w10),
                Expanded(
                  child: Text(
                    text ?? AssessmentLocaleKeys.recoveryProgramQuestionsAgeWithheld.tr(),
                    style: context.textTheme.titleSmall,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
