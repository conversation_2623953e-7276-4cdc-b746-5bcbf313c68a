import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/us_helper_bloc_widget.dart';
import 'package:breakingfree_v2/features/home_module/action_modules/lifestyle_module/widgets/parse_tagged_text.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class UsDrinkingHelperWidget extends StatelessWidget {
  const UsDrinkingHelperWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black38  ,width: 0.5),
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
                left: AppSize.h22, right: AppSize.h22, top: AppSize.h16),
            child: RichText(
              textAlign: TextAlign.center,
              text: parseTaggedText(
                text: CoreLocaleKeys.drinkingInfographicTitle.tr(),
                baseStyle: context.textTheme.titleSmall,
              ),
            ),
          ),
          SpaceV(AppSize.h20),
          Padding(
            padding:  EdgeInsets.symmetric(horizontal: AppSize.sp18),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.37,
                  child: AppTextWidget(
                    textAlign: TextAlign.center,
                    CoreLocaleKeys.drinkingInfographicDescription.tr(),
                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500)),
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.37,
                  child: AppTextWidget(
                    textAlign: TextAlign.center,
                    CoreLocaleKeys.drinkingInfographicEquivalents.tr(),
                      style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w500)),
                ),
              ],
            ),
          ),
          SpaceV(AppSize.h12),
          UsHelperBlocWidget(
            text: CoreLocaleKeys.drinkingInfographicBearRows,
            amount: CoreLocaleKeys.drinkingInfographicBearRows,
            length: 5,
            title: CoreLocaleKeys.drinkingInfographicBearTitle.tr(),
            color: Color.fromRGBO(201, 219, 128, 1),
          ),
          SpaceV(AppSize.h12),
          UsHelperBlocWidget(
            text: CoreLocaleKeys.drinkingInfographicWineRows,
            amount: CoreLocaleKeys.drinkingInfographicWineRows,
            length: 4,
              title: CoreLocaleKeys.drinkingInfographicWineTitle.tr(),
              color: Color.fromRGBO(155, 118, 179, 1)),
          SpaceV(AppSize.h12),
          UsHelperBlocWidget(
            text: CoreLocaleKeys.drinkingInfographicSpiritsRows,
            amount: CoreLocaleKeys.drinkingInfographicSpiritsRows,
            length: 2,
              title: CoreLocaleKeys.drinkingInfographicSpiritsTitle.tr(),
              color: Color.fromRGBO(144, 198, 195, 1)),
          SpaceV(AppSize.h20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.sp20),
            child: Column(
              children: [
                AppTextWidget(
                  (DynamicAssetLoader.getNestedValue(
                    CoreLocaleKeys.drinkingInfographicFooter,
                    context,
                  ) as List)
                      .join('\n\n'),
                ),
                SpaceV(AppSize.h12),
                AppTextWidget(
                  CoreLocaleKeys.drinkingInfographicSource.tr(),
                  textAlign: TextAlign.center,
                  style: context.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500,),
                ),
                SpaceV(AppSize.h10),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

String? getRowLabel({
  required BuildContext context,
  required String keyPath,
  required int index,
}) {
  final rows = DynamicAssetLoader.getNestedValue(keyPath, context);

  if (rows is List && rows.length > index) {
    final item = rows[index];
    if (item is Map && item['label'] != null) {
      return item['label'].toString();
    }
  }
  return null;
}

String? getRowAmount({
  required BuildContext context,
  required String keyPath,
  required int index,
}) {
  final rows = DynamicAssetLoader.getNestedValue(keyPath, context);

  if (rows is List && rows.length > index) {
    final item = rows[index];
    if (item is Map && item['amount'] != null) {
      return item['amount'].toString();
    }
  }
  return null;
}
