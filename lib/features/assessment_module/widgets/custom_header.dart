import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/assessment_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CustomHeader extends StatelessWidget {
  const CustomHeader({super.key,this.showArrowBack = true,});
  final bool showArrowBack;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentCubit, AssessmentState>(
      builder: (ctx, state) {
        return CustomAssessmentHeaderWidget(
          showArrowBack:showArrowBack ,
          onBackTap: getBackFunction(ctx, state.index),
          activeDotIndex: state.index,
          subTitle: getSubTitle(state.index),
        );
      },
    );
  }
}
