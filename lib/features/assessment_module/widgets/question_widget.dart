import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_icon_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class QuestionRowWidget extends StatelessWidget {
  const QuestionRowWidget({
    required this.questionText,
    super.key,
    this.oninfoTap,
    this.infoWidget,
    this.custominfoWidget,
    this.helpRefuseWidget,
  });
  final String questionText;
  final VoidCallback? oninfoTap;
  final CustomInfoWidget? infoWidget;
  final Widget? custominfoWidget;
  final Widget? helpRefuseWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 0),
              child: Assets.icons.questionGreenIcon.image(height: AppSize.w20, width: AppSize.w24),
            ),
            SpaceH(AppSize.w10),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: AppSize.w24),
                child: Row(
                  children: [
                    Expanded(
                      flex: 10,
                      child: AppTextWidget(
                        questionText,
                        style: context.textTheme.titleSmall?.copyWith(
                        ),
                      ),
                    ),
                    if (oninfoTap != null) ...[
                      SpaceH(AppSize.w2),
                      Expanded(
                        child: custominfoWidget ??
                            CustomIconButton(
                              onTap: oninfoTap,
                              assetIcon: Assets.icons.infoIcon,
                            ),
                      ),
                    ] else
                      const SizedBox(),
                  ],
                ),
              ),
            ),
          ],
        ),
        if (helpRefuseWidget != null) ...{
          Container(
            child: helpRefuseWidget,
          ),
        },
        if (infoWidget != null && (infoWidget?.visible ?? false)) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: AppSize.h8),
            child: infoWidget,
          ),
        ],
      ],
    );
  }
}
