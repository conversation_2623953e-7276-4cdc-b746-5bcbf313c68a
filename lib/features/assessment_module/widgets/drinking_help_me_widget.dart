import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drinking_gridview_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DrinkingHelpMeWidget extends StatelessWidget {
  const DrinkingHelpMeWidget({
    required this.visible,
    required this.assessmentCubit,
    this.showBackArrow = false, this.isAustralia,
    super.key,
    this.drinkItems,
    this.titleText,
    this.subTitleText,
    this.backonTap,
    this.drinkItemName,
    this.onCloseTap,
    this.percentageText,
  });
  final List<DrinkItem>? drinkItems;
  final String? drinkItemName;
  final String? titleText;
  final String? subTitleText;
  final String? percentageText;
  final bool visible;
  final bool showBackArrow;
  final void Function()? backonTap;
  final void Function()? onCloseTap;
  final AssessmentDrinkingCubit assessmentCubit;
  final bool? isAustralia;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrinkingCubit, AssessmentDrinkingState>(
      builder: (ctx, state) {
        return Visibility(
          visible: visible,
          child: Container(
            decoration: BoxDecoration(
              color: const Color.fromRGBO(235, 235, 235, 1),
              border: Border.all(
                color: const Color(0xFFBDBDBD),
              ),
              borderRadius: BorderRadius.circular(AppSize.r4)
            ),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w18, vertical: AppSize.h10),
                      child: Row(
                        children: [
                          if (showBackArrow) ...[
                            GestureDetector(onTap: backonTap, child: const Icon(Icons.arrow_back_sharp)),
                            SpaceH(AppSize.w10),
                          ],
                          Expanded(
                            child: AppTextWidget(
                              titleText ?? '',
                              style: context.textTheme.titleSmall?.copyWith(
                                color: context.themeColors.lightPurpleColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (assessmentCubit.isShowAlltheDetail.value) ...[
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: AppSize.w20, vertical: AppSize.h10),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Iterate over each DrinkDetail in the assessmentCubit's drinkDetails
                              for (final element in state.drinkDetails) ...[
                                // Row to contain drink details and delete icon
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(AppSize.r4),
                                    boxShadow: [
                                      BoxShadow(
                                        color: context.themeColors.blackColor.withOpacity(0.16),
                                        offset: Offset(0, AppSize.h3),
                                        blurRadius: AppSize.h10,
                                      ),
                                    ],
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(horizontal: AppSize.w16, vertical: AppSize.h12),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              if (element.drinkType != '')
                                                AppTextWidget(
                                                  element.drinkType ?? '',
                                                  style: context.textTheme.labelSmall?.copyWith(
                                                    fontSize: AppSize.sp11,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                )
                                              else
                                                const SizedBox(),
                                              if (element.size != '') ...[
                                                SpaceV(AppSize.h10),
                                                AppTextWidget(
                                                  'Size: ${element.size ?? ''}',
                                                  style: context.textTheme.labelSmall?.copyWith(
                                                    fontSize: AppSize.sp11,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ] else
                                                const SizedBox(),
                                              if (element.strength != '') ...[
                                                SpaceV(AppSize.h10),
                                                AppTextWidget(
                                                  'Strength: ${element.strength ?? ''}',
                                                  style: context.textTheme.labelSmall?.copyWith(
                                                    fontSize: AppSize.sp11,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ] else
                                                const SizedBox(),
                                              if (element.drinkType != '') ...[
                                                SpaceV(AppSize.h10),
                                                AppTextWidget(
                                                  'Quantity: ${element.quantity ?? '0'}',
                                                  style: context.textTheme.labelSmall?.copyWith(
                                                    fontSize: AppSize.sp11,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ] else
                                                const SizedBox(),
                                            ],
                                          ),
                                        ),
                                        // Add a delete button for each drink detail
                                        if (element.drinkType != '') ...[
                                          SpaceV(AppSize.h10), // Add some space between each drink detail

                                          InkWell(
                                            onTap: () {
                                              assessmentCubit.removeDrinkDetail(
                                                element,
                                                //  state.drinkDetails,
                                              );
                                            },
                                            child: Text(
                                              '🗑',
                                              style: TextStyle(
                                                fontSize: AppSize.sp24,
                                                color: context.themeColors.greyColor,
                                              ),
                                            ),
                                          ),
                                        ] else
                                          const SizedBox(),
                                      ],
                                    ),
                                  ),
                                ),
                                SpaceV(AppSize.h10), // Add space between each drink detail row
                              ],
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          // assessmentCubit.emitAddAthoerDrinkState();
                      

                             if(isAustralia ?? false) {
                            assessmentCubit
                              ..clearAuDrinkHistory()
                              ..initializeAuDrinkItems();
                          } else {
                            assessmentCubit
                            ..clearDrinkHistory()
                            ..initializeDrinkItems();
                          }
                        },
                        child: Align(
                          alignment: Alignment.bottomRight,
                          child: Padding(
                            padding: EdgeInsets.only(right: AppSize.w24, top: AppSize.h12),
                            child: AppTextWidget(
                              textAlign: TextAlign.center,
                              'ADD ANOTHER',
                              style: context.textTheme.titleSmall?.copyWith(
                                color: context.themeColors.greenColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ] else
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (showBackArrow) ...[
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: AppSize.w22, vertical: AppSize.h8),
                              child: Wrap(
                                // Change Row to Wrap
                                children: [
                                  AppTextWidget(
                                    textAlign: TextAlign.center,
                                    drinkItemName ?? '',
                                    style: context.textTheme.labelSmall
                                        ?.copyWith(fontSize: AppSize.sp11, fontWeight: FontWeight.w400),
                                  ),
                                  if (subTitleText != null && subTitleText != '') ...[
                                    SpaceH(AppSize.w4),
                                    Padding(
                                      padding: EdgeInsets.only(top: AppSize.w6),
                                      child: Icon(
                                        Icons.arrow_forward_ios_outlined,
                                        size: AppSize.sp8,
                                      ),
                                    ),
                                    SpaceH(AppSize.w4),
                                  ],
                                  AppTextWidget(
                                    textAlign: TextAlign.center,
                                    subTitleText ?? '',
                                    style: context.textTheme.labelSmall
                                        ?.copyWith(fontSize: AppSize.sp11, fontWeight: FontWeight.w400),
                                  ),
                                  if ((percentageText != null && percentageText != '0') && percentageText != '') ...[
                                    SpaceH(AppSize.w4),
                                    Padding(
                                      padding: EdgeInsets.only(top: AppSize.w6),
                                      child: Icon(
                                        Icons.arrow_forward_ios_outlined,
                                        size: AppSize.sp8,
                                      ),
                                    ),
                                    SpaceH(AppSize.w4),
                                    AppTextWidget(
                                      textAlign: TextAlign.center,
                                      percentageText ?? '',
                                      style: context.textTheme.labelSmall
                                          ?.copyWith(fontSize: AppSize.sp11, fontWeight: FontWeight.w400),
                                    ),
                                  ],
                                  SpaceV(AppSize.h10),
                                ],
                              ),
                            ),
                          ], //
                          SpaceV(AppSize.h10),
                          if ((percentageText == null || percentageText == '0') || percentageText == '')
                            DrinkGridWidget(
                              drinkItems: drinkItems ?? [],
                            )
                          else ...[
                            Row(
                              children: [
                                CustomAgeSelcetionWidget(
                                  onIncreaseTap: assessmentCubit.increaseCustomDrinkingValue,
                                  onDecreaseTap: assessmentCubit.decreaseCustomDrinkingValue,
                                  controller: assessmentCubit.unitshelpmeController,
                                ),
                                CustomRoundedButton(
                                  title: 'Add',
                                  fillColor: context.themeColors.blueColor,
                                  onTap: assessmentCubit.emitAddDrinkState,
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    SpaceV(AppSize.h18),
                  ],
                ),

                Padding(
                  padding: EdgeInsets.only(top: AppSize.h4, right: AppSize.w6),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                      onTap: onCloseTap,
                      child: const Icon(
                        Icons.close,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class DrinkDetail {
  DrinkDetail({
    this.drinkType,
    this.strength,
    this.quantity,
    this.size,
  });
  final String? drinkType;
  final String? strength;
  final String? quantity;
  final String? size;

  @override
  String toString() {
    return 'DrinkDetail(drinkType: $drinkType, strength: $strength, quantity: $quantity, size: $size)';
  }
}
