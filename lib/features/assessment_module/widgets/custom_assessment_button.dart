import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

// ValueNotifier<ButtonState> buttonState = ValueNotifier(ButtonState.bothDisabled);

class CustomAssessmentButton extends StatelessWidget {
  const CustomAssessmentButton({
    super.key,
    this.onNoTap,
    this.onYesTap,
    this.currentState,
    this.selectedValue = false,
    this.isNoLoading = false,
    this.isYesLoading = false,
    this.buttonFirstText,
    this.buttonSecondText,
    this.loaderPadding,
    this.buttonFirstKey,
    this.buttonSecondKey,
    this.switchPosition = false,
  });
  final void Function()? onNoTap;
  final void Function()? onYesTap;
  final ValueNotifier<ButtonState>? currentState;
  final bool selectedValue;
  final bool isNoLoading;
  final bool isYesLoading;
  final String? buttonFirstText;
  final String? buttonSecondText;
  final double? loaderPadding;
  final Key? buttonFirstKey;
  final Key? buttonSecondKey;
  final bool switchPosition;

  @override
  Widget build(BuildContext context) {
    log('message$selectedValue');
    log('message$currentState');
    return Column(
      children: [
        ValueListenableBuilder(
          valueListenable: currentState ?? ValueNotifier(false),
          builder: (context, value, child) {
            log('currentState$currentState');
            final bothDisabled = value == ButtonState.bothDisabled;
            // Set border color logic: red if both are disabled, green if either is selected
            final borderColor = bothDisabled && selectedValue
                ? context.themeColors.errorRedColor // Red if both are disabled
                : context.themeColors.greenColor; // Green if any one is selected
            final buttons = [
              //Todo: Added static text
              CustomRoundedButton(
                key: buttonFirstKey,
                onTap: onYesTap,
                title: buttonFirstText ?? 'No',
                width: AppSize.w78,
                verticalPadding: AppSize.h6,
                borderColor: borderColor, // Same border color for both buttons
                isLoading: isYesLoading,
                fillColor: (currentState?.value == ButtonState.noEnabled)
                    ? context.themeColors.greenColor
                    : context.themeColors.whiteColor,
                titleColor: (currentState?.value == ButtonState.noEnabled)
                    ? context.themeColors.whiteColor
                    : context.themeColors.greenColor,
                loaderPadding: loaderPadding,
                strokeWidth: 3,
              ),
              SpaceH(AppSize.w10),
              CustomRoundedButton(
                loaderPadding: loaderPadding,
                key: buttonSecondKey,
                onTap: onNoTap,
                title: buttonSecondText ?? 'Yes',
                width: AppSize.w78,
                verticalPadding: AppSize.h6,
                borderColor: borderColor, // Same border color for both buttons
                isLoading: isNoLoading,
                fillColor: (currentState?.value == ButtonState.yesEnabled)
                    ? context.themeColors.greenColor
                    : context.themeColors.whiteColor,
                titleColor: (currentState?.value == ButtonState.yesEnabled)
                    ? context.themeColors.whiteColor
                    : context.themeColors.greenColor,
                strokeWidth: 3,
              ),
            ];

            return Column(
              children: [
                Row(
                  children: switchPosition ? buttons.reversed.toList() : buttons,
                ),
                Visibility(
                  visible: bothDisabled && selectedValue,
                  child: CustomErrorWidget(
                    spacing: AppSize.h10,
                    errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}
