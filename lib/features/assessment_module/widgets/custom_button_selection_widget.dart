import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:breakingfree_v2/res/space_box.dart';

class CustomButtonSelectionWidget extends StatelessWidget {
  const CustomButtonSelectionWidget({
    required this.selectedIndex,
    required this.buttonTexts,
    required this.onTap, // Ensure onTap is required and takes an index
    this.buttonKeys,
    super.key,
    this.borderColor,
    this.isError,
  });

  final int selectedIndex;
  final List<String> buttonTexts;
  final List<String>? buttonKeys;
  final void Function(int index) onTap;
  final Color? borderColor;
  final bool? isError;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Alignment.topLeft, // Align the container to start
          child: Wrap(
            spacing: AppSize.w10, // Space between buttons
            runSpacing: AppSize.h10, // Space between rows if they wrap
            children: List.generate(buttonTexts.length, (index) {
              return InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                key: buttonKeys?[index] != null ? Key(buttonKeys?[index] ?? '') : null,
                onTap: () => onTap(index), // Pass the index to onTap when tapped
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: borderColor ?? context.themeColors.greenColor, width: AppSize.w3),
                    color: selectedIndex == index ? context.themeColors.greenColor : context.themeColors.scaffoldColor,
                    borderRadius: BorderRadius.circular(AppSize.r40), // Rounded corners
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppSize.w16, vertical: AppSize.h6),
                    child: AppTextWidget(
                      textAlign: TextAlign.center,
                      buttonTexts[index].tr(),
                      style: context.textTheme.labelSmall?.copyWith(
                        color: selectedIndex == index ? context.themeColors.whiteColor : context.themeColors.greenColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        Visibility(
          visible: isError ?? false,
          child: CustomErrorWidget(
            spacing: AppSize.h14,
            errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
          ),
        ),
      ],
    );
  }
}
