import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/dot_indicators.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomAssessmentHeaderWidget extends StatelessWidget {
  const CustomAssessmentHeaderWidget({
    super.key,
    this.subTitle,
    this.activeDotIndex,
    this.totalDots,
    this.onBackTap,
    this.showArrowBack = true,
  });
  final String? subTitle;
  final int? activeDotIndex;
  final int? totalDots;
  final void Function()? onBackTap;
  final bool showArrowBack;
  @override
  Widget build(BuildContext context) {
    //context.locale == const Locale('es','US')?AppSize.w24:
    return Column(
      children: [
        SpaceV(AppSize.h20),
        Stack(
          alignment: Alignment.center,
          children: [
            Row(
              children: [
                if (showArrowBack)
                  Padding(
                    padding: EdgeInsets.only(right: AppSize.w20),
                    child: CustomBackArrowButton(
                      // action: true,
                      padding : 0,
                      onTap: onBackTap,
                    ),
                  ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: context.locale == const Locale('es','US')?AppSize.w12:0),
              child: AppTextWidget(
                CoreLocaleKeys.titlesAssessment.tr(),
                textAlign: TextAlign.center,
                style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        SpaceV(AppSize.h4),
        AppTextWidget(
          subTitle ?? AssessmentLocaleKeys.welcomeVideoTitle.tr(),
          textAlign: TextAlign.center,
          style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
        if ((activeDotIndex ?? 0) > 1 && (activeDotIndex ?? 0) < 17) ...[
          SpaceV(AppSize.h16),
          DotIndicator(
            activeDotIndex: activeDotIndex ?? 0,
            totalDots: totalDots ?? 15, // Default to 16 if totalDots is null
          ),
        ],
        Padding(
          padding: EdgeInsets.only(bottom: AppSize.h12, top: AppSize.h10),
          child: const CustomDivider(),
        ),
      ],
    );
  }
}
