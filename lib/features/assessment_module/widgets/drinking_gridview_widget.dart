import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class DrinkGridWidget extends StatelessWidget {
  const DrinkGridWidget({super.key, this.drinkItems});
  final List<DrinkItem>? drinkItems;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSize.w18),
      child: GridView.builder(
        itemCount: drinkItems?.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
        ),
        itemBuilder: (context, index) {
          return DrinkGridItem(
            drinkItem: drinkItems?[index] ??
                DrinkItem(
                  name: 'Small bottle',
                  icon: Assets.icons.drinkingIcons.beer.bottle330ml,
                ),
          );
        },
        shrinkWrap: true, // Shrinks to fit content
        physics: const NeverScrollableScrollPhysics(), // Disable scrolling
      ),
    );
  }
}

class DrinkGridItem extends StatelessWidget {
  const DrinkGridItem({required this.drinkItem, super.key, this.isml = false, this.size});
  final DrinkItem drinkItem;
  final bool isml;
  final double? size;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: drinkItem.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color.fromRGBO(126, 20, 47, 1),
          borderRadius: BorderRadius.circular(AppSize.r8),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (drinkItem.icon != null)
                AppSvgAsset(
                  svgAsset: drinkItem.icon ?? '',
                  size: AppSize.h58,
                  color: Colors.white,
                )
              else
                const SizedBox(),
              AppTextWidget(
                textAlign: TextAlign.center,
                drinkItem.name,
                style: context.textTheme.labelSmall?.copyWith(
                  fontSize: AppSize.sp10,
                  color: context.themeColors.whiteColor,
                ),
              ), // Drink name
              if (drinkItem.ismil)
                Text(
                  drinkItem.volume ?? '',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                )
              else
                const SizedBox(),
            ],
          ),
        ),
      ),
    );
  }
}

class DrinkItem {
  DrinkItem({
    required this.name,
    this.icon,
    this.volume,
    this.onTap,
    this.ismil = false,
  });
  final String name;
  final String? volume;
  final String? icon;
  final bool ismil;
  final void Function()? onTap;
}

// import 'package:flutter/material.dart';

// class DrinkListWidget extends StatelessWidget {
//   DrinkListWidget({super.key});

//   final List<DrinkItem> drinkItems = [
//     DrinkItem('Small bottle', '275 ml', Icons.local_drink),
//     DrinkItem('Bottle', '330 ml', Icons.local_bar),
//     DrinkItem('Can', '440 ml', Icons.local_drink),
//     DrinkItem('Pint', '568 ml', Icons.local_cafe),
//     DrinkItem('Pint', '568 ml', Icons.local_cafe),
//   ];

//   @override
//   Widget build(BuildContext context) {
//     // Get the width of the screen
//     final screenWidth = MediaQuery.of(context).size.width;

//     // Determine how many items can fit in a row (based on screen width)
//     final itemsPerRow = (screenWidth / 200).floor(); // 200 is an approximate width for each item

//     // Build the rows by splitting the drink items list into rows of `itemsPerRow`
//     List<Widget> buildRows() {
//       final rows = <Widget>[];
//       for (var i = 0; i < drinkItems.length; i += itemsPerRow) {
//         final rowItems = <Widget>[];
//         for (var j = 0; j < itemsPerRow; j++) {
//           if (i + j < drinkItems.length) {
//             rowItems.add(Expanded(child: DrinkGridItem(drinkItem: drinkItems[i + j])));
//           } else {
//             rowItems.add(const Expanded(child: SizedBox())); // Fill with empty space
//           }
//         }
//         rows.add(
//           Row(
//             children: rowItems,
//           ),
//         );
//         rows.add(const SizedBox(height: 16)); // Add space between rows
//       }
//       return rows;
//     }

//     return Padding(
//       padding: const EdgeInsets.all(8),
//       child: ListView(
//         physics: const NeverScrollableScrollPhysics(), // Disable scrolling
//         shrinkWrap: true, // Adjust height as per content
//         children: buildRows(),
//       ),
//     );
//   }
// }

// class DrinkGridItem extends StatelessWidget {
//   const DrinkGridItem({required this.drinkItem, super.key});
//   final DrinkItem drinkItem;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.all(8), // Margin for individual items
//       decoration: BoxDecoration(
//         color: const Color.fromRGBO(126, 20, 47, 1),
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(drinkItem.icon, color: Colors.white, size: 40), // Drink icon
//           const SizedBox(height: 8),
//           Text(
//             drinkItem.name,
//             style: const TextStyle(color: Colors.white, fontSize: 16),
//           ), // Drink name
//           Text(
//             drinkItem.volume,
//             style: const TextStyle(color: Colors.white70, fontSize: 14),
//           ), // Drink volume
//         ],
//       ),
//     );
//   }
// }

// class DrinkItem {
//   DrinkItem(this.name, this.volume, this.icon);
//   final String name;
//   final String volume;
//   final IconData icon;
// }

// Main function for testing
