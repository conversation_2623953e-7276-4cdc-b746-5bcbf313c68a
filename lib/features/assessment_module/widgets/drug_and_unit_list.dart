import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';

class DrugAndUnitList {
  static List<String> drugList = [
    DrugsLocaleKeys.aH7921Drug.tr(),
    DrugsLocaleKeys.alfentanil.tr(),
    DrugsLocaleKeys.alphaD2pv.tr(),
    DrugsLocaleKeys.alphaPvp.tr(),
    DrugsLocaleKeys.ambien.tr(),
    DrugsLocaleKeys.amitriptyline.tr(),
    DrugsLocaleKeys.amphetamines.tr(),
    DrugsLocaleKeys.bromadoline.tr(),
    DrugsLocaleKeys.bromazolam.tr(),
    DrugsLocaleKeys.buprenorphine.tr(),
    DrugsLocaleKeys.buvidal.tr(),
    DrugsLocaleKeys.butane.tr(),
    DrugsLocaleKeys.caffeine.tr(),
    DrugsLocaleKeys.carfentanil.tr(),
    DrugsLocaleKeys.clonazepam.tr(),
    DrugsLocaleKeys.coCodamol.tr(),
    DrugsLocaleKeys.cocaine.tr(),
    DrugsLocaleKeys.codeine.tr(),
    DrugsLocaleKeys.crack.tr(),
    DrugsLocaleKeys.demerol.tr(),
    DrugsLocaleKeys.desomorphine.tr(),
    DrugsLocaleKeys.dexedrine.tr(),
    DrugsLocaleKeys.diazepam.tr(),
    DrugsLocaleKeys.diclazepam.tr(),
    DrugsLocaleKeys.disulfiram.tr(),
    DrugsLocaleKeys.ecstasy.tr(),
    DrugsLocaleKeys.ephedrine.tr(),
    DrugsLocaleKeys.etizolam.tr(),
    DrugsLocaleKeys.fentanyl.tr(),
    DrugsLocaleKeys.flephedrone.tr(),
    DrugsLocaleKeys.flubromazolam.tr(),
    DrugsLocaleKeys.gabapentin.tr(),
    DrugsLocaleKeys.ghb.tr(),
    DrugsLocaleKeys.gbl.tr(),
    DrugsLocaleKeys.heroin.tr(),
    DrugsLocaleKeys.hgh.tr(),
    DrugsLocaleKeys.hydromorphone.tr(),
    DrugsLocaleKeys.hysingla.tr(),
    DrugsLocaleKeys.ketamine.tr(),
    DrugsLocaleKeys.ketazolam.tr(),
    DrugsLocaleKeys.khat.tr(),
    DrugsLocaleKeys.kratom.tr(),
    DrugsLocaleKeys.lorezepam.tr(),
    DrugsLocaleKeys.lunesta.tr(),
    DrugsLocaleKeys.marijuana.tr(),
    DrugsLocaleKeys.mdpv.tr(),
    DrugsLocaleKeys.mephedrone.tr(),
    DrugsLocaleKeys.mephobarbital.tr(),
    DrugsLocaleKeys.meprobamate.tr(),
    DrugsLocaleKeys.metandienone.tr(),
    DrugsLocaleKeys.methadone.tr(),
    DrugsLocaleKeys.methamphetamine.tr(),
    DrugsLocaleKeys.methoxatamine.tr(),
    DrugsLocaleKeys.methylone.tr(),
    DrugsLocaleKeys.midazolam.tr(),
    DrugsLocaleKeys.modafinil.tr(),
    DrugsLocaleKeys.morphine.tr(),
    DrugsLocaleKeys.msj.tr(),
    DrugsLocaleKeys.nalmefene.tr(),
    DrugsLocaleKeys.naltrexone.tr(),
    DrugsLocaleKeys.nitrazepam.tr(),
    DrugsLocaleKeys.nitrousOxide.tr(),
    DrugsLocaleKeys.oPce.tr(),
    DrugsLocaleKeys.oxandrolone.tr(),
    DrugsLocaleKeys.oxycontin.tr(),
    DrugsLocaleKeys.oxymorphone.tr(),
    DrugsLocaleKeys.pcp.tr(),
    DrugsLocaleKeys.pentazocine.tr(),
    DrugsLocaleKeys.phenibut.tr(),
    DrugsLocaleKeys.phenobarbital.tr(),
    DrugsLocaleKeys.pholcodeine.tr(),
    DrugsLocaleKeys.pregabalin.tr(),
    DrugsLocaleKeys.ritalin.tr(),
    DrugsLocaleKeys.sonata.tr(),
    DrugsLocaleKeys.suboxone.tr(),
    DrugsLocaleKeys.sufentanil.tr(),
    DrugsLocaleKeys.sustanon.tr(),
    DrugsLocaleKeys.syntheticCannabis.tr(),
    DrugsLocaleKeys.tapentadol.tr(),
    DrugsLocaleKeys.temazepam.tr(),
    DrugsLocaleKeys.testosterone.tr(),
    DrugsLocaleKeys.tobacco.tr(),
    DrugsLocaleKeys.topiramate.tr(),
    DrugsLocaleKeys.tramadol.tr(),
    DrugsLocaleKeys.trenbolone.tr(),
    DrugsLocaleKeys.triazolam.tr(),
    DrugsLocaleKeys.u47700.tr(),
    DrugsLocaleKeys.vapeMarijuana.tr(),
    DrugsLocaleKeys.vapeNicotine.tr(),
    DrugsLocaleKeys.vicodin.tr(),
    DrugsLocaleKeys.vyvanse.tr(),
    DrugsLocaleKeys.xanax.tr(),
    DrugsLocaleKeys.xelstrym.tr(),
    DrugsLocaleKeys.xylazine.tr(),
    DrugsLocaleKeys.zohydro.tr(),
    DrugsLocaleKeys.zopiclon.tr(),
    DrugsLocaleKeys.acamprosate.tr(),
    DrugsLocaleKeys.adderall.tr(),
  ];

  // static List<String> drugList = [
  //   DrugsLocaleKeys.aH7921Drug.tr(),
  //   DrugsLocaleKeys.gbl.tr(),
  //   DrugsLocaleKeys.ghb.tr(),
  //   DrugsLocaleKeys.hgh.tr(),
  //   DrugsLocaleKeys.mdpv.tr(),
  //   DrugsLocaleKeys.msj.tr(),
  //   DrugsLocaleKeys.oPce.tr(),
  //   DrugsLocaleKeys.sustanon.tr(),
  //   DrugsLocaleKeys.testosterone.tr(),
  //   DrugsLocaleKeys.acamprosate.tr(),
  //   DrugsLocaleKeys.adderall.tr(),
  //   DrugsLocaleKeys.alphaPvp.tr(),
  //   DrugsLocaleKeys.ambien.tr(),
  //   DrugsLocaleKeys.amitriptyline.tr(),
  //   DrugsLocaleKeys.amphetamines.tr(),
  //   DrugsLocaleKeys.buprenorphine.tr(),
  //   DrugsLocaleKeys.butane.tr(),
  //   DrugsLocaleKeys.clonazepam.tr(),
  //   DrugsLocaleKeys.coCodamol.tr(),
  //   DrugsLocaleKeys.cocaine.tr(),
  //   DrugsLocaleKeys.codeine.tr(),
  //   DrugsLocaleKeys.crack.tr(),
  //   DrugsLocaleKeys.demerol.tr(),
  //   DrugsLocaleKeys.dexedrine.tr(),
  //   DrugsLocaleKeys.diazepam.tr(),
  //   DrugsLocaleKeys.disulfiram.tr(),
  //   DrugsLocaleKeys.ecstasy.tr(),
  //   DrugsLocaleKeys.ephedrine.tr(),
  //   DrugsLocaleKeys.etizolam.tr(),
  //   DrugsLocaleKeys.fentanyl.tr(),
  //   DrugsLocaleKeys.gabapentin.tr(),
  //   DrugsLocaleKeys.heroin.tr(),
  //   DrugsLocaleKeys.hydromorphone.tr(),
  //   DrugsLocaleKeys.ketamine.tr(),
  //   DrugsLocaleKeys.khat.tr(),
  //   DrugsLocaleKeys.lorezepam.tr(),
  //   DrugsLocaleKeys.lunesta.tr(),
  //   DrugsLocaleKeys.marijuana.tr(),
  //   DrugsLocaleKeys.mephedrone.tr(),
  //   DrugsLocaleKeys.mephobarbital.tr(),
  //   DrugsLocaleKeys.methadone.tr(),
  //   DrugsLocaleKeys.methamphetamine.tr(),
  //   DrugsLocaleKeys.methoxatamine.tr(),
  //   DrugsLocaleKeys.modafinil.tr(),
  //   DrugsLocaleKeys.morphine.tr(),
  //   DrugsLocaleKeys.nalmefene.tr(),
  //   DrugsLocaleKeys.naltrexone.tr(),
  //   DrugsLocaleKeys.nitrazepam.tr(),
  //   DrugsLocaleKeys.nitrousOxide.tr(),
  //   DrugsLocaleKeys.oxandrolone.tr(),
  //   DrugsLocaleKeys.oxycontin.tr(),
  //   DrugsLocaleKeys.oxymorphone.tr(),
  //   DrugsLocaleKeys.pcp.tr(),
  //   DrugsLocaleKeys.phenobarbital.tr(),
  //   DrugsLocaleKeys.pholcodeine.tr(),
  //   DrugsLocaleKeys.pregabalin.tr(),
  //   DrugsLocaleKeys.ritalin.tr(),
  //   DrugsLocaleKeys.sonata.tr(),
  //   DrugsLocaleKeys.suboxone.tr(),
  //   DrugsLocaleKeys.syntheticCannabis.tr(),
  //   DrugsLocaleKeys.temazepam.tr(),
  //   DrugsLocaleKeys.tobacco.tr(),
  //   DrugsLocaleKeys.tramadol.tr(),
  //   DrugsLocaleKeys.trenbolone.tr(),
  //   DrugsLocaleKeys.triazolam.tr(),
  //   DrugsLocaleKeys.vicodin.tr(),
  //   DrugsLocaleKeys.xanax.tr(),
  //   DrugsLocaleKeys.zopiclon.tr(),
  // ];

  static List<String> unitList1 = [
    DrugsLocaleKeys.milligrams.tr(),
    DrugsLocaleKeys.tablets.tr(),
    DrugsLocaleKeys.lines.tr(),
    DrugsLocaleKeys.hits.tr(),
    DrugsLocaleKeys.grams.tr(),
    DrugsLocaleKeys.wraps.tr(),
    DrugsLocaleKeys.cans.tr(),
    DrugsLocaleKeys.huffs.tr(),
    DrugsLocaleKeys.rocks.tr(),
    DrugsLocaleKeys.microgram.tr(),
    DrugsLocaleKeys.millilitres.tr(),
    DrugsLocaleKeys.bags.tr(),
    DrugsLocaleKeys.leaves.tr(),
    DrugsLocaleKeys.joints.tr(),
    DrugsLocaleKeys.balloons.tr(),
    DrugsLocaleKeys.cigarettes.tr(),
    DrugsLocaleKeys.injection.tr(),
    DrugsLocaleKeys.iu.tr(),
    DrugsLocaleKeys.gummies.tr(),
    DrugsLocaleKeys.ampules.tr(),
    DrugsLocaleKeys.pens.tr(),
    DrugsLocaleKeys.cartridges.tr(),
    DrugsLocaleKeys.patches.tr(),
    DrugsLocaleKeys.drinks.tr(),
  ];

  static Map<String, String> units = {
    'milligrams': DrugsLocaleKeys.milligrams.tr(),
    'tablets': DrugsLocaleKeys.tablets.tr(),
    'lines': DrugsLocaleKeys.lines.tr(),
    'hits': DrugsLocaleKeys.hits.tr(),
    'grams': DrugsLocaleKeys.grams.tr(),
    'wraps': DrugsLocaleKeys.wraps.tr(),
    'cans': DrugsLocaleKeys.cans.tr(),
    'huffs': DrugsLocaleKeys.huffs.tr(),
    'rocks': DrugsLocaleKeys.rocks.tr(),
    'microgram': DrugsLocaleKeys.microgram.tr(),
    'millilitres': DrugsLocaleKeys.millilitres.tr(),
    'bags': DrugsLocaleKeys.bags.tr(),
    'leaves': DrugsLocaleKeys.leaves.tr(),
    'joints': DrugsLocaleKeys.joints.tr(),
    'balloons': DrugsLocaleKeys.balloons.tr(),
    'cigarettes': DrugsLocaleKeys.cigarettes.tr(),
    'injection': DrugsLocaleKeys.injection.tr(),
    'IU': DrugsLocaleKeys.iu.tr(),
    'gummies': DrugsLocaleKeys.gummies.tr(),
    'ampules': DrugsLocaleKeys.ampules.tr(),
    'pens': DrugsLocaleKeys.pens.tr(),
    'cartridges': DrugsLocaleKeys.cartridges.tr(),
    'patches': DrugsLocaleKeys.patches.tr(),
    'drinks': DrugsLocaleKeys.drinks.tr(),
  };

  // static Map<String, String> createMap() {
  //   return {
  //     'AH-7921': DrugsLocaleKeys.aH7921Drug.tr(),
  //     'GBL': DrugsLocaleKeys.gbl.tr(),
  //     'GHB': DrugsLocaleKeys.ghb.tr(),
  //     'HGH': DrugsLocaleKeys.hgh.tr(),
  //     'MDPV': DrugsLocaleKeys.mdpv.tr(),
  //     'MSJ': DrugsLocaleKeys.msj.tr(),
  //     'O-PCE': DrugsLocaleKeys.oPce.tr(),
  //     'Sustanon': DrugsLocaleKeys.sustanon.tr(),
  //     'Testosterone': DrugsLocaleKeys.testosterone.tr(),
  //     'acamprosate': DrugsLocaleKeys.acamprosate.tr(),
  //     'adderall': DrugsLocaleKeys.adderall.tr(),
  //     'alpha-pvp': DrugsLocaleKeys.alphaPvp.tr(),
  //     'ambien': DrugsLocaleKeys.ambien.tr(),
  //     'amitriptyline': DrugsLocaleKeys.amitriptyline.tr(),
  //     'amphetamines': DrugsLocaleKeys.amphetamines.tr(),
  //     'buprenorphine': DrugsLocaleKeys.buprenorphine.tr(),
  //     'butane': DrugsLocaleKeys.butane.tr(),
  //     'clonazepam': DrugsLocaleKeys.clonazepam.tr(),
  //     'co-codamol': DrugsLocaleKeys.coCodamol.tr(),
  //     'cocaine': DrugsLocaleKeys.cocaine.tr(),
  //     'codeine': DrugsLocaleKeys.codeine.tr(),
  //     'crack': DrugsLocaleKeys.crack.tr(),
  //     'demerol': DrugsLocaleKeys.demerol.tr(),
  //     'dexedrine': DrugsLocaleKeys.dexedrine.tr(),
  //     'diazepam': DrugsLocaleKeys.diazepam.tr(),
  //     'disulfiram': DrugsLocaleKeys.disulfiram.tr(),
  //     'ecstasy': DrugsLocaleKeys.ecstasy.tr(),
  //     'ephedrine': DrugsLocaleKeys.ephedrine.tr(),
  //     'etizolam': DrugsLocaleKeys.etizolam.tr(),
  //     'fentanyl': DrugsLocaleKeys.fentanyl.tr(),
  //     'gabapentin': DrugsLocaleKeys.gabapentin.tr(),
  //     'heroin': DrugsLocaleKeys.heroin.tr(),
  //     'hydromorphone': DrugsLocaleKeys.hydromorphone.tr(),
  //     'ketamine': DrugsLocaleKeys.ketamine.tr(),
  //     'khat': DrugsLocaleKeys.khat.tr(),
  //     'lorezepam': DrugsLocaleKeys.lorezepam.tr(),
  //     'lunesta': DrugsLocaleKeys.lunesta.tr(),
  //     'marijuana': DrugsLocaleKeys.marijuana.tr(),
  //     'mephedrone': DrugsLocaleKeys.mephedrone.tr(),
  //     'mephobarbital': DrugsLocaleKeys.mephobarbital.tr(),
  //     'methadone': DrugsLocaleKeys.methadone.tr(),
  //     'methamphetamine': DrugsLocaleKeys.methamphetamine.tr(),
  //     'methoxatamine': DrugsLocaleKeys.methoxatamine.tr(),
  //     'modafinil': DrugsLocaleKeys.modafinil.tr(),
  //     'morphine': DrugsLocaleKeys.morphine.tr(),
  //     'nalmefene': DrugsLocaleKeys.nalmefene.tr(),
  //     'naltrexone': DrugsLocaleKeys.naltrexone.tr(),
  //     'nitrazepam': DrugsLocaleKeys.nitrazepam.tr(),
  //     'nitrous-oxide': DrugsLocaleKeys.nitrousOxide.tr(),
  //     'oxandrolone': DrugsLocaleKeys.oxandrolone.tr(),
  //     'oxycontin': DrugsLocaleKeys.oxycontin.tr(),
  //     'oxymorphone': DrugsLocaleKeys.oxymorphone.tr(),
  //     'pcp': DrugsLocaleKeys.pcp.tr(),
  //     'phenobarbital': DrugsLocaleKeys.phenobarbital.tr(),
  //     'pholcodeine': DrugsLocaleKeys.pholcodeine.tr(),
  //     'pregabalin': DrugsLocaleKeys.pregabalin.tr(),
  //     'ritalin': DrugsLocaleKeys.ritalin.tr(),
  //     'sonata': DrugsLocaleKeys.sonata.tr(),
  //     'suboxone': DrugsLocaleKeys.suboxone.tr(),
  //     'synthetic-cannabis': DrugsLocaleKeys.syntheticCannabis.tr(),
  //     'temazepam': DrugsLocaleKeys.temazepam.tr(),
  //     'tobacco': DrugsLocaleKeys.tobacco.tr(),
  //     'tramadol': DrugsLocaleKeys.tramadol.tr(),
  //     'trenbolone': DrugsLocaleKeys.trenbolone.tr(),
  //     'triazolam': DrugsLocaleKeys.triazolam.tr(),
  //     'vicodin': DrugsLocaleKeys.vicodin.tr(),
  //     'xanax': DrugsLocaleKeys.xanax.tr(),
  //     'zopiclon': DrugsLocaleKeys.zopiclon.tr(),
  //   };
  // }

  static Map<String, String> createMap() {
    return {
      'AH-7921': DrugsLocaleKeys.aH7921Drug.tr(),
      'GBL': DrugsLocaleKeys.gbl.tr(),
      'GHB': DrugsLocaleKeys.ghb.tr(),
      'HGH': DrugsLocaleKeys.hgh.tr(),
      'MDPV': DrugsLocaleKeys.mdpv.tr(),
      'MSJ': DrugsLocaleKeys.msj.tr(),
      'O-PCE': DrugsLocaleKeys.oPce.tr(),
      'Sustanon': DrugsLocaleKeys.sustanon.tr(),
      'Testosterone': DrugsLocaleKeys.testosterone.tr(),
      'acamprosate': DrugsLocaleKeys.acamprosate.tr(),
      'adderall': DrugsLocaleKeys.adderall.tr(),
      'alpha-pvp': DrugsLocaleKeys.alphaPvp.tr(),
      'ambien': DrugsLocaleKeys.ambien.tr(),
      'amitriptyline': DrugsLocaleKeys.amitriptyline.tr(),
      'amphetamines': DrugsLocaleKeys.amphetamines.tr(),
      'buprenorphine': DrugsLocaleKeys.buprenorphine.tr(),
      'butane': DrugsLocaleKeys.butane.tr(),
      'clonazepam': DrugsLocaleKeys.clonazepam.tr(),
      'co-codamol': DrugsLocaleKeys.coCodamol.tr(),
      'cocaine': DrugsLocaleKeys.cocaine.tr(),
      'codeine': DrugsLocaleKeys.codeine.tr(),
      'crack': DrugsLocaleKeys.crack.tr(),
      'demerol': DrugsLocaleKeys.demerol.tr(),
      'dexedrine': DrugsLocaleKeys.dexedrine.tr(),
      'diazepam': DrugsLocaleKeys.diazepam.tr(),
      'disulfiram': DrugsLocaleKeys.disulfiram.tr(),
      'ecstasy': DrugsLocaleKeys.ecstasy.tr(),
      'ephedrine': DrugsLocaleKeys.ephedrine.tr(),
      'etizolam': DrugsLocaleKeys.etizolam.tr(),
      'fentanyl': DrugsLocaleKeys.fentanyl.tr(),
      'gabapentin': DrugsLocaleKeys.gabapentin.tr(),
      'heroin': DrugsLocaleKeys.heroin.tr(),
      'hydromorphone': DrugsLocaleKeys.hydromorphone.tr(),
      'ketamine': DrugsLocaleKeys.ketamine.tr(),
      'khat': DrugsLocaleKeys.khat.tr(),
      'lorezepam': DrugsLocaleKeys.lorezepam.tr(),
      'lunesta': DrugsLocaleKeys.lunesta.tr(),
      'marijuana': DrugsLocaleKeys.marijuana.tr(),
      'mephedrone': DrugsLocaleKeys.mephedrone.tr(),
      'mephobarbital': DrugsLocaleKeys.mephobarbital.tr(),
      'methadone': DrugsLocaleKeys.methadone.tr(),
      'methamphetamine': DrugsLocaleKeys.methamphetamine.tr(),
      'methoxatamine': DrugsLocaleKeys.methoxatamine.tr(),
      'modafinil': DrugsLocaleKeys.modafinil.tr(),
      'morphine': DrugsLocaleKeys.morphine.tr(),
      'nalmefene': DrugsLocaleKeys.nalmefene.tr(),
      'naltrexone': DrugsLocaleKeys.naltrexone.tr(),
      'nitrazepam': DrugsLocaleKeys.nitrazepam.tr(),
      'nitrous-oxide': DrugsLocaleKeys.nitrousOxide.tr(),
      'oxandrolone': DrugsLocaleKeys.oxandrolone.tr(),
      'oxycontin': DrugsLocaleKeys.oxycontin.tr(),
      'oxymorphone': DrugsLocaleKeys.oxymorphone.tr(),
      'pcp': DrugsLocaleKeys.pcp.tr(),
      'phenobarbital': DrugsLocaleKeys.phenobarbital.tr(),
      'pholcodeine': DrugsLocaleKeys.pholcodeine.tr(),
      'pregabalin': DrugsLocaleKeys.pregabalin.tr(),
      'ritalin': DrugsLocaleKeys.ritalin.tr(),
      'sonata': DrugsLocaleKeys.sonata.tr(),
      'suboxone': DrugsLocaleKeys.suboxone.tr(),
      'synthetic-cannabis': DrugsLocaleKeys.syntheticCannabis.tr(),
      'temazepam': DrugsLocaleKeys.temazepam.tr(),
      'tobacco': DrugsLocaleKeys.tobacco.tr(),
      'tramadol': DrugsLocaleKeys.tramadol.tr(),
      'trenbolone': DrugsLocaleKeys.trenbolone.tr(),
      'triazolam': DrugsLocaleKeys.triazolam.tr(),
      'vicodin': DrugsLocaleKeys.vicodin.tr(),
      'xanax': DrugsLocaleKeys.xanax.tr(),
      'zopiclon': DrugsLocaleKeys.zopiclon.tr(),
      'alfentanil': DrugsLocaleKeys.alfentanil.tr(),
      'alpha-d2pv': DrugsLocaleKeys.alphaD2pv.tr(),
      'bromadoline': DrugsLocaleKeys.bromadoline.tr(),
      'bromazolam': DrugsLocaleKeys.bromazolam.tr(),
      'buvidal': DrugsLocaleKeys.buvidal.tr(),
      'caffeine': DrugsLocaleKeys.caffeine.tr(),
      'carfentanil': DrugsLocaleKeys.carfentanil.tr(),
      'desomorphine': DrugsLocaleKeys.desomorphine.tr(),
      'diclazepam': DrugsLocaleKeys.diclazepam.tr(),
      'flephedrone': DrugsLocaleKeys.flephedrone.tr(),
      'flubromazolam': DrugsLocaleKeys.flubromazolam.tr(),
      'hysingla': DrugsLocaleKeys.hysingla.tr(),
      'ketazolam': DrugsLocaleKeys.ketazolam.tr(),
      'kratom': DrugsLocaleKeys.kratom.tr(),
      'meprobamate': DrugsLocaleKeys.meprobamate.tr(),
      'metandienone': DrugsLocaleKeys.metandienone.tr(),
      'methylone': DrugsLocaleKeys.methylone.tr(),
      'midazolam': DrugsLocaleKeys.midazolam.tr(),
      'pentazocine': DrugsLocaleKeys.pentazocine.tr(),
      'phenibut': DrugsLocaleKeys.phenibut.tr(),
      'sufentanil': DrugsLocaleKeys.sufentanil.tr(),
      'tapentadol': DrugsLocaleKeys.tapentadol.tr(),
      'topiramate': DrugsLocaleKeys.topiramate.tr(),
      'u-47700': DrugsLocaleKeys.u47700.tr(),
      'vape-marijuana': DrugsLocaleKeys.vapeMarijuana.tr(),
      'vape-nicotine': DrugsLocaleKeys.vapeNicotine.tr(),
      'vyvanse': DrugsLocaleKeys.vyvanse.tr(),
      'xelstrym': DrugsLocaleKeys.xelstrym.tr(),
      'xylazine': DrugsLocaleKeys.xylazine.tr(),
      'zohydro': DrugsLocaleKeys.zohydro.tr(),
    };
  }
}
