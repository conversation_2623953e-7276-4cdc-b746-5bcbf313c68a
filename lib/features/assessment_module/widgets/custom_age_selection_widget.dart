import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomAgeSelcetionWidget extends StatelessWidget {
  const CustomAgeSelcetionWidget({
    this.isDisable = false,
    super.key,
    this.onDecreaseTap,
    this.onIncreaseTap,
    this.controller,
    this.increaseKey,
    this.decreaseKey,
    this.isButtonClicked,
    this.unitText,
    this.maxDigit = 4, this.inputFormatters,
  });

  final bool isDisable;
  final void Function()? onDecreaseTap;
  final void Function()? onIncreaseTap;
  final TextEditingController? controller;
  final Key? increaseKey;
  final Key? decreaseKey;
  final bool? isButtonClicked;
  final dynamic unitText;
  final int? maxDigit;
  final List<TextInputFormatter>? inputFormatters;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
      child: Column(
        children: [
          Row(
            children: [
              InkWell(
                key: decreaseKey,
                splashColor: context.themeColors.scaffoldColor,
                highlightColor: context.themeColors.scaffoldColor,
                onTap: isDisable ? null : onDecreaseTap,
                child: Container(
                  height: AppSize.h40,
                  width: AppSize.w40,
                  decoration: BoxDecoration(
                    color: isDisable ? const Color.fromRGBO(224, 224, 224, 1) : context.themeColors.greenColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.remove,
                      color: context.themeColors.whiteColor,
                    ),
                  ),
                ),
              ),
              SpaceH(AppSize.w8),
              SizedBox(
                width: AppSize.w72,
                child: isDisable
                    ? Container(
                        height: AppSize.h34,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            AppSize.r4,
                          ),
                          border: Border.all(
                            color: context.themeColors.textfieldTextColor,
                          ),
                        ),
                      )
                    : CustomOutlinedTextfield(
                      maxTextLength: maxDigit,
                      keyboardType: TextInputType.number,
                      inputFormatters: inputFormatters??[
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                        textAlign: TextAlign.center,
                        isDense: true,
                        controller: controller,
                        isError: unitText == '0' && (isButtonClicked ?? false),
                      ),
              ),
              SpaceH(AppSize.w8),
              InkWell(
                key: increaseKey,
                splashColor: context.themeColors.scaffoldColor,
                highlightColor: context.themeColors.scaffoldColor,
                onTap: isDisable ? null : onIncreaseTap,
                child: Container(
                  height: AppSize.h40,
                  width: AppSize.w40,
                  decoration: BoxDecoration(
                    color: isDisable ? const Color.fromRGBO(224, 224, 224, 1) : context.themeColors.greenColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.add,
                      color: context.themeColors.whiteColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          // if(unitText == '0' && (isButtonClicked ?? false)) Column(
          //     children: [
          //       SpaceV(AppSize.h5),
          //       Row(
          //         children: [
          //           SpaceH(MediaQuery.of(context).size.width * 0.09),
          //           AppTextWidget('This field is required',
          //             style: context.textTheme.bodyMedium?.copyWith(
          //               color: context.themeColors.errorRedColor,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ],
          //   )
          // else const SizedBox(),
        ],
      ),
    );
  }
}
