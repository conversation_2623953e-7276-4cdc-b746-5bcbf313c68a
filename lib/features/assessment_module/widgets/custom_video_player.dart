import 'package:better_player_plus/better_player_plus.dart';
import 'package:breakingfree_v2/custom_widgets/app_cached_network_image.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/app_video_player/app_better_player.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/app_video_player/app_better_player_controller.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomVideoPlayer extends StatefulWidget {
  const CustomVideoPlayer({
    required this.file,
    required this.image,
    // required this.onTap,
    this.onPlayTap,
    super.key,
    this.onVideoEnded,
    this.showControls = true,
    this.showThumbnail = true,
    this.autoPlay = false,
    this.loop = false,
    this.fullscreen = true,
    this.isExpanded = false,
    this.isFromLightBuilb = false,
    this.isFromLightBuilb1,
    this.onVideo90PercentageEnded,
    this.isContain,
  });
  final String file;

  final String image;
  // final void Function() onTap;
  final void Function()? onPlayTap;
  final void Function()? isFromLightBuilb1;
  final VoidCallback? onVideoEnded; // Callback to notify when video ends
  final bool showControls;
  final bool showThumbnail;
  final bool autoPlay;
  final bool loop;
  final bool fullscreen;
  final bool isExpanded;
  final bool isFromLightBuilb;
  final VoidCallback? onVideo90PercentageEnded;
  final bool? isContain; 

  @override
  State<CustomVideoPlayer> createState() => _CustomVideoPlayerState();
}

class _CustomVideoPlayerState extends State<CustomVideoPlayer> {
  //late VideoPlayerController _controller;
  AppBetterPlayerController? appController;

  bool blockEvent = false;

  final deviceOrientationsOnFullScreen = <DeviceOrientation>[
    DeviceOrientation.landscapeRight,
    DeviceOrientation.landscapeLeft,
  ];

  // Set to default Orientation
  final deviceOrientationsAfterFullScreen = <DeviceOrientation>[
    DeviceOrientation.portraitUp,
  ];
  bool _hasTriggered90Percent = false;

  @override
  void initState() {
    super.initState();
    try {
      // _controller = VideoPlayerController.asset(widget.file)
      //   ..initialize().then((_) {
      //     setState(() {});
      //   });
      widget.image.tr().logD;
      appController = AppBetterPlayerController.networkUrl(
        url: widget.file,
        enableCache: true,
        betterPlayerConfiguration: BetterPlayerConfiguration(
          autoPlay: widget.autoPlay,
          fit: BoxFit.cover,
          looping: widget.loop,
          controlsConfiguration: BetterPlayerControlsConfiguration(
            showControls: false,
            enableOverflowMenu: false, playerTheme: BetterPlayerTheme.cupertino,

            controlBarColor: Colors.black.withOpacity(0.7), // <- black background for controls

            // progressBarBackgroundColor: Colors.green,
            showControlsOnInitialize: widget.showControls,
          ),
          deviceOrientationsAfterFullScreen: deviceOrientationsAfterFullScreen,
          deviceOrientationsOnFullScreen: deviceOrientationsOnFullScreen,
        ),
      );
      listenEvent();
    } catch (e) {
      //print(e);
    }
  }

  void listenEvent() {
    appController?.addEventsListener(
      (event) {
        switch (event.betterPlayerEventType) {
          case BetterPlayerEventType.changedPlayerVisibility:
            if (!appController!.isFullScreen && !blockEvent) {
              'BetterPlayerEventType.changedPlayerVisibility +++++ === ++'.logV;
              blockEvent = true;
              'BetterPlayerEventType.changedPlayerVisibility +++++ === ++111'.logV;

              if (widget.fullscreen) appController?.pause();
              if (widget.isFromLightBuilb == true) {
                'widget.isFromLightBuilb'.logV;
                widget.isFromLightBuilb1?.call();
              }
              appController?.setBetterPlayerControlsConfiguration(
                BetterPlayerControlsConfiguration(
                  showControls: false,
                  enableOverflowMenu: false, playerTheme: BetterPlayerTheme.cupertino,

                  controlBarColor: Colors.black.withOpacity(0.7), // <- black background for controls

                  // progressBarBackgroundColor: Colors.blue,
                  // controlBarHeight: AppSize.h100,
                ),
              );
              if (appController?.isVideoInitialized() ?? false) {
                appController?.seekTo(const Duration(milliseconds: 1));
              }
              setState(() {});
              blockEvent = false;
            }
          case BetterPlayerEventType.hideFullscreen:
            'BetterPlayerEventType.hideFullscreen +++++ === ++'.logV;

            if (widget.fullscreen) appController?.pause();
            appController?.setBetterPlayerControlsConfiguration(
              BetterPlayerControlsConfiguration(
                showControls: false,
                enableOverflowMenu: false, playerTheme: BetterPlayerTheme.cupertino,

                controlBarColor: Colors.black.withOpacity(0.7), // <- black background for controls

                // progressBarBackgroundColor: Colors.pink,
              ),
            );
            appController?.seekTo(const Duration(milliseconds: 1));
            setState(() {});
          case BetterPlayerEventType.play:
            'BetterPlayerEventType.play +++++ === ++'.logV;

            if (!appController!.isFullScreen) {
              '+++++ ==='.logV;
              appController?.setBetterPlayerControlsConfiguration(
                BetterPlayerControlsConfiguration(
                  showControls: widget.showControls,
                  controlBarColor: Colors.black.withOpacity(0.7), // <- black background for controls

                  enableOverflowMenu: false,
                  // controlBarHeight: AppSize.h100,
                  // progressBarBackgroundColor: Colors.orange,
                  playerTheme: BetterPlayerTheme.cupertino,
                ),
              );
              if (widget.fullscreen) appController?.enterFullScreen();
              setState(() {});
            }

            final duration = appController?.videoPlayerController?.value.duration;
            final position = appController?.videoPlayerController?.value.position;

            if (duration != null && position != null && duration.inMilliseconds > 0) {
              final watchedPercentage = position.inMilliseconds / duration.inMilliseconds;

              if (watchedPercentage >= 0.9 && !_hasTriggered90Percent) {
                _hasTriggered90Percent = true;
                widget.onVideoEnded?.call(); // Trigger once at 90%
              }
            }
          case BetterPlayerEventType.progress:
            'BetterPlayerEventType.progress +++++ === ++'.logV;
            // if (widget.isFromLightBuilb == true) {
            //   'widget.isFromLightBuilb'.logV;
            //   widget.isFromLightBuilb1?.call();
            // }
            final duration = appController?.videoPlayerController?.value.duration;
            final position = appController?.videoPlayerController?.value.position;

            if (duration != null && position != null && duration.inMilliseconds > 0) {
              final watchedPercentage = position.inMilliseconds / duration.inMilliseconds;

              if (watchedPercentage >= 0.9 && !_hasTriggered90Percent) {
                _hasTriggered90Percent = true;
                '>?>?>?>? true'.logV;
                widget.onVideo90PercentageEnded?.call();
                //widget.onVideoEnded?.call(); // Trigger once at 90%
              }
            }
          case BetterPlayerEventType.finished:
            widget.onVideoEnded?.call();
          case _:
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    '>?>?>? start'.logV;
    return Stack(
      children: [
        if (widget.isExpanded)
          Positioned.fill(
            child: FittedBox(
              fit: BoxFit.cover,
              child: SizedBox(
                height: 1080,
                width: 1920,
                child: AppBetterPlayer(
                  key: ValueKey(widget.file),
                  controller: appController!,
                ),
              ),
            ),
          )
        else
          AppBetterPlayer(
            key: ValueKey(widget.file),
            controller: appController!..setOverriddenFit(
              (widget.isContain ?? false) ? BoxFit.contain : BoxFit.cover,//BoxFit.fill,//BoxFit.covers
            ),
          ),
        if (!(appController!.isPlaying() ?? true) && widget.showThumbnail) ...[
          Positioned.fill(child: AppCachedNetworkImage(imageUrl: widget.image)),
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                'close ++++'.logV;
                if (appController!.isPlaying() ?? false) {
                  appController?.pause();
                } else {
                  appController?.play();
                }
                widget.onPlayTap?.call();
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                ),
                child: Container(
                  child: Assets.icons.playIcon.image(
                    height: AppSize.h32,
                    width: AppSize.h32,
                    color: context.themeColors.whiteColor,
                  ),
                ),
                // Light black transparent background
              ),
            ),
          ),
        ],
      ],
    );
  }

  @override
  void dispose() {
    appController?.dispose();
    super.dispose();
  }
}
