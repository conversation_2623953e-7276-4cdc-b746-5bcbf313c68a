// ignore_for_file: avoid_unused_constructor_parameters

import 'dart:io';

import 'package:better_player_plus/better_player_plus.dart';
import 'package:flutter/material.dart';

/// Common video player controller for the app
class AppBetterPlayerController extends BetterPlayerController {
  /// Constructor for the video player controller using a network url
  AppBetterPlayerController.networkUrl({
    required String url,
    BetterPlayerConfiguration? betterPlayerConfiguration,
    BetterPlayerCacheConfiguration? cacheConfiguration,
    BetterPlayerBufferingConfiguration? bufferingConfiguration,
    bool enableCache = false,
  }) : super(
          betterPlayerConfiguration ?? AppBetterPlayerConfiguration(),
          betterPlayerDataSource: BetterPlayerDataSource.network(
            Uri.parse(url).toString(),
            cacheConfiguration: /* !enableCache
                ? null
                : cacheConfiguration ??*/
                BetterPlayerCacheConfiguration(
              key: url,
              useCache: true,
              maxCacheSize: 1024 * 1024 * 1024,
              maxCacheFileSize: 25 * 1024 * 1024,
              preCacheSize: 5 * 1024 * 1024,
            ),
            bufferingConfiguration: bufferingConfiguration ??
                const BetterPlayerBufferingConfiguration(
                  minBufferMs: 2000,
                  maxBufferMs: 5000,
                  bufferForPlaybackMs: 1000,
                  bufferForPlaybackAfterRebufferMs: 1000,
                ),
          ),
        );

  /// Constructor for the video player controller using a file
  AppBetterPlayerController.file(File file, {BetterPlayerConfiguration? betterPlayerConfiguration})
      : super(
          betterPlayerConfiguration ?? AppBetterPlayerConfiguration(),
          // https://github.com/flutter/flutter/issues/40429#issuecomment-549746165
          betterPlayerDataSource: BetterPlayerDataSource.file(
            Platform.isIOS ? Uri.encodeFull(file.path) : file.path,
          ),
        );
}

/// Configuration for the better video player
class AppBetterPlayerConfiguration extends BetterPlayerConfiguration {
  /// Constructor for the better player configuration
  AppBetterPlayerConfiguration({super.looping})
      : super(
          fit: BoxFit.fitWidth,
          handleLifecycle: true,
          autoPlay: false,
          placeholder: const Center(child: CircularProgressIndicator()),
          placeholderOnTop: true,
          fullScreenByDefault: false,
          controlsConfiguration: const BetterPlayerControlsConfiguration(
            showControls: false,
            showControlsOnInitialize: false,
            // enableAudioTracks: false,
            // enableFullscreen: false,
            // enableMute: false,
            // enableOverflowMenu: false,
            // enablePip: false,
            // enablePlaybackSpeed: false,
            // enablePlayPause: false,
            // enableProgressBar: false,
            // enableProgressBarDrag: false,
            // enableProgressText: false,
            // enableQualities: false,
            // enableSkips: false,
            // enableSubtitles: false,
            // showControlsOnInitialize: false,
            playerTheme: BetterPlayerTheme.material,
          ),
        );
}
