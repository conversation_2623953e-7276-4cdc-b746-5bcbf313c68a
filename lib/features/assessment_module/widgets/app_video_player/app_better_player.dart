import 'package:better_player_plus/better_player_plus.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/app_video_player/app_better_player_controller.dart';
import 'package:flutter/material.dart';

/// Common video player for the app
class AppBetterPlayer extends StatelessWidget {
  /// Default constructor
  const AppBetterPlayer({required this.controller, super.key});

  /// The video player controller
  final AppBetterPlayerController controller;

  @override
  Widget build(BuildContext context) {
    return BetterPlayer(controller: controller,);
  }
}
