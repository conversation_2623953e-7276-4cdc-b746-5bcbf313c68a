Map<String, dynamic> drugData = {
  'adderall': {
    'milligrams': {
      'incr': 2.5,
      'min': 0,
      'max': 250,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'acamprosate': {
    'milligrams': {
      'incr': 333,
      'min': 0,
      'max': 1665,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 5,
    },
  },
  'AH-7921': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 100,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
    'hits': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
  },
  'alpha-pvp': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'amitriptyline': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 500,
    },
    'tablets': {
      'incr': 5,
      'min': 0,
      'max': 500,
    },
  },
  'amphetamines': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'buprenorphine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'butane': {
    'cans': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'huffs': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'co-codamol': {
    'milligrams': {
      'incr': 500,
      'min': 0,
      'max': 25000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'cocaine': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'codeine': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 2500,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'crack': {
    'rocks': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'diazepam': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'disulfiram': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'ecstasy': {
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'etizolam': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'fentanyl': {
    'microgram': {
      'incr': 10,
      'min': 0,
      'max': 5000,
    },
  },
  'gabapentin': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 3000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'GBL': {
    'millilitres': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'GHB': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'heroin': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'bags': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'ketamine': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'khat': {
    'leaves': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'lorezepam': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'MDPV': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'MSJ': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'marijuana': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'joints': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'mephedrone': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'mephobarbital': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 3000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'methadone': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 250,
    },
  },
  'methamphetamine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 9999,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'wraps': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'methoxatamine': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 150,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'modafinil': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 5000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'nalmefene': {
    'milligrams': {
      'incr': 18,
      'min': 0,
      'max': 500,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'naltrexone': {
    'milligrams': {
      'incr': 50,
      'min': 0,
      'max': 800,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'nitrazepam': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'nitrous-oxide': {
    'balloons': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'O-PCE': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 100,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'pcp': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 25,
    },
    'joints': {
      'incr': 1,
      'min': 0,
      'max': 25,
    },
  },
  'phenobarbital': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'pholcodeine': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 500,
    },
  },
  'pregabalin': {
    'milligrams': {
      'incr': 25,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'suboxone': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'synthetic-cannabis': {
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'joints': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'temazepam': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'tobacco': {
    'cigarettes': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'tramadol': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'zopiclon': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'xanax': {
    'milligrams': {
      'incr': 0.25,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'oxycontin': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 2000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'vicodin': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 10000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'clonazepam': {
    'milligrams': {
      'incr': 0.5,
      'min': 0,
      'max': 2000,
    },
    'microgram': {
      'incr': 0.25,
      'min': 0,
      'max': 10000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'triazolam': {
    'milligrams': {
      'incr': 0.125,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'ambien': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'lunesta': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'sonata': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'oxymorphone': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 1000,
    },
    'millilitres': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'morphine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'millilitres': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
    'microgram': {
      'incr': 50,
      'min': 0,
      'max': 1000,
    },
  },
  'ritalin': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'dexedrine': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'trenbolone': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'Testosterone': {
    'milligrams': {
      'incr': 50,
      'min': 0,
      'max': 1000,
    },
    'injection': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'Sustanon': {
    'milligrams': {
      'incr': 250,
      'min': 0,
      'max': 10000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'HGH (Human Growth Hormone)': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 10000,
    },
    'IU': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'HGH': {
    'milligrams': {
      'incr': 100,
      'min': 0,
      'max': 10000,
    },
    'IU': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'oxandrolone': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 10000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'ephedrine': {
    'milligrams': {
      'incr': 50,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'hydromorphone': {
    'milligrams': {
      'incr': 0.1,
      'min': 0,
      'max': 10,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'demerol': {
    'milligrams': {
      'incr': 50,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'alfentanil': {
    'milligrams': {
      'incr': 0.5,
      'min': 0,
      'max': 1000,
    },
    'millilitres': {
      'incr': 0.5,
      'min': 0,
      'max': 1000,
    },
  },
  'alpha-d2pv': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'bromadoline': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'bromazolam': {
    'milligrams': {
      'incr': 0.5,
      'min': 0,
      'max': 20,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
    'gummies': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'buvidal': {
    'milligrams': {
      'incr': 8,
      'min': 0,
      'max': 40,
    },
  },
  'caffeine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'drinks': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'carfentanil': {
    'milligrams': {
      'incr': 0.1,
      'min': 0,
      'max': 5,
    },
  },
  'desomorphine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 500,
    },
    'ampules': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'diclazepam': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
  },
  'flephedrone': {
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'flubromazolam': {
    'milligrams': {
      'incr': 0.1,
      'min': 0,
      'max': 20,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
  },
  'hysingla': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'ketazolam': {
    'milligrams': {
      'incr': 15,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'kratom': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'meprobamate': {
    'milligrams': {
      'incr': 200,
      'min': 0,
      'max': 4000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
  },
  'metandienone': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 100,
    },
  },
  'methylone': {
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'lines': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
    'grams': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'midazolam': {
    'milligrams': {
      'incr': 2.5,
      'min': 0,
      'max': 100,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'pentazocine': {
    'milligrams': {
      'incr': 5,
      'min': 0,
      'max': 1000,
    },
  },
  'phenibut': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 50,
    },
  },
  'sufentanil': {
    'microgram': {
      'incr': 0.1,
      'min': 0,
      'max': 10,
    },
    'millilitres': {
      'incr': 0.1,
      'min': 0,
      'max': 10,
    },
  },
  'tapentadol': {
    'milligrams': {
      'incr': 50,
      'min': 0,
      'max': 1000,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
  },
  'topiramate': {
    'milligrams': {
      'incr': 25,
      'min': 0,
      'max': 400,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 16,
    },
  },
  'U-47700': {
    'milligrams': {
      'incr': 0.1,
      'min': 0,
      'max': 5,
    },
  },
  'vape-marijuana': {
    'pens': {
      'incr': 1,
      'min': 0,
      'max': 3,
    },
    'cartridges': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
    'hits': {
      'incr': 1,
      'min': 0,
      'max': 1000,
    },
    'grams': {
      'incr': 0.1,
      'min': 0,
      'max': 50,
    },
  },
  'vape-nicotine': {
    'pens': {
      'incr': 1,
      'min': 0,
      'max': 5,
    },
    'cartridges': {
      'incr': 1,
      'min': 0,
      'max': 10,
    },
    'hits': {
      'incr': 1,
      'min': 0,
      'max': 1000,
    },
  },
  'vyvanse': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 140,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 14,
    },
  },
  'xelstrym': {
    'milligrams': {
      'incr': 0.5,
      'min': 0,
      'max': 72,
    },
    'patches': {
      'incr': 1,
      'min': 0,
      'max': 8,
    },
  },
  'xylazine': {
    'milligrams': {
      'incr': 1,
      'min': 0,
      'max': 2400,
    },
    'millilitres': {
      'incr': 1,
      'min': 0,
      'max': 2400,
    },
  },
  'zohydro': {
    'milligrams': {
      'incr': 10,
      'min': 0,
      'max': 200,
    },
    'tablets': {
      'incr': 1,
      'min': 0,
      'max': 20,
    },
  },
};
