import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:flutter/material.dart';

class DotIndicator extends StatelessWidget {
  const DotIndicator({required this.totalDots, required this.activeDotIndex, super.key});
  final int totalDots;
  final int activeDotIndex;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalDots, (index) {
        Color dotColor;
        if (index + 2 < activeDotIndex) {
          dotColor = context.themeColors.greenColor; // Completed stages
        } else if (index + 2 == activeDotIndex) {
          dotColor = context.themeColors.greenColor; // Current stage
        } else {
          dotColor = context.themeColors.greyColor.withOpacity(0.3); // Incomplete stages
        }
        return Container(
          margin: EdgeInsets.symmetric(horizontal: AppSize.w4),
          width: AppSize.w8,
          height: AppSize.h8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: dotColor,
          ),
        );
      }),
    );
  }
}
