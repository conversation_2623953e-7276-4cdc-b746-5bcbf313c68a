import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/us_drinking_helper_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class UsHelperBlocWidget extends StatelessWidget {
  const UsHelperBlocWidget({
    required this.length,
    required this.amount,
    required this.text,
    required this.title,
    required this.color,
    super.key,
  });

  final String title;
  final Color color;
  final String text;
  final String amount;
  final int length;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: AppSize.w18,
        top: AppSize.h6,
        bottom: AppSize.h6,
        right: AppSize.w22,
      ),
      width: context.width,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(AppSize.r30),
          bottomRight: Radius.circular(AppSize.r30),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SpaceV(AppSize.h6),
          AppTextWidget(
            title,
            style: context.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          SpaceV(AppSize.h10),
          ...List.generate(length, (index) {
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.6,
                      child: AppTextWidget(
                        getRowLabel(context: context, keyPath: text, index: index) ?? '',
                        style: context.textTheme.labelSmall,
                      ),
                    ),
                    AppTextWidget(
                      getRowAmount(context: context, keyPath: amount, index: index) ?? '',
                      style: context.textTheme.labelSmall?.copyWith(
                        fontWeight: FontWeight.w600,  
                      ),
                    ),
                  ],
                ),
                SpaceV(AppSize.h6),
              ],
            );
          }),
        ],
      ),
    );
  }
}
