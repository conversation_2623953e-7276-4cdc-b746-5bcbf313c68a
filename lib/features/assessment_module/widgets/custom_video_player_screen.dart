
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:flutter/material.dart';

class VideoPlayerScreen extends StatelessWidget {
  const VideoPlayerScreen({
    required this.videoList,
    required this.imageList,
    required this.navigationFunction, this.isFromLightBuilb = false,
    super.key,
    this.onVideoEnded,
    this.onVideo90PercentageEnded,
    this.onTap, this.isFromLightBuilb1,
    this.isContain = false,
  });
  final List<String> videoList;
  final List<String> imageList;
  final void Function() navigationFunction;
  final void Function()? onTap;
  final VoidCallback? onVideoEnded; // Callback to notify when video ends
  final VoidCallback? onVideo90PercentageEnded; 
  final bool isFromLightBuilb;
  final void Function()? isFromLightBuilb1;
  final bool isContain;

  @override
  Widget build(BuildContext context) {
    '===${videoList.first}'.logD;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        clipBehavior: Clip.hardEdge,
        alignment: Alignment.center,
        //margin: EdgeInsets.only(left: 40.w, right: 40.w, top: 18.h, bottom: 8.h),
        // height: 120.h,
        // width: 100.w,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25), // Slightly darker shadow
              blurRadius: AppSize.r4,
              spreadRadius: 2,
              offset: Offset(
                AppSize.w4,
                AppSize.w4,
              ),
            ),
          ],
          borderRadius: BorderRadius.circular(AppSize.r12),
        ),
        /// Don't test video player when running widget tests.
        child: isWidgetTest
            ? const SizedBox()
            : CustomVideoPlayer(
                onVideo90PercentageEnded: onVideo90PercentageEnded,
                key: const Key('video_player'),
                file: videoList.first,
                image: imageList.first,
                // onTap: navigationFunction,
                isFromLightBuilb:isFromLightBuilb,
                isFromLightBuilb1:isFromLightBuilb1,
                onVideoEnded: onVideoEnded,
                onPlayTap: onTap,
                isContain: isContain,
              ),
      ),
    );
  }
}
