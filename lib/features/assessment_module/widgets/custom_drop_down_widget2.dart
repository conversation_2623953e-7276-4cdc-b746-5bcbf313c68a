import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:breakingfree_v2/res/validator/global_text_validator.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomDropDownListWidget2 extends StatelessWidget {
  CustomDropDownListWidget2({
    required this.list,
    required this.donotWishCheckBoxValue,
    required this.selectedValue,
    this.onChanged,
    super.key,
    this.labelText,
    this.isButtonClicked,
    this.isChecked,
    this.isSelectedValuechange,
    this.isError,
    this.isInlineError = true,
  });

  final List<String> list;
  final void Function(String?)? onChanged;
  final ValueNotifier<bool> donotWishCheckBoxValue;
  String? selectedValue;
  final String? labelText; // Declare labelText variable
  final bool? isButtonClicked;
  final bool? isChecked;
  final bool? isSelectedValuechange;
  final bool? isError;
  final bool isInlineError;

  @override
  Widget build(BuildContext context) {
  
    return ValueListenableBuilder<bool>(
      valueListenable: donotWishCheckBoxValue,
      builder: (context, isEthnicCheck, child) {
        return Column(
          children: [
            DropdownButtonFormField2<String>(
              isExpanded: true,
              value: isEthnicCheck ? null : selectedValue, // Ensure dropdown resets when checked
              decoration: InputDecoration(
                labelStyle: context.textTheme.titleSmall?.copyWith(
                  color: context.themeColors.textfieldTextColor,
                  overflow: TextOverflow.ellipsis
                ),
                labelText: labelText,
                contentPadding: EdgeInsets.symmetric(vertical: AppSize.h5,horizontal: AppSize.w14),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSize.r4),
                  borderSide: BorderSide(
                    color: context.themeColors.greyColor,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSize.r4),
                  borderSide: BorderSide(
                    color: (isError ?? false) ? context.themeColors.errorRedColor : context.themeColors.greyColor,
                  ),
                ),
              ),
              hint: Text(
                CoreLocaleKeys.labelsSelectPlaceholder.tr(),
                style: context.textTheme.titleSmall?.copyWith(
                  color: context.themeColors.textfieldTextColor,
                ),
              ),
            
              items: list.map((item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    // maxLines: 2,
                    style: context.textTheme.titleSmall?.copyWith(
                      color: isEthnicCheck ? const Color.fromRGBO(199, 199, 199, 1) : context.themeTextColors.text,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                );
              }).toList(),
              // validator: (value) {
              //   if (value == null && !isEthnicCheck) {
              //     return 'Please select a value.';
              //   }
              //   return null;
              // },
              onChanged: isEthnicCheck
                  ? null
                  : onChanged ??
                      (String? newValue) {
                        // Allow value selection if ethnic check is not checked
            
                        selectedValue = newValue;
                        log('Selected value: $newValue');
                      },
              buttonStyleData: const ButtonStyleData(
                padding: EdgeInsets.only(right: 8),
              ),
              iconStyleData: const IconStyleData(
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.black45,
                ),
              ),
              dropdownStyleData: DropdownStyleData(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppSize.r4),
                  color: Colors.white,
                ),
                width: MediaQuery.of(context).size.width * 0.7,
                offset: const Offset(-10, 0),
              ),
              menuItemStyleData: MenuItemStyleData(
                padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
              ),
            ),
            if (isInlineError == true && (isError ?? false))
              CustomErrorWidget(
                spacing: AppSize.h5,
                errorMessgaeText: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
              )
            else
              const SizedBox(),
          ],
        );
      },
    );
  }
}
