import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_drop_down_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DrugSelectionWidget extends StatelessWidget {
  const DrugSelectionWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
      builder: (ctx, state) {
        final assessmentCubit = ctx.read<AssessmentDrugCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        return ValueListenableBuilder(
          valueListenable: assessmentCubit.drugSelectionoVisible,
          builder: (context, value, child) {
            return ValueListenableBuilder(
              valueListenable: assessmentCubit.drugSelectionDialogVisible,
              builder: (context,val,child) {
                return Visibility(
                  visible: value,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color.fromRGBO(235, 235, 235, 1),
                      border: Border.all(
                        color: const Color(0xFFBDBDBD),
                      ),
                      borderRadius: BorderRadius.circular(AppSize.r4),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: AppSize.h10, horizontal: AppSize.w10),
                      child: Column(
                        children: [
                          if(state.drugDetailList.isNotEmpty)
                            QuestionRowWidget(
                            questionText: AssessmentLocaleKeys.drugsQuestionsListLabelsDrug.tr(),
                          )
                          else
                            QuestionRowWidget(
                            questionText: AssessmentLocaleKeys.drugsQuestionsListLabelsDrugFirst.tr(),
                          ),

                          SpaceV(AppSize.h16),
                          Padding(
                            padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                            child: SizedBox(
                              height: AppSize.h44,
                              child: CustomDropDownListWidget(
                                list: [...DrugAndUnitList.drugList]
  ..sort((a, b) => assessmentCubit.normalize(a).compareTo(assessmentCubit.normalize(b))),
                                //list: DrugAndUnitList.drugList..sort(),
                                donotWishCheckBoxValue: ValueNotifier(false),
                                onChanged: (String? newValue) {

                                  assessmentCubit
                                    ..updateSelecteDrugs(newValue)
                                    ..updateSelecteUnit(null);
                                  assessmentCubit.drugSelectionDialogVisible.value = false;
                                },
                                selectedValue: state.selecteDrugsValue != null &&
                                        state.selecteDrugsValue!.isNotEmpty &&
                                        DrugAndUnitList.drugList.contains(state.selecteDrugsValue)
                                    ? state.selecteDrugsValue
                                    : null,
                              ),
                            ),
                          ),
                          SpaceV(AppSize.h20),
                          if (state.selecteDrugsValue != null && !assessmentCubit.drugSelectionDialogVisible.value) ...{
                            QuestionRowWidget(
                              questionText: AppCommonFunctions.getFormattedTranslation(
                                AssessmentLocaleKeys.drugsQuestionsListLabelsUnit,
                                {
                                  'drug': assessmentCubit1.formatString(state.selecteDrugsValue ?? ''),
                                },
                              ),
                            ),
                            SpaceV(AppSize.h16),
                            Padding(
                              padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                              child: SizedBox(
                                height: AppSize.h44,
                                child: CustomDropDownListWidget(
                                  
                                  //Todo : update list after api update

                                  list: assessmentCubit
                                      .getMeasurementUnitsForDrug(state.selecteDrugsValue)
                                      .map(
                                        (unit) => DrugAndUnitList.units[unit] ?? '',
                                      )
                                      .toList(),
                                  donotWishCheckBoxValue: ValueNotifier(false),
                                  onChanged: assessmentCubit.updateSelecteUnit,
                                  selectedValue: state.selecteUnitValue != null &&
                                          state.selecteUnitValue!.isNotEmpty &&
                                          assessmentCubit
                                              .getMeasurementUnitsForDrug(state.selecteDrugsValue)
                                              .map(
                                                (unit) => DrugAndUnitList.units[unit] ?? '',
                                              )
                                              .toList()
                                              .contains(state.selecteUnitValue)
                                      ? state.selecteUnitValue
                                      : null, // Reset if not valid
                                ),
                              ),
                            ),
                            SpaceV(AppSize.h20),
                            if (state.selecteUnitValue != null && DrugAndUnitList.unitList1.contains(state.selecteUnitValue) && !assessmentCubit.drugSelectionDialogVisible.value) ...{
                              QuestionRowWidget(
                                questionText: AppCommonFunctions.getFormattedTranslation(
                                  AssessmentLocaleKeys.drugsQuestionsListLabelsAmount,
                                  {
                                    'drug': assessmentCubit1.formatString(state.selecteDrugsValue ?? ''),
                                    'unit': assessmentCubit1.formatString(state.selecteUnitValue ?? ''),
                                  },
                                ),

                                //  AssessmentLocaleKeys.drugsQuestionsListLabelsAmount.tr(
                                //   namedArgs: {
                                //     'drug': state.selecteDrugsValue ?? '',
                                //     'unit': state.selecteUnitValue ?? '',
                                //   },
                                // ),
                              ),
                              SpaceV(AppSize.h20),
                              CustomAgeSelcetionWidget(
                                controller: assessmentCubit.drugamountController,
                                onIncreaseTap: assessmentCubit.increaseDrugAmountValue,
                                onDecreaseTap: assessmentCubit.decreaseDrugAmountValue,
                              ),
                              SpaceV(AppSize.h30),
                              QuestionRowWidget(
                                questionText: AppCommonFunctions.getFormattedTranslation(
                                  AssessmentLocaleKeys.drugsQuestionsListLabelsFrequency,
                                  {
                                    'drug': assessmentCubit1.formatString(state.selecteDrugsValue ?? ''),
                                  },
                                ),

                                //  AssessmentLocaleKeys.drugsQuestionsListLabelsFrequency.tr(
                                //   namedArgs: {
                                //     'drug': state.selecteDrugsValue ?? '',
                                //   },
                                // ),
                              ),
                              SpaceV(AppSize.h20),
                              CustomAgeSelcetionWidget(
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  TextInputFormatter.withFunction(
                                        (oldValue, newValue) {
                                      if (newValue.text.isEmpty) {
                                        return newValue;
                                      }

                                      final value = int.tryParse(newValue.text);
                                      if (value != null && value <= 7) {
                                        return newValue;
                                      }

                                      return oldValue;
                                    },
                                  ),
                                ],
                                controller: assessmentCubit.drugfrequencyController,
                                onIncreaseTap: assessmentCubit.increaseDrugFrequencyValue,
                                onDecreaseTap: assessmentCubit.decreaseDrugFrequencyValue,
                              ),
                              SpaceV(AppSize.h20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ValueListenableBuilder(
                                    valueListenable: assessmentCubit.drugSelectionoVisible,
                                    builder: (context,drugSelection,child) {
                                      return CustomRoundedButton(
                                        title: AssessmentLocaleKeys.drugsQuestionsListButtonsCancel.tr(),
                                        fillColor: context.themeColors.redColor,
                                        onTap: () {
                                          assessmentCubit.clearData();
                                          if(state.drugDetailList.isEmpty) {
                                            assessmentCubit.drugSelectionoVisible.value = true;
                                            assessmentCubit.drugSelectionDialogVisible.value = true;
                                          }else{
                                            assessmentCubit.drugSelectionoVisible.value = false;
                                          }
                                        },
                                      );
                                    },
                                  ),
                                  SpaceH(AppSize.w10),
                                  CustomRoundedButton(
                                    title: AssessmentLocaleKeys.drugsQuestionsListButtonsSave.tr(),
                                    onTap: assessmentCubit.addDrygData,
                                  ),
                                ],
                              ),
                            },
                          },
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}

class DrugDetail {
  DrugDetail({
    this.drug,
    this.unit,
    this.amount,
    this.frequency,
  });

  // Factory constructor to create a DrugDetail from ListButane
  factory DrugDetail.fromListButane(ListButane listButane) {
    return DrugDetail(
      drug: listButane.drug,
      unit: listButane.unit,
      amount: listButane.amount?.toString(), // Convert amount to String
      frequency: listButane.frequency?.toString(), // Convert frequency to String
    );
  }

  final String? drug;
  final String? unit;
  final String? amount;
  final String? frequency;
}
