import 'package:breakingfree_v2/custom_widgets/app_svg_picture_asset.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:flutter/material.dart';

class CustomNotificationRowWidget extends StatelessWidget {
  const CustomNotificationRowWidget({required this.imageUrl,required this.text,super.key});
  final String imageUrl;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
                                        children: [
                                          AppSvgAsset(svgAsset: imageUrl,size: AppSize.sp36,),
                                          SpaceH(AppSize.h14),
                                          Expanded(
                                            child: AppTextWidget(
                                              text,
                                              style: context.textTheme.titleSmall,
                                            ),
                                          )
                                        ],
                                      );
  }
}