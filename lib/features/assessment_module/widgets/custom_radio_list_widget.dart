import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class CustomRadioListWidget extends StatelessWidget {
  const CustomRadioListWidget({
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    this.isButtonClicked = false,
    super.key,
    this.thumbColor,
    this.padding,
    this.height,
    this.fontSize,
    this.valueKey,
    this.isError,
    this.isMultiOption,
  });
  final bool? isMultiOption;

  final List<String> options;
  final String selectedValue;
  final void Function(String?) onChanged;
  final Color? thumbColor;
  final bool isButtonClicked;
  final EdgeInsets? padding;
  final double? height;
  final double? fontSize;
  final String? valueKey;
  final bool? isError;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: options.map((option) {
            return Container(
              height: height ?? AppSize.h36,
              padding: padding ?? EdgeInsets.symmetric(horizontal: AppSize.w22),
              child: InkWell(
                onTap: () => onChanged(option),
                splashColor: Colors.transparent,
                child: Row(
                  children: [
                    Transform.scale(
                      scale: 1.1,
                      child: Radio<String>(
                        key: valueKey != null ? Key('${option}_$valueKey') : null,
                        value: option,
                        groupValue: selectedValue,
                        onChanged: onChanged,
                        activeColor: context.themeColors.greenColor,
                        fillColor: WidgetStatePropertyAll(
                          (isError ?? false) ? context.themeColors.errorRedColor : context.themeColors.greenColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        splashColor: Colors.transparent,
                        onTap: () => onChanged(option),
                        child: AppTextWidget(
                          option.tr(),
                          style: context.textTheme.titleSmall?.copyWith(
                            fontSize: fontSize ?? AppSize.sp13,
                            height: 1.2
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        if (isError ?? false)
          Column(
            children: [
              SpaceV(
                AppSize.h8,
              ),
              Row(
                children: [
                  if (isMultiOption ?? false)
                    SpaceH(
                      MediaQuery.of(context).size.width * 0.035,
                    )
                  else
                    SpaceH(
                      MediaQuery.of(context).size.width * 0.095,
                    ),
                  Icon(
                    Icons.error_outline,
                    color: context.themeColors.errorRedColor,
                    size: AppSize.r18,
                  ),
                  SpaceH(AppSize.h5),
                  AppTextWidget(
                    AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                    style: context.textTheme.titleSmall?.copyWith(
                      color: context.themeColors.errorRedColor,
                      fontWeight: FontWeight.w400,
                      fontSize: AppSize.sp12,
                    ),
                  ),
                ],
              ),
            ],
          )
        else
          const SizedBox(),
      ],
    );
  }
}
//isButtonClicked && selectedValue.isEmpty
