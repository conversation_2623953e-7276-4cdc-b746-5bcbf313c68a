import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/api_services/api_function.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

final class AssessmentRepository {
  Future<AssessmentModel?> putAssessmentRecoveryProgram({
    required dynamic age,
    required dynamic gender,
    required dynamic ethnicity,
    required int addictionCase,
    required int? specialAddiction,
    required String? name,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'age': age ?? false,
          'gender': gender ?? false,
          'ethnicity': ethnicity ?? false,
          'name': name ?? '',
          'addictionCase': addictionCase,
          if (specialAddiction != null) 'specialAddiction': specialAddiction,
        },
        apiName: EndPoints.recoveryProgram,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putAssessmentLife({
    required int quality,
    required int health,
    required int activities,
    required int relationships,
    required int work,
    required int difficulties,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'quality': quality,
          'health': health,
          'activities': activities,
          'relationships': relationships,
          'work': work,
          'difficulties': difficulties,
          'rate': rate,
        },
        apiName: EndPoints.userLife,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putAssessmentDifficultSituations({
    required int conflict,
    required int work,
    required int money,
    required int risks,
    required int pressure,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'conflict': conflict,
          'work': work,
          'money': money,
          'risks': risks,
          'pressure': pressure,
          'rate': rate,
        },
        apiName: EndPoints.difficultSitutation,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putAssessmentNegativeThoughts({
    required int good,
    required int control,
    required int health,
    required int cope,
    required int trust,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'good': good,
          'control': control,
          'health': health,
          'cope': cope,
          'trust': trust,
          'rate': rate,
        },
        apiName: EndPoints.negativeThought,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putPhysicalSensation({
    required int cravings,
    required int shakes,
    required int cramps,
    required int nausea,
    required int tiredness,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'craving': cravings,
          'shakes': shakes,
          'cramps': cramps,
          'nausea': nausea,
          'tiredness': tiredness,
          'rate': rate,
        },
        apiName: EndPoints.physicalSenstation,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putUnhelpfulBehaviour({
    required int aggressive,
    required int avoid,
    required int active,
    required int care,
    required int police,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'aggressive': aggressive,
          'avoid': avoid,
          'active': active,
          'care': care,
          'police': police,
          'rate': rate,
        },
        apiName: EndPoints.unhelpfulBehaviour,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putLifeStyle({
    required int health,
    required int work,
    required int leisure,
    required int relationships,
    required int housing,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'health': health,
          'work': work,
          'leisure': leisure,
          'relationships': relationships,
          'housing': housing,
          'rate': rate,
        },
        apiName: EndPoints.lifeStyle,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putEmotionalImpact({
    required int nervous,
    required int worry,
    required int down,
    required int bad,
    required int interest,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'nervous': nervous,
          'worry': worry,
          'down': down,
          'bad': bad,
          'interest': interest,
          'rate': rate,
        },
        apiName: EndPoints.emotionalImpact,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putDrinking({
    required int units,
    required int days,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'units': units,
          'days': days,
          'rate': rate,
        },
        apiName: EndPoints.drinking,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putDrinkingFeeling({
    required int control,
    required int anxious,
    required int worry,
    required int will,
    required int difficulty,
    //  required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'control': control,
          'anxious': anxious,
          'worry': worry,
          'will': will,
          'difficulty': difficulty,
          // 'rate': rate,
        },
        apiName: EndPoints.drinkingFeeling,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putdrinkingGoal({
    required int units,
    required int freeDays,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'units': units,
          'freeDays': freeDays,
        },
        apiName: EndPoints.drinkingGoal,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putdrug({
    required Map<String, dynamic> list,
    required int rate,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'list': list,
          'rate': rate,
        },
        apiName: EndPoints.drug,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putdrugFeeling({
    required int control,
    required int anxious,
    required int worry,
    required int will,
    required int difficulty,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'control': control,
          'anxious': anxious,
          'worry': worry,
          'will': will,
          'difficulty': difficulty,
        },
        apiName: EndPoints.drugFeeling,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        '>?>?>?>?>? data = $data'.logV;
        if (response.statusCode == 200 && data?['success'] == true) {
          '>?>?>?>?>? response = 200 = '.logV;
          response.data.logV;
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> putdrugGoal({
    required double units,
    required int freeDays,
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        {
          'units': units,
          'freeDays': freeDays,
        },
        apiName: EndPoints.drugGoal,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> thankYou({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        <String, String>{},
        apiName: EndPoints.thankYou,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> assessmentVideo({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        <String, String>{},
        apiName: EndPoints.assessmentVideo,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }

  Future<AssessmentModel?> bridgingVideo({
    required BuildContext? context,
  }) async {
    try {
      final response = await APIFunction.postAPICall(
        <String, String>{},
        apiName: EndPoints.bridgingVideo,
        context: context,
      );

      if (response is Response<Map<String, dynamic>>) {
        final data = response.data;
        if (response.statusCode == 200 && data?['success'] == true) {
          return AssessmentModel.fromJson(response.data!);
        } else {
          CustomSnackbar.showErrorSnackBar(
            message: data?['message'] as String,
          );
          return null;
        }
      } else {
        if (response is DioException) {
          CustomSnackbar.showErrorSnackBar(
            message: Map<String, dynamic>.from(response.response!.data as Map)['message'] as String,
          );
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error logging in: $e');
      return null;
    }
  }
}
