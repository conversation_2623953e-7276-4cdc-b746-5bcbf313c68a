import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_feeling/assessment_drinking_feeling_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class AssessmentDrinkingFeelingPage extends StatelessWidget {
  const AssessmentDrinkingFeelingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrinkingFeelingCubit, AssessmentDrinkingFeelingState>(
      builder: (ctx, state) {
        final assessmentCubit = ctx.read<AssessmentDrinkingFeelingCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        return AppScaffold(
          isManuallyPaused: assessmentCubit.isManuallyPaused,
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            onSuffixTap: () {
              assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
          infoAudioUrl: ValueNotifier(AssessmentLocaleKeys.drinkingFeelingAudio.tr()),
          resizeToAvoidBottomInset: false,
          body: ValueListenableBuilder(
            valueListenable: assessmentCubit.isDrinkingFeelingButtonClicked,
            builder: (context, value, child) {
              return AbsorbPointer(
                absorbing: state.isApiLoading,
                child: Column(
                  children: [
                    SpaceV(AppSize.h4),
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Padding(
                                  padding: EdgeInsets.only(right: AppSize.w4),
                                  child: CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(
                                      minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.only(left: AppSize.w24,
                                        right: AppSize.w24,
                                        bottom: AppSize.h20),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment
                                          .spaceBetween,
                                      children: [
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment
                                              .start,
                                          children: [
                                            const CustomHeader(),
                                            SpaceV(AppSize.h6),
                                            AppTextWidget(
                                              AssessmentLocaleKeys
                                                  .drinkingFeelingSubtitle.tr(),
                                              style: context.textTheme.titleSmall
                                                  ?.copyWith(
                                                color: context.themeColors
                                                    .darkGreyColor,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            QuestionRowWidget(
                                              questionText: AssessmentLocaleKeys
                                                  .drinkingFeelingQuestionsControlLabel
                                                  .tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentCubit.controlKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentCubit
                                                    .control,
                                                builder: (context, control, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: assessmentCubit
                                                        .isDrinkingFeelingButtonClicked
                                                        .value,
                                                    options: assessmentCubit
                                                        .controlList,
                                                    selectedValue: control,
                                                    isError: assessmentCubit.isDrinkingFeelingButtonClicked.value && control.isEmpty,
                                                    onChanged: (newValue) {
                                                      assessmentCubit.control.value =
                                                          newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText: AssessmentLocaleKeys
                                                  .drinkingFeelingQuestionsAnxiousLabel
                                                  .tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentCubit.anxiousKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentCubit
                                                    .anxious,
                                                builder: (context, anxious, child) {
                                                  return CustomRadioListWidget(
                                                    isError: assessmentCubit.isDrinkingFeelingButtonClicked.value && anxious.isEmpty,
                              
                                                    isButtonClicked: assessmentCubit
                                                        .isDrinkingFeelingButtonClicked
                                                        .value,
                                                    options: assessmentCubit
                                                        .anxiousList,
                                                    selectedValue: anxious,
                                                    // Safely access first item
                                                    onChanged: (newValue) {
                                                      assessmentCubit.anxious.value =
                                                          newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText: AssessmentLocaleKeys
                                                  .drinkingFeelingQuestionsWorryLabel
                                                  .tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentCubit.worryKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentCubit
                                                    .worry,
                                                builder: (context, worry, child) {
                                                  return CustomRadioListWidget(
                                                    isError: assessmentCubit.isDrinkingFeelingButtonClicked.value && worry.isEmpty,
                              
                                                    isButtonClicked: assessmentCubit
                                                        .isDrinkingFeelingButtonClicked
                                                        .value,
                                                    options: assessmentCubit
                                                        .worryList,
                                                    selectedValue: worry,
                                                    // Safely access first item
                                                    onChanged: (newValue) {
                                                      assessmentCubit.worry.value =
                                                          newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText: AssessmentLocaleKeys
                                                  .drinkingFeelingQuestionsWillLabel
                                                  .tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentCubit.willKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentCubit.will,
                                                builder: (context, will, child) {
                                                  return CustomRadioListWidget(
                                                    isError: assessmentCubit.isDrinkingFeelingButtonClicked.value && will.isEmpty,
                              
                                                    isButtonClicked: assessmentCubit
                                                        .isDrinkingFeelingButtonClicked
                                                        .value,
                                                    options: assessmentCubit.willList,
                                                    selectedValue: will,
                                                    // Safely access first item
                                                    onChanged: (newValue) {
                                                      assessmentCubit.will.value =
                                                          newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText:
                                              AssessmentLocaleKeys
                                                  .drinkingFeelingQuestionsDifficultyLabel
                                                  .tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentCubit.difficultyKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentCubit
                                                    .difficulty,
                                                builder: (context, difficulty,
                                                    child) {
                                                  return CustomRadioListWidget(
                                                    isError: assessmentCubit.isDrinkingFeelingButtonClicked.value && difficulty.isEmpty,
                              
                                                    isButtonClicked: assessmentCubit
                                                        .isDrinkingFeelingButtonClicked
                                                        .value,
                                                    options: assessmentCubit
                                                        .difficultyList,
                                                    selectedValue: difficulty,
                                                    // Safely access first item
                                                    onChanged: (newValue) {
                                                      assessmentCubit.difficulty
                                                          .value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                          ],
                                        ),
                                        MultiValueListenableBuilder(
                                          valueListenables: [
                                            assessmentCubit.control,
                                            assessmentCubit.anxious,
                                            assessmentCubit.worry,
                                            assessmentCubit.will,
                                            assessmentCubit.difficulty,
                                          ],
                                          builder: (BuildContext context,
                                              List<dynamic> values, Widget? child) {
                                            // Destructure values for better readability
                                            final controlValue = values[0];
                                            final anxiousValue = values[1];
                                            final worryValue = values[2];
                                            final willValue = values[3];
                                            final difficultyValue = values[4];
                              
                                            // Check if any value is empty
                                            final isButtonEnabled = controlValue !=
                                                '' &&
                                                anxiousValue != '' &&
                                                worryValue != '' &&
                                                willValue != '' &&
                                                difficultyValue != '';
                              
                                            return CustomButton(
                                              key: const Key('next_btn'),
                                              disableColor: context.themeColors
                                                  .blueColor.withOpacity(0.1),
                                              title: CoreLocaleKeys.buttonsNext
                                                  .tr(),
                                              inProgress: state.isApiLoading,
                                              padding: EdgeInsets.zero,
                                              onTap: () async {
                                                assessmentCubit.isDrinkingFeelingButtonClicked.value = true; // Set flag to true when button is clicked
                                                if (assessmentCubit.control.value.isEmpty) {
                                                  await AppCommonFunctions.scrollToKey(assessmentCubit.controlKey);
                                                  return;
                                                }
                                                if (assessmentCubit.anxious.value.isEmpty) {
                                                  await AppCommonFunctions.scrollToKey(assessmentCubit.anxiousKey);
                                                  return;
                                                }
                                                if (assessmentCubit.worry.value.isEmpty) {
                                                  await AppCommonFunctions.scrollToKey(assessmentCubit.worryKey);
                                                  return;
                                                }
                                                if (assessmentCubit.will.value.isEmpty) {
                                                  await AppCommonFunctions.scrollToKey(assessmentCubit.willKey);
                                                  return;
                                                }
                                                if (assessmentCubit.difficulty.value.isEmpty) {
                                                  await AppCommonFunctions.scrollToKey(assessmentCubit.difficultyKey);
                                                  return;
                                                }
                                                if (!isButtonEnabled) {
                                                  // assessmentCubit
                                                  //     .isDrinkingFeelingButtonClicked
                                                  //     .value = true;
                                                } else {
                                                  await assessmentCubit
                                                      .putDrinkingFeelingAPI(
                                                    context: context,
                                                    control: assessmentCubit
                                                        .controlList.indexOf(
                                                        assessmentCubit.control
                                                            .value),
                                                    anxious: assessmentCubit
                                                        .anxiousList.indexOf(
                                                        assessmentCubit.anxious
                                                            .value),
                                                    worry: assessmentCubit.worryList
                                                        .indexOf(
                                                        assessmentCubit.worry
                                                            .value),
                                                    will: assessmentCubit.willList
                                                        .indexOf(
                                                        assessmentCubit.will.value),
                                                    difficulty: assessmentCubit
                                                        .difficultyList.indexOf(
                                                        assessmentCubit.difficulty
                                                            .value),
                                                  );
                                                }
                                              },
                                              isBottom: true,
                                              color: isButtonEnabled ? context
                                                  .themeColors.blueColor : context
                                                  .themeColors.blueColor
                                                  .withOpacity(0.7),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            }
            ),
         );
        },
    );

   }
}

