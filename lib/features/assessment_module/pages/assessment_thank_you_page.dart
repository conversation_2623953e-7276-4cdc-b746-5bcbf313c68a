import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentThankYouPage extends StatelessWidget {
  const AssessmentThankYouPage({super.key});

  @override
  Widget build(BuildContext context) {
    'AssessmentThankYouPage assessment = ${Injector.instance<AppDB>().userModel?.user.assessment?.toJson()}'.logV;
    'AssessmentThankYouPage user = ${Injector.instance<AppDB>().userModel?.user.toJson()}'.logV;
    return BlocBuilder<AssessmentCubit, AssessmentState>(
      builder: (ctx, state) {
        return AppScaffold(
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            onSuffixTap: () {
              ctx.read<AssessmentCubit>().isAudioPannelVisible.value =
                  !ctx.read<AssessmentCubit>().isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible: ctx.read<AssessmentCubit>().isAudioPannelVisible,
          infoAudioUrl: ValueNotifier(AssessmentLocaleKeys.thankYouAudio.tr()),
          resizeToAvoidBottomInset: false,
          body: AbsorbPointer(
            absorbing: state.isThankYouAPILoading,
            child: Column(
              children: [
                SpaceV(AppSize.h4),
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constrains) {
                      return Padding(
                                  padding: EdgeInsets.only(right: AppSize.w4),
                                  child: CustomRawScrollbar(
                          child: SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: constrains.maxHeight),
                              child: Padding(
                                padding: EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, bottom: AppSize.h20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      children: [
                                        const CustomHeader(),
                                        SpaceV(AppSize.h6),
                                        AppTextWidget(
                                          (DynamicAssetLoader.getNestedValue(
                                            AssessmentLocaleKeys.thankYouText,
                                            context,
                                          ) as List)
                                              .cast<String>()
                                              .join('\n\n'),
                                          style: context.textTheme.titleSmall?.copyWith(),
                                        ),
                                        SpaceV(AppSize.h20),
                                        SpaceV(AppSize.h30),
                                      ],
                                    ),
                                    CustomButton(
                                      padding: EdgeInsets.zero,
                                      key: const Key('next_btn'),
                                      inProgress: state.isThankYouAPILoading,
                                      disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                      onTap: () async {
                                        await ctx.read<AssessmentCubit>().thankYouAPI(context: context);
                                      },
                                      isBottom: true,
                                      color: context.themeColors.blueColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
