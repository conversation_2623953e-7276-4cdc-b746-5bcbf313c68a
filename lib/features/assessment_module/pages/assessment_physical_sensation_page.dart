import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_physical_senstion/assessment_physical_senstation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentPhysicalSensationPage extends StatelessWidget {
  const AssessmentPhysicalSensationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentPhysicalSenstationCubit, AssessmentPhysicalSenstationState>(
      builder: (ctx, state) {
        final assessmentPhysicalSentationCubit = ctx.read<AssessmentPhysicalSenstationCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();

        return ValueListenableBuilder(
          valueListenable: assessmentPhysicalSentationCubit.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: assessmentPhysicalSentationCubit.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              infoAudioUrl: assessmentPhysicalSentationCubit.infoAudioUrl,
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: assessmentPhysicalSentationCubit.isPhysicalSensationClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: CustomRawScrollbar(
                                  key: PageStorageKey('3'),
                                  child:  SingleChildScrollView(
                                        key: PageStorageKey('3'),
                                        child: Column(
                                          children: [
                                            ConstrainedBox(
                                              constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                              child: Padding(
                                                padding: EdgeInsets.only(
                                                  left: AppSize.w24,
                                                  right: AppSize.w24,
                                                  bottom: AppSize.h20,
                                                ),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Column(
                                                      children: [
                                                        const CustomHeader(
                                                        ),
                                                        SpaceV(AppSize.h6),
                                                        AppTextWidget(
                                                          AssessmentLocaleKeys.physicalSensationsSubtitle.tr(),
                                                          style: context.textTheme.titleSmall?.copyWith(
                                                            color: context.themeColors.darkGreyColor,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        QuestionRowWidget(
                                                          key: assessmentPhysicalSentationCubit.cravingsKey,
                                                          questionText: AssessmentLocaleKeys
                                                              .physicalSensationsQuestionsCravingLabel
                                                              .tr(),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: CustomAssessmentButton(
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                            currentState: assessmentPhysicalSentationCubit.cravingsState,
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            onNoTap: () {
                                                              assessmentPhysicalSentationCubit.cravingsState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentPhysicalSentationCubit.cravingsState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h26),
                                                        QuestionRowWidget(
                                                          key: assessmentPhysicalSentationCubit.shakesKey,
                                                          questionText: AssessmentLocaleKeys
                                                              .physicalSensationsQuestionsShakesLabel
                                                              .tr(),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: CustomAssessmentButton(
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                            currentState: assessmentPhysicalSentationCubit.shakesState,
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            onNoTap: () {
                                                              assessmentPhysicalSentationCubit.shakesState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentPhysicalSentationCubit.shakesState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h26),
                                                        QuestionRowWidget(
                                                          key: assessmentPhysicalSentationCubit.crampsKey,
                                                          questionText: AssessmentLocaleKeys
                                                              .physicalSensationsQuestionsCrampsLabel
                                                              .tr(),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: CustomAssessmentButton(
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                            currentState: assessmentPhysicalSentationCubit.crampsState,
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            onNoTap: () {
                                                              assessmentPhysicalSentationCubit.crampsState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentPhysicalSentationCubit.crampsState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h26),
                                                        QuestionRowWidget(
                                                          key: assessmentPhysicalSentationCubit.nauseaKey,
                                                          questionText: AssessmentLocaleKeys
                                                              .physicalSensationsQuestionsNauseaLabel
                                                              .tr(),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: CustomAssessmentButton(
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                            currentState: assessmentPhysicalSentationCubit.nauseaState,
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            onNoTap: () {
                                                              assessmentPhysicalSentationCubit.nauseaState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentPhysicalSentationCubit.nauseaState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h26),
                                                        QuestionRowWidget(
                                                          key: assessmentPhysicalSentationCubit.tirednessKey,
                                                          questionText: AssessmentLocaleKeys
                                                              .physicalSensationsQuestionsTirednessLabel
                                                              .tr(),
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: CustomAssessmentButton(
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                            currentState: assessmentPhysicalSentationCubit.tirednessState,
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            onNoTap: () {
                                                              assessmentPhysicalSentationCubit.tirednessState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentPhysicalSentationCubit.tirednessState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h30),
                                                        ValueListenableBuilder(
                                                          valueListenable: assessmentPhysicalSentationCubit
                                                              .ratePhysicalSensationsInfoVisible,
                                                          builder: (context, rateStateInfoVisible, child) {
                                                            return QuestionRowWidget(
                                                              oninfoTap: () {
                                                                
                                                                assessmentPhysicalSentationCubit
                                                                        .ratePhysicalSensationsInfoVisible.value =
                                                                    !assessmentPhysicalSentationCubit
                                                                        .ratePhysicalSensationsInfoVisible.value;
                                                                       
                                                                log('infoAudioUrlinfoAudioUrl${AssessmentLocaleKeys.physicalSensationsQuestionsRateInfoAudio.tr()}');
                                                                if (assessmentPhysicalSentationCubit
                                                                    .ratePhysicalSensationsInfoVisible.value) {
                                                                      
                                                                  assessmentPhysicalSentationCubit.isManuallyPaused.value =
                                                                      false;
                                                                  assessmentPhysicalSentationCubit.infoAudioUrl.value =
                                                                      AssessmentLocaleKeys
                                                                          .physicalSensationsQuestionsRateInfoAudio
                                                                          .tr();
                                                                          assessmentPhysicalSentationCubit
                                                                      .ratePhysicalSensationsInfoVisibleLabel.value = true; 
                                                                } else {
                                                                  assessmentPhysicalSentationCubit.isManuallyPaused.value =
                                                                      true;
                                                                  assessmentPhysicalSentationCubit.infoAudioUrl.value =
                                                                      AssessmentLocaleKeys.physicalSensationsAudio.tr();
                                                                      assessmentPhysicalSentationCubit
                                                                      .ratePhysicalSensationsInfoVisibleLabel.value = true; 
                                                                }
                                                              },
                                                              infoWidget: CustomInfoWidget(
                                                                padding: EdgeInsets.only(
                                                                  left: AppSize.w12,
                                                                  right: AppSize.w12,
                                                                  top: AppSize.h10,
                                                                ),
                                                                visible: rateStateInfoVisible,
                                                                onCloseTap: () {
                                                                  assessmentPhysicalSentationCubit
                                                                      .ratePhysicalSensationsInfoVisibleLabel.value = true;
                                                                  assessmentPhysicalSentationCubit.isManuallyPaused.value =
                                                                      true;
                                                                  assessmentPhysicalSentationCubit.infoAudioUrl.value =
                                                                      AssessmentLocaleKeys.physicalSensationsAudio.tr();
                                                                  assessmentPhysicalSentationCubit
                                                                      .ratePhysicalSensationsInfoVisible.value = false;
                                                                      
                                                                },
                                                                bodyText: AssessmentLocaleKeys
                                                                    .physicalSensationsQuestionsRateInfoText
                                                                    .tr(),
                                                              ),
                                                              questionText: AssessmentLocaleKeys
                                                                  .physicalSensationsQuestionsRateLabel
                                                                  .tr(),
                                                            );
                                                          },
                                                        ),
                                                        SpaceV(AppSize.h20),
                                                        Padding(
                                                          padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                          child: SliderScreen(
                                                            reverseGradient: true,
                                                            firstText: (DynamicAssetLoader.getNestedValue(
                                                              AssessmentLocaleKeys
                                                                  .physicalSensationsQuestionsRateSliderLabels,
                                                              context,
                                                            ) as List)
                                                                .cast<String>()
                                                                .first,
                                                            secondText: (DynamicAssetLoader.getNestedValue(
                                                              AssessmentLocaleKeys
                                                                  .physicalSensationsQuestionsRateSliderLabels,
                                                              context,
                                                            ) as List)
                                                                .cast<String>()
                                                                .last,
                                                            onSelect: (p0) {
                                                              assessmentPhysicalSentationCubit
                                                                  .ratePhysicalSensationSliderVlaue.value = p0;
                                                            },
                                                            selectedValue: assessmentPhysicalSentationCubit
                                                                .ratePhysicalSensationSliderVlaue,
                                                            isClick: assessmentPhysicalSentationCubit
                                                                .isPhysicalSensationClicked.value,
                                                          ),
                                                        ),
                                                        SpaceV(AppSize.h30),
                                                      ],
                                                    ),
                                                    MultiValueListenableBuilder(
                                                      valueListenables: [
                                                        assessmentPhysicalSentationCubit.cravingsState,
                                                        assessmentPhysicalSentationCubit.shakesState,
                                                        assessmentPhysicalSentationCubit.crampsState,
                                                        assessmentPhysicalSentationCubit.nauseaState,
                                                        assessmentPhysicalSentationCubit.tirednessState,
                                                        assessmentPhysicalSentationCubit.ratePhysicalSensationSliderVlaue,
                                                      ],
                                                      builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                                        final cravingsState = values[0];
                                                        final shakesState = values[1]; // ButtonState
                                                        final crampsState = values[2]; // ButtonState
                                                        final nauseaState = values[3]; // ButtonState
                                                        final tirednessState = values[4]; // ButtonState
                                                        final ratePhysicalSensationSliderValue = values[5]; // Slider value
                                      
                                                        final isButtonEnabled = cravingsState != ButtonState.bothDisabled &&
                                                            shakesState != ButtonState.bothDisabled &&
                                                            crampsState != ButtonState.bothDisabled &&
                                                            nauseaState != ButtonState.bothDisabled &&
                                                            tirednessState != ButtonState.bothDisabled &&
                                                            ratePhysicalSensationSliderValue != -1;
                                      
                                                        return CustomButton(
                                                          padding: EdgeInsets.zero,
                                                          key: const Key('next_btn'),
                                                          disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                          title: CoreLocaleKeys.buttonsNext.tr(),
                                                          inProgress: state.isApiLoading,
                                                          onTap: () async {
                                                            if (!isButtonEnabled) {
                                                              assessmentPhysicalSentationCubit
                                                                  .isPhysicalSensationClicked.value = true;
                                                              if (assessmentPhysicalSentationCubit.cravingsState.value ==
                                                                  ButtonState.bothDisabled) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  assessmentPhysicalSentationCubit.cravingsKey,
                                                                );
                                                                return;
                                                              }
                                                              if (assessmentPhysicalSentationCubit.shakesState.value ==
                                                                  ButtonState.bothDisabled) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  assessmentPhysicalSentationCubit.shakesKey,
                                                                );
                                                                return;
                                                              }
                                                              if (assessmentPhysicalSentationCubit.crampsState.value ==
                                                                  ButtonState.bothDisabled) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  assessmentPhysicalSentationCubit.crampsKey,
                                                                );
                                                                return;
                                                              }
                                                              if (assessmentPhysicalSentationCubit.nauseaState.value ==
                                                                  ButtonState.bothDisabled) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  assessmentPhysicalSentationCubit.nauseaKey,
                                                                );
                                                                return;
                                                              }
                                                              if (assessmentPhysicalSentationCubit.tirednessState.value ==
                                                                  ButtonState.bothDisabled) {
                                                                await AppCommonFunctions.scrollToKey(
                                                                  assessmentPhysicalSentationCubit.tirednessKey,
                                                                );
                                                                return;
                                                              }
                                                            } else {
                                                              await assessmentPhysicalSentationCubit.putPhysicalSensationAPI(
                                                                cravings: cravingsState == ButtonState.noEnabled ? 0 : 1,
                                                                shakes: shakesState == ButtonState.noEnabled ? 0 : 1,
                                                                cramps: crampsState == ButtonState.noEnabled ? 0 : 1,
                                                                nausea: nauseaState == ButtonState.noEnabled ? 0 : 1,
                                                                tiredness: tirednessState == ButtonState.noEnabled ? 0 : 1,
                                                                rate: assessmentPhysicalSentationCubit
                                                                    .ratePhysicalSensationSliderVlaue.value,
                                                                context: context,
                                                              );
                                                            }
                                                          },
                                                          isBottom: true,
                                                          color: isButtonEnabled
                                                              ? context.themeColors.blueColor
                                                              : context.themeColors.blueColor.withOpacity(0.7),
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                   
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
