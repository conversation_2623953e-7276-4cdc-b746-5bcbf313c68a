import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_recovery_program_page.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class AssessmentLifePage extends StatelessWidget {
  const AssessmentLifePage({super.key});

  @override
  Widget build(BuildContext context) {
    '>?>?>? life'.logV;
    return BlocBuilder<AssessmentLifeCubit, AssessmentLifeState>(
      builder: (ctx, state) {
        final assessmentLifeStyleCubit = ctx.read<AssessmentLifeCubit>();
        final assessmentCubit = ctx.read<AssessmentCubit>();

        return ValueListenableBuilder(
          valueListenable: assessmentLifeStyleCubit.infoAudioUrl,
          builder: (context, audioUrl, child) {
            return AppScaffold(
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit.isAudioPannelVisible.value =
                      !assessmentCubit.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit.isAudioPannelVisible,
              isManuallyPaused: assessmentLifeStyleCubit.isManuallyPaused,
              infoAudioUrl: assessmentLifeStyleCubit.infoAudioUrl,
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: assessmentLifeStyleCubit.isLifeButtonClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: CustomRawScrollbar(
                                  child: SingleChildScrollView(
                                    key: PageStorageKey('scroll'),
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(
                                          minHeight: constraints.maxHeight),
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                            left: AppSize.w24,
                                            right: AppSize.w24,
                                            bottom: AppSize.h20),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              children: [
                                                const CustomHeader(),
                                                QuestionRowWidget(
                                                  questionText:
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsQualityLabel
                                                          .tr(),
                                                ),
                                                SpaceV(AppSize.h6),
                                                SizedBox(
                                                  key:
                                                      assessmentLifeStyleCubit
                                                          .qualityKey,
                                                  child:
                                                      ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentLifeStyleCubit
                                                            .qualityValue,
                                                    builder: (context, value,
                                                        child) {
                                                      return CustomRadioListWidget(
                                                        valueKey:
                                                            'qualityList',
                                                        isButtonClicked:
                                                            assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value,
                                                        options:
                                                            assessmentLifeStyleCubit
                                                                .qualityList,
                                                        selectedValue: value,
                                                        isError: assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value &&
                                                            value
                                                                .isEmpty, // Safely access first item
                                                        onChanged:
                                                            (newValue) {
                                                          assessmentLifeStyleCubit
                                                                  .qualityValue
                                                                  .value =
                                                              newValue ?? '';
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText:
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsHealthLabel
                                                          .tr(),
                                                ),
                                                SpaceV(AppSize.h6),
                                                SizedBox(
                                                  key:
                                                      assessmentLifeStyleCubit
                                                          .healthKey,
                                                  child:
                                                      ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentLifeStyleCubit
                                                            .healthValue,
                                                    builder: (context, value,
                                                        child) {
                                                      return CustomRadioListWidget(
                                                        valueKey:
                                                            'healthList',
                                                        isButtonClicked:
                                                            assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value,
                                                        isError:
                                                            assessmentLifeStyleCubit
                                                                    .isLifeButtonClicked
                                                                    .value &&
                                                                value.isEmpty,
                                                        options:
                                                            assessmentLifeStyleCubit
                                                                .healthList,
                                                        selectedValue:
                                                            value, // Safely access first item
                                                        onChanged:
                                                            (newValue) {
                                                          assessmentLifeStyleCubit
                                                                  .healthValue
                                                                  .value =
                                                              newValue ?? '';
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText:
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsActivitiesLabel
                                                          .tr(),
                                                ),
                                                SpaceV(AppSize.h6),
                                                SizedBox(
                                                  key:
                                                      assessmentLifeStyleCubit
                                                          .activitiesKey,
                                                  child:
                                                      ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentLifeStyleCubit
                                                            .activitiesValue,
                                                    builder: (context, value,
                                                        child) {
                                                      return CustomRadioListWidget(
                                                        valueKey:
                                                            'activitiesValue',
                                                        isButtonClicked:
                                                            assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value,
            
                                                        options:
                                                            assessmentLifeStyleCubit
                                                                .activitiesList,
                                                        selectedValue: value,
                                                        // Safely access first item
                                                        onChanged:
                                                            (newValue) {
                                                          assessmentLifeStyleCubit
                                                                  .activitiesValue
                                                                  .value =
                                                              newValue ?? '';
                                                        },
                                                        isError:
                                                            assessmentLifeStyleCubit
                                                                    .isLifeButtonClicked
                                                                    .value &&
                                                                value.isEmpty,
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText:
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsRelationshipsLabel
                                                          .tr(),
                                                ),
                                                SpaceV(AppSize.h6),
                                                SizedBox(
                                                  key:
                                                      assessmentLifeStyleCubit
                                                          .relationshipKey,
                                                  child:
                                                      ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentLifeStyleCubit
                                                            .relationshipsValue,
                                                    builder: (context, value,
                                                        child) {
                                                      return CustomRadioListWidget(
                                                        valueKey:
                                                            'relationshipsList',
                                                        isButtonClicked:
                                                            assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value,
                                                        isError:
                                                            assessmentLifeStyleCubit
                                                                    .isLifeButtonClicked
                                                                    .value &&
                                                                value.isEmpty,
                                                        options:
                                                            assessmentLifeStyleCubit
                                                                .relationshipsList,
                                                        selectedValue:
                                                            value, // Safely access first item
                                                        onChanged:
                                                            (newValue) {
                                                          assessmentLifeStyleCubit
                                                                  .relationshipsValue
                                                                  .value =
                                                              newValue ?? '';
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                QuestionRowWidget(
                                                  questionText:
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsWorkLabel
                                                          .tr(),
                                                ),
                                                SpaceV(AppSize.h6),
                                                SizedBox(
                                                  key:
                                                      assessmentLifeStyleCubit
                                                          .workKey,
                                                  child:
                                                      ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentLifeStyleCubit
                                                            .workValue,
                                                    builder: (context, value,
                                                        child) {
                                                      return CustomRadioListWidget(
                                                        valueKey: 'workList',
                                                        isButtonClicked:
                                                            assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value,
                                                        options:
                                                            assessmentLifeStyleCubit
                                                                .workList,
                                                        selectedValue: value,
                                                        isError: assessmentLifeStyleCubit
                                                                .isLifeButtonClicked
                                                                .value &&
                                                            value
                                                                .isEmpty, // Safely access first item
                                                        onChanged:
                                                            (newValue) {
                                                          assessmentLifeStyleCubit
                                                                  .workValue
                                                                  .value =
                                                              newValue ?? '';
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                ValueListenableBuilder(
                                                  valueListenable:
                                                      assessmentLifeStyleCubit
                                                          .islifeSecondInfoVisible,
                                                  builder: (context,
                                                      islifeSecondInfoVisible,
                                                      child) {
                                                    return QuestionRowWidget(
                                                      oninfoTap: () async {
                                                        assessmentLifeStyleCubit
                                                                .islifeSecondInfoVisible
                                                                .value =
                                                            !assessmentLifeStyleCubit
                                                                .islifeSecondInfoVisible
                                                                .value;
            
                                                        if (assessmentLifeStyleCubit
                                                            .islifeSecondInfoVisible
                                                            .value) {
                                                          context
                                                              .read<
                                                                  AssessmentLifeCubit>()
                                                              .isManuallyPaused
                                                              .value = false;
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeQuestionsDifficultiesInfoAudio
                                                                  .tr();
                                                        } else {
                                                          context
                                                              .read<
                                                                  AssessmentLifeCubit>()
                                                              .isManuallyPaused
                                                              .value = true;
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeAudio
                                                                  .tr();
                                                        }
            
                                                        log('message${assessmentLifeStyleCubit.islifeFirstInfoVisible.value}');
                                                      },
                                                      infoWidget:
                                                          CustomInfoWidget(
                                                        padding:
                                                            EdgeInsets.only(
                                                          left: AppSize.w12,
                                                          right: AppSize.w12,
                                                          top: AppSize.h10,
                                                        ),
                                                        visible:
                                                            islifeSecondInfoVisible,
                                                        onCloseTap: () {
                                                          assessmentLifeStyleCubit
                                                              .isManuallyPaused
                                                              .value = true;
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeAudio
                                                                  .tr();
            
                                                          assessmentLifeStyleCubit
                                                              .islifeSecondInfoVisible
                                                              .value = false;
                                                        },
                                                        bodyText:
                                                            AssessmentLocaleKeys
                                                                .lifeQuestionsDifficultiesInfoText
                                                                .tr(),
                                                      ),
                                                      questionText:
                                                          AssessmentLocaleKeys
                                                              .lifeQuestionsDifficultiesLabel
                                                              .tr(),
                                                    );
                                                  },
                                                ),
                                                SpaceV(AppSize.h20),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      left: AppSize.w34,
                                                      right: AppSize.w28),
                                                  child: SliderScreen(
                                                    key: const Key(
                                                        'lifeFirstSlider'),
                                                    reverseGradient: false,
                                                    firstText:
                                                        (DynamicAssetLoader
                                                                .getNestedValue(
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsDifficultiesSliderLabels,
                                                      context,
                                                    ) as List)
                                                            .cast<String>()
                                                            .first,
                                                    secondText:
                                                        (DynamicAssetLoader
                                                                .getNestedValue(
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsDifficultiesSliderLabels,
                                                      context,
                                                    ) as List)
                                                            .cast<String>()
                                                            .last,
                                                    onSelect: (p0) {
                                                      assessmentLifeStyleCubit
                                                          .lifeFirstSliderValue
                                                          .value = p0;
                                                    },
                                                    selectedValue:
                                                        assessmentLifeStyleCubit
                                                            .lifeFirstSliderValue,
                                                    isClick:
                                                        assessmentLifeStyleCubit
                                                            .isLifeButtonClicked
                                                            .value,
                                                  ),
                                                ),
                                                SpaceV(AppSize.h30),
                                                ValueListenableBuilder(
                                                  valueListenable:
                                                      assessmentLifeStyleCubit
                                                          .islifeFirstInfoVisible,
                                                  builder: (context,
                                                      islifeFirstInfoVisible,
                                                      child) {
                                                    return QuestionRowWidget(
                                                      oninfoTap: () {
                                                        assessmentLifeStyleCubit
                                                                .islifeFirstInfoVisible
                                                                .value =
                                                            !assessmentLifeStyleCubit
                                                                .islifeFirstInfoVisible
                                                                .value;
            
                                                        if (assessmentLifeStyleCubit
                                                            .islifeFirstInfoVisible
                                                            .value) {
                                                          context
                                                              .read<
                                                                  AssessmentLifeCubit>()
                                                              .isManuallyPaused
                                                              .value = false;
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeQuestionsRateInfoAudio
                                                                  .tr();
                                                        } else {
                                                          context
                                                              .read<
                                                                  AssessmentLifeCubit>()
                                                              .isManuallyPaused
                                                              .value = true;
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeAudio
                                                                  .tr();
                                                        }
                                                      },
                                                      infoWidget:
                                                          CustomInfoWidget(
                                                        padding:
                                                            EdgeInsets.only(
                                                          left: AppSize.w12,
                                                          right: AppSize.w12,
                                                          top: AppSize.h10,
                                                        ),
                                                        visible:
                                                            islifeFirstInfoVisible,
                                                        onCloseTap: () {
                                                          assessmentLifeStyleCubit
                                                              .isManuallyPaused
                                                              .value = true;
            
                                                          assessmentLifeStyleCubit
                                                                  .infoAudioUrl
                                                                  .value =
                                                              AssessmentLocaleKeys
                                                                  .lifeAudio
                                                                  .tr();
                                                          assessmentLifeStyleCubit
                                                              .islifeFirstInfoVisible
                                                              .value = false;
                                                        },
                                                        bodyText:
                                                            AssessmentLocaleKeys
                                                                .lifeQuestionsRateInfoText
                                                                .tr(),
                                                      ),
                                                      questionText:
                                                          AssessmentLocaleKeys
                                                              .lifeQuestionsRateLabel
                                                              .tr(),
                                                    );
                                                  },
                                                ),
                                                SpaceV(AppSize.h20),
                                                Padding(
                                                  padding: EdgeInsets.only(
                                                      left: AppSize.w34,
                                                      right: AppSize.w28),
                                                  child: SliderScreen(
                                                    key: const Key(
                                                        'lifeSecondSlider'),
                                                    reverseGradient: false,
                                                    firstText:
                                                        (DynamicAssetLoader
                                                                .getNestedValue(
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsRateSliderLabels,
                                                      context,
                                                    ) as List)
                                                            .cast<String>()
                                                            .first,
                                                    secondText:
                                                        (DynamicAssetLoader
                                                                .getNestedValue(
                                                      AssessmentLocaleKeys
                                                          .lifeQuestionsRateSliderLabels,
                                                      context,
                                                    ) as List)
                                                            .cast<String>()
                                                            .last,
                                                    onSelect: (p0) {
                                                      assessmentLifeStyleCubit
                                                          .lifeSecondSliderValue
                                                          .value = p0;
                                                    },
                                                    selectedValue:
                                                        assessmentLifeStyleCubit
                                                            .lifeSecondSliderValue,
                                                    isClick:
                                                        assessmentLifeStyleCubit
                                                            .isLifeButtonClicked
                                                            .value,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SpaceV(AppSize.h20),
                                            MultiValueListenableBuilder(
                                              valueListenables: [
                                                assessmentLifeStyleCubit
                                                    .lifeFirstSliderValue,
                                                assessmentLifeStyleCubit
                                                    .lifeSecondSliderValue,
                                                assessmentLifeStyleCubit
                                                    .qualityValue,
                                                assessmentLifeStyleCubit
                                                    .healthValue,
                                                assessmentLifeStyleCubit
                                                    .activitiesValue,
                                                assessmentLifeStyleCubit
                                                    .relationshipsValue,
                                                assessmentLifeStyleCubit
                                                    .workValue,
                                              ],
                                              builder: (BuildContext context,
                                                  List<dynamic> values,
                                                  Widget? child) {
                                                // Destructure the values for better readability
                                                final lifeFirstSliderValue =
                                                    values[0];
                                                final lifeSecondSliderValue =
                                                    values[1];
                                                final qualityValue =
                                                    values[2];
                                                final healthValue = values[3];
                                                final activitiesValue =
                                                    values[4];
                                                final relationshipsValue =
                                                    values[5];
                                                final workValue = values[6];
                                                final isButtonEnabled =
                                                    lifeFirstSliderValue !=
                                                            -1 &&
                                                        lifeSecondSliderValue !=
                                                            -1 &&
                                                        qualityValue != '' &&
                                                        healthValue != '' &&
                                                        activitiesValue !=
                                                            '' &&
                                                        relationshipsValue !=
                                                            '' &&
                                                        workValue != '';
            
                                                return CustomButton(
                                                  key: const Key('next_btn'),
                                                  padding: EdgeInsets.zero,
                                                  disableColor: context
                                                      .themeColors.blueColor
                                                      .withOpacity(0.1),
                                                  title: CoreLocaleKeys
                                                      .buttonsNext
                                                      .tr(),
                                                  inProgress:
                                                      state.isApiLoading,
                                                  onTap: () async {
                                                    if (!isButtonEnabled) {
                                                      assessmentLifeStyleCubit
                                                          .isLifeButtonClicked
                                                          .value = true;
            
                                                      if (assessmentLifeStyleCubit
                                                          .qualityValue
                                                          .value
                                                          .isEmpty) {
                                                        await AppCommonFunctions
                                                            .scrollToKey(
                                                          assessmentLifeStyleCubit
                                                              .qualityKey,
                                                        );
                                                        return;
                                                      }
                                                      if (assessmentLifeStyleCubit
                                                          .healthValue
                                                          .value
                                                          .isEmpty) {
                                                        await AppCommonFunctions
                                                            .scrollToKey(
                                                          assessmentLifeStyleCubit
                                                              .healthKey,
                                                        );
                                                        return;
                                                      }
                                                      if (assessmentLifeStyleCubit
                                                          .activitiesValue
                                                          .value
                                                          .isEmpty) {
                                                        await AppCommonFunctions
                                                            .scrollToKey(
                                                          assessmentLifeStyleCubit
                                                              .activitiesKey,
                                                        );
                                                        return;
                                                      }
                                                      if (assessmentLifeStyleCubit
                                                          .relationshipsValue
                                                          .value
                                                          .isEmpty) {
                                                        await AppCommonFunctions
                                                            .scrollToKey(
                                                          assessmentLifeStyleCubit
                                                              .relationshipKey,
                                                        );
                                                        return;
                                                      }
                                                      if (assessmentLifeStyleCubit
                                                          .workValue
                                                          .value
                                                          .isEmpty) {
                                                        await AppCommonFunctions
                                                            .scrollToKey(
                                                          assessmentLifeStyleCubit
                                                              .workKey,
                                                        );
                                                        return;
                                                      }
                                                    } else {
                                                      assessmentLifeStyleCubit
                                                          .isLifeButtonClicked
                                                          .value = false;
                                                          assessmentLifeStyleCubit.isManuallyPaused.value = true;
                                                      await assessmentLifeStyleCubit
                                                          .putAssessmentLifeAPI(
                                                        quality: assessmentLifeStyleCubit
                                                            .qualityList
                                                            .indexOf(
                                                                assessmentLifeStyleCubit
                                                                    .qualityValue
                                                                    .value),
                                                        health: assessmentLifeStyleCubit
                                                            .healthList
                                                            .indexOf(
                                                                assessmentLifeStyleCubit
                                                                    .healthValue
                                                                    .value),
                                                        activities: assessmentLifeStyleCubit
                                                            .activitiesList
                                                            .indexOf(
                                                                assessmentLifeStyleCubit
                                                                    .activitiesValue
                                                                    .value),
                                                        relationships: assessmentLifeStyleCubit
                                                            .relationshipsList
                                                            .indexOf(
                                                                assessmentLifeStyleCubit
                                                                    .relationshipsValue
                                                                    .value),
                                                        work: assessmentLifeStyleCubit
                                                            .workList
                                                            .indexOf(
                                                                assessmentLifeStyleCubit
                                                                    .workValue
                                                                    .value),
                                                        difficulties:
                                                            assessmentLifeStyleCubit
                                                                .lifeFirstSliderValue
                                                                .value,
                                                        rate: assessmentLifeStyleCubit
                                                            .lifeSecondSliderValue
                                                            .value,
                                                        context: context,
                                                      );
                                                    }
                                                  },
                                                  isBottom: true,
                                                  color: isButtonEnabled
                                                      ? context.themeColors
                                                          .blueColor
                                                      : context.themeColors
                                                          .blueColor
                                                          .withOpacity(0.7),
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
