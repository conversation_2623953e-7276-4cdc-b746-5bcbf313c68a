import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_unhelpful_behaviour/assessment_unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentUnhelpfulBehaviourPage extends StatelessWidget {
  const AssessmentUnhelpfulBehaviourPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentUnhelpfulBehaviourCubit, AssessmentUnhelpfulBehaviourState>(
      builder: (ctx, state) {
        final assessmentUnhelpFulbehaviourCubit = ctx.read<AssessmentUnhelpfulBehaviourCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();

        return ValueListenableBuilder(
          valueListenable: assessmentUnhelpFulbehaviourCubit.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: assessmentUnhelpFulbehaviourCubit.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              infoAudioUrl: assessmentUnhelpFulbehaviourCubit.infoAudioUrl,
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: assessmentUnhelpFulbehaviourCubit.isUnHelpBehaviourClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: CustomRawScrollbar(
                                  key: PageStorageKey('4'),
                                  //key: (assessmentUnhelpFulbehaviourCubit.rateBehaviourInfoVisibleLabel.value) ? PageStorageKey('scroll') :key,
                                  child: ListView(
                                    key: PageStorageKey('4'),
                                    children: [
                                      ConstrainedBox(
                                        constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            left: AppSize.w24,
                                            right: AppSize.w24,
                                            bottom: AppSize.h20,
                                          ),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                children: [
                                                  const CustomHeader(),
                                                  SpaceV(AppSize.h6),
                                                  AppTextWidget(
                                                    AssessmentLocaleKeys.unhelpfulBehavioursSubtitle.tr(),
                                                    style: context.textTheme.titleSmall?.copyWith(
                                                      color: context.themeColors.darkGreyColor,
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  QuestionRowWidget(
                                                    key: assessmentUnhelpFulbehaviourCubit.aggressiveKey,
                                                    questionText: AssessmentLocaleKeys
                                                        .unhelpfulBehavioursQuestionsAggressiveLabel
                                                        .tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: CustomAssessmentButton(
                                                      selectedValue: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                      currentState: assessmentUnhelpFulbehaviourCubit.aggressiveState,
                                                      buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                      buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                      onNoTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.aggressiveState.value =
                                                            ButtonState.yesEnabled;
                                                      },
                                                      onYesTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.aggressiveState.value =
                                                            ButtonState.noEnabled;
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h26),
                                                  QuestionRowWidget(
                                                    key: assessmentUnhelpFulbehaviourCubit.avoidKey,
                                                    questionText: AssessmentLocaleKeys
                                                        .unhelpfulBehavioursQuestionsAvoidLabel
                                                        .tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: CustomAssessmentButton(
                                                      selectedValue: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                      currentState: assessmentUnhelpFulbehaviourCubit.avoidState,
                                                      buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                      buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                      onNoTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.avoidState.value =
                                                            ButtonState.yesEnabled;
                                                      },
                                                      onYesTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.avoidState.value =
                                                            ButtonState.noEnabled;
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h26),
                                                  QuestionRowWidget(
                                                    key: assessmentUnhelpFulbehaviourCubit.activeKey,
                                                    questionText: AssessmentLocaleKeys
                                                        .unhelpfulBehavioursQuestionsActiveLabel
                                                        .tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: CustomAssessmentButton(
                                                      selectedValue: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                      currentState: assessmentUnhelpFulbehaviourCubit.activeState,
                                                      buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                      buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                      onNoTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.activeState.value =
                                                            ButtonState.yesEnabled;
                                                      },
                                                      onYesTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.activeState.value =
                                                            ButtonState.noEnabled;
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h26),
                                                  QuestionRowWidget(
                                                    key: assessmentUnhelpFulbehaviourCubit.careKey,
                                                    questionText:
                                                        AssessmentLocaleKeys.unhelpfulBehavioursQuestionsCareLabel.tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: CustomAssessmentButton(
                                                      selectedValue: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                      currentState: assessmentUnhelpFulbehaviourCubit.careState,
                                                      buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                      buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                      onNoTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.careState.value =
                                                            ButtonState.yesEnabled;
                                                      },
                                                      onYesTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.careState.value =
                                                            ButtonState.noEnabled;
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h26),
                                                  QuestionRowWidget(
                                                    key: assessmentUnhelpFulbehaviourCubit.policeKey,
                                                    questionText: AssessmentLocaleKeys
                                                        .unhelpfulBehavioursQuestionsPoliceLabel
                                                        .tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: CustomAssessmentButton(
                                                      selectedValue: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                      currentState: assessmentUnhelpFulbehaviourCubit.policeState,
                                                      buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                      buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                      onNoTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.policeState.value =
                                                            ButtonState.yesEnabled;
                                                      },
                                                      onYesTap: () {
                                                        assessmentUnhelpFulbehaviourCubit.policeState.value =
                                                            ButtonState.noEnabled;
                                                      },
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h30),
                                                  ValueListenableBuilder(
                                                    valueListenable:
                                                        assessmentUnhelpFulbehaviourCubit.rateBehaviourInfoVisible,
                                                    builder: (context, rateStateInfoVisible, child) {
                                                      return QuestionRowWidget(
                                                        oninfoTap: () {
                                                          assessmentUnhelpFulbehaviourCubit.rateBehaviourInfoVisibleLabel.value = true;
                                                          assessmentUnhelpFulbehaviourCubit
                                                                  .rateBehaviourInfoVisible.value =
                                                              !assessmentUnhelpFulbehaviourCubit
                                                                  .rateBehaviourInfoVisible.value;
                                                          log('message${assessmentUnhelpFulbehaviourCubit.rateBehaviourInfoVisible.value}');
                                                          if (assessmentUnhelpFulbehaviourCubit
                                                              .rateBehaviourInfoVisible.value) {
                                                            assessmentUnhelpFulbehaviourCubit.isManuallyPaused.value =
                                                                false;
                                                            assessmentUnhelpFulbehaviourCubit.infoAudioUrl.value =
                                                                AssessmentLocaleKeys
                                                                    .unhelpfulBehavioursQuestionsRateInfoAudio
                                                                    .tr();
                                                          } else {
                                                            assessmentUnhelpFulbehaviourCubit.isManuallyPaused.value =
                                                                true;
                                                            assessmentUnhelpFulbehaviourCubit.infoAudioUrl.value =
                                                                AssessmentLocaleKeys.unhelpfulBehavioursAudio.tr();
                                                          }
                                                        },
                                                        infoWidget: CustomInfoWidget(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w12,
                                                            right: AppSize.w12,
                                                            top: AppSize.h10,
                                                          ),
                                                          visible: rateStateInfoVisible,
                                                          onCloseTap: () {
                                                            assessmentUnhelpFulbehaviourCubit.rateBehaviourInfoVisibleLabel.value = true;
                                                            assessmentUnhelpFulbehaviourCubit.isManuallyPaused.value =
                                                                true;
                                                            assessmentUnhelpFulbehaviourCubit.infoAudioUrl.value =
                                                                AssessmentLocaleKeys.unhelpfulBehavioursAudio.tr();
                                                            assessmentUnhelpFulbehaviourCubit
                                                                .rateBehaviourInfoVisible.value = false;
                                                          },
                                                          bodyText: AssessmentLocaleKeys
                                                              .unhelpfulBehavioursQuestionsRateInfoText
                                                              .tr(),
                                                        ),
                                                        questionText: AssessmentLocaleKeys
                                                            .unhelpfulBehavioursQuestionsRateLabel
                                                            .tr(),
                                                      );
                                                    },
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  Padding(
                                                    padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                    child: SliderScreen(
                                                      reverseGradient: true,
                                                      firstText: (DynamicAssetLoader.getNestedValue(
                                                        AssessmentLocaleKeys
                                                            .unhelpfulBehavioursQuestionsRateSliderLabels,
                                                        context,
                                                      ) as List)
                                                          .cast<String>()
                                                          .first,
                                                      secondText: (DynamicAssetLoader.getNestedValue(
                                                        AssessmentLocaleKeys
                                                            .unhelpfulBehavioursQuestionsRateSliderLabels,
                                                        context,
                                                      ) as List)
                                                          .cast<String>()
                                                          .last,
                                                      onSelect: (p0) {
                                                        assessmentUnhelpFulbehaviourCubit
                                                            .rateBehaviourSliderVlaue.value = p0;
                                                      },
                                                      selectedValue:
                                                          assessmentUnhelpFulbehaviourCubit.rateBehaviourSliderVlaue,
                                                      isClick: assessmentUnhelpFulbehaviourCubit
                                                          .isUnHelpBehaviourClicked.value,
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h30),
                                                ],
                                              ),
                                              MultiValueListenableBuilder(
                                                valueListenables: [
                                                  assessmentUnhelpFulbehaviourCubit.aggressiveState,
                                                  assessmentUnhelpFulbehaviourCubit.avoidState,
                                                  assessmentUnhelpFulbehaviourCubit.activeState,
                                                  assessmentUnhelpFulbehaviourCubit.careState,
                                                  assessmentUnhelpFulbehaviourCubit.policeState,
                                                  assessmentUnhelpFulbehaviourCubit.rateBehaviourSliderVlaue,
                                                ],
                                                builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                                  // Extract the state values for clarity
                                                  final aggressiveState = values[0]; // ButtonState
                                                  final avoidState = values[1]; // ButtonState
                                                  final activeState = values[2]; // ButtonState
                                                  final careState = values[3]; // ButtonState
                                                  final policeState = values[4]; // ButtonState
                                                  final rateBehaviourSliderValue = values[5]; // Slider value
                  
                                                  // Check conditions to enable the button
                                                  final isButtonEnabled = aggressiveState != ButtonState.bothDisabled &&
                                                      avoidState != ButtonState.bothDisabled &&
                                                      activeState != ButtonState.bothDisabled &&
                                                      careState != ButtonState.bothDisabled &&
                                                      policeState != ButtonState.bothDisabled &&
                                                      rateBehaviourSliderValue != -1;
                  
                                                  return CustomButton(
                                                    padding: EdgeInsets.zero,
                                                    key: const Key('next_btn'),
                                                    inProgress: state.isApiLoading,
                                                    disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                    title: CoreLocaleKeys.buttonsNext.tr(),
                                                    onTap: () async {
                                                      if (!isButtonEnabled) {
                                                        assessmentUnhelpFulbehaviourCubit
                                                            .isUnHelpBehaviourClicked.value = true;
                  
                                                        if (assessmentUnhelpFulbehaviourCubit.aggressiveState.value ==
                                                            ButtonState.bothDisabled) {
                                                          await AppCommonFunctions.scrollToKey(
                                                            assessmentUnhelpFulbehaviourCubit.aggressiveKey,
                                                          );
                                                          return;
                                                        }
                                                        if (assessmentUnhelpFulbehaviourCubit.avoidState.value ==
                                                            ButtonState.bothDisabled) {
                                                          await AppCommonFunctions.scrollToKey(
                                                            assessmentUnhelpFulbehaviourCubit.avoidKey,
                                                          );
                                                          return;
                                                        }
                                                        if (assessmentUnhelpFulbehaviourCubit.activeState.value ==
                                                            ButtonState.bothDisabled) {
                                                          await AppCommonFunctions.scrollToKey(
                                                            assessmentUnhelpFulbehaviourCubit.activeKey,
                                                          );
                                                          return;
                                                        }
                                                        if (assessmentUnhelpFulbehaviourCubit.careState.value ==
                                                            ButtonState.bothDisabled) {
                                                          await AppCommonFunctions.scrollToKey(
                                                            assessmentUnhelpFulbehaviourCubit.careKey,
                                                          );
                                                          return;
                                                        }
                                                        if (assessmentUnhelpFulbehaviourCubit.policeState.value ==
                                                            ButtonState.bothDisabled) {
                                                          await AppCommonFunctions.scrollToKey(
                                                            assessmentUnhelpFulbehaviourCubit.policeKey,
                                                          );
                                                          return;
                                                        }
                                                      } else {
                                                        await assessmentUnhelpFulbehaviourCubit
                                                            .putUnhelpfulBehaviourAPI(
                                                          aggressive: aggressiveState == ButtonState.noEnabled ? 0 : 1,
                                                          avoid: avoidState == ButtonState.noEnabled ? 0 : 1,
                                                          active: activeState == ButtonState.noEnabled ? 0 : 1,
                                                          care: careState == ButtonState.noEnabled ? 0 : 1,
                                                          police: policeState == ButtonState.noEnabled ? 0 : 1,
                                                          rate: assessmentUnhelpFulbehaviourCubit
                                                              .rateBehaviourSliderVlaue.value,
                                                          context: context,
                                                        );
                                                        //  assessmentCubit.gotoNextWidget();
                                                      }
                                                    },
                                                    isBottom: true,
                                                    color: isButtonEnabled
                                                        ? context.themeColors.blueColor
                                                        : context.themeColors.blueColor.withOpacity(0.7),
                                                  );
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
