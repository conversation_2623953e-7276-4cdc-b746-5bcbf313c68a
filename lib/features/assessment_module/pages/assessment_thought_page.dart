import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_thoughts/assessment_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentThoughtPage extends StatelessWidget {
  const AssessmentThoughtPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentThoughtsCubit, AssessmentThoughtsState>(
      builder: (ctx, state) {
        final assessmentThoughtsCubit = ctx.read<AssessmentThoughtsCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();

        return ValueListenableBuilder(
          valueListenable: assessmentThoughtsCubit.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: assessmentThoughtsCubit.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              infoAudioUrl: assessmentThoughtsCubit.infoAudioUrl,
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: assessmentThoughtsCubit.isThoughtButtonClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: CustomRawScrollbar(
                                  key:  PageStorageKey('2'),
                                  child: SingleChildScrollView(
                                    key:  PageStorageKey('2'),
                                    child: Column(
                                      children: [
                                        ConstrainedBox(
                                          constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                          child: Padding(
                                            padding: EdgeInsets.only(
                                              left: AppSize.w24,
                                              right: AppSize.w24,
                                              bottom: AppSize.h20,
                                            ),
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  children: [
                                                    const CustomHeader(),
                                                    SpaceV(AppSize.h6),
                                                    AppTextWidget(
                                                      AssessmentLocaleKeys.negativeThoughtsSubtitle.tr(),
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        color: context.themeColors.darkGreyColor,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    SizedBox(
                                                      key: assessmentThoughtsCubit.goodKey,
                                                    ),
                                                    QuestionRowWidget(
                                                      questionText:
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsGoodLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        buttonFirstKey: const Key('good_enough_no'),
                                                        buttonSecondKey: const Key('good_enough_yes'),
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        currentState: assessmentThoughtsCubit.goodState,
                                                        selectedValue:
                                                            assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                        onNoTap: () {
                                                          assessmentThoughtsCubit.goodState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentThoughtsCubit.goodState.value = ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    SizedBox(key: assessmentThoughtsCubit.controlKey),
                                                    QuestionRowWidget(
                                                      questionText:
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsControlLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        buttonFirstKey: const Key('out_of_control_no'),
                                                        buttonSecondKey: const Key('out_of_control_yes'),
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        selectedValue:
                                                            assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                        currentState: assessmentThoughtsCubit.controlState,
                                                        onNoTap: () {
                                                          assessmentThoughtsCubit.controlState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentThoughtsCubit.controlState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    SizedBox(key: assessmentThoughtsCubit.healthKey),
                                                    QuestionRowWidget(
                                                      questionText:
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsHealthLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        buttonFirstKey: const Key('health_being_damage_no'),
                                                        buttonSecondKey: const Key('health_being_damage_yes'),
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        selectedValue:
                                                            assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                        currentState: assessmentThoughtsCubit.healthState,
                                                        onNoTap: () {
                                                          assessmentThoughtsCubit.healthState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentThoughtsCubit.healthState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    SizedBox(key: assessmentThoughtsCubit.copeKey),
                                                    QuestionRowWidget(
                                                      questionText:
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsCopeLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        buttonFirstKey: const Key('health_being_damage_no'),
                                                        buttonSecondKey: const Key('health_being_damage_yes'),
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        selectedValue:
                                                            assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                        currentState: assessmentThoughtsCubit.copeState,
                                                        onNoTap: () {
                                                          assessmentThoughtsCubit.copeState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentThoughtsCubit.copeState.value = ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    SizedBox(
                                                      key: assessmentThoughtsCubit.trustKey,
                                                    ),
                                                    QuestionRowWidget(
                                                      questionText:
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsTrustLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        buttonFirstKey: const Key('health_being_damage_no'),
                                                        buttonSecondKey: const Key('health_being_damage_yes'),
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        selectedValue:
                                                            assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                        currentState: assessmentThoughtsCubit.trustState,
                                                        onNoTap: () {
                                                          assessmentThoughtsCubit.trustState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentThoughtsCubit.trustState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h30),
                                                    ValueListenableBuilder(
                                                      valueListenable: assessmentThoughtsCubit.rateThoughtsInfoVisible,
                                                      builder: (context, rateStateInfoVisible, child) {
                                                        return QuestionRowWidget(
                                                          oninfoTap: () {
                                                            assessmentThoughtsCubit.rateThoughtsInfoVisibleLable.value = true;
                                                            assessmentThoughtsCubit.rateThoughtsInfoVisible.value =
                                                                !assessmentThoughtsCubit.rateThoughtsInfoVisible.value;
                                                            log('message${assessmentThoughtsCubit.rateThoughtsInfoVisible.value}');
                                                            if (assessmentThoughtsCubit.rateThoughtsInfoVisible.value) {
                                                              assessmentThoughtsCubit.isManuallyPaused.value = false;
                                                              assessmentThoughtsCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys
                                                                      .negativeThoughtsQuestionsRateInfoAudio
                                                                      .tr();
                                                            } else {
                                                              assessmentThoughtsCubit.isManuallyPaused.value = true;
                                                              assessmentThoughtsCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys.negativeThoughtsAudio.tr();
                                                            }
                                                          },
                                                          infoWidget: CustomInfoWidget(
                                                            padding: EdgeInsets.only(
                                                              left: AppSize.w12,
                                                              right: AppSize.w12,
                                                              top: AppSize.h10,
                                                            ),
                                                            visible: rateStateInfoVisible,
                                                            onCloseTap: () {
                                                              assessmentThoughtsCubit.rateThoughtsInfoVisibleLable.value = true;
                                                              assessmentThoughtsCubit.isManuallyPaused.value = true;
                                    
                                                              assessmentThoughtsCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys.negativeThoughtsAudio.tr();
                                    
                                                              assessmentThoughtsCubit.rateThoughtsInfoVisible.value =
                                                                  false;
                                                            },
                                                            bodyText: AssessmentLocaleKeys
                                                                .negativeThoughtsQuestionsRateInfoText
                                                                .tr(),
                                                          ),
                                                          questionText: AssessmentLocaleKeys
                                                              .negativeThoughtsQuestionsRateLabel
                                                              .tr(),
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: SliderScreen(
                                                        reverseGradient: true,
                                                        firstText: (DynamicAssetLoader.getNestedValue(
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsRateSliderLabels,
                                                          context,
                                                        ) as List)
                                                            .cast<String>()
                                                            .first,
                                                        secondText: (DynamicAssetLoader.getNestedValue(
                                                          AssessmentLocaleKeys.negativeThoughtsQuestionsRateSliderLabels,
                                                          context,
                                                        ) as List)
                                                            .cast<String>()
                                                            .last,
                                                        onSelect: (p0) {
                                                          assessmentThoughtsCubit.rateThoughtSliderVlaue.value = p0;
                                                        },
                                                        selectedValue: assessmentThoughtsCubit.rateThoughtSliderVlaue,
                                                        isClick: assessmentThoughtsCubit.isThoughtButtonClicked.value,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h30),
                                                  ],
                                                ),
                                                MultiValueListenableBuilder(
                                                  valueListenables: [
                                                    assessmentThoughtsCubit.goodState,
                                                    assessmentThoughtsCubit.controlState,
                                                    assessmentThoughtsCubit.healthState,
                                                    assessmentThoughtsCubit.copeState,
                                                    assessmentThoughtsCubit.trustState,
                                                    assessmentThoughtsCubit.rateThoughtSliderVlaue,
                                                  ],
                                                  builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                                    // Extract the state values for clarity
                                                    final goodState = values[0]; // ButtonState
                                                    final controlState = values[1]; // ButtonState
                                                    final healthState = values[2]; // ButtonState
                                                    final copeState = values[3]; // ButtonState
                                                    final trustState = values[4]; // ButtonState
                                                    final rateThoughtSliderValue = values[5]; // Slider value
                                    
                                                    // Check conditions to enable the button
                                                    final isButtonEnabled = goodState != ButtonState.bothDisabled &&
                                                        controlState != ButtonState.bothDisabled &&
                                                        healthState != ButtonState.bothDisabled &&
                                                        copeState != ButtonState.bothDisabled &&
                                                        trustState != ButtonState.bothDisabled &&
                                                        rateThoughtSliderValue != -1;
                                    
                                                    return CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      key: const Key('next_btn'),
                                                      inProgress: state.isApiLoading,
                                                      disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      onTap: () async {
                                                        if (!isButtonEnabled) {
                                                          assessmentThoughtsCubit.isThoughtButtonClicked.value = true;
                                    
                                                          if (assessmentThoughtsCubit.goodState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentThoughtsCubit.goodKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentThoughtsCubit.controlState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentThoughtsCubit.controlKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentThoughtsCubit.healthState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentThoughtsCubit.healthKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentThoughtsCubit.copeState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentThoughtsCubit.copeKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentThoughtsCubit.trustState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentThoughtsCubit.trustKey,
                                                            );
                                                            return;
                                                          }
                                                        } else {
                                                          await assessmentThoughtsCubit.putAssessmentNegativeThoughtsAPI(
                                                            control: assessmentThoughtsCubit.controlState.value ==
                                                                    ButtonState.noEnabled
                                                                ? 0
                                                                : 1,
                                                            cope: assessmentThoughtsCubit.copeState.value ==
                                                                    ButtonState.noEnabled
                                                                ? 0
                                                                : 1,
                                                            good: assessmentThoughtsCubit.goodState.value ==
                                                                    ButtonState.noEnabled
                                                                ? 0
                                                                : 1,
                                                            health: assessmentThoughtsCubit.healthState.value ==
                                                                    ButtonState.noEnabled
                                                                ? 0
                                                                : 1,
                                                            trust: assessmentThoughtsCubit.trustState.value ==
                                                                    ButtonState.noEnabled
                                                                ? 0
                                                                : 1,
                                                            rate: assessmentThoughtsCubit.rateThoughtSliderVlaue.value,
                                                            context: context,
                                                          );
                                                        }
                                                      },
                                                      isBottom: true,
                                                      color: isButtonEnabled
                                                          ? context.themeColors.blueColor
                                                          : context.themeColors.blueColor.withOpacity(0.7),
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
