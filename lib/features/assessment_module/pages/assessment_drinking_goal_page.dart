import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_goal/assessment_drinking_goal_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentDrinkingGoalPage extends StatelessWidget {
  const AssessmentDrinkingGoalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrinkingGoalCubit,
        AssessmentDrinkingGoalState>(
      builder: (ctx, state) {
        final assessmentCubit = ctx.read<AssessmentDrinkingGoalCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        final assessmentDrinkingCubit = ctx.read<AssessmentDrinkingCubit>();

        return AppScaffold(
          isManuallyPaused: assessmentCubit.isManuallyPaused,
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            onSuffixTap: () {
              assessmentCubit1.isAudioPannelVisible.value =
                  !assessmentCubit1.isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
          infoAudioUrl: assessmentDrinkingCubit.matchvalue() == 0 ?
              ValueNotifier(AssessmentLocaleKeys.drinkingGoalAudioAbstinent.tr())
              :(assessmentDrinkingCubit.matchvalue() >=1 && assessmentDrinkingCubit.matchvalue() < 14) ?
              ValueNotifier(AssessmentLocaleKeys.drinkingGoalAudioLight.tr())
              : ValueNotifier(AssessmentLocaleKeys.drinkingGoalAudioHeavy.tr()),
          resizeToAvoidBottomInset: true,
          body: AbsorbPointer(
            absorbing: state.isApiLoading,
            child: Column(
              children: [
                SpaceV(AppSize.h4),
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constrains) {
                      return Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: CustomRawScrollbar(
                          child: SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                  minHeight: constrains.maxHeight),
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: AppSize.w24,
                                    right: AppSize.w24,
                                    bottom: AppSize.h20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      children: [
                                        const CustomHeader(),
                                        SpaceV(AppSize.h6),
                                        if (assessmentDrinkingCubit.matchvalue() == 0) ...[
                                          AppTextWidget(
                                            (DynamicAssetLoader.getNestedValue(
                                              AssessmentLocaleKeys
                                                  .drinkingGoalSubtitlesAbstinent,
                                              context,
                                            ) as List)
                                                .cast<String>()
                                                .join('\n\n'),
                                            style: context.textTheme.titleSmall
                                                ?.copyWith(
                                              color: context
                                                  .themeColors.darkGreyColor,
                                            ),
                                          ),
                                        ],
                                         if (assessmentDrinkingCubit.matchvalue() >= 1 && assessmentDrinkingCubit.matchvalue() < 14)...[
                                                      AppTextWidget(
                                            (DynamicAssetLoader.getNestedValue(
                                              AssessmentLocaleKeys
                                                  .drinkingGoalSubtitlesLight,
                                              context,
                                            ) as List)
                                                .cast<String>()
                                                .join('\n\n'),
                                            style: context.textTheme.titleSmall
                                                ?.copyWith(
                                              color: context
                                                  .themeColors.darkGreyColor,
                                            ),
                                          ),
                                        ],
                                        if(assessmentDrinkingCubit.matchvalue() >= 14)...[
                                          AppTextWidget(
                                            (DynamicAssetLoader.getNestedValue(
                                              AssessmentLocaleKeys
                                                  .drinkingGoalSubtitlesHeavy,
                                              context,
                                            ) as List)
                                                .cast<String>()
                                                .join('\n\n'),
                                            style: context.textTheme.titleSmall
                                                ?.copyWith(
                                              color: context
                                                  .themeColors.darkGreyColor,
                                            ),
                                          ),
                                        ],
                                        // AppTextWidget(
                                        //   (DynamicAssetLoader.getNestedValue(
                                        //     AssessmentLocaleKeys.drinkingGoalSubtitlesHeavy,
                                        //     context,
                                        //   ) as List)
                                        //       .cast<String>()
                                        //       .join('\n\n'),
                                        //   style: context.textTheme.titleSmall?.copyWith(
                                        //     color: context.themeColors.darkGreyColor,
                                        //   ),
                                        // ),
                                        SpaceV(AppSize.h20),
                                        QuestionRowWidget(
                                          questionText: AssessmentLocaleKeys
                                              .drinkingGoalQuestionsUnitsLabel
                                              .tr(),
                                        ),
                                        SpaceV(AppSize.h10),
                                        ValueListenableBuilder(
                                          valueListenable: assessmentCubit
                                                  .drinkingGoalUnitsController,
                                          builder: (context, value, child) {
                                            return CustomAgeSelcetionWidget(
                                              controller: assessmentCubit
                                                  .drinkingGoalUnitsController.value,
                                              onIncreaseTap: assessmentCubit
                                                  .increaseDrinkingGoalUnitsValue,
                                              onDecreaseTap: assessmentCubit
                                                  .decreaseDrinkingUnitValue,
                                            );
                                          },
                                        ),
                                        SpaceV(AppSize.h30),
                                        QuestionRowWidget(
                                          questionText: AssessmentLocaleKeys
                                              .drinkingGoalQuestionsFreeDaysLabel
                                              .tr(),
                                        ),
                                        SpaceV(AppSize.h10),
                                        CustomAgeSelcetionWidget(
                                          controller: assessmentCubit
                                              .drinkingGoalFreeDayController.value,
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly,
                                            TextInputFormatter.withFunction(
                                              (oldValue, newValue) {
                                                if (newValue.text.isEmpty) {
                                                  return newValue;
                                                }

                                                final value =
                                                    int.tryParse(newValue.text);
                                                if (value != null &&
                                                    value <= 7) {
                                                  return newValue;
                                                }

                                                return oldValue;
                                              },
                                            ),
                                          ],
                                          onIncreaseTap: assessmentCubit
                                              .increaseDrinkingGoalFreeDayValue,
                                          onDecreaseTap: assessmentCubit
                                              .decreaseDrinkingFreeDayValue,
                                        ),
                                        SpaceV(AppSize.h30),
                                      ],
                                    ),
                                    CustomButton(
                                      padding: EdgeInsets.zero,
                                      key: const Key('next_btn'),
                                      inProgress: state.isApiLoading,
                                      disableColor: context
                                          .themeColors.blueColor
                                          .withOpacity(0.1),
                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                      onTap: () async {
                                        assessmentCubit.assignValue();
                                        await assessmentCubit
                                            .putdrinkingGoalAPI(
                                              units: int.tryParse(assessmentCubit.drinkingGoalUnitsController.value.text) ?? 0,
                                              freeDays: int.tryParse(assessmentCubit.drinkingGoalFreeDayController.value.text) ?? 0,
                                              context: context,
                                          // units: state.currentDrinkingUnit,
                                          // freeDays:
                                          //     state.currentDrinkingfreeDays,
                                          // context: context,
                                        );
                                        assessmentCubit.isManuallyPaused.value = false;
                                        '/// assessment = ${Injector.instance<AppDB>().userModel?.user.assessment?.toJson()}'.logV;
                                      },
                                      isBottom: true,
                                      color: context.themeColors.blueColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
