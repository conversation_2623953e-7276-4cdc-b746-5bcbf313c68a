import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_outlined_textfield.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_button_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_check_box_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_drop_down_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentRecoveryProgramPage extends StatelessWidget {
  const AssessmentRecoveryProgramPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: context.read<AssessmentCubit>().isManuallyPaused,//assessmentCubit.isManuallyPaused,
      builder: (context, value, child)  {
        return ValueListenableBuilder(
          valueListenable: context.read<AssessmentCubit>().isAudioPannelVisible,
          builder: (context, value, child) {
            return AppScaffold(
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  context.read<AssessmentCubit>().isAudioPannelVisible.value = !context.read<AssessmentCubit>().isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: context.read<AssessmentCubit>().isAudioPannelVisible,
              infoAudioUrl: ValueNotifier(AssessmentLocaleKeys.recoveryProgramAudio.tr()),
              isManuallyPaused: context.read<AssessmentCubit>().isManuallyPaused,
              resizeToAvoidBottomInset: false,
              body: BlocBuilder<AssessmentCubit, AssessmentState>(
                builder: (ctx, state) {
                   final assessmentCubit = ctx.read<AssessmentCubit>();
                  return ValueListenableBuilder(
                    valueListenable: assessmentCubit.isRecoveryButtonClicked,
                    builder: (context, value, child) {
                      return ValueListenableBuilder(
                        valueListenable: assessmentCubit.isGenderCheckValue,
                        builder: (context, value, child) {
                          return ValueListenableBuilder(
                            valueListenable: assessmentCubit.isEthnicCheckValue,
                            builder: (context, value, child) {
                              return Column(
                                children: [
                                  SpaceV(AppSize.h4),
                                  Expanded(
                                    child: LayoutBuilder(
                                      builder: (context, constraints) {
                                        return Padding(
                                          padding: EdgeInsets.only(right: AppSize.w4),
                                          child: CustomRawScrollbar(
                                            child: SingleChildScrollView(
                                              child: ConstrainedBox(
                                                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                                child: Column(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Padding(
                                                      padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                                      child: Column(
                                                        children: [
                                                          const CustomHeader(
                                                            showArrowBack: false,
                                                          ),
                                                          QuestionRowWidget(
                                                            questionText:
                                                                AssessmentLocaleKeys.recoveryProgramQuestionsNameLabel.tr(),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          ValueListenableBuilder(
                                                            valueListenable: assessmentCubit.isMaxTextLength,
                                                            builder: (context,isMaxTextLength,value) {
                                                              return Padding(
                                                                padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                                child: Column(
                                                                  children: [
                                                                    CustomOutlinedTextfield(
                                                                      key: assessmentCubit.nameLength,
                                                                      controller: assessmentCubit.nameController,
                                                                      hintText: CoreLocaleKeys.labelsTextPlaceholder.tr(),
                                                                      isError:assessmentCubit.isMaxTextLength.value && assessmentCubit.nameController.text.length >= 100,
                                                                      onChanged: (val){
                                                                        if(assessmentCubit.nameController.text.length <= 100){
                                                                          assessmentCubit.isMaxTextLength.value = false;
                                                                        }
                                                                      },
                                                                      inputFormatters: [
                                                                          LengthLimitingTextInputFormatter(100),
                                                                      ],
                                                                      contentHeight: AppSize.h10,
                                                                    ),
                                                                    Padding(
                                                                      padding: EdgeInsets.symmetric(
                                                                        horizontal: AppSize.w20,
                                                                        vertical: AppSize.h6,
                                                                      ),
                                                                      child: Text(
                                                                        AssessmentLocaleKeys.recoveryProgramQuestionsNameHint.tr(),
                                                                        style: context.textTheme.labelSmall?.copyWith(
                                                                          fontSize: AppSize.sp10,
                                                                          color:assessmentCubit.isMaxTextLength.value && assessmentCubit.nameController.text.length > 100 ? context.themeColors.errorRedColor : context.themeColors.textfieldTextColor 
                                                                        ),
                                                                      ),
                                                                    ),
                                                                    if(assessmentCubit.nameController.text.length > 100 && assessmentCubit.isMaxTextLength.value)
                                                                      CustomErrorWidget(errorMessgaeText: AssessmentLocaleKeys.errorsNameMaxLengthMessage.tr())
                                  
                                                                  ],
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          QuestionRowWidget(
                                                            questionText:
                                                                AssessmentLocaleKeys.recoveryProgramQuestionsAgeLabel.tr(),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          ValueListenableBuilder(
                                                            valueListenable: assessmentCubit.ageCheckValue,
                                                            builder: (context, value, child) {
                                                              return CustomAgeSelcetionWidget(
                                                                inputFormatters: [
                                                    FilteringTextInputFormatter.digitsOnly,
                                                    TextInputFormatter.withFunction(
                                                          (oldValue, newValue) {
                                                        if (newValue.text.isEmpty) {
                                                          return newValue;
                                                        }
                                  
                                                        final value = int.tryParse(newValue.text);
                                                        if (value != null && value <= 99) {
                                                          return newValue;
                                                        }
                                  
                                                        return oldValue;
                                                      },
                                                    ),
                                                  ],
                                                                maxDigit: 2,
                                                                key:assessmentCubit.ageKey,
                                                                unitText: 0,
                                                                increaseKey: const Key('age_increase_key'),
                                                                decreaseKey: const Key('age_decrease_key'),
                                                                controller: assessmentCubit.ageController,
                                                                onDecreaseTap: assessmentCubit.decreaseAgeValue,
                                                                onIncreaseTap: assessmentCubit.increaseAgeValue,
                                                                isDisable: assessmentCubit.ageCheckValue.value,
                                                              );
                                                            },
                                                          ),
                                                          ValueListenableBuilder(
                                                            valueListenable: assessmentCubit.isConsentBoxCheck,
                                                            builder: (context,isConsentBoxCheck,child) {
                                                              return Padding(
                                                                padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                                child: Column(
                                                                  children: [
                                                                    SpaceV(AppSize.h14),
                                                                    CustomCheckbox(
                                                                      isChecked: assessmentCubit.ageCheckValue,
                                                                      onTap: () {
                                                                        assessmentCubit
                                                                          ..toggleCheckbox(assessmentCubit.ageCheckValue)
                                                                          ..validateAndShowError();
                                                                      },
                                                                    ),
                                                                    ValueListenableBuilder(
                                                                      valueListenable: assessmentCubit.confirmAgeValue,
                                                                      builder: (context,confirmAgeValue,child) {
                                                                        return ValueListenableBuilder(
                                                                          valueListenable: assessmentCubit
                                                                              .ageCheckValue, // Listen to ageCheckValue changes
                                                                          builder: (context, bool ageCheck, child) {
                                                                            if ((int.parse(assessmentCubit.ageController.text) <= 15 || int.parse(assessmentCubit.ageController.text) >= 15) &&
                                                                                assessmentCubit.ageCheckValue.value) {
                                                                              return Column(
                                                                                    children: [
                                                                                      SpaceV(AppSize.h10),
                                                                                      CustomCheckbox(
                                                                                        containerPadding: EdgeInsets.symmetric(
                                                                                          horizontal: AppSize.w12,
                                                                                          vertical: AppSize.h10,
                                                                                        ),
                                                                                        borderColor:context.themeColors.greenColor ,
                                                                                        containerColor: const Color(0xFFEEEEEE),
                                                                                        containerRadius: BorderRadius.circular(AppSize.r4),
                                                                                        isChecked: assessmentCubit
                                                                                            .confirmAgeValue, // Value for checkbox
                                                                                        text: AssessmentLocaleKeys
                                                                                            .recoveryProgramQuestionsAgeConsent
                                                                                            .tr(),
                                                                                        onTap: () {
                                                                                          assessmentCubit.isConsentBoxCheck.value  = false;
                                                                                          assessmentCubit
                                                                                            ..toggleCheckbox(
                                                                                              assessmentCubit.confirmAgeValue,
                                                                                            )
                                                                                            ..validateAndShowError();
                                                                                        },
                                                                                      ),
                                                                                      if(assessmentCubit.ageCheckValue.value)
                                                                                        if(assessmentCubit.isConsentBoxCheck.value && !assessmentCubit.confirmAgeValue.value)
                                                                                          CustomErrorWidget(errorMessgaeText: AssessmentLocaleKeys.errorsConsentRequiredMessage.tr())
                                  
                                                                                    ],
                                                                                  );
                                  
                                                                            } else {
                                                                              return const SizedBox
                                                                                  .shrink(); // Return empty widget if condition is false
                                                                            }
                                                                          },
                                                                        );
                                                                      },
                                                                    ),
                                                                  ],
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                          SpaceV(AppSize.h30),
                                                          QuestionRowWidget(
                                                            questionText:
                                                                AssessmentLocaleKeys.recoveryProgramQuestionsGenderLabel.tr(),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          Padding(
                                                            padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                            child: Column(
                                                              children: [
                                                                SizedBox(
                                                                  height: AppSize.h44,
                                                                  child: CustomDropDownListWidget(
                                                                    // key: const Key('gender_select'),
                                                                    key: assessmentCubit.genderKey,
                                                                    onChanged: assessmentCubit.updateSelectedGender,
                                                                    selectedValue: state.selectedGenderValue,
                                                                    list: assessmentCubit.genderList,
                                                                    donotWishCheckBoxValue: assessmentCubit.isGenderCheckValue,
                                                                    isInlineError: false,
                                                                    isError:
                                                                        assessmentCubit.isGenderCheckValue.value == false &&
                                                                            assessmentCubit.isRecoveryButtonClicked.value &&
                                                                            assessmentCubit.state.selectedGenderValue == null,
                                                                  ),
                                                                ),
                                                                SpaceV(AppSize.h14),
                                                                ValueListenableBuilder(
                                                                  valueListenable: assessmentCubit.isGenderCheckValue,
                                                                  builder: (context, value, child) {
                                                                    return Column(
                                                                      children: [
                                                                        CustomCheckbox(
                                                                          borderColor: !value &&
                                                                                  state.selectedGenderValue == null &&
                                                                                  assessmentCubit.isRecoveryButtonClicked.value
                                                                              ? context.themeColors.errorRedColor
                                                                              : context.themeColors.greenColor,
                                                                          isChecked: assessmentCubit.isGenderCheckValue,
                                                                          onTap: () {
                                                                            assessmentCubit
                                                                              ..toggleCheckbox(
                                                                                assessmentCubit.isGenderCheckValue,
                                                                              )
                                                                              ..validateAndShowError();
                                                                          },
                                                                        ),
                                                                        Visibility(
                                                                          visible: assessmentCubit.isGenderCheckValue.value ==
                                                                                  false &&
                                                                              assessmentCubit.isRecoveryButtonClicked.value &&
                                                                              assessmentCubit.state.selectedGenderValue == null,
                                                                          child: CustomErrorWidget(
                                                                            spacing: AppSize.h14,
                                                                            errorMessgaeText:
                                                                                AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    );
                                                                  },
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          SpaceV(AppSize.h30),
                                                          QuestionRowWidget(
                                                            questionText: AssessmentLocaleKeys
                                                                .recoveryProgramQuestionsEthnicityLabel
                                                                .tr(),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          Padding(
                                                            padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                            child: Column(
                                                              children: [
                                                                SizedBox(
                                                                  height: AppSize.h44,
                                                                  child: CustomDropDownListWidget(
                                                                    // key: const Key('ethnicity_select'),
                                                                    key: assessmentCubit.ethiniCityKey,
                                                                    onChanged: assessmentCubit.updateSelectedEthnic,
                                                                    selectedValue: state.selectedEthnicValue,
                                                                    list: assessmentCubit.ethnicList,
                                                                    donotWishCheckBoxValue: assessmentCubit.isEthnicCheckValue,
                                                                    isInlineError: false,
                                                                    isError:
                                                                        assessmentCubit.isEthnicCheckValue.value == false &&
                                                                            assessmentCubit.isRecoveryButtonClicked.value &&
                                                                            assessmentCubit.state.selectedEthnicValue == null,
                                                                  ),
                                                                ),
                                                                SpaceV(AppSize.h14),
                                                                ValueListenableBuilder(
                                                                  valueListenable: assessmentCubit.isEthnicCheckValue,
                                                                  builder: (context, value, child) {
                                                                    return Column(
                                                                      children: [
                                                                        CustomCheckbox(
                                                                          borderColor: !assessmentCubit
                                                                                      .isEthnicCheckValue.value &&
                                                                                  state.selectedEthnicValue == null &&
                                                                                  assessmentCubit.isRecoveryButtonClicked.value
                                                                              ? context.themeColors.errorRedColor
                                                                              : context.themeColors.greenColor,
                                                                          isChecked: assessmentCubit.isEthnicCheckValue,
                                                                          onTap: () {
                                                                            assessmentCubit
                                                                              ..toggleCheckbox(
                                                                                assessmentCubit.isEthnicCheckValue,
                                                                              )
                                                                              ..validateAndShowError();
                                                                          },
                                                                        ),
                                                                        Visibility(
                                                                          visible: assessmentCubit.isEthnicCheckValue.value ==
                                                                                  false &&
                                                                              assessmentCubit.isRecoveryButtonClicked.value &&
                                                                              assessmentCubit.state.selectedEthnicValue == null,
                                                                          child: CustomErrorWidget(
                                                                            spacing: AppSize.h14,
                                                                            errorMessgaeText:
                                                                                AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    );
                                                                  },
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          SpaceV(AppSize.h30),
                                                          QuestionRowWidget(
                                                            questionText: AssessmentLocaleKeys
                                                                .recoveryProgramQuestionsAddictionCaseLabel
                                                                .tr(),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          Padding(
                                                            padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                            child: ValueListenableBuilder(
                                                              valueListenable: assessmentCubit.selectedBreakingFreeIndex,
                                                              builder: (context, value, child) {
                                                                return CustomButtonSelectionWidget(
                                                                  isError: assessmentCubit.isRecoveryButtonClicked.value &&
                                                                      assessmentCubit.selectedBreakingFreeIndex.value == -1,
                                                                  borderColor: assessmentCubit.isRecoveryButtonClicked.value &&
                                                                          assessmentCubit.selectedBreakingFreeIndex.value == -1
                                                                      ? context.themeColors.errorRedColor
                                                                      : context.themeColors.greenColor,
                                                                  selectedIndex:
                                                                      assessmentCubit.selectedBreakingFreeIndex.value,
                                                                  onTap: (index) {
                                                                    assessmentCubit
                                                                      ..onButtonTapped(
                                                                        index,
                                                                        assessmentCubit.selectedBreakingFreeIndex,
                                                                      )
                                                                      ..validateAndShowError();
                                                                  },
                                                                  buttonTexts: (DynamicAssetLoader.getNestedValue(
                                                                    AssessmentLocaleKeys
                                                                        .recoveryProgramQuestionsAddictionCaseItems,
                                                                    context,
                                                                  ) as List)
                                                                      .cast<String>(),
                                                                  buttonKeys: const [
                                                                    'alcohol_1',
                                                                    'rugs_1',
                                                                    'alcohol_drugs_1',
                                                                  ],
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                          SpaceV(AppSize.h20),
                                                          ValueListenableBuilder(
                                                            valueListenable: assessmentCubit.isSelectedButton,
                                                            builder: (context,isSelectedButton,child) {
                                                              return ValueListenableBuilder(
                                                                valueListenable: assessmentCubit.selectedBreakingFreeIndex,
                                                                builder: (context, value, child) {
                                                                  return Visibility(
                                                                    visible: assessmentCubit.selectedBreakingFreeIndex.value == 2,
                                                                    child: Column(
                                                                      children: [
                                                                        QuestionRowWidget(
                                                                          questionText: AssessmentLocaleKeys
                                                                              .recoveryProgramQuestionsSpecialAddictionLabel
                                                                              .tr(),
                                                                        ),
                                                                        SpaceV(AppSize.h20),
                                                                        Padding(
                                                                          padding: EdgeInsets.only(
                                                                            left: AppSize.w34,
                                                                            right: AppSize.w28,
                                                                            bottom: AppSize.h20,
                                                                          ),
                                                                          child: ValueListenableBuilder(
                                                                            valueListenable: assessmentCubit.selectedrecoveryIndex,
                                                                            builder: (context, value, child) {
                                                                              return CustomButtonSelectionWidget(
                                                                                borderColor:
                                                                                    assessmentCubit.isSelectedButton.value &&
                                                                                            assessmentCubit
                                                                                                    .selectedrecoveryIndex.value ==
                                                                                                -1
                                                                                        ? context.themeColors.errorRedColor
                                                                                        : context.themeColors.greenColor,
                                                                                selectedIndex:
                                                                                    assessmentCubit.selectedrecoveryIndex.value,
                                                                                isError: assessmentCubit
                                                                                        .isSelectedButton.value &&
                                                                                    assessmentCubit.selectedrecoveryIndex.value == -1,
                                                                                onTap: (index) {
                                                                                  assessmentCubit
                                                                                    ..onButtonTapped(
                                                                                      index,
                                                                                      assessmentCubit.selectedrecoveryIndex,
                                                                                    )
                                                                                    ..validateAndShowError();
                                                                                },
                                                                                buttonTexts: assessmentCubit.specialAddictionList,
                                                                                buttonKeys: assessmentCubit.specialAddictionList,
                                                                              );
                                                                            },
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  );
                                                                },
                                                              );
                                                            },
                                                          ),
                                                          //   SpaceV(AppSize.h30),
                                                        ],
                                                      ),
                                                    ),
                                                    ValueListenableBuilder<bool>(
                                                      valueListenable: assessmentCubit.isRecoveryButtonDisable,
                                                      builder: (context, isDisabled, child) {
                                                        return CustomButton(
                                                          key: const Key('next_btn'),
                                                          inProgress: state.isRecoveryProgramAPILoading,
                                                          title: CoreLocaleKeys.buttonsNext.tr(),
                                                          onTap: () async {
                                                            assessmentCubit.isRecoveryButtonClicked.value = true;
                                                            
                                                            if(assessmentCubit.nameController.text.length > 100){
                                                              assessmentCubit.isMaxTextLength.value = true;
                                                            }else{
                                                              assessmentCubit.isMaxTextLength.value = false;
                                                            }
                                  
                                                            if(assessmentCubit.ageCheckValue.value){
                                                              if(!assessmentCubit.confirmAgeValue.value){
                                                                assessmentCubit.isConsentBoxCheck.value = true;
                                                              }else{
                                                                assessmentCubit.isConsentBoxCheck.value = false;
                                                              }
                                                            }else{
                                                              assessmentCubit.isConsentBoxCheck.value = false;
                                                            }
                                                            if(assessmentCubit.selectedBreakingFreeIndex.value == 2){
                                                              assessmentCubit.isSelectedButton.value = true;
                                                            }else{
                                                              assessmentCubit.isSelectedButton.value = false;
                                                            }
                                  
                                                            if(assessmentCubit.nameController.text.length > 100 && assessmentCubit.isMaxTextLength.value ){
                                                              await AppCommonFunctions.scrollToKey(assessmentCubit.nameLength);
                                                              return;
                                                            }
                                                            if(assessmentCubit.ageCheckValue.value) {
                                                              if (assessmentCubit.isConsentBoxCheck.value && !assessmentCubit.confirmAgeValue.value) {
                                                                await AppCommonFunctions.scrollToKey(assessmentCubit.ageKey);
                                                                return;
                                                              }
                                                            }
                                  
                                                            // if (state.selectedGenderValue == null &&
                                                            //     !assessmentCubit.isGenderCheckValue.value) {
                                                            //   await AppCommonFunctions.scrollToKey(assessmentCubit.genderKey);
                                                            //   return;
                                                            // }
                                  
                                                            // if (state.selectedEthnicValue == null &&
                                                            //     !assessmentCubit.isEthnicCheckValue.value) {
                                                            //   await AppCommonFunctions.scrollToKey(
                                                            //     assessmentCubit.ethiniCityKey,
                                                            //   );
                                                            //   return;
                                                            // }
                                                            // if(assessmentCubit.ageCheckValue.value && !assessmentCubit.confirmAgeValue.value){
                                                            //   await AppCommonFunctions.scrollToKey(
                                                            //     assessmentCubit.ageKey,
                                                            //   );
                                                            //   return;
                                                            // }
                                                            if (assessmentCubit.validateAndShowError(shouldShowError: true)) {
                                                              if (isDisabled) {
                                  
                                                                
                                  
                                                                'state.selectedEthnicValue${state.selectedEthnicValue}'.logD;
                                                                await assessmentCubit.putAssessmentRecoveryProgramAPI(
                                                                  addictionCase:
                                                                      assessmentCubit.selectedBreakingFreeIndex.value,
                                                                  name: assessmentCubit.nameController.text,
                                                                  age: assessmentCubit.confirmAgeValue.value == true
                                                                      ? false
                                                                      : int.parse(assessmentCubit.ageController.text),
                                                                  context: context,
                                                                  ethnicity: state.selectedEthnicValue == null
                                                                      ? null
                                                                      : assessmentCubit.ethnicList
                                                                          .indexOf(state.selectedEthnicValue ?? ''),
                                                                  gender: state.selectedGenderValue == null
                                                                      ? null
                                                                      : assessmentCubit.genderList
                                                                          .indexOf(state.selectedGenderValue ?? ''),
                                                                  specialAddiction:
                                                                      assessmentCubit.selectedrecoveryIndex.value == -1
                                                                          ? null
                                                                          : assessmentCubit.selectedrecoveryIndex.value,
                                                                );
                                                                assessmentCubit.assignValue();
                                                                assessmentCubit.isManuallyPaused.value = false;
                                                              }
                                                              '/// Assessment = ${Injector.instance<AppDB>().userModel?.user.toJson()}'.logV;
                                                            }
                                                          },
                                                          isBottom: true,
                                                          color: !isDisabled // Use the ValueListenableBuilder value directly
                                                              ? context.themeColors.blueColor.withOpacity(
                                                                  0.7,
                                                                ) // Change color when the button should be disabled
                                                              : context
                                                                  .themeColors.blueColor, // Normal color when validation passes
                                                        );
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                      );
                    },
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
