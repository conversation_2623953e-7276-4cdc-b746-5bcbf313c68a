import 'dart:developer';
import 'dart:io';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/assessment_header_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/res/space_box.dart';

class AssessmentWelcomeVideoPage extends StatelessWidget {
  const AssessmentWelcomeVideoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: Platform.isIOS,
        onPopInvoked: (didPop) {
          '????? backk'.logD;
          //if (didPop) {
          '????? back'.logD;
          if (!didPop) {
            exit(0);
          }
        },
      child: BlocBuilder<AssessmentCubit, AssessmentState>(
        builder: (ctx, state) {
          final assessmentCubit = ctx.read<AssessmentCubit>();
          return AppScaffold(
            appBar: CommonAppBar(
              prefixIcon: Icon(
                Icons.logout,
                size: AppSize.sp20,
              ),
              onPrefixTap: () {
                LogOutDialog.showLogOutDialog(context);
              },
              suffixIcon: Icon(
                Icons.volume_up,
                size: AppSize.h24,
                color: context.themeColors.greyColor,
              ),
              onSuffixTap: () {
                // if (assessmentCubit.infoAudioUrl.value.isNotEmptyAndNotNull) {
                //   assessmentCubit.isAudioPannelVisible.value = !assessmentCubit.isAudioPannelVisible.value;
                // }
              },
            ),
            isAudioPanelVisible: assessmentCubit.isAudioPannelVisible,
            infoAudioUrl: assessmentCubit.infoAudioUrl,
            resizeToAvoidBottomInset: false,
            body: ValueListenableBuilder(
              valueListenable: assessmentCubit.isVideoEnded,
              builder: (context, value, child) {
                return Column(
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: constraints.maxHeight),
                              child: ValueListenableBuilder(
                                valueListenable: assessmentCubit.isButtonClicked,
                                builder: (context, value2, child) {
                                  return Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        children: [
                                          Padding(
                                            padding: EdgeInsets.only(
                                              left: AppSize.w24,
                                              right: AppSize.w24,
                                              top: AppSize.h4,
                                            ),
                                            child: CustomAssessmentHeaderWidget(
                                              showArrowBack: false,
                                              onBackTap: getBackFunction(ctx, state.index),
                                              activeDotIndex: state.index,
                                              subTitle: getSubTitle(state.index),
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                            child: VideoPlayerScreen(
                                              // onVideoEnded: () {
                                              //   assessmentCubit.isVideoEnded.value = true;
                                              //   AppNavigation.previousScreen(context);
                                              // },
                                              onVideoEnded: () async {
                                            assessmentCubit.isVideoEnded.value = true;
                                            await Future.delayed(const Duration(milliseconds: 300));
                                            if (Navigator.of(context).canPop()) {
                                              Navigator.of(context).pop();
                                            }
                                            log('value:  ${assessmentCubit.isAssessmentVideoEnded.value}');
                                          },
                                              onVideo90PercentageEnded: () {
                                                assessmentCubit.isVideoEnded.value = true;
                                              },
                                              imageList: [AssessmentLocaleKeys.welcomeVideoPoster.tr()],
                                              navigationFunction: () {},
                                              videoList: [AssessmentLocaleKeys.welcomeVideoSrc.tr()],
                                            ),
                                          ),
                                          if (!value && assessmentCubit.isButtonClicked.value)
                                            Padding(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: AppSize.w24,
                                              ),
                                              child: CustomErrorWidget(
                                                spacing: AppSize.h14,
                                                errorMessgaeText: AssessmentLocaleKeys.errorsVideoMessage.tr(),
                                              ),
                                            )
                                          else
                                            const SizedBox(),
                                        ],
                                      ),
                                      CustomButton(
                                        key: const Key('next_btn'),
                                        disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                        title: CoreLocaleKeys.buttonsNext.tr(),
                                        onTap: () async {
                                          if (value) {
                                            log('state ${state.index}');
                                            assessmentCubit.isButtonClicked.value = false;
                                            assessmentCubit.gotoNextWidget();
                                          } else {
                                            assessmentCubit.isButtonClicked.value = true;
                                            // CustomSnackbar.showErrorSnackBar(
                                            //   message: AssessmentLocaleKeys.errorsVideoMessage.tr(),
                                            //   color: context.themeColors.redColor,
                                            // );
                                          }
                                        },
                                        isBottom: true,
                                        color: value
                                            ? context.themeColors.blueColor
                                            : context.themeColors.blueColor.withOpacity(0.7),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }
}
