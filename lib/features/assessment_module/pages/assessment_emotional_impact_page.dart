import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_emotional_imapct/assessment_emotional_impact_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentEmotionalImpactPage extends StatelessWidget {
  const AssessmentEmotionalImpactPage({super.key});

  @override
  Widget build(BuildContext context) {
    '>?>?>? emotional impact page'.logV;
    return BlocBuilder<AssessmentEmotionalImpactCubit, AssessmentEmotionalImpactState>(
      builder: (ctx, state) {
        final ref = ctx.read<AssessmentEmotionalImpactCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: ref.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              // infoAudioUrlStr: AssessmentLocaleKeys.lifeAudio.tr(),
              infoAudioUrl: ref.infoAudioUrl,
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: ref.isEmotionButtonClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              return CustomRawScrollbar(
                                key: PageStorageKey('6'),
                                child: SingleChildScrollView(
                                  key: PageStorageKey('6'),
                                  //key: ref.rateEmotionalImpactInfoVisibleLabel.value ? PageStorageKey('scroll') : key,
                                  child: Padding(
                                    padding:
                                        EdgeInsets.only(left: AppSize.w24, right: AppSize.w28, bottom: AppSize.h20),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            const CustomHeader(),
                                            SpaceV(AppSize.h6),
                                            AppTextWidget(
                                              AssessmentLocaleKeys.emotionalImpactSubtitle.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                color: context.themeColors.darkGreyColor,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            QuestionRowWidget(
                                              questionText:
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsNervousLabel.tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: ref.nervousKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: ref.nervousValue,
                                                builder: (context, value, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: ref.isEmotionButtonClicked.value,
                                                    options: ref.nervousList,
                                                    selectedValue:
                                                        ref.nervousValue.value, // Safely access first item
                                                    onChanged: (newValue) {
                                                      ref.nervousValue.value = newValue ?? '';
                                                    },
                                                    isError: ref.isEmotionButtonClicked.value &&
                                                        ref.nervousValue.value.isEmpty,
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText:
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsWorryLabel.tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: ref.worryKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: ref.worryValue,
                                                builder: (context, value, child) {
                                                  return CustomRadioListWidget(
                                                    options: ref.worryList,
                                                    isButtonClicked: ref.isEmotionButtonClicked.value,
                                                    isError: ref.isEmotionButtonClicked.value &&
                                                        ref.worryValue.value.isEmpty,
                                  
                                                    selectedValue: ref.worryValue.value, // Safely access first item
                                                    onChanged: (newValue) {
                                                      ref.worryValue.value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText:
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsDownLabel.tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: ref.downKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: ref.downValue,
                                                builder: (context, value, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: ref.isEmotionButtonClicked.value,
                                                    isError: ref.isEmotionButtonClicked.value &&
                                                        ref.downValue.value.isEmpty,
                                  
                                                    options: ref.downList,
                                                    selectedValue: ref.downValue.value, // Safely access first item
                                                    onChanged: (newValue) {
                                                      ref.downValue.value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText:
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsBadLabel.tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: ref.badKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: ref.badValue,
                                                builder: (context, value, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: ref.isEmotionButtonClicked.value,
                                                    isError: ref.isEmotionButtonClicked.value &&
                                                        ref.badValue.value.isEmpty,
                                  
                                                    options: ref.badList,
                                                    selectedValue: ref.badValue.value, // Safely access first item
                                                    onChanged: (newValue) {
                                                      ref.badValue.value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText:
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsInterestLabel.tr(),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: ref.interestKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: ref.interestValue,
                                                builder: (context, value, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: ref.isEmotionButtonClicked.value,
                                                    isError: ref.isEmotionButtonClicked.value &&
                                                        ref.interestValue.value.isEmpty,
                                  
                                                    options: ref.interestList,
                                                    selectedValue:
                                                        ref.interestValue.value, // Safely access first item
                                                    onChanged: (newValue) {
                                                      ref.interestValue.value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            ValueListenableBuilder(
                                              valueListenable: ref.rateEmotionalImpactInfoVisible,
                                              builder: (context, rateEmtionalImpactInfoVisible, child) {
                                                return QuestionRowWidget(
                                                  oninfoTap: () {
                                                    ref.rateEmotionalImpactInfoVisibleLabel.value = true;
                                                    ref.rateEmotionalImpactInfoVisible.value =
                                                        !ref.rateEmotionalImpactInfoVisible.value;
                                                    log('message${ref.rateEmotionalImpactInfoVisible.value}');
                                                    if (ref.rateEmotionalImpactInfoVisible.value) {
                                                      ref.isManuallyPaused.value = false;
                                                      ref.infoAudioUrl.value = AssessmentLocaleKeys
                                                          .emotionalImpactQuestionsRateInfoAudio
                                                          .tr();
                                                    } else {
                                                      ref.isManuallyPaused.value = true;
                                  
                                                      ref.infoAudioUrl.value =
                                                          AssessmentLocaleKeys.emotionalImpactAudio.tr();
                                                    }
                                                  },
                                                  infoWidget: CustomInfoWidget(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w12,
                                                      right: AppSize.w12,
                                                      top: AppSize.h10,
                                                    ),
                                                    visible: rateEmtionalImpactInfoVisible,
                                                    onCloseTap: () {
                                                      ref.rateEmotionalImpactInfoVisibleLabel.value = true;
                                                      ref.isManuallyPaused.value = true;
                                  
                                                      ref.infoAudioUrl.value =
                                                          AssessmentLocaleKeys.emotionalImpactAudio.tr();
                                                      ref.rateEmotionalImpactInfoVisible.value = false;
                                                    },
                                                    bodyText: AssessmentLocaleKeys
                                                        .emotionalImpactQuestionsRateInfoText
                                                        .tr(),
                                                  ),
                                                  questionText:
                                                      AssessmentLocaleKeys.emotionalImpactQuestionsRateLabel.tr(),
                                                );
                                              },
                                            ),
                                            SpaceV(AppSize.h20),
                                            Padding(
                                              padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                              child: SliderScreen(
                                                reverseGradient: true,
                                                firstText: (DynamicAssetLoader.getNestedValue(
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsRateSliderLabels,
                                                  context,
                                                ) as List)
                                                    .cast<String>()
                                                    .first,
                                                secondText: (DynamicAssetLoader.getNestedValue(
                                                  AssessmentLocaleKeys.emotionalImpactQuestionsRateSliderLabels,
                                                  context,
                                                ) as List)
                                                    .cast<String>()
                                                    .last,
                                                onSelect: (p0) {
                                                  ref.emtionSliderValue.value = p0;
                                                },
                                                selectedValue: ref.emtionSliderValue,
                                                isClick: ref.isEmotionButtonClicked.value,
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                          ],
                                        ),
                                        MultiValueListenableBuilder(
                                          valueListenables: [
                                            ref.nervousValue,
                                            ref.worryValue,
                                            ref.downValue,
                                            ref.badValue,
                                            ref.interestValue,
                                            ref.emtionSliderValue,
                                            ref.selectedBreakingFreeIndex,
                                          ],
                                          builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                            // Extract values for better readability
                                            final nervousValue = values[0]; // String
                                            final worryValue = values[1]; // String
                                            final downValue = values[2]; // String
                                            final badValue = values[3]; // String
                                            final interestValue = values[4]; // String
                                            final emtionSliderValue = values[5]; // int
                                            // int
                                            // Check if the button should be enabled
                                            final isButtonEnabled = nervousValue != '' &&
                                                worryValue != '' &&
                                                downValue != '' &&
                                                badValue != '' &&
                                                interestValue != '' &&
                                                emtionSliderValue != -1;
                                  
                                            return CustomButton(
                                              padding: EdgeInsets.zero,
                                              key: const Key('next_btn'),
                                              inProgress: state.isApiLoading,
                                              disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                              onTap: () async {
                                                if (!isButtonEnabled) {
                                                  ref.isEmotionButtonClicked.value = true;
                                                  '>?>?>? not isButtonEnabled: $isButtonEnabled'.logV;
                                                  //ref.isEmotionButtonClicked.value = true;
                                  
                                                  if (ref.nervousValue.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(ref.nervousKey);
                                                    return;
                                                  }
                                                  if (ref.worryValue.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(ref.worryKey);
                                                    return;
                                                  }
                                                  if (ref.downValue.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(ref.downKey);
                                                    return;
                                                  }
                                                  if (ref.badValue.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(ref.badKey);
                                                    return;
                                                  }
                                                  if (ref.interestValue.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(ref.interestKey);
                                                    return;
                                                  }
                                                  // Set flag to true when button is clicked
                                                  // CustomSnackbar.showErrorSnackBar(
                                                  //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                  // );
                                                } else {
                                                  '>?>?>? isButonEnabled: $isButtonEnabled'.logV;
                                                  //ref.infoAudioUrl.value = null;
                                                  '>?>?>? before api'.logV;
                                                  await ref.putEmotionalImpactAPI(
                                                    nervous: ref.nervousList.indexOf(ref.nervousValue.value),
                                                    worry: ref.worryList.indexOf(ref.worryValue.value),
                                                    down: ref.downList.indexOf(ref.downValue.value),
                                                    bad: ref.badList.indexOf(ref.badValue.value),
                                                    interest: ref.interestList.indexOf(ref.interestValue.value),
                                                    rate: ref.emtionSliderValue.value,
                                                    context: context,
                                                  );
                                                  ref.isManuallyPaused.value = false;
                                  
                                                  // Navigate based on selected index
                                                  // if (selectedBreakingFreeIndex == 1) {
                                                  //  // assessmentCubit.gotoNextWidget(increment: 4);
                                                  // } else {
                                                  // //  assessmentCubit.gotoNextWidget();
                                                  // }
                                                }
                                              },
                                              isBottom: true,
                                              color: isButtonEnabled
                                                  ? context.themeColors.blueColor
                                                  : context.themeColors.blueColor.withOpacity(0.7),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
