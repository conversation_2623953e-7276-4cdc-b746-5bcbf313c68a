import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life_style/assessment_life_style_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentLifeStylePage extends StatelessWidget {
  const AssessmentLifeStylePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentLifeStyleCubit, AssessmentLifeStyleState>(
      builder: (ctx, state) {
        final assessmentLifeStyleCubit = ctx.read<AssessmentLifeStyleCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        return ValueListenableBuilder(
          valueListenable: assessmentLifeStyleCubit.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: assessmentLifeStyleCubit.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              infoAudioUrl: assessmentLifeStyleCubit.infoAudioUrl,
              //infoAudioUrlStr: AssessmentLocaleKeys.lifeAudio.tr(),
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: assessmentLifeStyleCubit.isLifeStyleClicked,
                builder: (context, value, child) {
                  return AbsorbPointer(
                    absorbing: state.isApiLoading,
                    child: Column(
                      children: [
                        SpaceV(AppSize.h4),
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constrains) {
                              return Padding(
                                padding: EdgeInsets.only(right: AppSize.w4),
                                child: CustomRawScrollbar(
                                  key: PageStorageKey('5'),
                                  child: SingleChildScrollView(
                                    key: PageStorageKey('5'),
                                    child: Column(
                                      children: [
                                        ConstrainedBox(
                                          constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                          child: Padding(
                                            padding: EdgeInsets.only(
                                              left: AppSize.w24,
                                              right: AppSize.w24,
                                              bottom: AppSize.h20,
                                            ),
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  children: [
                                                    const CustomHeader(),
                                                    SpaceV(AppSize.h6),
                                                    AppTextWidget(
                                                      AssessmentLocaleKeys.lifestyleSubtitle.tr(),
                                                      style: context.textTheme.titleSmall?.copyWith(
                                                        color: context.themeColors.darkGreyColor,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    QuestionRowWidget(
                                                      key: assessmentLifeStyleCubit.healthLifeStyleKey,
                                                      questionText:
                                                          AssessmentLocaleKeys.lifestyleQuestionsHealthLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        selectedValue: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                        currentState: assessmentLifeStyleCubit.healthLifeStyleState,
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        onNoTap: () {
                                                          assessmentLifeStyleCubit.healthLifeStyleState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentLifeStyleCubit.healthLifeStyleState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    QuestionRowWidget(
                                                      key: assessmentLifeStyleCubit.workLifeStyleKey,
                                                      questionText: AssessmentLocaleKeys.lifestyleQuestionsWorkLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        selectedValue: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                        currentState: assessmentLifeStyleCubit.workLifeStyleState,
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        onNoTap: () {
                                                          assessmentLifeStyleCubit.workLifeStyleState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentLifeStyleCubit.workLifeStyleState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    QuestionRowWidget(
                                                      key: assessmentLifeStyleCubit.leisureKey,
                                                      questionText:
                                                          AssessmentLocaleKeys.lifestyleQuestionsLeisureLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        selectedValue: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                        currentState: assessmentLifeStyleCubit.leisureState,
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        onNoTap: () {
                                                          assessmentLifeStyleCubit.leisureState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentLifeStyleCubit.leisureState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    QuestionRowWidget(
                                                      key: assessmentLifeStyleCubit.relationshipsKey,
                                                      questionText:
                                                          AssessmentLocaleKeys.lifestyleQuestionsRelationshipsLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        selectedValue: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                        currentState: assessmentLifeStyleCubit.relationshipsState,
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        onNoTap: () {
                                                          assessmentLifeStyleCubit.relationshipsState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentLifeStyleCubit.relationshipsState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h26),
                                                    QuestionRowWidget(
                                                      key: assessmentLifeStyleCubit.housingKey,
                                                      questionText:
                                                          AssessmentLocaleKeys.lifestyleQuestionsHousingLabel.tr(),
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: CustomAssessmentButton(
                                                        selectedValue: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                        currentState: assessmentLifeStyleCubit.housingState,
                                                        buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                        buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                        onNoTap: () {
                                                          assessmentLifeStyleCubit.housingState.value =
                                                              ButtonState.yesEnabled;
                                                        },
                                                        onYesTap: () {
                                                          assessmentLifeStyleCubit.housingState.value =
                                                              ButtonState.noEnabled;
                                                        },
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h30),
                                                    ValueListenableBuilder(
                                                      valueListenable: assessmentLifeStyleCubit.rateLifeStyleInfoVisible,
                                                      builder: (context, rateLifeStyleInfoVisible, child) {
                                                        return QuestionRowWidget(
                                                          oninfoTap: () {
                                                            assessmentLifeStyleCubit.rateLifeStyleInfoVisibleLabel.value = true;
                                                            assessmentLifeStyleCubit.rateLifeStyleInfoVisible.value =
                                                                !assessmentLifeStyleCubit.rateLifeStyleInfoVisible.value;
                                                            log('message${assessmentLifeStyleCubit.rateLifeStyleInfoVisible.value}');
                                                            if (assessmentLifeStyleCubit.rateLifeStyleInfoVisible.value) {
                                                              assessmentLifeStyleCubit.isManuallyPaused.value = false;
                                                              assessmentLifeStyleCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys.lifestyleQuestionsRateInfoAudio
                                                                      .tr();
                                                            } else {
                                                              assessmentLifeStyleCubit.isManuallyPaused.value = true;
                                                              assessmentLifeStyleCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys.lifestyleAudio.tr();
                                                            }
                                                          },
                                                          infoWidget: CustomInfoWidget(
                                                            padding: EdgeInsets.only(
                                                              left: AppSize.w12,
                                                              right: AppSize.w12,
                                                              top: AppSize.h10,
                                                            ),
                                                            visible: rateLifeStyleInfoVisible,
                                                            onCloseTap: () {
                                                              assessmentLifeStyleCubit.rateLifeStyleInfoVisibleLabel.value = true;
                                                              assessmentLifeStyleCubit.isManuallyPaused.value = true;
                                    
                                                              assessmentLifeStyleCubit.infoAudioUrl.value =
                                                                  AssessmentLocaleKeys.lifestyleAudio.tr();
                                                              assessmentLifeStyleCubit.rateLifeStyleInfoVisible.value =
                                                                  false;
                                                            },
                                                            bodyText:
                                                                AssessmentLocaleKeys.lifestyleQuestionsRateInfoText.tr(),
                                                          ),
                                                          questionText: AssessmentLocaleKeys
                                                              .lifestyleQuestionsRateLabel
                                                              .tr(),
                                                        );
                                                      },
                                                    ),
                                                    SpaceV(AppSize.h20),
                                                    Padding(
                                                      padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                      child: SliderScreen(
                                                        reverseGradient: true,
                                                        firstText: (DynamicAssetLoader.getNestedValue(
                                                          AssessmentLocaleKeys.lifestyleQuestionsRateSliderLabels,
                                                          context,
                                                        ) as List)
                                                            .cast<String>()
                                                            .first,
                                                        secondText: (DynamicAssetLoader.getNestedValue(
                                                          AssessmentLocaleKeys.lifestyleQuestionsRateSliderLabels,
                                                          context,
                                                        ) as List)
                                                            .cast<String>()
                                                            .last,
                                                        onSelect: (p0) {
                                                          assessmentLifeStyleCubit.rateLifeStyleSliderVlaue.value = p0;
                                                        },
                                                        selectedValue: assessmentLifeStyleCubit.rateLifeStyleSliderVlaue,
                                                        isClick: assessmentLifeStyleCubit.isLifeStyleClicked.value,
                                                      ),
                                                    ),
                                                    SpaceV(AppSize.h30),
                                                  ],
                                                ),
                                                MultiValueListenableBuilder(
                                                  valueListenables: [
                                                    assessmentLifeStyleCubit.healthLifeStyleState,
                                                    assessmentLifeStyleCubit.workLifeStyleState,
                                                    assessmentLifeStyleCubit.leisureState,
                                                    assessmentLifeStyleCubit.relationshipsState,
                                                    assessmentLifeStyleCubit.housingState,
                                                    assessmentLifeStyleCubit.rateLifeStyleSliderVlaue,
                                                  ],
                                                  builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                                    // Extract the state values for clarity
                                                    final healthLifeStyleState = values[0]; // ButtonState
                                                    final workLifeStyleState = values[1]; // ButtonState
                                                    final leisureState = values[2]; // ButtonState
                                                    final relationshipsState = values[3]; // ButtonState
                                                    final housingState = values[4]; // ButtonState
                                                    final rateLifeStyleSliderValue = values[5]; // Slider value
                                    
                                                    // Check conditions to enable the button
                                                    final isButtonEnabled =
                                                        healthLifeStyleState != ButtonState.bothDisabled &&
                                                            workLifeStyleState != ButtonState.bothDisabled &&
                                                            leisureState != ButtonState.bothDisabled &&
                                                            relationshipsState != ButtonState.bothDisabled &&
                                                            housingState != ButtonState.bothDisabled &&
                                                            rateLifeStyleSliderValue != -1;
                                    
                                                    return CustomButton(
                                                      padding: EdgeInsets.zero,
                                                      key: const Key('next_btn'),
                                                      inProgress: state.isApiLoading,
                                                      disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                                      onTap: () async {
                                                        if (!isButtonEnabled) {
                                                          assessmentLifeStyleCubit.isLifeStyleClicked.value = true;
                                                          if (assessmentLifeStyleCubit.healthLifeStyleState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentLifeStyleCubit.healthLifeStyleKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentLifeStyleCubit.workLifeStyleState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentLifeStyleCubit.workLifeStyleKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentLifeStyleCubit.leisureState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentLifeStyleCubit.leisureKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentLifeStyleCubit.relationshipsState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentLifeStyleCubit.relationshipsKey,
                                                            );
                                                            return;
                                                          }
                                                          if (assessmentLifeStyleCubit.housingState.value ==
                                                              ButtonState.bothDisabled) {
                                                            await AppCommonFunctions.scrollToKey(
                                                              assessmentLifeStyleCubit.housingKey,
                                                            );
                                                            return;
                                                          }
                                                        } else {
                                                          await assessmentLifeStyleCubit.putLifeStyleAPI(
                                                            context: context,
                                                            health: healthLifeStyleState == ButtonState.noEnabled ? 0 : 1,
                                                            housing: housingState == ButtonState.noEnabled ? 0 : 1,
                                                            leisure: leisureState == ButtonState.noEnabled ? 0 : 1,
                                                            rate: assessmentLifeStyleCubit.rateLifeStyleSliderVlaue.value,
                                                            relationships:
                                                                relationshipsState == ButtonState.noEnabled ? 0 : 1,
                                                            work: workLifeStyleState == ButtonState.noEnabled ? 0 : 1,
                                                          );
                                                          //assessmentCubit.gotoNextWidget();
                                                        }
                                                      },
                                                      isBottom: true,
                                                      color: isButtonEnabled
                                                          ? context.themeColors.blueColor
                                                          : context.themeColors.blueColor.withOpacity(0.7),
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
