import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_main_page.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/assessment_header_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_video_player_screen.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentVideoPage extends StatelessWidget {
  const AssessmentVideoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentCubit, AssessmentState>(
      builder: (ctx, state) {
        final assessmentCubit = ctx.read<AssessmentCubit>();
        return AppScaffold(
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            suffixIcon: Icon(
              Icons.volume_up,
              size: AppSize.h24,
              color: context.themeColors.greyColor,
            ),
            onSuffixTap: () {
              // ctx.read<AssessmentCubit>().isAudioPannelVisible.value =
              //     !ctx.read<AssessmentCubit>().isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible: ctx.read<AssessmentCubit>().isAudioPannelVisible,
          infoAudioUrlStr: AssessmentLocaleKeys.lifeAudio.tr(),
          resizeToAvoidBottomInset: false,
          body: ValueListenableBuilder(
            valueListenable: assessmentCubit.isAssessmentVideoEnded,
            builder: (context, value, child) {
              return ValueListenableBuilder(
                valueListenable: assessmentCubit.isButtonClicked,
                builder: (context, isButtonClicked, child) {
                  return AbsorbPointer(
                    absorbing: state.isAssessmentVideoAPILoading,
                    child: LayoutBuilder(
                      builder: (context, constrains) {
                        return SingleChildScrollView(
                          child: ConstrainedBox(
                            constraints: BoxConstraints(minHeight: constrains.maxHeight),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(
                                        left: AppSize.w24,
                                        right: AppSize.w24,
                                        top: AppSize.h4,
                                      ),
                                      child: CustomAssessmentHeaderWidget(
                                        onBackTap: getBackFunction(ctx, state.index),
                                        activeDotIndex: state.index,
                                        subTitle: getSubTitle(state.index),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: AppSize.w24),
                                      child: VideoPlayerScreen(
                                        onVideo90PercentageEnded: () {
                                          assessmentCubit.isAssessmentVideoEnded.value = true;
                                        },
                                        onVideoEnded: () async {
                                          assessmentCubit.isAssessmentVideoEnded.value = true;
                                          '///done1'.logV;
                                          await Future.delayed(const Duration(milliseconds: 300));
                                          if (Navigator.of(context).canPop()) {
                                            Navigator.of(context).pop();
                                          }
                                          log('value:  ${assessmentCubit.isAssessmentVideoEnded.value}');
                                        },
                                        imageList: [AssessmentLocaleKeys.assessmentVideoPoster.tr()],
                                        navigationFunction: () {},
                                        videoList: [AssessmentLocaleKeys.assessmentVideoSrc.tr()],
                                      ),
                                    ),
                                    if (!value && assessmentCubit.isButtonClicked.value)
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: AppSize.w24,
                                        ),
                                        child: CustomErrorWidget(
                                          spacing: AppSize.h14,
                                          errorMessgaeText: AssessmentLocaleKeys.errorsVideoMessage.tr(),
                                        ),
                                      )
                                    else
                                      const SizedBox(),
                                  ],
                                ),
                                CustomButton(
                                  key: const Key('next_btn'),
                                  inProgress: state.isAssessmentVideoAPILoading,
                                  disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                  title: CoreLocaleKeys.buttonsNext.tr(),
                                  onTap: () async {
                                    log('value:  $value');
                                    if (value) {
                                      await ctx.read<AssessmentCubit>().assessmentVideoAPI(context: context);
                                      assessmentCubit.isButtonClicked.value = false;

                                      // assessmentCubit.gotoNextWidget();
                                    } else {
                                      assessmentCubit.isButtonClicked.value = true;

                                      // CustomSnackbar.showErrorSnackBar(
                                      //   message: AssessmentLocaleKeys.errorsVideoMessage.tr(),
                                      //   color: context.themeColors.redColor,
                                      // );
                                    }
                                  },
                                  isBottom: true,
                                  color: value
                                      ? context.themeColors.blueColor
                                      : context.themeColors.blueColor.withOpacity(0.7),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}
