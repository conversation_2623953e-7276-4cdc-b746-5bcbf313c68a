import 'package:breakingfree_v2/custom_widgets/app_loader.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_arrow.dart';
import 'package:breakingfree_v2/custom_widgets/custom_divider.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_yesno_button2.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_notification/assessment_notification_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_notification_row_widget.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/cubit/my_alert_cubit.dart';
import 'package:breakingfree_v2/features/home_module/my_alert_module/keys/app_locale_key.dart';
import 'package:breakingfree_v2/features/home_module/my_diagram_module/my_diagram_page/my_diagram_page.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/gen/assets.gen.dart';
import 'package:breakingfree_v2/location_service/location_service.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

class AssessmentNotificationPage extends StatefulWidget {
  const AssessmentNotificationPage({super.key});

  @override
  State<AssessmentNotificationPage> createState() => _AssessmentNotificationPageState();
}

class _AssessmentNotificationPageState extends State<AssessmentNotificationPage> {
  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    '>?>?>? resumed'.logV;
    if (state == AppLifecycleState.resumed) {
      context.read<AssessmentNotificationCubit>().isGrantedNotification.value =
          await Permission.notification.status.isGranted;
      context.read<AssessmentNotificationCubit>().isGrantedNotification.value
          ? context.read<AssessmentNotificationCubit>().isGrantedNotificationLabel.value = true
          : context.read<AssessmentNotificationCubit>().isGrantedNotificationLabel.value = false;
      if (context.read<AssessmentNotificationCubit>().isGrantedNotification.value) {
        await FlutterLocalNotificationsPlugin()
            .initialize(const InitializationSettings(iOS: DarwinInitializationSettings()));
      }
      context.read<AssessmentNotificationCubit>().isGrantedLocation.value = await Permission.location.status.isGranted;
      context.read<AssessmentNotificationCubit>().isGrantedLocation.value
          ? context.read<AssessmentNotificationCubit>().isGrantedLocationLabel.value = true
          : context.read<AssessmentNotificationCubit>().isGrantedLocationLabel.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentNotificationCubit, AssessmentNotificationState>(
      builder: (ctx, state) {
        final ref = ctx.read<AssessmentNotificationCubit>();
        return ValueListenableBuilder(
          valueListenable: ref.isButtomClicked,
          builder: (context, value, child) {
            return AppScaffold(
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                suffixIcon: Icon(
                  Icons.volume_up,
                  size: AppSize.h24,
                  color: context.themeColors.greyColor,
                ),
                onSuffixTap: () {
                  //ctx.read<AssessmentCubit>().isAudioPannelVisible.value = !ctx.read<AssessmentCubit>().isAudioPannelVisible.value;
                },
              ),
              // isAudioPanelVisible: ctx.read<AssessmentCubit>().isAudioPannelVisible,
              infoAudioUrlStr: AssessmentLocaleKeys.lifeAudio.tr(),
              resizeToAvoidBottomInset: false,
              body: ValueListenableBuilder(
                valueListenable: ref.isGrantedLocationLabel,
                builder: (context, value, child) {
                  return ValueListenableBuilder(
                    valueListenable: ref.isGrantedLocationLabel,
                    builder: (context, value, child) {
                      return ValueListenableBuilder(
                        valueListenable: ref.isLoading,
                        builder: (context, value, child) {
                          return AppLoader(
                            isShowLoader: ref.isLoading.value,
                            child: LayoutBuilder(
                              builder: (context, constrains) {
                                return SingleChildScrollView(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.only(
                                                left: AppSize.w24,
                                                right: AppSize.w24,
                                              ),
                                              child: Column(
                                                children: [
                                                  SpaceV(AppSize.h16),
                                                  Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      CustomBackArrowButton(
                                                        // action: true,
                                                        padding: 0,
                                                        onTap: () => AppNavigation.previousScreen(context),
                                                      ),
                                                      AppTextWidget(
                                                        CoreLocaleKeys.pageTitle.tr(),
                                                        textAlign: TextAlign.center,
                                                        style: context.textTheme.titleMedium?.copyWith(
                                                          fontWeight: FontWeight.w500,
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.h20),
                                                    ],
                                                  ),
                                                  Padding(
                                                    padding: EdgeInsets.only(
                                                      bottom: AppSize.h12,
                                                      top: AppSize.h10,
                                                    ),
                                                    child: const CustomDivider(),
                                                  ),
                                                  AppTextWidget(
                                                    AppLocaleKey.assessmentNotificationText1.tr(),
                                                    style: context.textTheme.titleSmall,
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  AppTextWidget(
                                                    AppLocaleKey.assessmentNotificationText2.tr(),
                                                    style: context.textTheme.titleSmall,
                                                  ),
                                                  SpaceV(AppSize.h24),
                                                  CustomNotificationRowWidget(
                                                    imageUrl: Assets.icons.dashboard.updateReport,
                                                    text: AppLocaleKey.assessmentNotificationProgressCheck.tr(),
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  CustomNotificationRowWidget(
                                                    imageUrl: Assets.icons.actionIcons.situtation,
                                                    text: AppLocaleKey.assessmentNotificationSituations.tr(),
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  CustomNotificationRowWidget(
                                                    imageUrl: Assets.icons.actionStrategiesPlanningYourTimePositively,
                                                    text: AppLocaleKey.assessmentNotificationActivities.tr(),
                                                  ),
                                                  SpaceV(AppSize.h14),
                                                  CustomNotificationRowWidget(
                                                    imageUrl: Assets.icons.actionIcons.lifestyle,
                                                    text: AppLocaleKey.assessmentNotificationLifestyle.tr(),
                                                  ),
                                                  SpaceV(AppSize.h20),
                                                  AppTextWidget(AppLocaleKey.assessmentNotificationText3.tr(),
                                                    style: context.textTheme.titleSmall,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                        CustomYesNoButton2(
                                          isShowArrowForward: false,
                                          isShowArrowBackward: false,
                                          isYesNoButton: true,
                                          exitText: AppLocaleKey.assessmentNotificationDontAllow.tr(),
                                          agreeText: AppLocaleKey.assessmentNotificationAllow.tr(),
                                          onTapYes: () async {
                                            await LocationService.checkNotificationPermission(context: context);
                                            await LocationService.checkLocationForPermission(context: context);
                                            ref.isGrantedNotification.value = await Permission.notification.status.isGranted;
                                            ref.isGrantedLocation.value = await Permission.location.status.isGranted;
                                            ref.isGrantedNotification.value
                                                ? ref.isGrantedNotificationLabel.value = true
                                                : ref.isGrantedNotificationLabel.value = false;
                                            ref.isGrantedLocation.value
                                                ? ref.isGrantedLocationLabel.value = true
                                                : ref.isGrantedLocationLabel.value = false;
                                            if (context.read<AssessmentNotificationCubit>().isGrantedNotification.value) {
                                              await FlutterLocalNotificationsPlugin().initialize(
                                                const InitializationSettings(
                                                  iOS: DarwinInitializationSettings(),
                                                  android: AndroidInitializationSettings('ic_launcher'),
                                                ),
                                              );
                                            }
                                            if(ref.isGrantedNotification.value || ref.isGrantedLocation.value){
                                              ref.isLoading.value = true;
                                              await context.read<MyAlertCubit>().setMySituationAlert(value: true);
                                              await context.read<MyAlertCubit>().setPlanningAlert(value: true);
                                              await context.read<MyAlertCubit>().setMyLifestyleAlert(value: true);
                                              await context.read<MyAlertCubit>().setProgressCheckAlert(value: true);
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: false, alertType: 'progress');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: false, alertType: 'situation');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: false, alertType: 'planning');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: false, alertType: 'activity');
                                              context.read<MyAlertCubit>()..initalData();
                                              ref.isLoading.value = false;
                                            }
                                            // if(!ref.isGrantedNotification.value && !ref.isGrantedLocation.value){
                                            //   await context.read<MyAlertCubit>().setProgressCheckAlert(value: false);
                                            //   await context.read<MyAlertCubit>().setMySituationAlert(value: false);
                                            //   await context.read<MyAlertCubit>().setPlanningAlert(value: false);
                                            //   await context.read<MyAlertCubit>().setMyLifestyleAlert(value: false);
                            
                                            //   await context.read<MyAlertCubit>().myAlertCall(isNo: true, alertType: 'progress');
                                            // }
                                            '>?>?>?>?>? notification = ${ref.isGrantedNotification.value}'.logV;
                                            '>?>?>?>?>? location = ${ref.isGrantedLocation.value}'.logV;
                                            if (ref.isGrantedNotification.value || ref.isGrantedLocation.value) {
                                              await AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                            }
                                          },
                                          onTapNo: () async {
                                            ref.isButtomClicked.value = true;
                                            '>?>?>? 4'.logV;
                                            if(!ref.isGrantedNotification.value && !ref.isGrantedLocation.value){
                                              ref.isLoading.value = true;
                                              await context.read<MyAlertCubit>().setProgressCheckAlert(value: false);
                                              await context.read<MyAlertCubit>().setMySituationAlert(value: false);
                                              await context.read<MyAlertCubit>().setPlanningAlert(value: false);
                                              await context.read<MyAlertCubit>().setMyLifestyleAlert(value: false);
                            
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: true, alertType: 'progress');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: true, alertType: 'situation');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: true, alertType: 'planning');
                                              await context.read<MyAlertCubit>().myAlertCall(isNo: true, alertType: 'activity');
                                              context.read<MyAlertCubit>()..initalData();
                                              ref.isLoading.value = false;
                                            }
                                            await AppNavigation.pushAndRemoveAllScreen(context, const MyDiagramPage());
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
