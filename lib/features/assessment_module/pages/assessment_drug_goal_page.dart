import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentDrugGoalPage extends StatelessWidget {
  const AssessmentDrugGoalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
      builder: (ctx, state) {
        final assessmentCubit = ctx.read<AssessmentDrugCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();

        return AppScaffold(
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            onSuffixTap: () {
              assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible:  assessmentCubit1.isAudioPannelVisible,
          infoAudioUrl: (num.parse(state.drugDetailList.first.amount.toString()) <= 0 &&
                                            num.parse(state.drugDetailList.first.frequency.toString()) <= 0) ?
                                             ValueNotifier(AssessmentLocaleKeys.drugsGoalAbstinentAudio.tr()) :
                                             ValueNotifier(AssessmentLocaleKeys.drugsGoalTakingAudio.tr()),
          resizeToAvoidBottomInset: true,
          body: AbsorbPointer(
            absorbing: state.isGoalApiLoading,
            child: Column(
              children: [
                SpaceV(AppSize.h4),
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constrains) {
                      return Padding(
                        padding: EdgeInsets.only(right: AppSize.w4),
                        child: CustomRawScrollbar(
                          child: SingleChildScrollView(
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: constrains.maxHeight),
                              child: Padding(
                                padding: EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, bottom: AppSize.h20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const CustomHeader(),
                                        SpaceV(AppSize.h6),
                                        if (num.parse(state.drugDetailList.first.amount.toString()) <= 0 &&
                                            num.parse(state.drugDetailList.first.frequency.toString()) <= 0) ...[
                                          AppTextWidget(
                                            (DynamicAssetLoader.getNestedValue(
                                              AssessmentLocaleKeys.drugsGoalAbstinentSubtitle,
                                              context,
                                            ) as List)
                                                .cast<String>()
                                                .join('\n\n'),
                                            style: context.textTheme.titleSmall?.copyWith(
                                              color: context.themeColors.darkGreyColor,
                                            ),
                                          ),
                                        ] else ...[
                                          AppTextWidget(
                                            (DynamicAssetLoader.getNestedValue(
                                              AssessmentLocaleKeys.drugsGoalTakingSubtitle,
                                              context,
                                            ) as List)
                                                .cast<String>()
                                                .join('\n\n'),
                                            style: context.textTheme.titleSmall?.copyWith(
                                              color: context.themeColors.darkGreyColor,
                                            ),
                                          ),
                                        ],
                                        SpaceV(AppSize.h20),
                                        QuestionRowWidget(
                                          questionText: AppCommonFunctions.getFormattedTranslation(
                                            AssessmentLocaleKeys.drugsGoalUnitsLabel,
                                            {
                                              'drug': state.drugDetailList.isEmpty
                                                  ? ''
                                                  : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',),
                                              'unit': state.drugDetailList.isEmpty
                                                  ? ''
                                                  : assessmentCubit1.formatString(state.drugDetailList.first.unit ?? ''),
                                            },
                                          ),
                                          // questionText: AssessmentLocaleKeys.drugsGoalUnitsLabel.tr(
                                          //   namedArgs: {
                                          //     'drug':
                                          //         state.drugDetailList.isEmpty ? '' : state.drugDetailList.first.drug ?? '',
                                          //     'unit':
                                          //         state.drugDetailList.isEmpty ? '' : state.drugDetailList.first.unit ?? '',
                                          //   },
                                          // ),
                                        ),
                                        SpaceV(AppSize.h10),
                                        CustomAgeSelcetionWidget(
                                          controller: assessmentCubit.drugGoalUnitsController,
                                          onIncreaseTap: assessmentCubit.increaseDrugGoal,
                                          onDecreaseTap: assessmentCubit.decreaseDrugGoal,
                                        ),
                                        SpaceV(AppSize.h30),
                                        QuestionRowWidget(
                                          questionText: AppCommonFunctions.getFormattedTranslation(
                                            AssessmentLocaleKeys.drugsGoalFreeDaysLabel,
                                            {
                                              'drug': state.drugDetailList.isEmpty
                                                  ? ''
                                                  : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',), // assessmentCubit1.formatString(state.drugDetailList.first.drug ?? ''),
                                            },
                                          ),
                                        ),
                                        SpaceV(AppSize.h10),
                                        CustomAgeSelcetionWidget(
                                          inputFormatters: [
                                            FilteringTextInputFormatter.digitsOnly,
                                            TextInputFormatter.withFunction(
                                                  (oldValue, newValue) {
                                                if (newValue.text.isEmpty) {
                                                  return newValue;
                                                }

                                                final value = int.tryParse(newValue.text);
                                                if (value != null && value <= 7) {
                                                  return newValue;
                                                }

                                                return oldValue;
                                              },
                                            ),
                                          ],
                                          controller: assessmentCubit.drugGoalFreeDayController,
                                          onIncreaseTap: assessmentCubit.increaseDrugGoalFreeDayValue,
                                          onDecreaseTap: assessmentCubit.decreaseDrugFreeDayValue,
                                        ),
                                        SpaceV(AppSize.h30),
                                      ],
                                    ),
                                    CustomButton(
                                      padding: EdgeInsets.zero,
                                      key: const Key('next_btn'),
                                      disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                      title: CoreLocaleKeys.buttonsNext.tr(),
                                      inProgress: state.isGoalApiLoading,
                                      onTap: () async {
                                        '/// ${state.drugGoalUnitValue}'.logV;
                                        await assessmentCubit.putdrugGoalAPI(
                                          units: double.tryParse(assessmentCubit.drugGoalUnitsController.text) ?? state.currentdrugfreeDaysValue.toDouble(),
                                          freeDays: int.tryParse(assessmentCubit.drugGoalFreeDayController.text) ?? state.currentdrugfreeDaysValue,
                                          context: context,
                                          // freeDays: state.currentdrugfreeDaysValue,
                                          // units: state.drugGoalUnitValue.toDouble(), // change 0.0
                                          // context: context,
                                        );
                                        //  assessmentCubit.gotoNextWidget();
                                      },
                                      isBottom: true,
                                      color: context.themeColors.blueColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
