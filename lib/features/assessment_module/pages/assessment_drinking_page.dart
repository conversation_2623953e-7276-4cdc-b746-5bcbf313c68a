import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_pdf_opener_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_age_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drinking_help_me_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/us_drinking_helper_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class AssessmentDrinkingPage extends StatelessWidget {
  const AssessmentDrinkingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentDrinkingCubit, AssessmentDrinkingState>(
      builder: (ctx, state) {
        final assessmentDrinkingCubit = ctx.read<AssessmentDrinkingCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        'context.locale.countryCode ${context.locale.countryCode}'.logV;
        'context.locale.countryCode ${context.locale.languageCode}'.logV;
        return ValueListenableBuilder(
          valueListenable: assessmentDrinkingCubit.infoAudioUrl,
          builder: (context, value, child) {
            return AppScaffold(
              isManuallyPaused: assessmentDrinkingCubit.isManuallyPaused,
              appBar: CommonAppBar(
                prefixIcon: Icon(
                  Icons.logout,
                  size: AppSize.sp20,
                ),
                onPrefixTap: () {
                  LogOutDialog.showLogOutDialog(context);
                },
                onSuffixTap: () {
                  assessmentCubit1.isAudioPannelVisible.value =
                      !assessmentCubit1.isAudioPannelVisible.value;
                },
              ),
              isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
              // infoAudioUrlStr: AssessmentLocaleKeys.lifeAudio.tr(),
              infoAudioUrl: assessmentDrinkingCubit.infoAudioUrl,
              resizeToAvoidBottomInset: true,
              body: ValueListenableBuilder(
                valueListenable:
                    assessmentDrinkingCubit.rateDrinkingSliderValue,
                builder: (context, value, child) {
                  return ValueListenableBuilder(
                    valueListenable:
                        assessmentDrinkingCubit.isDrinkingButtonClick,
                    builder: (context, value, child) {
                      return ValueListenableBuilder(
                        valueListenable:
                            assessmentDrinkingCubit.isUnitAndDrinkingButton,
                        builder: (context, value, child) {
                          return AbsorbPointer(
                            absorbing: state.isApiLoading,
                            child: Column(
                              children: [
                                SpaceV(AppSize.h4),
                                Expanded(
                                  child: LayoutBuilder(
                                    builder: (context, constraints) {
                                      return Padding(
                                        padding:
                                            EdgeInsets.only(right: AppSize.w4),
                                        child: CustomRawScrollbar(
                                          key: PageStorageKey('7'),
                                          child: SingleChildScrollView(
                                            key:  PageStorageKey('7'),
                                            child: Column(
                                              children: [
                                                ConstrainedBox(
                                                  constraints: BoxConstraints(
                                                      minHeight:
                                                          constraints.maxHeight),
                                                  child: Padding(
                                                    padding: EdgeInsets.only(
                                                      left: AppSize.w24,
                                                      right: AppSize.w24,
                                                      bottom: AppSize.h20,
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment.start,
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            const CustomHeader(),
                                                            SpaceV(AppSize.h6),
                                                            AppTextWidget(
                                                              (DynamicAssetLoader
                                                                      .getNestedValue(
                                                                AssessmentLocaleKeys
                                                                    .drinkingSubtitle0,
                                                                context,
                                                              ) as List)
                                                                  .cast<String>()
                                                                  .join('\n\n'),
                                                              style: context
                                                                  .textTheme
                                                                  .titleSmall
                                                                  ?.copyWith(
                                                                color: context
                                                                    .themeColors
                                                                    .darkGreyColor,
                                                              ),
                                                            ),
                                                            SpaceV(AppSize.h20),
                                                            QuestionRowWidget(
                                                              questionText:
                                                                  AssessmentLocaleKeys
                                                                      .drinkingQuestionsUnitsLabel
                                                                      .tr(),
                                                            ),
                                                            SpaceV(AppSize.h18),
                                                            Column(
                                                              children: [
                                                                CustomAgeSelcetionWidget(
                                                                  key:
                                                                      assessmentDrinkingCubit
                                                                          .unitKey,
                                                                  controller:
                                                                      assessmentDrinkingCubit
                                                                          .unitsController,
                                                                  onIncreaseTap:
                                                                      assessmentDrinkingCubit
                                                                          .increaseDrinkingValue,
                                                                  onDecreaseTap:
                                                                      assessmentDrinkingCubit
                                                                          .decreaseDrinkingValue,
                                                                  unitText:
                                                                      assessmentDrinkingCubit
                                                                          .unitsController
                                                                          .text,
                                                                  isButtonClicked:
                                                                      assessmentDrinkingCubit
                                                                          .isUnitAndDrinkingButton
                                                                          .value,
                                                                ),
                                                                if (assessmentDrinkingCubit
                                                                        .isUnitAndDrinkingButton
                                                                        .value &&
                                                                    (state.currentDrinkingValue ==
                                                                            0 &&
                                                                        state.currentDayValue >
                                                                            0))
                                                                  Padding(
                                                                    padding: EdgeInsets
                                                                        .symmetric(
                                                                            horizontal:
                                                                                AppSize.w24),
                                                                    child:
                                                                        CustomErrorWidget(
                                                                      errorMessgaeText:
                                                                          AssessmentLocaleKeys
                                                                              .errorsInvalidUnitsMessage
                                                                              .tr(),
                                                                    ),
                                                                  ),
                                                              ],
                                                            ),
                                                            SpaceV(AppSize.h18),
                                                            if (context.locale ==
                                                                    const Locale(
                                                                        'en',
                                                                        'GB') ||
                                                                context.locale ==
                                                                    const Locale(
                                                                        'en', 'UK'))
                                                              ValueListenableBuilder(
                                                                valueListenable:
                                                                    assessmentDrinkingCubit
                                                                        .drinkHelpMeCalculate,
                                                                builder: (context,
                                                                    value, child) {
                                                                  return Column(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .only(
                                                                          left: AppSize
                                                                              .w34,
                                                                          right: AppSize
                                                                              .w28,
                                                                        ),
                                                                        child:
                                                                            CustomRoundedButton(
                                                                          onTap:
                                                                              () {
                                                                            assessmentDrinkingCubit
                                                                                .drinkHelpMeCalculate
                                                                                .value = !assessmentDrinkingCubit.drinkHelpMeCalculate.value;
                                                                            assessmentDrinkingCubit
                                                                              ..clearDrinkHistory()
                                                                              ..initializeDrinkItems();
                                                                          },
                                                                          title: AssessmentLocaleKeys
                                                                              .drinkingButton
                                                                              .tr(),
                                                                          fillColor: context
                                                                              .themeColors
                                                                              .orangeColor,
                                                                        ),
                                                                      ),
                                                                      SpaceV(AppSize
                                                                          .h18),
                                                                      ValueListenableBuilder(
                                                                        valueListenable:
                                                                            assessmentDrinkingCubit
                                                                                .drinkItemsNotifier,
                                                                        builder: (context,
                                                                            value,
                                                                            child) {
                                                                          return DrinkingHelpMeWidget(
                                                                            visible: assessmentDrinkingCubit
                                                                                .drinkHelpMeCalculate
                                                                                .value,
                                                                            drinkItems: assessmentDrinkingCubit
                                                                                .drinkItemsNotifier
                                                                                .value,
                                                                            titleText:
                                                                                state.drinkHelpMeCalculateTitleText,
                                                                            drinkItemName:
                                                                                state.selecteddrinkHelpMeCalculateTitleText,
                                                                            subTitleText:
                                                                                state.selecteddrinkHelpMeCalculateSubTitleText,
                                                                            percentageText:
                                                                                state.selecteddrinkHelpMeCalculatePercentageText,
                                                                            showBackArrow:
                                                                                state.showBackArrownQuestion,
                                                                            backonTap:
                                                                                assessmentDrinkingCubit.goBack,
                                                                            assessmentCubit:
                                                                                assessmentDrinkingCubit,
                                                                            onCloseTap:
                                                                                () {
                                                                              assessmentDrinkingCubit
                                                                                  .drinkHelpMeCalculate
                                                                                  .value = false;
                                                                            },
                                                                          );
                                                                        },
                                                                      ),
                                                                    ],
                                                                  );
                                                                },
                                                              )
                                                            else if (context
                                                                    .locale ==
                                                                const Locale(
                                                                    'en', 'AU'))
                                                              ValueListenableBuilder(
                                                                valueListenable:
                                                                    assessmentDrinkingCubit
                                                                        .audrinkHelpMeCalculate,
                                                                builder: (context,
                                                                    value, child) {
                                                                  return Column(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .only(
                                                                          left: AppSize
                                                                              .w34,
                                                                          right: AppSize
                                                                              .w28,
                                                                        ),
                                                                        child:
                                                                            CustomRoundedButton(
                                                                          onTap:
                                                                              () {
                                                                            assessmentDrinkingCubit
                                                                                .audrinkHelpMeCalculate
                                                                                .value = !assessmentDrinkingCubit.audrinkHelpMeCalculate.value;
                                                                            assessmentDrinkingCubit
                                                                              ..clearAuDrinkHistory()
                                                                              ..initializeAuDrinkItems();
                                                                          },
                                                                          title: AssessmentLocaleKeys
                                                                              .drinkingButton
                                                                              .tr(),
                                                                          fillColor: context
                                                                              .themeColors
                                                                              .orangeColor,
                                                                        ),
                                                                      ),
                                                                      SpaceV(AppSize
                                                                          .h18),
                                                                      ValueListenableBuilder(
                                                                        valueListenable:
                                                                            assessmentDrinkingCubit
                                                                                .drinkItemsAuNotifier,
                                                                        builder: (context,
                                                                            value,
                                                                            child) {
                                                                          return DrinkingHelpMeWidget(
                                                                            visible: assessmentDrinkingCubit
                                                                                .audrinkHelpMeCalculate
                                                                                .value,
                                                                            drinkItems: assessmentDrinkingCubit
                                                                                .drinkItemsAuNotifier
                                                                                .value,
                                                                            titleText:
                                                                                state.drinkHelpMeCalculateTitleText,
                                                                            drinkItemName:
                                                                                state.selecteddrinkHelpMeCalculateTitleText,
                                                                            subTitleText:
                                                                                state.selecteddrinkHelpMeCalculateSubTitleText,
                                                                            percentageText:
                                                                                state.selecteddrinkHelpMeCalculatePercentageText,
                                                                            showBackArrow:
                                                                                state.showBackArrownQuestion,
                                                                            backonTap:
                                                                                assessmentDrinkingCubit.goBackAu,
                                                                            assessmentCubit:
                                                                                assessmentDrinkingCubit,
                                                                            onCloseTap:
                                                                                () {
                                                                              assessmentDrinkingCubit
                                                                                  .audrinkHelpMeCalculate
                                                                                  .value = false;
                                                                            },
                                                                            isAustralia:
                                                                                true,
                                                                          );
                                                                        },
                                                                      ),
                                                                    ],
                                                                  );
                                                                },
                                                              )
                                                            else if (context
                                                                        .locale ==
                                                                    const Locale(
                                                                        'en',
                                                                        'US') ||
                                                                context.locale ==
                                                                    const Locale(
                                                                        'es', 'US'))
                                                              ValueListenableBuilder(
                                                                valueListenable:
                                                                    assessmentDrinkingCubit
                                                                        .usdrinkHelpMeCalculate,
                                                                builder: (context,
                                                                    value, child) {
                                                                  return Column(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Padding(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .only(
                                                                          left: AppSize
                                                                              .w34,
                                                                          right: AppSize
                                                                              .w28,
                                                                        ),
                                                                        child:
                                                                            CustomRoundedButton(
                                                                          onTap:
                                                                              () {
                                                                            assessmentDrinkingCubit
                                                                                .usdrinkHelpMeCalculate
                                                                                .value = !assessmentDrinkingCubit.usdrinkHelpMeCalculate.value;
                                                                          },
                                                                          title: AssessmentLocaleKeys
                                                                              .drinkingButton
                                                                              .tr(),
                                                                          fillColor: context
                                                                              .themeColors
                                                                              .orangeColor,
                                                                        ),
                                                                      ),
                                                                      SpaceV(AppSize
                                                                          .h18),
                                                                      Visibility(
                                                                          visible: assessmentDrinkingCubit
                                                                              .usdrinkHelpMeCalculate
                                                                              .value,
                                                                          child:
                                                                              UsDrinkingHelperWidget()
                                                                          // Container(
                                                                          //   decoration: BoxDecoration(
                                                                          //     //borderRadius: BorderRadius.circular(AppSize.r4),
                                                                          //     border: Border.all(
                                                                          //       color: context.themeColors.greyColor,
                                                                          //     ),
                                                                          //   ),
                                                                          //   child: InteractiveViewer(
                                                                          //     child: CachedNetworkImage(
                                                                          //       imageUrl: AssessmentLocaleKeys
                                                                          //           .drinkinginfographic
                                                                          //           .tr(),
                                                                          //       errorWidget: (context, url, error) {
                                                                          //         return const Icon(Icons.error);
                                                                          //       },
                                                                          //     ),
                                                                          //   ),
                                                                          // ),
                                                                          ),
                                                                    ],
                                                                  );
                                                                },
                                                              )
                                                            else if (context
                                                                        .locale ==
                                                                    const Locale(
                                                                        'en',
                                                                        'CA') ||
                                                                context.locale ==
                                                                    const Locale(
                                                                        'fr', 'CA'))
                                                              ValueListenableBuilder(
                                                                valueListenable:
                                                                    assessmentDrinkingCubit
                                                                        .cadrinkHelpMeCalculate,
                                                                builder: (context,
                                                                    value, child) {
                                                                  return ValueListenableBuilder(
                                                                    valueListenable:
                                                                        assessmentDrinkingCubit
                                                                            .isPdfLoading,
                                                                    builder:
                                                                        (context,
                                                                            value,
                                                                            child) {
                                                                      return Padding(
                                                                        padding:
                                                                            EdgeInsets
                                                                                .only(
                                                                          left: AppSize
                                                                              .w34,
                                                                          right: AppSize
                                                                              .w28,
                                                                        ),
                                                                        child: value
                                                                            ? Center(
                                                                                child:
                                                                                    CupertinoActivityIndicator(
                                                                                  radius: AppSize.r8,
                                                                                  color: context.themeColors.greenColor,
                                                                                ),
                                                                              )
                                                                            : CustomRoundedButton(
                                                                                onTap:
                                                                                    () async {
                                                                                  assessmentDrinkingCubit.cadrinkHelpMeCalculate.value = !assessmentDrinkingCubit.cadrinkHelpMeCalculate.value;
                                                                                  await PdfFromUrlOpener.downloadAndOpenPDF(
                                                                                    'assessment.drinking.pdf'.tr(),
                                                                                    assessmentDrinkingCubit.isPdfLoading,
                                                                                  );
                                                                                },
                                                                                title:
                                                                                    AssessmentLocaleKeys.drinkingButton.tr(),
                                                                                fillColor:
                                                                                    context.themeColors.orangeColor,
                                                                              ),
                                                                      );
                                                                    },
                                                                  );
                                                                },
                                                              ),
                                                            SpaceV(AppSize.h20),
                                                            QuestionRowWidget(
                                                              questionText:
                                                                  AssessmentLocaleKeys
                                                                      .drinkingQuestionsDaysLabel
                                                                      .tr(),
                                                            ),
                                                            SpaceV(AppSize.h18),
                                                            Column(
                                                              children: [
                                                                CustomAgeSelcetionWidget(
                                                                  key:
                                                                      assessmentDrinkingCubit
                                                                          .dayKey,
                                                                  controller:
                                                                      assessmentDrinkingCubit
                                                                          .daysController,
                                                                  inputFormatters: [
                                                                    FilteringTextInputFormatter
                                                                        .digitsOnly,
                                                                    TextInputFormatter
                                                                        .withFunction(
                                                                      (oldValue,
                                                                          newValue) {
                                                                        if (newValue
                                                                            .text
                                                                            .isEmpty) {
                                                                          return newValue;
                                                                        }
                                                
                                                                        final value =
                                                                            int.tryParse(
                                                                                newValue.text);
                                                                        if (value !=
                                                                                null &&
                                                                            value <=
                                                                                7) {
                                                                          return newValue;
                                                                        }
                                                
                                                                        return oldValue;
                                                                      },
                                                                    ),
                                                                  ],
                                                                  onIncreaseTap:
                                                                      assessmentDrinkingCubit
                                                                          .increaseDaysValue,
                                                                  onDecreaseTap:
                                                                      assessmentDrinkingCubit
                                                                          .decreaseDayValue,
                                                                  unitText:
                                                                      assessmentDrinkingCubit
                                                                          .daysController
                                                                          .text,
                                                                  isButtonClicked:
                                                                      assessmentDrinkingCubit
                                                                          .isUnitAndDrinkingButton
                                                                          .value,
                                                                ),
                                                                if (assessmentDrinkingCubit
                                                                        .isUnitAndDrinkingButton
                                                                        .value &&
                                                                    (state.currentDrinkingValue >
                                                                            0 &&
                                                                        state.currentDayValue ==
                                                                            0))
                                                                  Padding(
                                                                    padding: EdgeInsets
                                                                        .symmetric(
                                                                            horizontal:
                                                                                AppSize.w24),
                                                                    child:
                                                                        CustomErrorWidget(
                                                                      errorMessgaeText:
                                                                          AssessmentLocaleKeys
                                                                              .errorsInvalidDaysMessage
                                                                              .tr(),
                                                                    ),
                                                                  ),
                                                              ],
                                                            ),
                                                            SpaceV(AppSize.h30),
                                                            ValueListenableBuilder(
                                                              valueListenable:
                                                                  assessmentDrinkingCubit
                                                                      .rateDrinkingInfoVisible,
                                                              builder: (context,
                                                                  rateEmtionalImpactInfoVisible,
                                                                  child) {
                                                                return QuestionRowWidget(
                                                                  oninfoTap: () {
                                                                    assessmentDrinkingCubit
                                                                        .rateDrinkingInfoVisibleLabel
                                                                        .value = true;
                                                                    assessmentDrinkingCubit
                                                                            .rateDrinkingInfoVisible
                                                                            .value =
                                                                        !assessmentDrinkingCubit
                                                                            .rateDrinkingInfoVisible
                                                                            .value;
                                                                    log('message${assessmentDrinkingCubit.rateDrinkingInfoVisible.value}');
                                                                    if (assessmentDrinkingCubit
                                                                        .rateDrinkingInfoVisible
                                                                        .value) {
                                                                      assessmentDrinkingCubit
                                                                          .isManuallyPaused
                                                                          .value = false;
                                                                      assessmentDrinkingCubit
                                                                              .infoAudioUrl
                                                                              .value =
                                                                          AssessmentLocaleKeys
                                                                              .drinkingQuestionsRateInfoAudio
                                                                              .tr();
                                                                    } else {
                                                                      assessmentDrinkingCubit
                                                                          .isManuallyPaused
                                                                          .value = true;
                                                                      assessmentDrinkingCubit
                                                                              .infoAudioUrl
                                                                              .value =
                                                                          AssessmentLocaleKeys
                                                                              .drinkingAudio
                                                                              .tr();
                                                                    }
                                                                  },
                                                                  infoWidget:
                                                                      CustomInfoWidget(
                                                                    padding:
                                                                        EdgeInsets
                                                                            .only(
                                                                      left: AppSize
                                                                          .w12,
                                                                      right: AppSize
                                                                          .w12,
                                                                      top: AppSize
                                                                          .h10,
                                                                    ),
                                                                    visible:
                                                                        rateEmtionalImpactInfoVisible,
                                                                    onCloseTap: () {
                                                                      assessmentDrinkingCubit
                                                                          .rateDrinkingInfoVisibleLabel
                                                                          .value = true;
                                                                      assessmentDrinkingCubit
                                                                          .isManuallyPaused
                                                                          .value = true;
                                                                      assessmentDrinkingCubit
                                                                              .infoAudioUrl
                                                                              .value =
                                                                          AssessmentLocaleKeys
                                                                              .drinkingAudio
                                                                              .tr();
                                                                      assessmentDrinkingCubit
                                                                          .rateDrinkingInfoVisible
                                                                          .value = false;
                                                                    },
                                                                    bodyText:
                                                                        AssessmentLocaleKeys
                                                                            .drinkingQuestionsRateInfoText
                                                                            .tr(),
                                                                  ),
                                                                  questionText:
                                                                      AssessmentLocaleKeys
                                                                          .drinkingQuestionsRateLabel
                                                                          .tr(),
                                                                );
                                                              },
                                                            ),
                                                            SpaceV(AppSize.h20),
                                                            Padding(
                                                              padding:
                                                                  EdgeInsets.only(
                                                                      left: AppSize
                                                                          .w34,
                                                                      right: AppSize
                                                                          .w28),
                                                              child: SliderScreen(
                                                                reverseGradient:
                                                                    true,
                                                                firstText:
                                                                    (DynamicAssetLoader
                                                                            .getNestedValue(
                                                                  AssessmentLocaleKeys
                                                                      .drinkingQuestionsRateSliderLabels,
                                                                  context,
                                                                ) as List)
                                                                        .cast<
                                                                            String>()
                                                                        .first,
                                                                secondText:
                                                                    (DynamicAssetLoader
                                                                            .getNestedValue(
                                                                  AssessmentLocaleKeys
                                                                      .drinkingQuestionsRateSliderLabels,
                                                                  context,
                                                                ) as List)
                                                                        .cast<
                                                                            String>()
                                                                        .last,
                                                                onSelect: (p0) {
                                                                  assessmentDrinkingCubit
                                                                      .rateDrinkingSliderValue
                                                                      .value = p0;
                                                                },
                                                                selectedValue:
                                                                    assessmentDrinkingCubit
                                                                        .rateDrinkingSliderValue,
                                                                isClick:
                                                                    assessmentDrinkingCubit
                                                                        .isDrinkingButtonClick
                                                                        .value,
                                                              ),
                                                            ),
                                                            SpaceV(AppSize.h30),
                                                          ],
                                                        ),
                                                        MultiValueListenableBuilder(
                                                          valueListenables: [
                                                            assessmentDrinkingCubit
                                                                .unitsController, // TextEditingController
                                                            assessmentDrinkingCubit
                                                                .daysController, // TextEditingController
                                                            assessmentDrinkingCubit
                                                                .rateDrinkingSliderValue, // ValueNotifier<int>
                                                          ],
                                                          builder: (BuildContext
                                                                  context,
                                                              List<dynamic> values,
                                                              Widget? child) {
                                                            final unitsText =
                                                                values[0].text;
                                                            final daysText =
                                                                values[1].text;
                                                            final rateDrinkingValue =
                                                                values[2];
                                                
                                                            final isButtonEnabled =
                                                                rateDrinkingValue !=
                                                                    -1;
                                                
                                                            return CustomButton(
                                                              padding:
                                                                  EdgeInsets.zero,
                                                              key: const Key(
                                                                  'next_btn'),
                                                              inProgress: state
                                                                  .isApiLoading,
                                                              disableColor: context
                                                                  .themeColors
                                                                  .blueColor
                                                                  .withOpacity(0.1),
                                                              title: CoreLocaleKeys
                                                                  .buttonsNext
                                                                  .tr(),
                                                              onTap: () async {
                                                                final int?
                                                                    unitsValue =
                                                                    int.tryParse(
                                                                        unitsText
                                                                            .toString());
                                                                final int?
                                                                    daysValue =
                                                                    int.tryParse(
                                                                        daysText
                                                                            .toString());
                                                                final rateDrinkingValue =
                                                                    assessmentDrinkingCubit
                                                                        .rateDrinkingSliderValue
                                                                        .value;
                                                
                                                                final isRateMissing =
                                                                    rateDrinkingValue ==
                                                                        -1;
                                                
                                                                if (unitsValue ==
                                                                        null ||
                                                                    daysValue ==
                                                                        null) {
                                                                  // Scroll or show error as needed
                                                                  await AppCommonFunctions
                                                                      .scrollToKey(
                                                                          assessmentDrinkingCubit
                                                                              .unitKey);
                                                                  return;
                                                                }
                                                
                                                                final isInvalidPartialInput =
                                                                    (unitsValue >
                                                                                0 &&
                                                                            daysValue ==
                                                                                0) ||
                                                                        (daysValue >
                                                                                0 &&
                                                                            unitsValue ==
                                                                                0);
                                                
                                                                if (isRateMissing ||
                                                                    isInvalidPartialInput) {
                                                                  assessmentDrinkingCubit
                                                                      .isDrinkingButtonClick
                                                                      .value = true;
                                                
                                                                  if (isInvalidPartialInput) {
                                                                    assessmentDrinkingCubit
                                                                        .isUnitAndDrinkingButton
                                                                        .value = true;
                                                                  }
                                                
                                                                  if (unitsValue ==
                                                                      0) {
                                                                    await AppCommonFunctions
                                                                        .scrollToKey(
                                                                            assessmentDrinkingCubit
                                                                                .unitKey);
                                                                  } else if (daysValue ==
                                                                      0) {
                                                                    await AppCommonFunctions
                                                                        .scrollToKey(
                                                                            assessmentDrinkingCubit
                                                                                .dayKey);
                                                                  }
                                                
                                                                  return;
                                                                }
                                                
                                                                assessmentDrinkingCubit
                                                                    .isDrinkingButtonClick
                                                                    .value = false;
                                                                assessmentDrinkingCubit
                                                                    .assignValue();
                                                
                                                                assessmentDrinkingCubit
                                                                    .isManuallyPaused
                                                                    .value = true;
                                                                await assessmentDrinkingCubit
                                                                    .putDrinkingAPI(
                                                                  units: unitsValue,
                                                                  days: daysValue,
                                                                  rate:
                                                                      rateDrinkingValue,
                                                                  context: context,
                                                                );
                                                                assessmentDrinkingCubit
                                                                    .isManuallyPaused
                                                                    .value = false;
                                                                '/// assessment = ${Injector.instance<AppDB>().userModel?.user.assessment}'
                                                                    .logD;
                                                              },
                                                              isBottom: true,
                                                              color: isButtonEnabled
                                                                  ? context
                                                                      .themeColors
                                                                      .blueColor
                                                                  : context
                                                                      .themeColors
                                                                      .blueColor
                                                                      .withOpacity(
                                                                          0.7),
                                                            );
                                                          },
                                                        ),
                                                
                                                        /* MultiValueListenableBuilder(
                                                          valueListenables: [
                                                            assessmentDrinkingCubit
                                                                .unitsController, // TextEditingController
                                                            assessmentDrinkingCubit.daysController, // TextEditingController
                                                            assessmentDrinkingCubit
                                                                .rateDrinkingSliderValue, // Assuming this is a ValueNotifier<int>
                                                          ],
                                                          builder:
                                                              (BuildContext context, List<dynamic> values, Widget? child) {
                                                            // Extract values for better readability
                                                            final unitsText = values[0]
                                                                .text; // Get the text from the TextEditingController
                                                            final daysText = values[1]
                                                                .text; // Get the text from the TextEditingController
                                                            final rateDrinkingValue =
                                                                values[2]; // Directly access the value since it's an int
                                                
                                                            // Check if the button should be enabled
                                                            final isButtonEnabled = rateDrinkingValue != -1;
                                                
                                                            return CustomButton(
                                                              padding: EdgeInsets.zero,
                                                              key: const Key('next_btn'),
                                                              inProgress: state.isApiLoading,
                                                              disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                                              onTap: () async {
                                                                if (!isButtonEnabled) {
                                                                  assessmentDrinkingCubit.isDrinkingButtonClick.value = true;
                                                                  if (unitsText == '0' || unitsText == '') {
                                                                    await AppCommonFunctions.scrollToKey(
                                                                      assessmentDrinkingCubit.unitKey,
                                                                    );
                                                                  }
                                                                } else {
                                                                  assessmentDrinkingCubit.isDrinkingButtonClick.value = false;
                                                                  if ((state.currentDrinkingValue > 0 &&
                                                                          state.currentDayValue == 0) ||
                                                                      (state.currentDayValue > 0 &&
                                                                          state.currentDrinkingValue == 0)) {
                                                                    assessmentDrinkingCubit.isUnitAndDrinkingButton.value = true;
                                                                  } else {
                                                                    await assessmentDrinkingCubit.putDrinkingAPI(
                                                                      units: int.parse(
                                                                        assessmentDrinkingCubit.unitsController.text,
                                                                      ),
                                                                      days: int.parse(
                                                                        assessmentDrinkingCubit.daysController.text,
                                                                      ),
                                                                      rate: assessmentDrinkingCubit
                                                                          .rateDrinkingSliderValue.value,
                                                                      context: context,
                                                                    );
                                                                  }
                                                                }
                                                              },
                                                              isBottom: true,
                                                              color: isButtonEnabled
                                                                  ? context.themeColors.blueColor
                                                                  : context.themeColors.blueColor.withOpacity(0.7),
                                                            );
                                                          },
                                                        ),*/
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
