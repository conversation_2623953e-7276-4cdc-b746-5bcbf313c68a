import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';

import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_feeling/assessment_drinking_feeling_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_goal/assessment_drinking_goal_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_emotional_imapct/assessment_emotional_impact_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life_style/assessment_life_style_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_state.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_notification/assessment_notification_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_physical_senstion/assessment_physical_senstation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_situtation/assessment_situtation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_thoughts/assessment_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_unhelpful_behaviour/assessment_unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assesment_bridging_video_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_difficult_situtation_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_feeling_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_goal_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drug_goal_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drugs_feeling_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drugs_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_emotional_impact_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_life_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_life_style_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_notification_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_physical_sensation_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_recovery_program_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_thank_you_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_thought_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_unhelpful_behaviour_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_video_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_welcome_video_page.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentMainPage extends StatefulWidget {
  const AssessmentMainPage({super.key});

  @override
  State<AssessmentMainPage> createState() => _AssessmentMainPageState();
}

class _AssessmentMainPageState extends State<AssessmentMainPage> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _refreshUserData();
  }

  Future<void> _refreshUserData() async {
    final authRepository = AuthRepository();
    await authRepository.getUserData(context: context);
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // if (_isLoading) {
    //   return const Center(child: CircularProgressIndicator());
    // }
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => AssessmentCubit()
            ..initialData()
            ..userRecoveryData(),
        ),
        BlocProvider(create: (_) => AssessmentDrinkingCubit()..userDrinkingData()),
        BlocProvider(create: (_) => AssessmentSitutationCubit()..userSitutationData()),
        BlocProvider(create: (_) => AssessmentLifeCubit()..userLiftData()),
        BlocProvider(create: (_) => AssessmentThoughtsCubit()..userThoughtData()),
        BlocProvider(create: (_) => AssessmentPhysicalSenstationCubit()..userPhysicalSenstiontData()),
        BlocProvider(create: (_) => AssessmentUnhelpfulBehaviourCubit()..userUnhelpfulBehaviourData()),
        BlocProvider(create: (_) => AssessmentLifeStyleCubit()..userLifeStyleData()),
        BlocProvider(create: (_) => AssessmentEmotionalImpactCubit()..userEmotionalImpactData()),
        BlocProvider(create: (_) => AssessmentDrinkingFeelingCubit()..userDrinkingFeelingData()),
        BlocProvider(create: (_) => AssessmentDrinkingGoalCubit()..userDrinkingGoalData()),
        BlocProvider(
          create: (_) => AssessmentDrugCubit()
            ..userDrugData()
            ..userDrugFeelingData()
            ..userDrugGoalData(),
        ),
      ],
      child: BlocBuilder<AssessmentCubit, AssessmentState>(
        builder: (ctx, state) {
          final assessmentCubit = ctx.read<AssessmentCubit>();
          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, result) {
              if (!didPop) {
                getBackFunction(ctx, state.index).call();
              }
            },
            child: Column(
              children: [
                Expanded(child: getWidget(state.index)),
              ],
            ),
          );
        },
      ),
    );
  }
}

Widget getWidget(int index) {
  '/// index = $index'.logV;
  return switch (index) {
    1 => const AssessmentWelcomeVideoPage(),
    2 => const AssessmentRecoveryProgramPage(),
    3 => const AssessmentLifePage(),
    4 => const AssessmentDifficultSitutationPage(),
    5 => const AssessmentThoughtPage(),
    6 => const AssessmentPhysicalSensationPage(),
    7 => const AssessmentUnhelpfulBehaviourPage(),
    8 => const AssessmentLifeStylePage(),
    9 => const AssessmentEmotionalImpactPage(),
    10 => const AssessmentDrinkingPage(),
    11 => const AssessmentDrinkingFeelingPage(),
    12 => const AssessmentDrinkingGoalPage(),
    13 => const AssessmentDrugsPage(),
    14 => const AssessmentDrugsFeelingPage(),
    15 => const AssessmentDrugGoalPage(),
    16 => const AssessmentThankYouPage(),
    17 => const AssessmentVideoPage(),
    18 => const AssesmentBridgingVideoPage(),
    _ => const AssessmentWelcomeVideoPage(),
  };
}

String getSubTitle(int index) {
  return switch (index) {
    1 => AssessmentLocaleKeys.welcomeVideoTitle.tr(),
    2 => AssessmentLocaleKeys.recoveryProgramTitle.tr(),
    3 => AssessmentLocaleKeys.lifeTitle.tr(),
    4 => AssessmentLocaleKeys.difficultSituationsTitle.tr(),
    5 => AssessmentLocaleKeys.negativeThoughtsTitle.tr(),
    6 => AssessmentLocaleKeys.physicalSensationsTitle.tr(),
    7 => AssessmentLocaleKeys.unhelpfulBehavioursTitle.tr(),
    8 => AssessmentLocaleKeys.lifestyleTitle.tr(),
    9 => AssessmentLocaleKeys.emotionalImpactTitle.tr(),
    10 => AssessmentLocaleKeys.drinkingTitle.tr(),
    11 => AssessmentLocaleKeys.drinkingFeelingTitle.tr(),
    12 => AssessmentLocaleKeys.drinkingGoalTitle.tr(),
    13 => AssessmentLocaleKeys.drugsTitle.tr(),
    14 => AssessmentLocaleKeys.drugsFeelingTitle.tr(),
    15 => AssessmentLocaleKeys.drugsGoalTitle.tr(),
    16 => AssessmentLocaleKeys.thankYouTitle.tr(),
    17 => AssessmentLocaleKeys.assessmentVideoTitle.tr(),
    18 => AssessmentLocaleKeys.bridgingVideoTitle.tr(),
    19 => AssessmentLocaleKeys.bridgingVideoTitle.tr(),
    _ => AssessmentLocaleKeys.welcomeVideoTitle.tr(),
  };
}

void Function() getBackFunction(BuildContext context, int index) {
  log('index$index');

  return () {
    final cubit = context.read<AssessmentCubit>();

    switch (index) {
      case 2:
      // if ((Injector.instance<AppDB>().userModel?.user.assessment?.completed?.contains('recovery-program') ?? false) ==
      //     true) {
      //   return;
      // }
      case 3:
        if (context.read<AssessmentLifeCubit>().islifeSecondInfoVisible.value == true) {
          context.read<AssessmentLifeCubit>().infoAudioUrl.value = AssessmentLocaleKeys.lifeAudio.tr();
          context.read<AssessmentLifeCubit>().islifeSecondInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentLifeCubit>(
              context,
            ).isLifeButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentLifeCubit>(
            context,
          ).isLifeButtonClicked.value = false;
        }
      case 4:
        context.read<AssessmentLifeCubit>().isManuallyPaused.value = false;
        if (context.read<AssessmentSitutationCubit>().rateSitutationInfoVisible.value == true) {
          context.read<AssessmentSitutationCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.difficultSituationsAudio.tr();
          context.read<AssessmentSitutationCubit>().rateSitutationInfoVisible.value = false;
        }
        // cubit.gotoPreviousWidget();

        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentSitutationCubit>(
              context,
            ).isSitutationButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentSitutationCubit>(
            context,
          ).isSitutationButtonClicked.value = false;
        }
      case 5:
        context.read<AssessmentSitutationCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentThoughtsCubit>().rateThoughtsInfoVisible.value == true) {
          context.read<AssessmentThoughtsCubit>().infoAudioUrl.value = AssessmentLocaleKeys.negativeThoughtsAudio.tr();

          context.read<AssessmentThoughtsCubit>().rateThoughtsInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentThoughtsCubit>(
              context,
            ).isThoughtButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentThoughtsCubit>(
            context,
          ).isThoughtButtonClicked.value = false;
        }
      case 6:
        context.read<AssessmentThoughtsCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentPhysicalSenstationCubit>().ratePhysicalSensationsInfoVisible.value == true) {
          context.read<AssessmentPhysicalSenstationCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.physicalSensationsAudio.tr();
          context.read<AssessmentPhysicalSenstationCubit>().ratePhysicalSensationsInfoVisible.value = false;
        }

        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentPhysicalSenstationCubit>(
              context,
            ).isPhysicalSensationClicked.value ==
            true) {
          BlocProvider.of<AssessmentPhysicalSenstationCubit>(
            context,
          ).isPhysicalSensationClicked.value = false;
        }
      case 7:
        context.read<AssessmentPhysicalSenstationCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentUnhelpfulBehaviourCubit>().rateBehaviourInfoVisible.value == true) {
          context.read<AssessmentUnhelpfulBehaviourCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.unhelpfulBehavioursAudio.tr();
          context.read<AssessmentUnhelpfulBehaviourCubit>().rateBehaviourInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();
        // cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentUnhelpfulBehaviourCubit>(
              context,
            ).isUnHelpBehaviourClicked.value ==
            true) {
          BlocProvider.of<AssessmentUnhelpfulBehaviourCubit>(
            context,
          ).isUnHelpBehaviourClicked.value = false;
        }
      case 8:
        context.read<AssessmentUnhelpfulBehaviourCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentLifeStyleCubit>().rateLifeStyleInfoVisible.value == true) {
          context.read<AssessmentLifeStyleCubit>().infoAudioUrl.value = AssessmentLocaleKeys.lifestyleAudio.tr();
          context.read<AssessmentLifeStyleCubit>().rateLifeStyleInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentLifeStyleCubit>(
              context,
            ).isLifeStyleClicked.value ==
            true) {
          BlocProvider.of<AssessmentLifeStyleCubit>(
            context,
          ).isLifeStyleClicked.value = false;
        }
      case 9:
        context.read<AssessmentLifeStyleCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentEmotionalImpactCubit>().rateEmotionalImpactInfoVisible.value == true) {
          context.read<AssessmentEmotionalImpactCubit>().infoAudioUrl.value =
              AssessmentLocaleKeys.emotionalImpactAudio.tr();
          context.read<AssessmentEmotionalImpactCubit>().rateEmotionalImpactInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();

        // cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentEmotionalImpactCubit>(
              context,
            ).isEmotionButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentEmotionalImpactCubit>(
            context,
          ).isEmotionButtonClicked.value = false;
        }
      case 10:
        context.read<AssessmentEmotionalImpactCubit>().isManuallyPaused.value = false;

        if (context.read<AssessmentDrinkingCubit>().rateDrinkingInfoVisible.value == true) {
          context.read<AssessmentDrinkingCubit>().infoAudioUrl.value = AssessmentLocaleKeys.drinkingAudio.tr();
          context.read<AssessmentDrinkingCubit>().rateDrinkingInfoVisible.value = false;
        }
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentDrinkingFeelingCubit>(
              context,
            ).isDrinkingFeelingButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentDrinkingFeelingCubit>(
            context,
          ).isDrinkingFeelingButtonClicked.value = false;
        }
        if (BlocProvider.of<AssessmentDrinkingCubit>(
              context,
            ).isButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentDrinkingCubit>(
            context,
          ).isButtonClicked.value = false;
        }
      case 11:
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentDrinkingFeelingCubit>(
              context,
            ).isDrinkingFeelingButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentDrinkingFeelingCubit>(
            context,
          ).isDrinkingFeelingButtonClicked.value = false;
        }
      case 12:
      case 14:
        cubit.gotoPreviousWidget();
        if (BlocProvider.of<AssessmentDrugCubit>(
              context,
            ).isDrugFeelingButtonClicked.value ==
            true) {
          BlocProvider.of<AssessmentDrugCubit>(
            context,
          ).isDrugFeelingButtonClicked.value = false;
        }
      case 15:
      case 17:
      case 18:
        cubit.gotoPreviousWidget();

      case 13:
        if (context.read<AssessmentDrugCubit>().rateDrugInfoVisible.value == true) {
          context.read<AssessmentDrugCubit>().infoAudioUrl.value = AssessmentLocaleKeys.drugsAudio.tr();
          context.read<AssessmentDrugCubit>().rateDrugInfoVisible.value = false;
        }
        if (Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 1) {
          cubit.gotoPreviousWidget(decrement: 4);
        } else {
          cubit.gotoPreviousWidget();
        }

      case 16:
        if (Injector.instance<AppDB>().userModel?.user.assessment?.rp?.addictionCase == 0) {
          cubit.gotoPreviousWidget(decrement: 4);
        } else {
          cubit.gotoPreviousWidget();
        }

      default:
        break;
    }
  };
}
