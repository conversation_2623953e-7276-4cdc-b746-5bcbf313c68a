import 'dart:developer';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_rounded_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/custom_widgets/error_widget.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/drugs_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_selection_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssessmentDrugsPage extends StatelessWidget {
  const AssessmentDrugsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: context.read<AssessmentDrugCubit>().isManuallyPaused,
      builder: (context, value, child) {
        return ValueListenableBuilder(
          valueListenable: context.read<AssessmentCubit>().isAudioPannelVisible,
          builder: (context, value, child) {
                return ValueListenableBuilder(
                  valueListenable: context.read<AssessmentDrugCubit>().infoAudioUrl,
                  builder: (context, value, child) {
                    return AppScaffold(
                      isManuallyPaused: context.read<AssessmentDrugCubit>().isManuallyPaused,
                      appBar: CommonAppBar(
                        prefixIcon: Icon(
                          Icons.logout,
                          size: AppSize.sp20,
                        ),
                        onPrefixTap: () {
                          LogOutDialog.showLogOutDialog(context);
                        },
                        onSuffixTap: () {
                          context.read<AssessmentCubit>().isAudioPannelVisible.value = !context.read<AssessmentCubit>().isAudioPannelVisible.value;
                        },
                      ),
                      isAudioPanelVisible: context.read<AssessmentCubit>().isAudioPannelVisible,
                      infoAudioUrl: context.read<AssessmentDrugCubit>().infoAudioUrl,
                      resizeToAvoidBottomInset: true,
                      body: BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
                        builder: (ctx,state) {
                          final assessmentDrugCubit = ctx.read<AssessmentDrugCubit>();
                          final assessmentCubit1 = ctx.read<AssessmentCubit>();     
                          return ValueListenableBuilder(
                            valueListenable: assessmentDrugCubit.isDrugButtonClick,
                            builder: (context, value, child) {
                              return AbsorbPointer(
                                absorbing: state.isApiLoading,
                                child: Column(
                                  children: [
                                    SpaceV(AppSize.h4),
                                    Expanded(
                                      child: LayoutBuilder(
                                        builder: (context, constrains) {
                                          
                                          return Padding(
                                            padding: EdgeInsets.only(right: AppSize.w4),
                                            child: CustomRawScrollbar(
                                              child: SingleChildScrollView(
                                                child: ConstrainedBox(
                                                  constraints: BoxConstraints(minHeight: constrains.maxHeight),
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, bottom: AppSize.h20),
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Column(
                                                          children: [
                                                            const CustomHeader(),
                                                            SpaceV(AppSize.h6),
                                                            AppTextWidget(
                                                              (DynamicAssetLoader.getNestedValue(
                                                                AssessmentLocaleKeys.drugsSubtitle,
                                                                context,
                                                              ) as List)
                                                                  .cast<String>()
                                                                  .join('\n\n'),
                                                              style: context.textTheme.titleSmall?.copyWith(
                                                                color: context.themeColors.darkGreyColor,
                                                              ),
                                                            ),
                                                            SpaceV(AppSize.h20),
                                                            if (state.drugDetailList.isNotEmpty) ...{
                                                              // Display only the first three items
                                                              ListView.builder(
                                                                shrinkWrap: true,
                                                                physics: const NeverScrollableScrollPhysics(),
                                                                itemCount: state.drugDetailList.length > 3
                                                                    ? 3
                                                                    : state.drugDetailList.length,
                                                                itemBuilder: (context, index) {
                                                                  '000 ${double.parse(state.drugDetailList[index].frequency ?? '0')}'
                                                                      .logV;
                                                                      '///${state.drugDetailList[index].frequency}'.logV;
                                                                  return Padding(
                                                                    padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                                                                    child: Container(
                                                                      decoration: BoxDecoration(
                                                                        color: const Color.fromRGBO(235, 235, 235, 1),
                                                                        borderRadius: BorderRadius.circular(AppSize.r4),
                                                                        border: Border.all(
                                                                          color: const Color(0xFFBDBDBD),
                                                                        ),
                                                                        // boxShadow: [
                                                                        //   BoxShadow(
                                                                        //     color: Colors.black.withOpacity(0.05), // Very light shadow color
                                                                        //     offset: const Offset(0, 3), // Slight vertical shadow (bottom only)
                                                                        //     blurRadius: 6, // Subtle blur effect
                                                                        //     spreadRadius: 1, // Light spreading of the shadow
                                                                        //   ),
                                                                        // ],
                                                                      ),
                                                                      child: Padding(
                                                                        padding: EdgeInsets.symmetric(
                                                                          horizontal: AppSize.w12,
                                                                          vertical: AppSize.h10,
                                                                        ),
                                                                        child: Row(
                                                                          children: [
                                                                            Column(
                                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                                              children: [
                                                                                AppTextWidget(
                                                                                  assessmentDrugCubit.getLocalizedDrugNameFromKey(state.drugDetailList[index].drug ?? ''),
                                                                                  style: context.textTheme.titleSmall,
                                                                                ),
                                                                                
                                                                                SpaceV(AppSize.h10),
                                                                                AppTextWidget(
                                                                                  '${state.drugDetailList[index].amount ?? ''} ${AppCommonFunctions.getFormattedTranslation(
                                                                                    AssessmentLocaleKeys
                                                                                        .drugsQuestionsListLabelsUnitsPerDay,
                                                                                    {
                                                                                      'units':
                                                                                          state.drugDetailList[index].unit ?? '',
                                                                                      //'unit': state.selecteUnitValue ?? '',
                                                                                    },
                                                                                  )}',
                                                                                  //     '${state.drugDetailList[index].amount ?? ''} ${assessmentCubit.getDrugUnitValueForKey(state.drugDetailList[index].unit ?? '')} a day',
                                                                                  style: context.textTheme.titleSmall,
                                                                                ),
                                                                                SpaceV(AppSize.h10),
                                                                              
                                                                                AppTextWidget(
                                                                                  '${state.drugDetailList[index].frequency ?? ''} '
                                      '${(state.drugDetailList[index].frequency != null && (state.drugDetailList[index].frequency == '0' || state.drugDetailList[index].frequency == '1')) 
                                      ? AssessmentLocaleKeys.drugsQuestionsListLabelsDayPerWeek.tr() 
                                      : AssessmentLocaleKeys.drugsQuestionsListLabelsDaysPerWeek.tr()}',
                                      
                                                                                  style: context.textTheme.titleSmall,
                                                                                ),
                                                                              ],
                                                                            ),
                                                                            const Spacer(),
                                                                            InkWell(
                                                                              onTap: () {
                                                                                assessmentDrugCubit.removeDrugData(index);
                                                                              },
                                                                              child: Icon(
                                                                                Icons.delete,
                                                                                color: context.themeColors.greyColor,
                                                                              ),
                                                                            ),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                              ),
                                                            },
                                                            if (state.drugDetailList.isNotEmpty &&
                                                                state.drugDetailList.length < 3) ...{
                                                              SpaceV(AppSize.h12),
                                                              Align(
                                                                alignment: Alignment.topRight,
                                                                child: CustomRoundedButton(
                                                                  title: AssessmentLocaleKeys.drugsQuestionsListButtonsAdd.tr(),
                                                                  onTap: () {
                                                                    assessmentDrugCubit.cleaseDrugData();
                                                                    assessmentDrugCubit.drugSelectionoVisible.value = true;
                                                                  },
                                                                ),
                                                              ),
                                                              SpaceV(AppSize.h12),
                                                            },
                                                            const DrugSelectionWidget(),
                                                            if (state.drugDetailList.isEmpty &&
                                                                assessmentDrugCubit.isDrugButtonClick.value) ...{
                                                              CustomErrorWidget(
                                                                errorMessgaeText: AssessmentLocaleKeys.errorsMinimumOneDrug.tr(),
                                                              ),
                                                            },
                                                            SpaceV(AppSize.h20),
                                                            ValueListenableBuilder(
                                                              valueListenable: assessmentDrugCubit.rateDrugInfoVisible,
                                                              builder: (context, rateDrugInfoVisible, child) {
                                                                return QuestionRowWidget(
                                                                  oninfoTap: () {
                                                                    assessmentDrugCubit.rateDrugInfoVisible.value =
                                                                        !assessmentDrugCubit.rateDrugInfoVisible.value;
                                                                    if (assessmentDrugCubit.rateDrugInfoVisible.value) {
                                                                      assessmentDrugCubit.isManuallyPaused.value = false;
                                                                      assessmentDrugCubit.infoAudioUrl.value =
                                                                          AssessmentLocaleKeys.drugsQuestionsRateInfoAudio.tr();
                                                                    } else {
                                                                      assessmentDrugCubit.isManuallyPaused.value = true;
                                                                      assessmentDrugCubit.infoAudioUrl.value =
                                                                          AssessmentLocaleKeys.drugsAudio.tr();
                                                                    }
                                      
                                                                    log('message${assessmentDrugCubit.rateDrugInfoVisible.value}');
                                                                  },
                                                                  infoWidget: CustomInfoWidget(
                                                                    padding: EdgeInsets.only(
                                                                      left: AppSize.w12,
                                                                      right: AppSize.w12,
                                                                      top: AppSize.h10,
                                                                    ),
                                                                    visible: rateDrugInfoVisible,
                                                                    onCloseTap: () {
                                                                      assessmentDrugCubit.isManuallyPaused.value = true;
                                      
                                                                      assessmentDrugCubit.infoAudioUrl.value =
                                                                          AssessmentLocaleKeys.drugsAudio.tr();
                                                                      assessmentDrugCubit.rateDrugInfoVisible.value = false;
                                                                    },
                                                                    bodyText: AssessmentLocaleKeys.drugsQuestionsRateInfoText.tr(),
                                                                  ),
                                                                  questionText: AssessmentLocaleKeys.drugsQuestionsRateLabel.tr(),
                                                                );
                                                              },
                                                            ),
                                                            SpaceV(AppSize.h20),
                                                            Padding(
                                                              padding: EdgeInsets.only(left: AppSize.w34, right: AppSize.w28),
                                                              child: SliderScreen(
                                                                reverseGradient: true,
                                                                firstText: (DynamicAssetLoader.getNestedValue(
                                                                  AssessmentLocaleKeys.drugsQuestionsRateSliderLabels,
                                                                  context,
                                                                ) as List)
                                                                    .cast<String>()
                                                                    .first,
                                                                secondText: (DynamicAssetLoader.getNestedValue(
                                                                  AssessmentLocaleKeys.drugsQuestionsRateSliderLabels,
                                                                  context,
                                                                ) as List)
                                                                    .cast<String>()
                                                                    .last,
                                                                onSelect: (p0) {
                                                                  assessmentDrugCubit.rateDrugsSliderVlaue.value = p0;
                                                                },
                                                                selectedValue: assessmentDrugCubit.rateDrugsSliderVlaue,
                                                                isClick: assessmentDrugCubit.isDrugButtonClick.value,
                                                              ),
                                                            ),
                                                            SpaceV(AppSize.h30),
                                                          ],
                                                        ),
                                                        ValueListenableBuilder(
                                                          valueListenable: assessmentDrugCubit.rateDrugsSliderVlaue,
                                                          builder: (context, value, child) {
                                                            return CustomButton(
                                                              padding: EdgeInsets.zero,
                                                              key: const Key('next_btn'),
                                                              disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                                              inProgress: state.isApiLoading,
                                                              onTap: () async {
                                                                assessmentDrugCubit.isDrugButtonClick.value = true;
                                                                if (state.drugDetailList.isEmpty ||
                                                                    assessmentDrugCubit.rateDrugsSliderVlaue.value == -1) {
                                                                  
                                                                  // CustomSnackbar.showErrorSnackBar(
                                                                  //   message: AssessmentLocaleKeys.errorsRequiredMessage.tr(),
                                                                  // );
                                                                } else {
                                                                  assessmentDrugCubit.assignValueForDrugDays();
                                                                  await assessmentDrugCubit.putdrugAPI(
                                                                    list: assessmentDrugCubit.updatedDrugMap ?? {},
                                                                    rate: assessmentDrugCubit.rateDrugsSliderVlaue.value,
                                                                    context: context,
                                                                  );
                                                                  '/// assessment = ${Injector.instance<AppDB>().userModel?.user.assessment?.toJson()}'.logV;
                                                                  //  assessmentCubit.gotoNextWidget();
                                                                }
                                                              },
                                                              isBottom: true,
                                                              color: (state.drugDetailList.isEmpty ||
                                                                    assessmentDrugCubit.rateDrugsSliderVlaue.value == -1)
                                                                  ? context.themeColors.blueColor.withOpacity(0.7)
                                                                  : context.themeColors.blueColor,
                                                            );
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                      ),
                    );
                  },
                );
          },
        );
      },
    );
  }
}
