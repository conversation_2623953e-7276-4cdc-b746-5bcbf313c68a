import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_radio_list_widget.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/drug_and_unit_list.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/logger.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/app_common_functions.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

class AssessmentDrugsFeelingPage extends StatelessWidget {
  const AssessmentDrugsFeelingPage({super.key});

  @override
  Widget build(BuildContext context) {
    
    return BlocBuilder<AssessmentDrugCubit, AssessmentDrugState>(
      builder: (ctx, state) {
        final assessmentDrugCubit = ctx.read<AssessmentDrugCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();
        '${state.selecteDrugsValue ?? '' ?? ''}'.logV;
        '${state.drugDetailList.first.drug ?? ''}'.logV;
        return AppScaffold(
          appBar: CommonAppBar(
            prefixIcon: Icon(
              Icons.logout,
              size: AppSize.sp20,
            ),
            onPrefixTap: () {
              LogOutDialog.showLogOutDialog(context);
            },
            onSuffixTap: () {
              assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
            },
          ),
          isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
          infoAudioUrl: ValueNotifier(AssessmentLocaleKeys.drugsFeelingAudio.tr()),
          resizeToAvoidBottomInset: false,
          body: AbsorbPointer(
            absorbing: state.isFeelingApiLoading,
            child: ValueListenableBuilder(
              valueListenable: assessmentDrugCubit.isDrugFeelingButtonClicked,
              builder: (context, value, child) {
                return Column(
                  children: [
                    SpaceV(AppSize.h4),
                    Expanded(
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return Padding(
                                  padding: EdgeInsets.only(right: AppSize.w4),
                                  child: CustomRawScrollbar(
                              child: SingleChildScrollView(
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                                  child: Padding(
                                    padding: EdgeInsets.only(left: AppSize.w24, right: AppSize.w24, bottom: AppSize.h20),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const CustomHeader(),
                                            SpaceV(AppSize.h6),
                                            AppTextWidget(
                                              AssessmentLocaleKeys.drugsFeelingSubtitle.tr(),
                                              style: context.textTheme.titleSmall?.copyWith(
                                                color: context.themeColors.darkGreyColor,
                                              ),
                                            ),
                                            SpaceV(AppSize.h20),
                                            QuestionRowWidget(
                                              questionText: AppCommonFunctions.getFormattedTranslation(
                                                AssessmentLocaleKeys.drugsFeelingControlLabel,
                                                {
                                                  'drug': state.drugDetailList.isEmpty
                                                      ? ''
                                                      : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',)
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentDrugCubit.drugControlKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentDrugCubit.drugControl,
                                                builder: (context, drugControl, child) {
                                                  return CustomRadioListWidget(
                                                    isError: assessmentDrugCubit.isDrugFeelingButtonClicked.value && drugControl.isEmpty,
                              
                                                    isButtonClicked: assessmentDrugCubit.isDrugFeelingButtonClicked.value,
                                                    options: assessmentDrugCubit.drugControlList,
                                                    selectedValue: drugControl,
                                                    onChanged: (newValue) {
                                                      assessmentDrugCubit.drugControl.value = newValue ?? '';
                                                    },
                                                  );
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h30),
                                            QuestionRowWidget(
                                              questionText: AppCommonFunctions.getFormattedTranslation(
                                                AssessmentLocaleKeys.drugsFeelingAnxiousLabel,
                                                {
                                                  'drug': state.drugDetailList.isEmpty
                                                      ? ''
                                                      : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',)
                                                },
                                              ),
                                            ),
                                            SpaceV(AppSize.h6),
                                            SizedBox(
                                              key: assessmentDrugCubit.drugAnxiousKey,
                                              child: ValueListenableBuilder(
                                                valueListenable: assessmentDrugCubit.drugAnxious,
                                                builder: (context, drugAnxious, child) {
                                                  return CustomRadioListWidget(
                                                    isButtonClicked: assessmentDrugCubit.isDrugFeelingButtonClicked.value,
                              
                                                    options: assessmentDrugCubit.drugAnxiousList,
                                                    selectedValue: drugAnxious, // Safely access first item
                                                    onChanged: (newValue) {
                                                      assessmentDrugCubit.drugAnxious.value = newValue ?? '';
                                                      },
                                                    isError: assessmentDrugCubit.isDrugFeelingButtonClicked.value && drugAnxious.isEmpty,
                                                );
                                                          },
                                                        ),
                                            ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          AssessmentLocaleKeys.drugsFeelingWorryLabel,
                                                          {
                                                            'drug': state.drugDetailList.isEmpty
                                                                ? ''
                                                                : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',)
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h6),
                                                      SizedBox(
                                                        key: assessmentDrugCubit.drugWorryKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: assessmentDrugCubit.drugWorry,
                                                          builder: (context, drugWorry, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: assessmentDrugCubit.isDrugFeelingButtonClicked.value,
                              
                                                              options: assessmentDrugCubit.drugWorryList,
                                                              selectedValue: drugWorry, // Safely access first item
                                                              onChanged: (newValue) {
                                                                assessmentDrugCubit.drugWorry.value = newValue ?? '';
                                                              },
                                                              isError: assessmentDrugCubit.isDrugFeelingButtonClicked.value && drugWorry.isEmpty,
                                                                                                  );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText: AppCommonFunctions.getFormattedTranslation(
                                                          AssessmentLocaleKeys.drugsFeelingWillLabel,
                                                          {
                                                            'drug': state.drugDetailList.isEmpty
                                                     
                                                    ? ''
                                                     : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',)
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h6),
                                                      SizedBox(
                                                        key: assessmentDrugCubit.drugWillKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: assessmentDrugCubit.drugWill,
                                                          builder: (context, drugWill, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: assessmentDrugCubit.isDrugFeelingButtonClicked.value,
                                                              options: assessmentDrugCubit.drugWillList,
                                                              selectedValue: drugWill, // Safely access first item
                                                              onChanged: (newValue) {
                                                                assessmentDrugCubit.drugWill.value = newValue ?? '';
                                                              },
                                                              isError: assessmentDrugCubit.isDrugFeelingButtonClicked.value && drugWill.isEmpty,
                                                                                                  );
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      QuestionRowWidget(
                                                        questionText:  AppCommonFunctions.getFormattedTranslation(
                                                          AssessmentLocaleKeys.drugsFeelingDifficultyLabel,
                                                         {'drug': state.drugDetailList.isEmpty
                                                             ? ''
                                                             : assessmentCubit1.formatString(assessmentCubit1.getFormattedDrugName(
                state.drugDetailList.first.drug ?? '',
                DrugAndUnitList.drugList,
             ) ?? '',)
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h6),
                                                      SizedBox(
                                                        key: assessmentDrugCubit.drugDifficultyKey,
                                                        child: ValueListenableBuilder(
                                                          valueListenable: assessmentDrugCubit.drugDifficulty,
                                                          builder: (context, drugDifficulty, child) {
                                                            return CustomRadioListWidget(
                                                              isButtonClicked: assessmentDrugCubit.isDrugFeelingButtonClicked.value,
                                                              isError: assessmentDrugCubit.isDrugFeelingButtonClicked.value && drugDifficulty.isEmpty,
                                                              options: assessmentDrugCubit.drugDifficultyList,
                                                              selectedValue: drugDifficulty, // Safely access first item
                                                              onChanged: (newValue) {
                                                                assessmentDrugCubit.drugDifficulty.value = newValue ?? '';
                                                              },
                                                            );
                                                          },
                                                        ),
                                                      ),
                                            SpaceV(AppSize.h30),
                                          ],
                                        ),
                                        MultiValueListenableBuilder(
                                          valueListenables: [
                                            assessmentDrugCubit.drugControl, // Assuming these are ValueNotifiers
                                            assessmentDrugCubit.drugAnxious,
                                            assessmentDrugCubit.drugWorry,
                                            assessmentDrugCubit.drugWill,
                                            assessmentDrugCubit.drugDifficulty,
                                          ],
                                          builder: (BuildContext context, List<dynamic> values, Widget? child) {
                                            // Destructure the values for readability
                                            final drugControl = values[0];
                                            final drugAnxious = values[1];
                                            final drugWorry = values[2];
                                            final drugWill = values[3];
                                            final drugDifficulty = values[4];
                              
                                                            // Check conditions to enable the button
                                            final isButtonEnabled = drugControl != '' &&
                                                drugAnxious != '' &&
                                                drugWorry != '' &&
                                                drugWill != '' &&
                                                drugDifficulty != '';
                              
                                            return CustomButton(
                                              padding: EdgeInsets.zero,
                                              key: const Key('next_btn'),
                                              disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                              title: CoreLocaleKeys.buttonsNext.tr(),
                                              inProgress: state.isFeelingApiLoading,
                                              onTap: () async {
                              
                                                if (!isButtonEnabled) {
                                                  assessmentDrugCubit.isDrugFeelingButtonClicked.value = true;
                              
                                                  if (assessmentDrugCubit.drugControl.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(assessmentDrugCubit.drugControlKey);
                                                    return;
                                                  }
                                                  if (assessmentDrugCubit.drugAnxious.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(assessmentDrugCubit.drugAnxiousKey);
                                                    return;
                                                  }
                                                  if (assessmentDrugCubit.drugWorry.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(assessmentDrugCubit.drugWorryKey);
                                                    return;
                                                  }
                                                  if (assessmentDrugCubit.drugWill.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(assessmentDrugCubit.drugWillKey);
                                                    return;
                                                  }
                                                  if (assessmentDrugCubit.drugDifficulty.value.isEmpty) {
                                                    await AppCommonFunctions.scrollToKey(assessmentDrugCubit.drugDifficultyKey);
                                                    return;
                                                  }
                              
                                                } else {
                                                  '>?>?>?>?>? anxious = ${assessmentDrugCubit.drugAnxiousList.indexOf(assessmentDrugCubit.drugAnxious.value)}'.logV;
                                                  '>?>?>?>?>? control = ${assessmentDrugCubit.drugControlList.indexOf(assessmentDrugCubit.drugControl.value)}'.logV;
                                                  '>?>?>?>?>? difficulty = ${assessmentDrugCubit.drugDifficultyList.indexOf(assessmentDrugCubit.drugDifficulty.value)}'.logV;
                                                  '>?>?>?>?>? will = ${assessmentDrugCubit.drugWillList.indexOf(assessmentDrugCubit.drugWill.value)}'.logV;
                                                  '>?>?>?>?>? worry = ${assessmentDrugCubit.drugWorryList.indexOf(assessmentDrugCubit.drugWorry.value)}'.logV;
                                                  
                                                  await assessmentDrugCubit.putdrugFeelingAPI(
                                                    anxious: assessmentDrugCubit.drugAnxiousList
                                                        .indexOf(assessmentDrugCubit.drugAnxious.value),
                                                    control: assessmentDrugCubit.drugControlList
                                                        .indexOf(assessmentDrugCubit.drugControl.value),
                                                    difficulty: assessmentDrugCubit.drugDifficultyList
                                                        .indexOf(assessmentDrugCubit.drugDifficulty.value),
                                                    will: assessmentDrugCubit.drugWillList
                                                        .indexOf(assessmentDrugCubit.drugWill.value),
                                                    worry: assessmentDrugCubit.drugWorryList
                                                        .indexOf(assessmentDrugCubit.drugWorry.value),
                                                    context: context,
                                                  );
                                                  //   assessmentCubit.gotoNextWidget();
                                                }
                                              },
                                              isBottom: true,
                                              color: isButtonEnabled
                                                  ? context.themeColors.blueColor
                                                  : context.themeColors.blueColor.withOpacity(0.7),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
