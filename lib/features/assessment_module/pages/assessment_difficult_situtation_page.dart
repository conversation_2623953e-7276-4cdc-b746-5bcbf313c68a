import 'dart:developer';

import 'package:breakingfree_v2/custom_widgets/app_scaffold.dart';
import 'package:breakingfree_v2/custom_widgets/app_text_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_app_bar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_button.dart';
import 'package:breakingfree_v2/custom_widgets/custom_info_widget.dart';
import 'package:breakingfree_v2/custom_widgets/custom_raw_scrollbar.dart';
import 'package:breakingfree_v2/custom_widgets/custom_slider/slider_screen.dart';
import 'package:breakingfree_v2/custom_widgets/custom_snackbar.dart';
import 'package:breakingfree_v2/extensions/ext_build_context.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_situtation/assessment_situtation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/locale_keys/assessment_locale_keys.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_assessment_button.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/custom_header.dart';
import 'package:breakingfree_v2/features/assessment_module/widgets/question_widget.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/data_locale_keys.dart';
import 'package:breakingfree_v2/features/home_module/widgets/log_out_dialog.dart';
import 'package:breakingfree_v2/localization/dynamic_asset_loader.dart';
import 'package:breakingfree_v2/res/appnavigation.dart';
import 'package:breakingfree_v2/res/dimension.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/space_box.dart';
import 'package:breakingfree_v2/utils/core_locale_keys.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_value_listenable_builder/multi_value_listenable_builder.dart';

import 'package:breakingfree_v2/utils/app_common_functions.dart';

class AssessmentDifficultSitutationPage extends StatelessWidget {
  const AssessmentDifficultSitutationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssessmentSitutationCubit, AssessmentSitutationState>(
      builder: (ctx, state) {
        final assessmentSituationCubit = ctx.read<AssessmentSitutationCubit>();
        final assessmentCubit1 = ctx.read<AssessmentCubit>();

        return PopScope(
          onPopInvokedWithResult: (didPop, result) {
                if (didPop) {
                  AppNavigation.previousScreen(context);
                }
              },
          child: ValueListenableBuilder(
            valueListenable: assessmentSituationCubit.infoAudioUrl,
            builder: (context, value, child) {
              return AppScaffold(
                isManuallyPaused: assessmentSituationCubit.isManuallyPaused,
                appBar: CommonAppBar(
                  prefixIcon: Icon(
                    Icons.logout,
                    size: AppSize.sp20,
                  ),
                  onPrefixTap: () {
                    LogOutDialog.showLogOutDialog(context);
                  },
                  onSuffixTap: () {
                    assessmentCubit1.isAudioPannelVisible.value = !assessmentCubit1.isAudioPannelVisible.value;
                  },
                ),
                isAudioPanelVisible: assessmentCubit1.isAudioPannelVisible,
                infoAudioUrl: assessmentSituationCubit.infoAudioUrl,
                resizeToAvoidBottomInset: false,
                body: ValueListenableBuilder(
                  valueListenable: assessmentSituationCubit.isSitutationButtonClicked,
                  builder: (context, value, child) {
                    return AbsorbPointer(
                      absorbing: state.isApiLoading,
                      child: Column(
                        children: [
                          SpaceV(AppSize.h4),
                          Expanded(
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                return Padding(
                                  padding: EdgeInsets.only(right: AppSize.w4),
                                  child: CustomRawScrollbar(
                                    key: PageStorageKey('1'),
                                    child: SingleChildScrollView(
                                      key: PageStorageKey('1'),
                                      child: Column(
                                        children: [
                                          ConstrainedBox(
                                            constraints: BoxConstraints(
                                              minHeight: constraints.maxHeight,
                                            ),
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                left: AppSize.w24,
                                                right: AppSize.w24,
                                                bottom: AppSize.h20,
                                              ),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    children: [
                                                      const CustomHeader(),
                                                      SpaceV(AppSize.h6),
                                                      AppTextWidget(
                                                        AssessmentLocaleKeys.difficultSituationsSubtitle.tr(),
                                                        style: context.textTheme.titleSmall?.copyWith(
                                                          color: context.themeColors.darkGreyColor,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      QuestionRowWidget(
                                                        questionText: AssessmentLocaleKeys
                                                            .difficultSituationsQuestionsConflictLabel
                                                            .tr(),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      Padding(
                                                        key: assessmentSituationCubit.conflictKey,
                                                        padding: EdgeInsets.only(
                                                          left: AppSize.w34,
                                                          right: AppSize.w28,
                                                        ),
                                                        child: CustomAssessmentButton(
                                                          buttonFirstKey: const Key(
                                                            'conflict_no',
                                                          ),
                                                          buttonSecondKey: const Key(
                                                            'conflict_yes',
                                                          ),
                                                          buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                          buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                          selectedValue:
                                                              assessmentSituationCubit.isSitutationButtonClicked.value,
                                                          currentState: assessmentSituationCubit.conflictState,
                                                          onNoTap: () {
                                                            assessmentSituationCubit.conflictState.value =
                                                                ButtonState.yesEnabled;
                                                          },
                                                          onYesTap: () {
                                                            assessmentSituationCubit.conflictState.value =
                                                                ButtonState.noEnabled;
                                                          },
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h26),
                                                      QuestionRowWidget(
                                                        questionText:
                                                            AssessmentLocaleKeys.difficultSituationsQuestionsWorkLabel.tr(),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      SizedBox(
                                                        key: assessmentSituationCubit.workKey,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomAssessmentButton(
                                                            buttonFirstKey: const Key(
                                                              'problems_no',
                                                            ),
                                                            buttonSecondKey: const Key(
                                                              'problems_yes',
                                                            ),
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            selectedValue:
                                                                assessmentSituationCubit.isSitutationButtonClicked.value,
                                                            currentState: assessmentSituationCubit.workState,
                                                            onNoTap: () {
                                                              assessmentSituationCubit.workState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentSituationCubit.workState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h26),
                                                      QuestionRowWidget(
                                                        questionText: AssessmentLocaleKeys
                                                            .difficultSituationsQuestionsMoneyLabel
                                                            .tr(),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      SizedBox(
                                                        key: assessmentSituationCubit.moneyKey,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomAssessmentButton(
                                                            buttonFirstKey: const Key(
                                                              'money_no',
                                                            ),
                                                            buttonSecondKey: const Key(
                                                              'money_yes',
                                                            ),
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            selectedValue:
                                                                assessmentSituationCubit.isSitutationButtonClicked.value,
                                                            currentState: assessmentSituationCubit.moneyState,
                                                            onNoTap: () {
                                                              assessmentSituationCubit.moneyState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentSituationCubit.moneyState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h26),
                                                      QuestionRowWidget(
                                                        questionText: AssessmentLocaleKeys
                                                            .difficultSituationsQuestionsRisksLabel
                                                            .tr(),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      SizedBox(
                                                        key: assessmentSituationCubit.riskKey,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomAssessmentButton(
                                                            buttonFirstKey: const Key(
                                                              'risky_no',
                                                            ),
                                                            buttonSecondKey: const Key(
                                                              'risky_yes',
                                                            ),
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            selectedValue:
                                                                assessmentSituationCubit.isSitutationButtonClicked.value,
                                                            currentState: assessmentSituationCubit.risksState,
                                                            onNoTap: () {
                                                              assessmentSituationCubit.risksState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentSituationCubit.risksState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h26),
                                                      QuestionRowWidget(
                                                        questionText: AssessmentLocaleKeys
                                                            .difficultSituationsQuestionsPressureLabel
                                                            .tr(),
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      SizedBox(
                                                        key: assessmentSituationCubit.pressureKey,
                                                        child: Padding(
                                                          padding: EdgeInsets.only(
                                                            left: AppSize.w34,
                                                            right: AppSize.w28,
                                                          ),
                                                          child: CustomAssessmentButton(
                                                            buttonFirstKey: const Key(
                                                              'pressured_no',
                                                            ),
                                                            buttonSecondKey: const Key(
                                                              'pressured_yes',
                                                            ),
                                                            buttonFirstText: DataJsonKeys.buttonNo.tr(),
                                                            buttonSecondText: DataJsonKeys.buttonYes.tr(),
                                                            selectedValue:
                                                                assessmentSituationCubit.isSitutationButtonClicked.value,
                                                            currentState: assessmentSituationCubit.pressureState,
                                                            onNoTap: () {
                                                              assessmentSituationCubit.pressureState.value =
                                                                  ButtonState.yesEnabled;
                                                            },
                                                            onYesTap: () {
                                                              assessmentSituationCubit.pressureState.value =
                                                                  ButtonState.noEnabled;
                                                            },
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h30),
                                                      ValueListenableBuilder(
                                                        valueListenable: assessmentSituationCubit.rateSitutationInfoVisible,
                                                        builder: (
                                                          context,
                                                          rateStateInfoVisible,
                                                          child,
                                                        ) {
                                                          return QuestionRowWidget(
                                                            oninfoTap: () {
                                                              assessmentSituationCubit.rateSitutationInfoVisible.value =
                                                                  !assessmentSituationCubit.rateSitutationInfoVisible.value;
                                                                            
                                                              if (assessmentSituationCubit
                                                                  .rateSitutationInfoVisible.value) {
                                                                assessmentSituationCubit.isManuallyPaused.value = false;
                                                                assessmentSituationCubit.infoAudioUrl.value =
                                                                    AssessmentLocaleKeys
                                                                        .difficultSituationsQuestionsRateInfoAudio
                                                                        .tr();
                                                              } else {
                                                                assessmentSituationCubit.isManuallyPaused.value = true;
                                                                assessmentSituationCubit.infoAudioUrl.value =
                                                                    AssessmentLocaleKeys.difficultSituationsAudio.tr();
                                                              }
                                                            },
                                                            infoWidget: CustomInfoWidget(
                                                              padding: EdgeInsets.only(
                                                                left: AppSize.w12,
                                                                right: AppSize.w12,
                                                                top: AppSize.h10,
                                                              ),
                                                              visible: rateStateInfoVisible,
                                                              onCloseTap: () {
                                                                assessmentSituationCubit.isManuallyPaused.value = true;
                                                                            
                                                                assessmentSituationCubit.infoAudioUrl.value =
                                                                    AssessmentLocaleKeys.difficultSituationsAudio.tr();
                                                                assessmentSituationCubit.rateSitutationInfoVisible.value =
                                                                    false;
                                                              },
                                                              bodyText: AssessmentLocaleKeys
                                                                  .difficultSituationsQuestionsRateInfoText
                                                                  .tr(),
                                                            ),
                                                            questionText: AssessmentLocaleKeys
                                                                .difficultSituationsQuestionsRateLabel
                                                                .tr(),
                                                          );
                                                        },
                                                      ),
                                                      SpaceV(AppSize.h20),
                                                      Padding(
                                                        padding: EdgeInsets.only(
                                                          left: AppSize.w34,
                                                          right: AppSize.w28,
                                                        ),
                                                        child: SliderScreen(
                                                          
                                                          reverseGradient: true,
                                                          firstText: (DynamicAssetLoader.getNestedValue(
                                                            AssessmentLocaleKeys
                                                                .difficultSituationsQuestionsRateSliderLabels,
                                                            context,
                                                          ) as List)
                                                              .cast<String>()
                                                              .first,
                                                          secondText: (DynamicAssetLoader.getNestedValue(
                                                            AssessmentLocaleKeys
                                                                .difficultSituationsQuestionsRateSliderLabels,
                                                            context,
                                                          ) as List)
                                                              .cast<String>()
                                                              .last,
                                                          key: const Key(
                                                            'overall_slider',
                                                          ),
                                                          onSelect: (p0) {
                                                            assessmentSituationCubit.rateStateSliderVlaue.value = p0;
                                                          },
                                                          selectedValue: assessmentSituationCubit.rateStateSliderVlaue,
                                                          isClick: assessmentSituationCubit.isSitutationButtonClicked.value,
                                                        ),
                                                      ),
                                                      SpaceV(AppSize.h50),
                                                    ],
                                                  ),
                                                  MultiValueListenableBuilder(
                                                    valueListenables: [
                                                      assessmentSituationCubit.conflictState,
                                                      assessmentSituationCubit.workState,
                                                      assessmentSituationCubit.moneyState,
                                                      assessmentSituationCubit.risksState,
                                                      assessmentSituationCubit.pressureState,
                                                      assessmentSituationCubit.rateStateSliderVlaue,
                                                    ],
                                                    builder: (
                                                      BuildContext context,
                                                      List<dynamic> values,
                                                      Widget? child,
                                                    ) {
                                                      // Directly use the values without a getter
                                                      final conflictState = values[0];
                                                      final workState = values[1];
                                                      final moneyState = values[2];
                                                      final risksState = values[3];
                                                      final pressureState = values[4];
                                                      final rateStateSliderValue = values[5];
                                                                            
                                                      // Check conditions to enable the button
                                                      final isButtonEnabled = conflictState != ButtonState.bothDisabled &&
                                                          workState != ButtonState.bothDisabled &&
                                                          moneyState != ButtonState.bothDisabled &&
                                                          risksState != ButtonState.bothDisabled &&
                                                          pressureState != ButtonState.bothDisabled &&
                                                          rateStateSliderValue != -1;
                                                                            
                                                      return CustomButton(
                                                        padding: EdgeInsets.zero,
                                                        key: const Key(
                                                          'next_btn',
                                                        ),
                                                        disableColor: context.themeColors.blueColor.withOpacity(0.1),
                                                        title: CoreLocaleKeys.buttonsNext.tr(),
                                                        inProgress: state.isApiLoading,
                                                        onTap: () async {
                                                          if (!isButtonEnabled) {
                                                            assessmentSituationCubit.isSitutationButtonClicked.value = true;
                                                            if (assessmentSituationCubit.conflictState.value ==
                                                                ButtonState.bothDisabled) {
                                                              await AppCommonFunctions.scrollToKey(
                                                                assessmentSituationCubit.conflictKey,
                                                              );
                                                              return;
                                                            }
                                                            if (assessmentSituationCubit.workState.value ==
                                                                ButtonState.bothDisabled) {
                                                              await AppCommonFunctions.scrollToKey(
                                                                assessmentSituationCubit.workKey,
                                                              );
                                                              return;
                                                            }
                                                            if (assessmentSituationCubit.moneyState.value ==
                                                                ButtonState.bothDisabled) {
                                                              await AppCommonFunctions.scrollToKey(
                                                                assessmentSituationCubit.moneyKey,
                                                              );
                                                              return;
                                                            }
                                                            if (assessmentSituationCubit.risksState.value ==
                                                                ButtonState.bothDisabled) {
                                                              await AppCommonFunctions.scrollToKey(
                                                                assessmentSituationCubit.riskKey,
                                                              );
                                                              return;
                                                            }
                                                            if (assessmentSituationCubit.pressureState.value ==
                                                                ButtonState.bothDisabled) {
                                                              await AppCommonFunctions.scrollToKey(
                                                                assessmentSituationCubit.pressureKey,
                                                              );
                                                              return;
                                                            }
                                                          } else {
                                                            assessmentSituationCubit.isSitutationButtonClicked.value =
                                                                false;
                                                                            
                                                            await assessmentSituationCubit.putAssessmentLifeAPI(
                                                              context: context,
                                                              conflict: assessmentSituationCubit.conflictState.value ==
                                                                      ButtonState.noEnabled
                                                                  ? 0
                                                                  : 1,
                                                              money: assessmentSituationCubit.moneyState.value ==
                                                                      ButtonState.noEnabled
                                                                  ? 0
                                                                  : 1,
                                                              pressure: assessmentSituationCubit.pressureState.value ==
                                                                      ButtonState.noEnabled
                                                                  ? 0
                                                                  : 1,
                                                              rate: assessmentSituationCubit.rateStateSliderVlaue.value,
                                                              risks: assessmentSituationCubit.risksState.value ==
                                                                      ButtonState.noEnabled
                                                                  ? 0
                                                                  : 1,
                                                              work: assessmentSituationCubit.workState.value ==
                                                                      ButtonState.noEnabled
                                                                  ? 0
                                                                  : 1,
                                                            );
                                                          }
                                                        },
                                                        isBottom: true,
                                                        color: isButtonEnabled
                                                            ? context.themeColors.blueColor
                                                            : context.themeColors.blueColor.withOpacity(
                                                                0.7,
                                                              ),
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }
}
