class AssessmentModel {
  AssessmentModel({
    this.assessment,
    this.success,
  });

  factory AssessmentModel.fromJson(Map<String, dynamic> json) => AssessmentModel(
        assessment: json['assessment'] == null ? null : Assessment.fromJson(json['assessment'] as Map<String, dynamic>),
        success: json['success'] as bool,
      );
  Assessment? assessment;
  bool? success;

  Map<String, dynamic> toJson() => {
        'assessment': assessment?.toJson(),
        'success': success,
      };
}

class Assessment {
  Assessment({
    this.rp,
    this.life,
    this.ds,
    this.nt,
    this.ps,
    this.ub,
    this.ls,
    this.ei,
    this.drinking,
    this.drinkingFeeling,
    this.drinkingGoal,
    this.drugs,
    this.completed,
    this.drugsFeeling,
    this.drugsGoal,
    this.assessmentComplete,
    this.time,
  });

  factory Assessment.fromJson(Map<String, dynamic> json) => Assessment(
        rp: json['rp'] == null
            ? null
            : ((json['rp'] as Map<String, dynamic>).isEmpty
                ? Rp.fromJson({})
                : Rp.from<PERSON>son(json['rp'] as Map<String, dynamic>)),
        life: json['life'] == null
            ? null
            : ((json['life'] as Map<String, dynamic>).isEmpty
                ? Life.fromJson({})
                : Life.fromJson(json['life'] as Map<String, dynamic>)),
        ds: json['ds'] == null
            ? null
            : ((json['ds'] as Map<String, dynamic>).isEmpty
                ? Ds.fromJson({})
                : Ds.fromJson(json['ds'] as Map<String, dynamic>)),
        nt: json['nt'] == null
            ? null
            : ((json['nt'] as Map<String, dynamic>).isEmpty
                ? Nt.fromJson({})
                : Nt.fromJson(json['nt'] as Map<String, dynamic>)),
        ps: json['ps'] == null
            ? null
            : ((json['ps'] as Map<String, dynamic>).isEmpty
                ? Ps.fromJson({})
                : Ps.fromJson(json['ps'] as Map<String, dynamic>)),
        ub: json['ub'] == null
            ? null
            : ((json['ub'] as Map<String, dynamic>).isEmpty
                ? Ub.fromJson({})
                : Ub.fromJson(json['ub'] as Map<String, dynamic>)),
        ls: json['ls'] == null
            ? null
            : ((json['ls'] as Map<String, dynamic>).isEmpty
                ? Ls.fromJson({})
                : Ls.fromJson(json['ls'] as Map<String, dynamic>)),
        ei: json['ei'] == null
            ? null
            : ((json['ei'] as Map<String, dynamic>).isEmpty
                ? Ei.fromJson({})
                : Ei.fromJson(json['ei'] as Map<String, dynamic>)),
        drinking: json['drinking'] == null
            ? null
            : ((json['drinking'] as Map<String, dynamic>).isEmpty
                ? Drinking.fromJson({})
                : Drinking.fromJson(json['drinking'] as Map<String, dynamic>)),
        drinkingFeeling: json['drinkingFeeling'] == null
            ? null
            : ((json['drinkingFeeling'] as Map<String, dynamic>).isEmpty
                ? DrinkingFeeling.fromJson({})
                : DrinkingFeeling.fromJson(json['drinkingFeeling'] as Map<String, dynamic>)),
        drinkingGoal: json['drinkingGoal'] == null
            ? null
            : ((json['drinkingGoal'] as Map<String, dynamic>).isEmpty
                ? DrinkingGoal.fromJson({})
                : DrinkingGoal.fromJson(json['drinkingGoal'] as Map<String, dynamic>)),
        drugs: json['drugs'] == null
            ? null
            : ((json['drugs'] as Map<String, dynamic>).isEmpty
                ? Drugs.fromJson({})
                : Drugs.fromJson(json['drugs'] as Map<String, dynamic>)),
        completed: json['completed'] == null ? [] : List<String>.from(json['completed'] as List<dynamic>),
        drugsFeeling: json['drugsFeeling'] == null
            ? null
            : ((json['drugsFeeling'] as Map<String, dynamic>).isEmpty
                ? DrugsFeeling.fromJson({})
                : DrugsFeeling.fromJson(json['drugsFeeling'] as Map<String, dynamic>)),
        drugsGoal: json['drugsGoal'] == null
            ? null
            : ((json['drugsGoal'] as Map<String, dynamic>).isEmpty
                ? DrugsGoal.fromJson({})
                : DrugsGoal.fromJson(json['drugsGoal'] as Map<String, dynamic>)),
        assessmentComplete: json['assessmentComplete'] == null ? null : json['assessmentComplete'] as bool,
        time: json['time'] == null ? null : json['time'] as int,
      );
  Rp? rp;
  Life? life;
  Ds? ds;
  Nt? nt;
  Ps? ps;
  Ub? ub;
  Ls? ls;
  Ei? ei;
  Drinking? drinking;
  DrinkingFeeling? drinkingFeeling;
  DrinkingGoal? drinkingGoal;
  Drugs? drugs;
  List<String>? completed;
  DrugsFeeling? drugsFeeling;
  DrugsGoal? drugsGoal;
  bool? assessmentComplete;
  int? time;

  Map<String, dynamic> toJson() => {
        'rp': rp?.toJson(),
        'life': life?.toJson(),
        'ds': ds?.toJson(),
        'nt': nt?.toJson(),
        'ps': ps?.toJson(),
        'ub': ub?.toJson(),
        'ls': ls?.toJson(),
        'ei': ei?.toJson(),
        'drinking': drinking?.toJson(),
        'drinkingFeeling': drinkingFeeling?.toJson(),
        'drinkingGoal': drinkingGoal?.toJson(),
        'drugs': drugs?.toJson(),
        'completed': completed == null ? <dynamic>[] : List<dynamic>.from(completed!.map((x) => x)),
        'drugsFeeling': drugsFeeling?.toJson(),
        'drugsGoal': drugsGoal?.toJson(),
        'assessmentComplete': assessmentComplete,
        'time': time,
      };
}

class Drinking {
  Drinking({
    this.units,
    this.days,
    this.rate,
  });

  factory Drinking.fromJson(Map<String, dynamic> json) => Drinking(
        units: json['units'] == null ? null : json['units'] as int,
        days: json['days'] == null ? null : json['days'] as int,
        rate: json['rate'] == null ? null : json['rate'] as int,
      );
  int? units;
  int? days;
  int? rate;

  Map<String, dynamic> toJson() => {
        'units': units,
        'days': days,
        'rate': rate,
      };
}

class DrinkingGoal {
  DrinkingGoal({
    this.units,
    this.freeDays,
  });

  factory DrinkingGoal.fromJson(Map<String, dynamic> json) => DrinkingGoal(
        units: json['units'] == null ? null : json['units'] as int,
        freeDays: json['freeDays'] == null ? null : json['freeDays'] as int,
      );

  int? units;
  int? freeDays;

  Map<String, dynamic> toJson() => {
        'units': units,
        'freeDays': freeDays,
      };
}

class DrinkingFeeling {
  DrinkingFeeling({
    this.control,
    this.anxious,
    this.will,
    this.difficulty,
    this.worry,
  });

  factory DrinkingFeeling.fromJson(Map<String, dynamic> json) => DrinkingFeeling(
        control: json['control'] == null ? null : json['control'] as int,
        anxious: json['anxious'] == null ? null : json['anxious'] as int,
        will: json['will'] == null ? null : json['will'] as int,
        difficulty: json['difficulty'] == null ? null : json['difficulty'] as int,
        worry: json['worry'] == null ? null : json['worry'] as int,
      );

  int? control;
  int? anxious;
  int? will;
  int? difficulty;
  int? worry;

  Map<String, dynamic> toJson() => {
        'control': control,
        'anxious': anxious,
        'will': will,
        'difficulty': difficulty,
        'worry': worry,
      };
}

class Drugs {
  Drugs({
    this.list,
    this.rate,
  });

  factory Drugs.fromJson(Map<String, dynamic> json) {
    // Check if 'list' is null or not a Map
    return Drugs(
      list: json['list'] != null && json['list'] is Map<String, dynamic>
          ? (json['list'] as Map<String, dynamic>).map(
              (key, value) {
                // Check if the value is not null before parsing it
                if (value != null && value is Map<String, dynamic>) {
                  return MapEntry(
                    key,
                    ListButane.fromJson(value),
                  );
                }
                // If value is null, return a default entry (can adjust as needed)
                return MapEntry(key, ListButane(drug: 'Unknown'));
              },
            )
          : null, // If 'list' is null or not a map, set it to null
      rate: json['rate'] as int?,
    );
  }

  Map<String, ListButane>? list; // Map of drug names to their details
  int? rate;

  Map<String, dynamic> toJson() => {
        'list': list?.map((key, value) => MapEntry(key, value.toJson())),
        'rate': rate,
      };
}

class ListButane {
  ListButane({
    this.drug,
    this.unit,
    this.amount,
    this.frequency,
  });

  factory ListButane.fromJson(Map<String, dynamic> json) {
    return ListButane(
      drug: json['drug'] as String,
      unit: json['unit'] as String,
      amount: json['amount'] as dynamic,
      frequency: json['frequency'] as int,
    );
  }

  String? drug;
  String? unit;
  dynamic amount;
  int? frequency;

  Map<String, dynamic> toJson() => {
        'drug': drug,
        'unit': unit,
        'amount': amount,
        'frequency': frequency,
      };
}

class DrugsFeeling {
  DrugsFeeling({
    this.control,
    this.anxious,
    this.will,
    this.difficulty,
    this.worry,
  });

  factory DrugsFeeling.fromJson(Map<String, dynamic> json) {
    if (json.entries.first.value is Map<String, dynamic>) {
      final internalVal = json.entries.first.value as Map<String, dynamic>;
      return DrugsFeeling(
         control: internalVal['control'] as int?,
        anxious: internalVal['anxious'] as int?,
        will: internalVal['will'] as int?,
        difficulty: internalVal['difficulty'] as int?,
        worry: internalVal['worry'] as int?,
      );
    } else {
      return DrugsFeeling(
        control: json['control'] as int?,
        anxious: json['anxious'] as int?,
        will: json['will'] as int?,
        difficulty: json['difficulty'] as int?,
        worry: json['worry'] as int?,
      );
    }
  } 
  int? control;
  int? anxious;
  int? will;
  int? difficulty;
  int? worry;

  Map<String, dynamic> toJson() => {
        'control': control,
        'anxious': anxious,
        'will': will,
        'difficulty': difficulty,
        'worry': worry,
      };
}

class DrugsGoal {
  DrugsGoal({required this.drugDetails});

  factory DrugsGoal.fromJson(Map<String, dynamic> json) {
    final parsedDrugs = <String, DrugsDetail>{};
    json.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        parsedDrugs[key] = DrugsDetail.fromJson(value);
      }
    });
    return DrugsGoal(drugDetails: parsedDrugs);
  }
  final Map<String, DrugsDetail> drugDetails;

  Map<String, dynamic> toJson() {
    return drugDetails.map((key, value) => MapEntry(key, value.toJson()));
  }
}


class DrugsDetail {
  DrugsDetail({
    this.units,
    this.freeDays,
  });

  // Factory constructor to create a Drug object from JSON
  factory DrugsDetail.fromJson(Map<String, dynamic> json) => DrugsDetail(
        units: json['units'] as int?,
        freeDays: json['freeDays'] as int?,
      );

  int? units;
  int? freeDays;

  Map<String, dynamic> toJson() => {
        'units': units,
        'freeDays': freeDays,
      };
}
// class DrugsGoal {
//   DrugsGoal({
//     this.units,
//     this.freeDays,
//   });

//   factory DrugsGoal.fromJson(Map<String, dynamic> json) => DrugsGoal(
//         units: json['units'] as int? ?? 0,
//         freeDays: json['freeDays'] as int? ?? 0,
//       );
//   int? units;
//   int? freeDays;

//   Map<String, dynamic> toJson() => {
//         'units': units,
//         'freeDays': freeDays,
//       };
// }

class Ds {
  Ds({
    this.conflict,
    this.risks,
    this.work,
    this.money,
    this.pressure,
    this.rate,
  });

  factory Ds.fromJson(Map<String, dynamic> json) => Ds(
        conflict: json['conflict'] as int?,
        risks: json['risks'] as int?,
        work: json['work'] as int?,
        money: json['money'] as int?,
        pressure: json['pressure'] as int?,
        rate: json['rate'] as int?,
      );
  int? conflict;
  int? risks;
  int? work;
  int? money;
  int? pressure;
  int? rate;

  Map<String, dynamic> toJson() => {
        'conflict': conflict,
        'risks': risks,
        'work': work,
        'money': money,
        'pressure': pressure,
        'rate': rate,
      };
}

class Ei {
  Ei({
    this.nervous,
    this.worry,
    this.down,
    this.bad,
    this.interest,
    this.rate,
  });

  factory Ei.fromJson(Map<String, dynamic> json) => Ei(
        nervous: json['nervous'] as int?,
        worry: json['worry'] as int?,
        down: json['down'] as int?,
        bad: json['bad'] as int?,
        interest: json['interest'] as int?,
        rate: json['rate'] as int?,
      );
  int? nervous;
  int? worry;
  int? down;
  int? bad;
  int? interest;
  int? rate;

  Map<String, dynamic> toJson() => {
        'nervous': nervous,
        'worry': worry,
        'down': down,
        'bad': bad,
        'interest': interest,
        'rate': rate,
      };
}

class Life {
  Life({
    this.quality,
    this.health,
    this.activities,
    this.relationships,
    this.work,
    this.difficulties,
    this.rate,
  });

  factory Life.fromJson(Map<String, dynamic> json) => Life(
        quality: json['quality'] as int?,
        health: json['health'] as int?,
        activities: json['activities'] as int?,
        relationships: json['relationships'] as int?,
        work: json['work'] as int?,
        difficulties: json['difficulties'] as int?,
        rate: json['rate'] as int?,
      );
  int? quality;
  int? health;
  int? activities;
  int? relationships;
  int? work;
  int? difficulties;
  int? rate;

  Map<String, dynamic> toJson() => {
        'quality': quality,
        'health': health,
        'activities': activities,
        'relationships': relationships,
        'work': work,
        'difficulties': difficulties,
        'rate': rate,
      };
}

class Ls {
  Ls({
    this.health,
    this.work,
    this.leisure,
    this.relationships,
    this.housing,
    this.rate,
  });

  factory Ls.fromJson(Map<String, dynamic> json) => Ls(
        health: json['health'] as int?,
        work: json['work'] as int?,
        leisure: json['leisure'] as int?,
        relationships: json['relationships'] as int?,
        housing: json['housing'] as int?,
        rate: json['rate'] as int?,
      );
  int? health;
  int? work;
  int? leisure;
  int? relationships;
  int? housing;
  int? rate;

  Map<String, dynamic> toJson() => {
        'health': health,
        'work': work,
        'leisure': leisure,
        'relationships': relationships,
        'housing': housing,
        'rate': rate,
      };
}

class Nt {
  Nt({
    this.good,
    this.control,
    this.health,
    this.cope,
    this.trust,
    this.rate,
  });

  factory Nt.fromJson(Map<String, dynamic> json) => Nt(
        good: json['good'] as int?,
        control: json['control'] as int?,
        health: json['health'] as int?,
        cope: json['cope'] as int?,
        trust: json['trust'] as int?,
        rate: json['rate'] as int?,
      );
  int? good;
  int? control;
  int? health;
  int? cope;
  int? trust;
  int? rate;

  Map<String, dynamic> toJson() => {
        'good': good,
        'control': control,
        'health': health,
        'cope': cope,
        'trust': trust,
        'rate': rate,
      };
}

class Ps {
  Ps({
    this.craving,
    this.shakes,
    this.cramps,
    this.nausea,
    this.tiredness,
    this.rate,
  });

  factory Ps.fromJson(Map<String, dynamic> json) => Ps(
        craving: json['craving'] as int?,
        shakes: json['shakes'] as int?,
        cramps: json['cramps'] as int?,
        nausea: json['nausea'] as int?,
        tiredness: json['tiredness'] as int?,
        rate: json['rate'] as int?,
      );
  int? craving;
  int? shakes;
  int? cramps;
  int? nausea;
  int? tiredness;
  int? rate;

  Map<String, dynamic> toJson() => {
        'craving': craving,
        'shakes': shakes,
        'cramps': cramps,
        'nausea': nausea,
        'tiredness': tiredness,
        'rate': rate,
      };
}

class Rp {
  Rp({
    this.age,
    this.gender,
    this.ethnicity,
    this.addictionCase,
    this.specialAddiction,
  });

  factory Rp.fromJson(Map<String, dynamic> json) => Rp(
        age: json['age'] as dynamic,
        gender: json['gender'] as dynamic,
        ethnicity: json['ethnicity'] as dynamic,
        addictionCase: json['addictionCase'] as int? ?? -1,
        specialAddiction: json['specialAddiction'] as int? ?? -1,
      );
  dynamic age;
  dynamic gender;
  dynamic ethnicity;
  int? addictionCase;
  int? specialAddiction;

  Map<String, dynamic> toJson() => {
        'age': age,
        'gender': gender,
        'ethnicity': ethnicity,
        'addictionCase': addictionCase,
        'specialAddiction': specialAddiction,
      };
}

class Ub {
  Ub({
    this.aggressive,
    this.avoid,
    this.active,
    this.care,
    this.police,
    this.rate,
  });

  factory Ub.fromJson(Map<String, dynamic> json) => Ub(
        aggressive: json['aggressive'] as int?,
        avoid: json['avoid'] as int?,
        active: json['active'] as int?,
        care: json['care'] as int?,
        police: json['police'] as int?,
        rate: json['rate'] as int?,
      );
  int? aggressive;
  int? avoid;
  int? active;
  int? care;
  int? police;
  int? rate;

  Map<String, dynamic> toJson() => {
        'aggressive': aggressive,
        'avoid': avoid,
        'active': active,
        'care': care,
        'police': police,
        'rate': rate,
      };
}
