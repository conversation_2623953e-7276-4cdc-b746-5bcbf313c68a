import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/model/assessment_model.dart';
import 'package:breakingfree_v2/features/assessment_module/repository/assessment_repository.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';

import '../mocks/mock_hive.dart';

Future<void> main() async {
  await assessmentUnitTests();
}

Future<void> assessmentUnitTests() async {
  late final DioAdapter dioAdapter;
  setUpAll(() async {
    /// Cooking dependencies
    TestWidgetsFlutterBinding.ensureInitialized();
    integrationMode = IntegrationMode.unit;
    await setUpTestHive();
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
        'Is-App': true,
        // 'Cookie': Injector.instance<AppDB>().cookie,
      },
    );
    final dio = Dio(options);
    dioAdapter = DioAdapter(dio: dio);
    dio.httpClientAdapter = dioAdapter;
    await Injector.initMockModules(dio);
    await Injector.instance.isReady<AppDB>();
  });

  tearDownAll(() async {
    await tearDownTestHive();
  });

  test('Assessment recovery program test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.recoveryProgram,
        (server) => server.reply(200, {'success': true}),
        data: {
          'age': 20,
          'gender': false,
          'ethnicity': false,
          'name': '',
          'addictionCase': 0,
        },
      )
      ..onPost(
        EndPoints.recoveryProgram,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'age': 22,
          'gender': false,
          'ethnicity': false,
          'name': '',
          'addictionCase': 110,

          /// Invalid data
        },
      );

    /// Test recovery program (simulate successful recovery program)
    final result = await assessmentRepo.putAssessmentRecoveryProgram(
      addictionCase: 0,
      age: 20,
      ethnicity: false,
      gender: false,
      name: '',
      specialAddiction: null,
      context: null,
    );
    // if return value is [AssessmentModel] then recovery program is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test recovery program (simulate failed recovery program)
    final result1 = await assessmentRepo.putAssessmentRecoveryProgram(
      addictionCase: 110,
      age: 22,
      ethnicity: false,
      gender: false,
      name: '',
      specialAddiction: null,
      context: null,
    );
    expect(result1, null);
  });

  test('Life assessment test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.userLife,
        (server) => server.reply(200, {'success': true}),
        data: {
          'quality': 5,
          'health': 5,
          'activities': 5,
          'relationships': 5,
          'work': 5,
          'difficulties': 5,
          'rate': 5,
        },
      )
      ..onPost(
        EndPoints.userLife,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'quality': 11,
          'health': 11,
          'activities': 11,
          'relationships': 11,
          'work': 11,
          'difficulties': 11,
          'rate': 11,
        },
      );

    /// Test Life assessment (simulate successful Life assessment)
    final result = await assessmentRepo.putAssessmentLife(
      activities: 5,
      difficulties: 5,
      health: 5,
      quality: 5,
      rate: 5,
      relationships: 5,
      work: 5,
      context: null,
    );
    // if return value is [AssessmentModel] then Life assessment is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test Life assessment (simulate failed Life assessment)
    final result1 = await assessmentRepo.putAssessmentLife(
      activities: 11,
      difficulties: 11,
      health: 11,
      quality: 11,
      rate: 11,
      relationships: 11,
      work: 11,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment difficult situations test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.difficultSitutation,
        (server) => server.reply(200, {'success': true}),
        data: {
          'conflict': 6,
          'work': 6,
          'money': 6,
          'risks': 6,
          'pressure': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.difficultSitutation,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'conflict': 12,
          'work': 12,
          'money': 12,
          'risks': 12,
          'pressure': 12,
          'rate': 12,
        },
      );

    /// Test assessment difficult situations (simulate successful assessment difficult situations)
    final result = await assessmentRepo.putAssessmentDifficultSituations(
      conflict: 6,
      money: 6,
      pressure: 6,
      risks: 6,
      work: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment difficult situations is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment difficult situations (simulate failed assessment difficult situations)
    final result1 = await assessmentRepo.putAssessmentDifficultSituations(
      conflict: 12,
      money: 12,
      pressure: 12,
      risks: 12,
      work: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment negative thoughts test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.negativeThought,
        (server) => server.reply(200, {'success': true}),
        data: {
          'good': 6,
          'control': 6,
          'health': 6,
          'cope': 6,
          'trust': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.negativeThought,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'good': 12,
          'control': 12,
          'health': 12,
          'cope': 12,
          'trust': 12,
          'rate': 12,
        },
      );

    /// Test assessment negative thoughts (simulate successful assessment negative thoughts)
    final result = await assessmentRepo.putAssessmentNegativeThoughts(
      control: 6,
      cope: 6,
      good: 6,
      health: 6,
      trust: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment negative thoughts is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment negative thoughts (simulate failed negative thoughts situations)
    final result1 = await assessmentRepo.putAssessmentNegativeThoughts(
      control: 12,
      cope: 12,
      good: 12,
      health: 12,
      trust: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment physical sensation test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.physicalSenstation,
        (server) => server.reply(200, {'success': true}),
        data: {
          'craving': 6,
          'shakes': 6,
          'cramps': 6,
          'nausea': 6,
          'tiredness': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.physicalSenstation,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'craving': 12,
          'shakes': 12,
          'cramps': 12,
          'nausea': 12,
          'tiredness': 12,
          'rate': 12,
        },
      );

    /// Test assessment physical sensation (simulate successful assessment physical sensation)
    final result = await assessmentRepo.putPhysicalSensation(
      cramps: 6,
      cravings: 6,
      nausea: 6,
      shakes: 6,
      tiredness: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment physical sensation is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment negative thoughts (simulate failed physical sensation)
    final result1 = await assessmentRepo.putPhysicalSensation(
      cramps: 12,
      cravings: 12,
      nausea: 12,
      shakes: 12,
      tiredness: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment unhelpful behavior test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.unhelpfulBehaviour,
        (server) => server.reply(200, {'success': true}),
        data: {
          'aggressive': 6,
          'avoid': 6,
          'active': 6,
          'care': 6,
          'police': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.unhelpfulBehaviour,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'aggressive': 12,
          'avoid': 12,
          'active': 12,
          'care': 12,
          'police': 12,
          'rate': 12,
        },
      );

    /// Test assessment unhelpful behavior (simulate successful assessment unhelpful behavior)
    final result = await assessmentRepo.putUnhelpfulBehaviour(
      active: 6,
      aggressive: 6,
      avoid: 6,
      care: 6,
      police: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment unhelpful behavior is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment unhelpful behavior (simulate failed unhelpful behavior)
    final result1 = await assessmentRepo.putUnhelpfulBehaviour(
      active: 12,
      aggressive: 12,
      avoid: 12,
      care: 12,
      police: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment life-style test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.lifeStyle,
        (server) => server.reply(200, {'success': true}),
        data: {
          'health': 6,
          'work': 6,
          'leisure': 6,
          'relationships': 6,
          'housing': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.lifeStyle,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'health': 12,
          'work': 12,
          'leisure': 12,
          'relationships': 12,
          'housing': 12,
          'rate': 12,
        },
      );

    /// Test assessment life-style (simulate successful assessment life-style)
    final result = await assessmentRepo.putLifeStyle(
      health: 6,
      housing: 6,
      leisure: 6,
      relationships: 6,
      work: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment life-style is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment life-style (simulate failed life-style)
    final result1 = await assessmentRepo.putLifeStyle(
      health: 12,
      housing: 12,
      leisure: 12,
      relationships: 12,
      work: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment emotional impact test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.emotionalImpact,
        (server) => server.reply(200, {'success': true}),
        data: {
          'nervous': 6,
          'worry': 6,
          'down': 6,
          'bad': 6,
          'interest': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.emotionalImpact,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'nervous': 12,
          'worry': 12,
          'down': 12,
          'bad': 12,
          'interest': 12,
          'rate': 12,
        },
      );

    /// Test assessment emotional impact (simulate successful assessment emotional impact)
    final result = await assessmentRepo.putEmotionalImpact(
      bad: 6,
      down: 6,
      interest: 6,
      nervous: 6,
      worry: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment emotional impact is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment emotional impact (simulate failed emotional impact)
    final result1 = await assessmentRepo.putEmotionalImpact(
      bad: 12,
      down: 12,
      interest: 12,
      nervous: 12,
      worry: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment drinking test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drinking,
        (server) => server.reply(200, {'success': true}),
        data: {
          'days': 6,
          'units': 6,
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.drinking,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'days': 12,
          'units': 12,
          'rate': 12,
        },
      );

    /// Test assessment drinking (simulate successful assessment drinking)
    final result = await assessmentRepo.putDrinking(
      days: 6,
      units: 6,
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drinking is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drinking (simulate failed drinking)
    final result1 = await assessmentRepo.putDrinking(
      days: 12,
      units: 12,
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment drinking feeling test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drinkingFeeling,
        (server) => server.reply(200, {'success': true}),
        data: {
          'control': 6,
          'anxious': 6,
          'worry': 6,
          'will': 6,
          'difficulty': 6,
        },
      )
      ..onPost(
        EndPoints.drinkingFeeling,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'control': 12,
          'anxious': 12,
          'worry': 12,
          'will': 12,
          'difficulty': 12,
        },
      );

    /// Test assessment drinking feeling (simulate successful assessment drinking feeling)
    final result = await assessmentRepo.putDrinkingFeeling(
      anxious: 6,
      control: 6,
      difficulty: 6,
      worry: 6,
      will: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drinking feeling is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drinking feeling (simulate failed drinking feeling)
    final result1 = await assessmentRepo.putDrinkingFeeling(
      anxious: 12,
      control: 12,
      difficulty: 12,
      worry: 12,
      will: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment drinking goal test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drinkingGoal,
        (server) => server.reply(200, {'success': true}),
        data: {
          'units': 6,
          'freeDays': 6,
        },
      )
      ..onPost(
        EndPoints.drinkingGoal,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'units': 12,
          'freeDays': 12,
        },
      );

    /// Test assessment drinking goal (simulate successful assessment drinking goal)
    final result = await assessmentRepo.putdrinkingGoal(
      freeDays: 6,
      units: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drinking goal is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drinking goal (simulate failed drinking goal)
    final result1 = await assessmentRepo.putdrinkingGoal(
      freeDays: 12,
      units: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment drug test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drug,
        (server) => server.reply(200, {'success': true}),
        data: {
          'list': {
            'drug_name': {'valid': 'data'},
          },
          'rate': 6,
        },
      )
      ..onPost(
        EndPoints.drug,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'list': {
            'drug_name': {'invalid': 'data'},
          },
          'rate': 12,
        },
      );

    /// Test assessment drug (simulate successful assessment drug)
    final result = await assessmentRepo.putdrug(
      list: {
        'drug_name': {'valid': 'data'},
      },
      rate: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drug is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drug (simulate failed drug)
    final result1 = await assessmentRepo.putdrug(
      list: {
        'drug_name': {'invalid': 'data'},
      },
      rate: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Put assessment drug feeling test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drugFeeling,
        (server) => server.reply(200, {'success': true}),
        data: {
          'control': 6,
          'anxious': 6,
          'worry': 6,
          'will': 6,
          'difficulty': 6,
        },
      )
      ..onPost(
        EndPoints.drugFeeling,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'control': 12,
          'anxious': 12,
          'worry': 12,
          'will': 12,
          'difficulty': 12,
        },
      );

    /// Test assessment drug feeling (simulate successful assessment drug feeling)
    final result = await assessmentRepo.putdrugFeeling(
      anxious: 6,
      control: 6,
      difficulty: 6,
      worry: 6,
      will: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drug feeling is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drug feeling (simulate failed drug feeling)
    final result1 = await assessmentRepo.putdrugFeeling(
      anxious: 12,
      control: 12,
      difficulty: 12,
      worry: 12,
      will: 12,
      context: null,
    );
    expect(result1, null);
  });
  test('Put assessment drug goal test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.drugGoal,
        (server) => server.reply(200, {'success': true}),
        data: {
          'units': 6,
          'freeDays': 6,
        },
      )
      ..onPost(
        EndPoints.drugGoal,
        (server) => server.reply(402, {'success': false, 'message': 'Invalid data'}),
        data: {
          'units': 12,
          'freeDays': 12,
        },
      );

    /// Test assessment drug goal (simulate successful assessment drug goal)
    final result = await assessmentRepo.putdrugGoal(
      freeDays: 6,
      units: 6,
      context: null,
    );
    // if return value is [AssessmentModel] then assessment drug goal is successful.
    expect(result, isInstanceOf<AssessmentModel>());

    /// Test assessment drug goal (simulate failed drug goal)
    final result1 = await assessmentRepo.putdrugGoal(
      freeDays: 12,
      units: 12,
      context: null,
    );
    expect(result1, null);
  });

  test('Assessment thank you test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.thankYou,
      (server) => server.reply(200, {'success': true}),
      data: <String, String>{},
    );

    /// Test assessment drug goal (simulate successful assessment thank you)
    final result = await assessmentRepo.thankYou(
      context: null,
    );
    // if return value is [AssessmentModel] then assessment thank you is successful.
    expect(result, isInstanceOf<AssessmentModel>());
  });

  test('Assessment video test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.assessmentVideo,
      (server) => server.reply(200, {'success': true}),
      data: <String, String>{},
    );

    /// Test assessment video (simulate successful assessment video)
    final result = await assessmentRepo.assessmentVideo(
      context: null,
    );
    // if return value is [AssessmentModel] then assessment video is successful.
    expect(result, isInstanceOf<AssessmentModel>());
  });

  test('Assessment bridging video test', () async {
    final assessmentRepo = AssessmentRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.bridgingVideo,
      (server) => server.reply(200, {'success': true}),
      data: <String, String>{},
    );

    /// Test assessment bridging video (simulate successful assessment bridging video)
    final result = await assessmentRepo.bridgingVideo(
      context: null,
    );
    // if return value is [AssessmentModel] then assessment bridging video is successful.
    expect(result, isInstanceOf<AssessmentModel>());
  });
}
