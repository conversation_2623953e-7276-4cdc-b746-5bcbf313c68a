import 'dart:convert';
import 'dart:io';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking/assessment_drinking_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_feeling/assessment_drinking_feeling_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drinking_goal/assessment_drinking_goal_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_drug/assessment_drug_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_emotional_imapct/assessment_emotional_impact_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life/assessment_life_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_life_style/assessment_life_style_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_main_cubit/assessment_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_notification/assessment_notification_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_physical_senstion/assessment_physical_senstation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_situtation/assessment_situtation_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_thoughts/assessment_thoughts_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/cubit/assessment_unhelpful_behaviour/assessment_unhelpful_behaviour_cubit.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assesment_bridging_video_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_difficult_situtation_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_feeling_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_goal_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drinking_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drug_goal_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drugs_feeling_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_drugs_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_emotional_impact_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_life_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_life_style_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_physical_sensation_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_recovery_program_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_thank_you_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_thought_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_unhelpful_behaviour_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_video_page.dart';
import 'package:breakingfree_v2/features/assessment_module/pages/assessment_welcome_video_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/main.dart' as app;
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/theme_light.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/src/localization.dart';
import 'package:easy_localization/src/translations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:shared_preferences/src/shared_preferences_legacy.dart';

import '../mocks/mock_hive.dart';

Future<void> main() async {
  late final DioAdapter dioAdapter;
  setUpAll(() async {
    SharedPreferences.setMockInitialValues({});
    integrationMode = IntegrationMode.widget;
    HttpOverrides.global = null;
    Localization.load(
      const Locale('en'),
      translations: Translations(
        json.decode(await rootBundle.loadString('assets/translations/en-US.json')) as Map<String, dynamic>?,
      ),
    );
    TestWidgetsFlutterBinding.ensureInitialized();
    await EasyLocalization.ensureInitialized();
    await setUpTestHive();
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
        'Is-App': true,
        // 'Cookie': Injector.instance<AppDB>().cookie,
      },
    );
    final dio = Dio(options);
    dioAdapter = DioAdapter(dio: dio);
    dio.httpClientAdapter = dioAdapter;
    await Injector.initMockModules(dio);
    await Injector.instance.isReady<AppDB>();
  });

  testWidgets('test Assessment welcome screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentWelcomeVideoPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to watch this video before continuing');
    expect(snackbar, findsOneWidget);
  });
  testWidgets('test Assessment recovery program screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentRecoveryProgramPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment life screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentLifePage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment difficult situation screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        Scaffold(body: AssessmentDifficultSitutationPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment thought screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentThoughtPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment physical sensation screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentPhysicalSensationPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment unhelpful behavior screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentUnhelpfulBehaviourPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment life style screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentLifeStylePage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment emotional impact screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentEmotionalImpactPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment drinking screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrinkingPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to choose a number of units before continuing');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment drinking feeling screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrinkingFeelingPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment drinking goal screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrinkingGoalPage()),
      ),
    );
    // Mock api response
    dioAdapter.onPost(
      EndPoints.drinkingGoal,
      (server) => server.reply(200, {'success': true}),
      data: {
        'units': 0,
        'freeDays': 0,
      },
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
  });

  testWidgets('test Assessment drug screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrugsPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment drug feeling screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrugsFeelingPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to answer all the questions before you can continue');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment drug goal widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentDrugGoalPage()),
      ),
    );
    // Mock api response
    dioAdapter.onPost(
      EndPoints.drugGoal,
      (server) => server.reply(200, {'success': true}),
      data: {
        'units': 0,
        'freeDays': 0,
      },
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
  });

  testWidgets('test Assessment thank you widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentThankYouPage()),
      ),
    );
    // Mock api response
    dioAdapter.onPost(
      EndPoints.thankYou,
      (server) => server.reply(200, {'success': true}),
      data: <String, String>{},
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
  });
  testWidgets('test Assessment video screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssessmentVideoPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to watch this video before continuing');
    expect(snackbar, findsOneWidget);
  });

  testWidgets('test Assessment bridging video screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAssessment(
        const Scaffold(body: AssesmentBridgingVideoPage()),
      ),
    );
    await tester.pump();
    final nextBtn = find.byKey(const Key('next_btn'));
    expect(nextBtn, findsOneWidget);
    await tester.tap(nextBtn);
    await tester.pumpAndSettle();
    final snackbar = find.text('You need to watch this video before continuing');
    expect(snackbar, findsOneWidget);
  });
}

Widget baseSetupAssessment(Widget child2) {
  return EasyLocalization(
    supportedLocales: const [
      Locale('en', 'UK'),
      Locale('fr', 'CA'),
    ],
    path: 'assets/translations',
    child: MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => SignupCubit()),
        BlocProvider(
          create: (_) => AssessmentCubit()
            ..initialData()
            ..userRecoveryData(),
        ),
        BlocProvider(create: (_) => AssessmentDrinkingCubit()..userDrinkingData()),
        BlocProvider(create: (_) => AssessmentSitutationCubit()..userSitutationData()),
        BlocProvider(create: (_) => AssessmentLifeCubit()..userLiftData()),
        BlocProvider(create: (_) => AssessmentThoughtsCubit()..userThoughtData()),
        BlocProvider(create: (_) => AssessmentPhysicalSenstationCubit()..userPhysicalSenstiontData()),
        BlocProvider(create: (_) => AssessmentUnhelpfulBehaviourCubit()..userUnhelpfulBehaviourData()),
        BlocProvider(create: (_) => AssessmentLifeStyleCubit()..userLifeStyleData()),
        BlocProvider(create: (_) => AssessmentEmotionalImpactCubit()..userEmotionalImpactData()),
        BlocProvider(create: (_) => AssessmentDrinkingFeelingCubit()..userDrinkingFeelingData()),
        BlocProvider(create: (_) => AssessmentDrinkingGoalCubit()..userDrinkingGoalData()),
        BlocProvider(
          create: (_) => AssessmentDrugCubit()
            ..userDrugData()
            ..userDrugFeelingData()
            ..userDrugGoalData(),
        ),
        BlocProvider(create: (_) => AssessmentNotificationCubit()),
      ],
      child: ScreenUtilInit(
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            title: 'Breaking Free',
            debugShowCheckedModeBanner: false,
            theme: lightTheme,
            // localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            home: child2,
            navigatorKey: app.navigatorKey,
          );
        },
      ),
    ),
  );
}
