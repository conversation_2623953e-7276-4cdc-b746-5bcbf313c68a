// 🎯 Dart imports:
import 'dart:io';
import 'dart:math';

// 📦 Package imports:
import 'package:hive/hive.dart';
import 'package:path/path.dart' as path;

final _random = Random();
String _tempPath = path.join(Directory.current.path, '.dart_tool', 'test', 'tmp');

/// Returns a temporary directory in which a Hive can be initialized
Future<Directory> getTempDir() async {
  final name = _random.nextInt(pow(2, 32) as int);
  final dir = Directory(path.join(_tempPath, '${name}_tmp'));

  if (dir.existsSync()) await dir.delete(recursive: true);

  await dir.create(recursive: true);
  return dir;
}

/// Initializes a [Hive] in a temporary directory.
///
/// Be sure to run [tearDownTestHive] once your test has completed.
Future<void> setUpTestHive() async {
  final tempDir = await getTempDir();
  Hive.init(tempDir.path);
}

/// Deletes the temporary [Hive].
Future<void> tearDownTestHive() async {
  await Hive.deleteFromDisk();
}
