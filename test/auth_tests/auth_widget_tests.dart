import 'dart:convert';
import 'dart:io';

import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/forget_password_module/pages/forget_password_page.dart';
import 'package:breakingfree_v2/features/authentication_module/locale_keys/authentication_locale_keys.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/cubit/login_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/pages/login_page.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/cubit/signup_cubit.dart';
import 'package:breakingfree_v2/features/authentication_module/sign_up_module/pages/sign_up_page.dart';
import 'package:breakingfree_v2/main.dart' as app;
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/res/theme_light.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization/src/localization.dart';
import 'package:easy_localization/src/translations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../mocks/mock_hive.dart';

Future<void> main() async {
  late final DioAdapter dioAdapter;
  setUpAll(() async {
    SharedPreferences.setMockInitialValues({});
    integrationMode = IntegrationMode.widget;
    HttpOverrides.global = null;
    Localization.load(
      const Locale('en'),
      translations: Translations(
        json.decode(await rootBundle.loadString('assets/translations/en-US.json')) as Map<String, dynamic>?,
      ),
    );
    TestWidgetsFlutterBinding.ensureInitialized();
    await EasyLocalization.ensureInitialized();
    await setUpTestHive();
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
        'Is-App': true,
        // 'Cookie': Injector.instance<AppDB>().cookie,
      },
    );
    final dio = Dio(options);
    dioAdapter = DioAdapter(dio: dio);
    dio.httpClientAdapter = dioAdapter;
    await Injector.initMockModules(dio);
    await Injector.instance.isReady<AppDB>();
  });

  testWidgets('test sign-up screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAuth(
         LoginPage(),
      ),
    );
    await tester.pump();
    final loginBtn = find.byKey(const Key('login_button'));
    final emailField = find.byKey(const Key('email_field'));
    final passField = find.byKey(const Key('pass_field'));
    expect(loginBtn, findsOneWidget);
    expect(emailField, findsOneWidget);
    expect(passField, findsOneWidget);
    await tester.enterText(emailField, '<EMAIL>');
    await tester.enterText(passField, '12345678');
    dioAdapter.onPost(
      EndPoints.login,
      (server) => server.reply(400, {'success': true}),
      data: {'email': '<EMAIL>', 'password': '12345678'},
    );
    await tester.tap(loginBtn);
    await tester.pump(const Duration(seconds: 1));
    final element = tester.element(emailField);
    final cubit = element.read<LoginCubit>();
    expect(cubit.state, isInstanceOf<LoginFailureState>());
    dioAdapter
      ..onPost(
        EndPoints.login,
        (server) => server.reply(
          200,
          {
            'success': true,
            'user': {
              'email': {
                'address': '',
                'verified': true,
              },
            },
          },
        ),
        data: {'email': '<EMAIL>', 'password': '12345678'},
      )
      ..onGet(
        EndPoints.getUserData,
        (server) => server.reply(
          200,
          {
            'success': true,
            'user': {
              'email': {
                'address': '',
                'verified': true,
              },
            },
          },
        ),
      );
    await tester.tap(loginBtn);
    await tester.pump(const Duration(seconds: 1));
    expect(cubit.state, isInstanceOf<LoginSuccessState>());
  });

  testWidgets('test login screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAuth(
        const SignUpPage(),
      ),
    );

    await tester.pump();
    final createAccountBtn = find.text('Create Account');
    final emailField = find.byKey(const Key('email'));
    final confirmEmailField = find.byKey(const Key('confirm_email'));
    final passField = find.byKey(const Key('password'));
    final confirmPassField = find.byKey(const Key('confirm_password'));
    final serviceCodeField = find.byKey(const Key('access_code'));
    final scrollView = find.byKey(const Key('scroll_view'));

    expect(createAccountBtn, findsOneWidget);
    expect(emailField, findsOneWidget);
    expect(confirmEmailField, findsOneWidget);
    expect(passField, findsOneWidget);
    expect(confirmPassField, findsOneWidget);
    expect(serviceCodeField, findsOneWidget);
    expect(scrollView, findsOneWidget);

    await tester.tap(createAccountBtn);
    await tester.pump();
    final emailError = find.text('Email is required');
    final passwordError = find.text('Password is required');
    final serviceCodeError = find.text(AuthLocaleKeys.signUpCodeRequired.tr());

    expect(emailError, findsExactly(2));
    expect(passwordError, findsExactly(2));
    expect(serviceCodeError, findsExactly(1));
  });

  testWidgets('test forgot password screen widget', (tester) async {
    await tester.pumpWidget(
      baseSetupAuth(
        const ForgetPasswordPage(),
      ),
    );

    await tester.pump();
    final resetBtn = find.text('Reset');
    final emailField = find.byKey(const Key('email'));

    expect(resetBtn, findsOneWidget);
    expect(emailField, findsOneWidget);

    await tester.tap(resetBtn);
    await tester.pump();
    final emailError = find.text('Email is required');

    expect(emailError, findsOneWidget);
  });
}

Widget baseSetupAuth(Widget child2) {
  return EasyLocalization(
    supportedLocales: const [
      Locale('en', 'UK'),
      Locale('fr', 'CA'),
    ],
    path: 'assets/translations',
    child: MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => SignupCubit()),
      ],
      child: ScreenUtilInit(
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            title: 'Breaking Free',
            debugShowCheckedModeBanner: false,
            theme: lightTheme,
            // localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: context.locale,
            home: child2,
            navigatorKey: app.navigatorKey,
          );
        },
      ),
    ),
  );
}
