import 'package:breakingfree_v2/app/db/app_db.dart';
import 'package:breakingfree_v2/features/authentication_module/login_module/models/user_model.dart';
import 'package:breakingfree_v2/features/authentication_module/repository/auth_repository.dart';
import 'package:breakingfree_v2/main.dart';
import 'package:breakingfree_v2/res/enums.dart';
import 'package:breakingfree_v2/services/api_services/api_endpoint.dart';
import 'package:breakingfree_v2/services/di/injector.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';

import '../mocks/mock_hive.dart';

Future<void> main() async {
  await authUnitTests();
}

Future<void> authUnitTests() async {
  late final DioAdapter dioAdapter;
  setUpAll(() async {
    /// Cooking dependencies
    TestWidgetsFlutterBinding.ensureInitialized();
    integrationMode = IntegrationMode.unit;
    await setUpTestHive();
    final options = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      connectTimeout: const Duration(milliseconds: 600000),
      receiveTimeout: const Duration(milliseconds: 600000),
      headers: {
        'Is-App': true,
        // 'Cookie': Injector.instance<AppDB>().cookie,
      },
    );
    final dio = Dio(options);
    dioAdapter = DioAdapter(dio: dio);
    dio.httpClientAdapter = dioAdapter;
    await Injector.initMockModules(dio);
    await Injector.instance.isReady<AppDB>();
  });

  tearDownAll(() async {
    await tearDownTestHive();
  });

  /// Unit testing for auth repository and auth cubits
  test('Auth login tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter
      ..onPost(
        EndPoints.login,
        (server) => server.reply(200, {'success': true}),
        data: {'email': '<EMAIL>', 'password': '********'},
      )
      ..onPost(
        EndPoints.login,
        (server) => server.reply(402, {'success': false}),
        data: {'email': '<EMAIL>', 'password': '********'},
      );

    /// Test account login (simulate successful login)
    final result = await authRepo.login(email: '<EMAIL>', password: '********', context: null);
    // if return value is [UserModel] then login is successful.
    expect(result, isInstanceOf<UserModel>());

    /// Test account login (simulate failed login)
    final result1 = await authRepo.login(email: '<EMAIL>', password: '********', context: null);
    expect(result1, null);
  });

  test('Auth logout tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.logout,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      data: <String, dynamic>{},
    );

    /// simulate successful logout
    final result = await authRepo.logOut(context: null);
    // if return value is [Response<Map<String, dynamic>>] then logout is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onPost(
      EndPoints.logout,
      (server) => server.reply(402, {'success': false, 'message': 'logout is not successful'}),
      data: <String, dynamic>{},
    );

    /// simulate failed logout
    final result1 = await authRepo.logOut(context: null);
    expect(result1, null);
  });

  test('Auth sign-up tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.register,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      data: {
        'email': '<EMAIL>',
        'password': '********',
        'serviceCode': 'specialaccess',
      },
    );

    /// simulate successful sign-up
    final result = await authRepo.signUp(
      email: '<EMAIL>',
      password: '********',
      serviceCode: 'specialaccess',
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then sign-up is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onPost(
      EndPoints.register,
      (server) => server.reply(402, {'success': false, 'message': 'User already exists'}),
      data: {
        'email': '<EMAIL>',
        'password': '********',
        'serviceCode': 'specialaccess',
      },
    );

    /// simulate failed sign-up
    final result1 = await authRepo.signUp(
      email: '<EMAIL>',
      password: '********',
      serviceCode: 'specialaccess',
      context: null,
    );
    expect(result1, null);
  });

  test('Auth getPolicies tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onGet(
      EndPoints.policies,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      queryParameters: {
        'lang': 'en',
      },
    );

    /// simulate get policy
    final result = await authRepo.getPolicies(
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then get policy is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onGet(
      EndPoints.policies,
      (server) => server.reply(402, {'success': false, 'message': 'failed to get policy'}),
      queryParameters: {
        'lang': 'en',
      },
    );

    /// simulate failed get policy
    final result1 = await authRepo.getPolicies(
      context: null,
    );
    expect(result1, null);
  });

  test('Auth forget password tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.forgotPassword,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      data: {
        'email': '<EMAIL>',
        'role': 'user',
        'lang': 'en',
      },
    );

    /// simulate successful sign-up
    final result = await authRepo.forgotPassword(
      email: '<EMAIL>',
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then sign-up is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onPost(
      EndPoints.forgotPassword,
      (server) => server.reply(402, {'success': false, 'message': 'User does not exists'}),
      data: {
        'email': '<EMAIL>',
        'role': 'user',
        'lang': 'en',
      },
    );

    /// simulate failed sign-up
    final result1 = await authRepo.forgotPassword(
      email: '<EMAIL>',
      context: null,
    );
    expect(result1, null);
  });

  test('Auth resend verification email tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.resendVerificationEmail,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      data: {
        'token': '',
      },
    );

    /// simulate successful sign-up
    final result = await authRepo.resendVerificationEmail(
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then resend email is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onPost(
      EndPoints.resendVerificationEmail,
      (server) => server.reply(402, {'success': false, 'message': 'Too many requests'}),
      data: {
        'token': '',
      },
    );

    /// simulate failed resend email
    final result1 = await authRepo.resendVerificationEmail(
      context: null,
    );
    expect(result1, null);
  });

  test('Auth get user data tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onGet(
      EndPoints.getUserData,
      (server) => server.reply(
        200,
        {'success': true},
      ),
    );

    /// simulate get user data
    final result = await authRepo.getUserData(
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then get user data is successful.
    expect(result, isInstanceOf<UserModel>());

    /// Mock response
    dioAdapter.onGet(
      EndPoints.getUserData,
      (server) => server.reply(402, {'success': false, 'message': 'failed to get user data'}),
    );

    /// simulate failed get user data
    final result1 = await authRepo.getUserData(
      context: null,
    );
    expect(result1, null);
  });

  test('Auth user stats tests', () async {
    final authRepo = AuthRepository();

    /// Mock response
    dioAdapter.onPost(
      EndPoints.userStats,
      (server) => server.reply(
        200,
        {'success': true},
      ),
      data: {
        'type': 'tutorialVideo',
      },
    );

    /// simulate successful get user stats
    final result = await authRepo.stats(
      type: 'tutorialVideo',
      context: null,
    );
    // if return value is [Response<Map<String, dynamic>>] then get user stats is successful.
    expect(result, isInstanceOf<Response<Map<String, dynamic>>>());

    /// Mock response
    dioAdapter.onPost(
      EndPoints.userStats,
      (server) => server.reply(402, {'success': false, 'message': 'Stat does not exist'}),
      data: {
        'type': 'tutorialVideo',
      },
    );

    /// simulate failed to get user stats
    final result1 = await authRepo.stats(
      type: 'tutorialVideo',
      context: null,
    );
    expect(result1, null);
  });
}
