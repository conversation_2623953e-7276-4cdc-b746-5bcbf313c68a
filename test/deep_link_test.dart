import 'package:flutter_test/flutter_test.dart';
import 'package:breakingfree_v2/services/deep_link_service.dart';

void main() {
  group('DeepLinkService Tests', () {
    late DeepLinkService deepLinkService;

    setUp(() {
      deepLinkService = DeepLinkService();
    });

    test('should identify password reset link correctly', () {
      // Test with the example URL format
      const testUrl = 'https://www.breakingfreeonline.com.au/accounts/reset/f46b2fe945c58cbdf8ae18ad5f5176d19e83599c';
      final uri = Uri.parse(testUrl);
      
      // Since _isPasswordResetLink is private, we'll test through the public testDeepLink method
      expect(uri.host, contains('breakingfreeonline.com.au'));
      expect(uri.pathSegments.length, greaterThanOrEqualTo(3));
      expect(uri.pathSegments[0], equals('accounts'));
      expect(uri.pathSegments[1], equals('reset'));
      expect(uri.pathSegments[2], equals('f46b2fe945c58cbdf8ae18ad5f5176d19e83599c'));
    });

    test('should parse different domain variations', () {
      final testUrls = [
        'https://www.breakingfreeonline.com.au/accounts/reset/token123',
        'https://breakingfreeonline.com.au/accounts/reset/token456',
        'https://breakingfreeonline.com/accounts/reset/token789',
      ];

      for (final url in testUrls) {
        final uri = Uri.parse(url);
        expect(uri.pathSegments.length, greaterThanOrEqualTo(3));
        expect(uri.pathSegments[0], equals('accounts'));
        expect(uri.pathSegments[1], equals('reset'));
        expect(uri.pathSegments[2].isNotEmpty, isTrue);
      }
    });

    test('should reject invalid URLs', () {
      final invalidUrls = [
        'https://example.com/accounts/reset/token',
        'https://breakingfreeonline.com.au/invalid/path/token',
        'https://breakingfreeonline.com.au/accounts/invalid/token',
        'https://breakingfreeonline.com.au/accounts/reset/', // empty token
      ];

      for (final url in invalidUrls) {
        final uri = Uri.parse(url);
        final isValidDomain = uri.host.contains('breakingfreeonline.com.au') || 
                             uri.host.contains('breakingfreeonline.com');
        final isValidPath = uri.pathSegments.length >= 3 && 
                           uri.pathSegments[0] == 'accounts' && 
                           uri.pathSegments[1] == 'reset' &&
                           uri.pathSegments[2].isNotEmpty;
        
        expect(isValidDomain && isValidPath, isFalse);
      }
    });

    tearDown(() {
      deepLinkService.dispose();
    });
  });
}
