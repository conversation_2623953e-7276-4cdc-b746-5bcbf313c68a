<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="svg" viewBox="0 0 533.48 231.81">
        <defs>
          <filter id="Sinlrjqdu6" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="6"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#A1DA9C"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <mask id="maskSinlrjqduj">
            <path id="key" fill="#FFFFFF" d="M104.158,52.077C104.158,23.318,80.839,0,52.079,0S0,23.318,0,52.077C0,76.79,17.236,97.44,40.328,102.778 v127.797h23.505v-8.165h21.771v-11.875H63.833v-8.163h10.886v-11.876H63.833v-7.668h18.801v-11.875H63.833v-68.178 C86.925,97.44,104.158,76.79,104.158,52.077z M52.076,83.869c-17.555,0-31.789-14.234-31.789-31.792 c0-17.555,14.234-31.789,31.789-31.789c17.554,0,31.79,14.234,31.79,31.789C83.866,69.634,69.63,83.869,52.076,83.869z"/>
          </mask>
          <filter id="Sinlrjqduu" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#dae4e6"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu15" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d6e4d5"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu1g" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d5e5cc"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu1r" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d2e5bd"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu22" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#cfe6b1"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu2d" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d1e6b7"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu2o" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d1e6ba"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu2z" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d2e5bd"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu3a" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d2e5be"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu3l" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d2e5be"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu3w" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d1e6bb"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu47" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d1e6b7"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu4i" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#cfe6b1"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu4t" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d0e6b3"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu54" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#d0e6b3"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu5f" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#cfe6b1"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu5q" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#cfe6b1"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu61" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#cfe6b1"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
          <filter id="Sinlrjqdu6c" filterUnits="userSpaceOnUse">
            <feGaussianBlur in="SourceAlpha" stdDeviation="8"/>
            <feOffset dx="0" dy="0" result="offsetblur"/>
            <feFlood floodColor="#A1DA9C"/>
            <feComposite in2="offsetblur" operator="in"/>
            <feComponentTransfer>
              <feFuncA type="linear" slope="1"/>
            </feComponentTransfer>
            <feMerge>
              <feMergeNode/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
      
        <g transform="matrix(1,0,0,1,0,0)">
          <g id="bars-right" transform="matrix(1,0,0,1,438,0)">
            <path fill="#9D9B95" d="M78.654,228.985c-5.619,3.627-11.41,3.895-17.422,0V2.526c6.018-3.165,11.797-2.774,17.422,0V228.985z"/>
            <linearGradient id="SVGID_31_" gradientUnits="userSpaceOnUse" x1="61.232" y1="116.052" x2="78.654" y2="116.052">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_31_')" d="M78.654,228.985c-5.619,3.627-11.41,3.895-17.422,0V2.526c6.018-3.165,11.797-2.774,17.422,0 V228.985z"/>
            <path fill="#9D9B95" d="M0 0H10.004V231.513H0z"/>
            <linearGradient id="SVGID_32_" gradientUnits="userSpaceOnUse" y1="115.756" x2="10.004" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_32_')" d="M0 0H10.004V231.513H0z"/>
            <path fill="#9D9B95" d="M10.004 100.714H95.477V110.454H10.004z"/>
            <linearGradient id="SVGID_33_" gradientUnits="userSpaceOnUse" x1="52.74" y1="100.714" x2="52.74" y2="110.454">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_33_')" d="M10.004 100.714H95.477V110.454H10.004z"/>
          </g>
          <g id="bars-left">
            <path fill="#9D9B95" d="M173.571,228.69c-5.613,3.628-11.408,3.894-17.42,0V2.229c6.02-3.164,11.799-2.773,17.42,0V228.69z"/>
            <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="156.151" y1="115.756" x2="173.571" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_1_')" d="M173.571,228.69c-5.613,3.628-11.408,3.894-17.42,0V2.229c6.02-3.164,11.799-2.773,17.42,0V228.69z"/>
            <path fill="#9D9B95" d="M224.622 0H234.63000000000002V231.513H224.622z"/>
            <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="224.622" y1="115.756" x2="234.629" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_2_')" d="M224.622 0H234.63000000000002V231.513H224.622z"/>
            <path fill="#9D9B95" d="M105.071,228.69c-5.613,3.628-11.408,3.894-17.42,0V2.229c6.02-3.164,11.799-2.773,17.42,0V228.69z"/>
            <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="87.651" y1="115.756" x2="105.071" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_3_')" d="M105.071,228.69c-5.613,3.628-11.408,3.894-17.42,0V2.229c6.02-3.164,11.799-2.773,17.42,0V228.69z"/>
            <path fill="#9D9B95" d="M36.424,228.69c-5.614,3.628-11.409,3.894-17.418,0V2.229c6.017-3.164,11.796-2.773,17.418,0V228.69z"/>
            <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="19.006" y1="115.756" x2="36.424" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_4_')" d="M36.424,228.69c-5.614,3.628-11.409,3.894-17.418,0V2.229c6.017-3.164,11.796-2.773,17.418,0 V228.69z"/>
            <path fill="#9D9B95" d="M0 100.713H224.516V110.45299999999999H0z"/>
            <linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="112.258" y1="100.713" x2="112.258" y2="110.453">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_5_')" d="M0 100.713H224.516V110.45299999999999H0z"/>
          </g>
          <g id="bars-door" transform="matrix(1,0,0,1,235,0)">
            <path fill="#9D9B95" d="M144.609,228.69c-5.615,3.628-11.409,3.894-17.421,0V2.229c6.02-3.164,11.799-2.773,17.421,0V228.69z"/>
            <linearGradient id="SVGID_23_" gradientUnits="userSpaceOnUse" x1="127.189" y1="115.756" x2="144.609" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_23_')" d="M144.609,228.69c-5.615,3.628-11.409,3.894-17.421,0V2.229c6.02-3.164,11.799-2.773,17.421,0 V228.69z"/>
            <path fill="#9D9B95" d="M75.962,228.69c-5.614,3.628-11.409,3.894-17.418,0V2.229c6.017-3.164,11.796-2.773,17.418,0V228.69z"/>
            <linearGradient id="SVGID_24_" gradientUnits="userSpaceOnUse" x1="58.544" y1="115.756" x2="75.962" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_24_')" d="M75.962,228.69c-5.614,3.628-11.409,3.894-17.418,0V2.229c6.017-3.164,11.796-2.773,17.418,0 V228.69z"/>
            <path fill="#9D9B95" d="M193.242 0H203.25V231.513H193.242z"/>
            <linearGradient id="SVGID_25_" gradientUnits="userSpaceOnUse" x1="193.242" y1="115.756" x2="203.25" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_25_')" d="M193.242 0H203.25V231.513H193.242z"/>
            <path fill="#9D9B95" d="M0 0H10.004V231.513H0z"/>
            <linearGradient id="SVGID_26_" gradientUnits="userSpaceOnUse" y1="115.756" x2="10.004" y2="115.756">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_26_')" d="M0 0H10.004V231.513H0z"/>
            <path fill="#9D9B95" d="M10.012 100.713H193.242V110.45299999999999H10.012z"/>
            <linearGradient id="SVGID_27_" gradientUnits="userSpaceOnUse" x1="101.627" y1="100.713" x2="101.627" y2="110.453">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".03" stop-color="#D5D4D4"/>
              <stop offset=".084" stop-color="#F3F2F2"/>
              <stop offset=".097" stop-color="#FFFFFF"/>
              <stop offset=".133" stop-color="#FBFAFA"/>
              <stop offset=".151" stop-color="#F0EEEE"/>
              <stop offset=".165" stop-color="#E1DFDE"/>
              <stop offset=".177" stop-color="#CDCBCB"/>
              <stop offset=".188" stop-color="#B6B4B4"/>
              <stop offset=".197" stop-color="#9D9B9B"/>
              <stop offset=".206" stop-color="#828282"/>
              <stop offset=".206" stop-color="#808080"/>
              <stop offset=".794" stop-color="#808080"/>
              <stop offset=".933" stop-color="#444444"/>
              <stop offset="1" stop-color="#CACACA"/>
            </linearGradient>
            <path fill="url('#SVGID_27_')" d="M10.012 100.713H193.242V110.45299999999999H10.012z"/>
            <path fill="#9D9B95" d="M193.242,165.937c0,0.93-0.752,1.683-1.682,1.683h-68.516c-0.93,0-1.682-0.753-1.682-1.683v-53.803 c0-0.93,0.752-1.682,1.682-1.682h68.516c0.93,0,1.682,0.752,1.682,1.682V165.937z"/>
            <linearGradient id="SVGID_28_" gradientUnits="userSpaceOnUse" x1="121.363" y1="139.036" x2="193.242" y2="139.036">
              <stop offset="0" stop-color="#CACACA"/>
              <stop offset=".051" stop-color="#D5D4D4"/>
              <stop offset=".141" stop-color="#F3F2F2"/>
              <stop offset=".164" stop-color="#FFFFFF"/>
              <stop offset=".703" stop-color="#444444"/>
            </linearGradient>
            <path fill="url('#SVGID_28_')" d="M193.242,165.937c0,0.93-0.752,1.683-1.682,1.683h-68.516c-0.93,0-1.682-0.753-1.682-1.683v-53.803 c0-0.93,0.752-1.682,1.682-1.682h68.516c0.93,0,1.682,0.752,1.682,1.682V165.937z"/>
            <defs>
              <path id="SVGID_7_" d="M191.561,164.679c0,0.886-0.715,1.604-1.602,1.604h-65.312c-0.883,0-1.602-0.719-1.602-1.604v-51.286 c0-0.886,0.719-1.605,1.602-1.605h65.312c0.887,0,1.602,0.72,1.602,1.605V164.679z"/>
            </defs>
            <use xlinkHref="#SVGID_7_" overflow="visible" fill="#9D9B95"/>
            <clipPath id="SVGID_8_">
              <use xlinkHref="#SVGID_7_" overflow="visible"/>
            </clipPath>
            <radialGradient id="SVGID_9_" cx="135.898" cy="105.583" r="57.217" gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#FFFAEC"/>
              <stop offset=".303" stop-color="#C4BFB9"/>
              <stop offset=".593" stop-color="#9D9A98"/>
              <stop offset=".818" stop-color="#888786"/>
              <stop offset=".952" stop-color="#808080"/>
            </radialGradient>
            <path clipPath="url('#SVGID_8_')" fill="url('#SVGID_9_')" d="M193.113,105.584c0-31.6-25.615-57.218-57.213-57.218 c-31.599,0-57.218,25.619-57.218,57.218c0,31.598,25.62,57.217,57.218,57.217C167.498,162.801,193.113,137.182,193.113,105.584z"/>
            <radialGradient id="SVGID_10_" cx="-74.63" cy="198.459" r="10.138" gradientTransform="matrix(.546 .123 -.22 .976 263.121 -45.312)" gradientUnits="userSpaceOnUse">
              <stop offset="0" stop-color="#FFFAEC"/>
              <stop offset=".303" stop-color="#C4BFB9"/>
              <stop offset=".593" stop-color="#9D9A98"/>
              <stop offset=".818" stop-color="#888786"/>
              <stop offset=".952" stop-color="#808080"/>
            </radialGradient>
            <path opacity=".5" clipPath="url('#SVGID_8_')" fill="url('#SVGID_10_')" d="M184.328,140.379c1.23-5.462-0.25-10.446-3.311-11.136 c-3.055-0.688-6.529,3.183-7.762,8.644c-1.229,5.461,0.256,10.448,3.311,11.135C179.625,149.71,183.098,145.84,184.328,140.379z"/>
            <path fill="#4B4A4B" d="M189.955,160.481c-0.016-2.108-1.738-3.805-3.852-3.789c-2.111,0.019-3.805,1.74-3.785,3.849 c0.014,2.111,1.738,3.803,3.844,3.79C188.271,164.313,189.969,162.592,189.955,160.481z"/>
            <path fill="#82807A" d="M189.18,160.487c-0.014-1.681-1.389-3.035-3.07-3.021c-1.678,0.013-3.033,1.388-3.021,3.069 c0.012,1.685,1.389,3.034,3.07,3.022C187.84,163.542,189.195,162.168,189.18,160.487z"/>
            <radialGradient id="SVGID_11_" cx="48.747" cy="253.862" r="5.316" gradientTransform="matrix(1 -.008 .008 1 136.565 -94.289)" gradientUnits="userSpaceOnUse">
              <stop offset=".042" stop-color="#FFFFFF"/>
              <stop offset="1" stop-color="#444444"/>
            </radialGradient>
            <path fill="url('#SVGID_11_')" d="M189.18,160.487c-0.014-1.681-1.389-3.035-3.07-3.021c-1.678,0.013-3.033,1.388-3.021,3.069 c0.012,1.685,1.389,3.034,3.07,3.022C187.84,163.542,189.195,162.168,189.18,160.487z"/>
            <path fill="#4B4A4B" d="M189.955,117.53c-0.016-2.109-1.738-3.806-3.852-3.79c-2.111,0.019-3.805,1.74-3.785,3.849 c0.014,2.112,1.738,3.805,3.844,3.789C188.271,121.361,189.969,119.64,189.955,117.53z"/>
            <path fill="#82807A" d="M189.18,117.535c-0.014-1.681-1.389-3.035-3.07-3.021c-1.678,0.014-3.033,1.388-3.021,3.07 c0.012,1.684,1.389,3.033,3.07,3.022C187.84,120.59,189.195,119.216,189.18,117.535z"/>
            <radialGradient id="SVGID_12_" cx="49.086" cy="210.913" r="5.317" gradientTransform="matrix(1 -.008 .008 1 136.565 -94.289)" gradientUnits="userSpaceOnUse">
              <stop offset=".042" stop-color="#FFFFFF"/>
              <stop offset="1" stop-color="#444444"/>
            </radialGradient>
            <path fill="url('#SVGID_12_')" d="M189.18,117.535c-0.014-1.681-1.389-3.035-3.07-3.021c-1.678,0.014-3.033,1.388-3.021,3.07 c0.012,1.684,1.389,3.033,3.07,3.022C187.84,120.59,189.195,119.216,189.18,117.535z"/>
            <path fill="#4B4A4B" d="M132.29,160.481c-0.019-2.108-1.741-3.805-3.851-3.789c-2.107,0.019-3.803,1.74-3.785,3.849 c0.013,2.111,1.738,3.803,3.844,3.79C130.607,164.313,132.305,162.592,132.29,160.481z"/>
            <path fill="#82807A" d="M131.516,160.487c-0.016-1.681-1.388-3.035-3.07-3.021c-1.678,0.013-3.031,1.388-3.021,3.069 c0.014,1.685,1.387,3.034,3.07,3.022C130.176,163.542,131.527,162.168,131.516,160.487z"/>
            <radialGradient id="SVGID_13_" cx="-8.914" cy="253.407" r="5.316" gradientTransform="matrix(1 -.008 .008 1 136.565 -94.289)" gradientUnits="userSpaceOnUse">
              <stop offset=".042" stop-color="#FFFFFF"/>
              <stop offset="1" stop-color="#444444"/>
            </radialGradient>
            <path fill="url('#SVGID_13_')" d="M131.516,160.487c-0.016-1.681-1.388-3.035-3.07-3.021c-1.678,0.013-3.031,1.388-3.021,3.069 c0.014,1.685,1.387,3.034,3.07,3.022C130.176,163.542,131.527,162.168,131.516,160.487z"/>
            <path fill="#4B4A4B" d="M132.29,117.53c-0.019-2.109-1.741-3.806-3.851-3.79c-2.107,0.019-3.803,1.74-3.785,3.849 c0.013,2.112,1.738,3.805,3.844,3.789C130.607,121.361,132.305,119.64,132.29,117.53z"/>
            <path fill="#82807A" d="M131.516,117.535c-0.016-1.681-1.388-3.035-3.07-3.021c-1.678,0.014-3.031,1.388-3.021,3.07 c0.014,1.684,1.387,3.033,3.07,3.022C130.176,120.59,131.527,119.216,131.516,117.535z"/>
            <radialGradient id="SVGID_14_" cx="-8.575" cy="210.457" r="5.317" gradientTransform="matrix(1 -.008 .008 1 136.565 -94.289)" gradientUnits="userSpaceOnUse">
              <stop offset=".042" stop-color="#FFFFFF"/>
              <stop offset="1" stop-color="#444444"/>
            </radialGradient>
            <path fill="url('#SVGID_14_')" d="M131.516,117.535c-0.016-1.681-1.388-3.035-3.07-3.021c-1.678,0.014-3.031,1.388-3.021,3.07 c0.014,1.684,1.387,3.033,3.07,3.022C130.176,120.59,131.527,119.216,131.516,117.535z"/>
            <path fill="#3E3B35" d="M180.58,137.336c-0.018-2.151-1.775-3.882-3.928-3.864c-2.148,0.017-3.877,1.772-3.859,3.924 c0.012,1.487,0.855,2.768,2.08,3.416l0.031,3.924c0.008,1.015,0.838,1.833,1.852,1.825c1.02-0.008,1.836-0.838,1.824-1.855 l-0.027-3.924C179.768,140.117,180.594,138.82,180.58,137.336z"/>
            <radialGradient id="SVGID_15_" cx="33.962" cy="234.716" r="10.762" gradientTransform="matrix(1 -.008 .008 1 136.565 -94.289)" gradientUnits="userSpaceOnUse">
              <stop offset=".042" stop-color="#FFFFFF"/>
              <stop offset="1" stop-color="#444444"/>
            </radialGradient>
            <path fill="url('#SVGID_15_')" d="M180.58,137.336c-0.018-2.151-1.775-3.882-3.928-3.864c-2.148,0.017-3.877,1.772-3.859,3.924 c0.012,1.487,0.855,2.768,2.08,3.416l0.031,3.924c0.008,1.015,0.838,1.833,1.852,1.825c1.02-0.008,1.836-0.838,1.824-1.855 l-0.027-3.924C179.768,140.117,180.594,138.82,180.58,137.336z"/>
          </g>
        </g>
      </svg>