{"v": "5.12.2", "fr": 60, "ip": 0, "op": 600, "w": 1000, "h": 500, "nm": "#03_Gate Closed State Animation", "ddd": 0, "assets": [{"id": "image_0", "w": 382, "h": 535, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 441, "h": 535, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_2", "w": 180, "h": 536, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "nm": "HELPLESS Text Color_01", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "HELPLESS", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 60, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.3, -53.175, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 150, "f": "Poppins-ExtraBold", "t": "HELPLESS", "ca": 0, "j": 2, "tr": -25, "lh": 180, "ls": 0, "fc": [0.639, 0.643, 0.992]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_1", "nm": "HELPLESS Text Color_02", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "HELPLESS", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 60, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-0.3, -53.175, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 150, "f": "Poppins-ExtraBold", "t": "HELPLESS", "ca": 0, "j": 2, "tr": -25, "lh": 180, "ls": 0, "fc": [0.396, 0.38, 0.902]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}]}], "fonts": {"list": [{"origin": 0, "fPath": "", "fClass": "", "fFamily": "<PERSON><PERSON><PERSON>", "fWeight": "", "fStyle": "ExtraBold", "fName": "Poppins-ExtraBold", "ascent": 73.9990234375}]}, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "Door.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [631.256, 250.237, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [191, 267.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "L_Fix Bars.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [220.25, 250.237, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [220.5, 267.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 600, "st": -1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "R_Fix Bars.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [910.779, 250.792, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [90, 268, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 600, "st": 0, "bm": 0}], "markers": [], "props": {}}