<svg id='svg' width='100%' viewBox='-5 -5 175 250' xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
          <filter id='Sinlrjqdu6' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='6' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#A1DA9C' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <mask id='maskSinlrjqduj'>
            <path
              id='key'
              fill='#FFFFFF'
              d='M104.158,52.077C104.158,23.318,80.839,0,52.079,0S0,23.318,0,52.077C0,76.79,17.236,97.44,40.328,102.778 v127.797h23.505v-8.165h21.771v-11.875H63.833v-8.163h10.886v-11.876H63.833v-7.668h18.801v-11.875H63.833v-68.178 C86.925,97.44,104.158,76.79,104.158,52.077z M52.076,83.869c-17.555,0-31.789-14.234-31.789-31.792 c0-17.555,14.234-31.789,31.789-31.789c17.554,0,31.79,14.234,31.79,31.789C83.866,69.634,69.63,83.869,52.076,83.869z'
            />
          </mask>
          <filter id='Sinlrjqduu' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#dae4e6' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu15' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d6e4d5' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu1g' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d5e5cc' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu1r' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d2e5bd' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu22' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#cfe6b1' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu2d' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d1e6b7' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu2o' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d1e6ba' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu2z' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d2e5bd' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu3a' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d2e5be' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu3l' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d2e5be' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu3w' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d1e6bb' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu47' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d1e6b7' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu4i' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#cfe6b1' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu4t' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d0e6b3' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu54' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#d0e6b3' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu5f' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#cfe6b1' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu5q' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#cfe6b1' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu61' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#cfe6b1' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
          <filter id='Sinlrjqdu6c' filterUnits='userSpaceOnUse'>
            <feGaussianBlur in='SourceAlpha' stdDeviation='8' />
            <feOffset dx='0' dy='0' result='offsetblur' />
            <feFlood flood-color='#A1DA9C' />
            <feComposite in2='offsetblur' operator='in' />
            <feComponentTransfer>
              <feFuncA type='linear' slope='1' />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in='SourceGraphic' />
            </feMerge>
          </filter>
        </defs>
        <g transform='matrix(1,0,0,1,0,0)'> 
          <g filter="url('#Sinlrjqdu6c')">
            <g mask="url('#maskSinlrjqduj')">
              <rect x='0' y='0' width='104.158' height='230.575' fill='#dbe4eb' />
            </g>
          </g>
          <g transform="matrix(1,0,0,1,110,230.575)">
            <path d='M0,0L15,-15L60,-15L60,15L15,15z' fill='#035200' />
            <text x='16' y='5' fill='#ffffff'>
              0 / 4
            </text>
          </g>

        </g>
      </svg>