name: breakingfree_v2
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

# ios:
version: 1.0.0+16

# ios:
# version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  audioplayers: ^6.1.0
  auto_size_text: ^3.0.0
  better_player_plus: ^1.0.7
  bloc: ^8.1.4
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  chewie: ^1.8.5
  dio: ^5.7.0
  dotted_border: ^2.1.0
  dropdown_button2: ^2.3.9
  easy_localization: ^3.0.7
  fl_chart: ^0.69.0
  flick_video_player: ^0.9.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_cached_pdfview: ^0.4.2
  flutter_gen: ^5.7.0
  flutter_hooks: ^0.20.5
  flutter_html: ^3.0.0-beta.2
  flutter_i18n: ^0.36.2
  flutter_pdfview: ^1.3.3
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.10+1
  form_field_validator: ^1.1.0
  freezed_annotation: ^2.4.2
  geocoding: ^3.0.0
  geolocator: ^13.0.1
  google_maps_flutter: ^2.9.0
  get_it: ^8.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.0
  http: ^1.2.2
  logger: ^2.4.0
  
  multi_value_listenable_builder: ^0.0.2
  open_file: ^3.5.9
  permission_handler: ^11.3.1
  custom_info_window: ^1.0.1
  responsive_framework: ^1.5.1
  simple_ripple_animation: ^0.0.9
  url_launcher: ^6.3.0
  shimmer: ^3.0.0
  http_mock_adapter: ^0.6.1
  device_info_plus: ^11.2.0
  flutter_local_notifications: ^18.0.1
  geofence_service: ^6.0.0+1
  location: ^6.0.2
  flutter_timezone: ^3.0.1
  confetti: ^0.8.0
  lottie: ^3.2.0
  change_app_package_name: ^1.5.0
  visibility_detector: ^0.4.0+2
  syncfusion_flutter_charts:
  media_store_plus: ^0.1.3
  app_links: ^6.4.0
  shared_preferences: ^2.5.3
  connectivity_plus: ^6.1.4
dependency_overrides:
  flutter_widget_from_html_core: ^0.15.2

dev_dependencies:
  build_runner: ^2.4.11
  envied_generator: ^0.5.4+1
  flutter_gen_runner: ^5.4.0
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.2
  integration_test:
    sdk: flutter
  mockito: ^5.4.4
  test: ^1.25.7
    

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
     - assets/images/
     - assets/icons/
     - assets/icons/sidemenu/
     - assets/icons/dashboard/
     - assets/icons/dashboard/trophy/
     - assets/icons/dashboard/medal/
     - assets/icons/dashboard/rosette/
     - assets/icons/dashboard/garland/
     - assets/icons/info_page/
     - assets/translations/
     - assets/icons/drinking_icons/
     - assets/icons/drinking_icons/alcopops/
     - assets/icons/drinking_icons/beer/
     - assets/icons/drinking_icons/wine/
     - assets/icons/drinking_icons/normally_drink/
     - assets/icons/drinking_icons/spirits/
     - assets/icons/drinking_aus_icons/
     - assets/icons/drinking_aus_icons/aus_alcopops/
     - assets/icons/drinking_aus_icons/aus_beer/
     - assets/icons/drinking_aus_icons/aus_cider/
     - assets/icons/drinking_aus_icons/aus_normally drink/
     - assets/icons/drinking_aus_icons/aus_premixed spirits/
     - assets/icons/drinking_aus_icons/aus_sherry_port/
     - assets/icons/drinking_aus_icons/aus_sparkling wine/
     - assets/icons/drinking_aus_icons/aus_spirits/
     - assets/icons/drinking_aus_icons/aus_wine/
     - assets/icons/action_icons/
     - assets/icons/anim_asset/
     - assets/icons/info_icons/
     - assets/icons/action_icons/key_one_animation.json
     - assets/icons/action_icons/key_two_animation.json
     - assets/icons/action_icons/key_three_animation.json
     - assets/icons/action_icons/key_four_animation.json
  
  fonts:

    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-MediumItalic.ttf
    - family: Anton
      fonts:
        - asset: assets/fonts/Anton-Regular.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
# flutter_gen:
#   {
#     integrations: { flutter_svg: true },
#   }